using Auditdata.InventoryService.InventoryPathwayIntegration.Models.Inventory;
using Auditdata.InventoryService.InventoryPathwayIntegration.Persistence;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.Extensions.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Auditdata.InventoryService.InventoryPathwayIntegration.Models.Pathways;

namespace Auditdata.InventoryService.InventoryPathwayIntegration
{
    public class InventoryPathwayIntegrationFunction
    {

        private readonly IDbContextFactory<PathwayDbContext> _pathwayDbContext;
        private readonly IDbContextFactory<InventoryDbContext> _inventoryDbContext;

        public InventoryPathwayIntegrationFunction(IDbContextFactory<PathwayDbContext> pathwayDbContext, IDbContextFactory<InventoryDbContext> inventoryDbContext)
        {
            _pathwayDbContext = pathwayDbContext;
            _inventoryDbContext = inventoryDbContext;
        }


        [FunctionName("InventoryPathwayIntegration")]
        public async Task<IActionResult> Run(
            [HttpTrigger(AuthorizationLevel.Function, "get", "post", Route = null)] HttpRequest req,
            ILogger log)
        {
            log.LogInformation("Inventory&Pathway migration functions is triggered.");

            await using (var pathwayContext = await _pathwayDbContext.CreateDbContextAsync())
            {
                var availablePathways = await pathwayContext.PathwayTemplates.Where(c => !c.IsDeleted).ToListAsync();

                await using (var inventoryContext = await _inventoryDbContext.CreateDbContextAsync())
                {
                    var hardCodedProductPathways = await inventoryContext.ProductPathways
                        .Include(c => c.Pathway)
                        .Include(c => c.Product)
                        .Where(c => c.Pathway != null)
                        .ToListAsync();

                    if (hardCodedProductPathways.Any())
                    {
                        foreach (var productPathway in hardCodedProductPathways)
                        {
                            var exactPathwayInEditor = availablePathways.FirstOrDefault(c => c.Title == productPathway.Pathway.Name);

                            if (exactPathwayInEditor is not null)
                            {
                                var pathwayIsNotAssignedToProduct = !
                                    await inventoryContext.ProductPathways
                                        .Where(c =>
                                            c.ProductId == productPathway.ProductId &&
                                            c.Pathway == null &&
                                            c.PathwayId == exactPathwayInEditor.Id)
                                        .AnyAsync();

                                if (pathwayIsNotAssignedToProduct)
                                {
                                    await AssignPathwayToProductFromEditor(inventoryContext, exactPathwayInEditor, productPathway);
                                }
                            }
                        }
                    }
                }

                await AssignPathwayTemplateIdsForInvoiceSupportedPathways(pathwayContext, availablePathways);

            }

            return new OkObjectResult("");
        }

        private async Task AssignPathwayTemplateIdsForInvoiceSupportedPathways(PathwayDbContext pathwayDbContext, List<PathwayTemplate> availablePathways)
        {
            var patientPathwaysWithInvoiceStepType = await
                pathwayDbContext.Pathways
                    .Include(c => c.Steps.Where(d => d.Type == PathwayStepType.Invoice.ToString()))
                    .Where(c => c.Status == PathwayStatus.Active.ToString() && c.PathwayTemplateId == null)
                    .ToListAsync();

            foreach (var patientPathway in patientPathwaysWithInvoiceStepType)
            {
                var pathwayTemplate = availablePathways.FirstOrDefault(c => c.Title == patientPathway.Name);

                if (pathwayTemplate is not null)
                {
                    patientPathway.PathwayTemplateId = pathwayTemplate.Id;
                    pathwayDbContext.Pathways.Update(patientPathway);
                    await pathwayDbContext.SaveChangesAsync();
                }
            }
        }

        private async Task AssignPathwayToProductFromEditor(
            InventoryDbContext inventoryDbContext,
            PathwayTemplate pathwayInEditor,
            ProductPathway productPathway)
        {
            var newProductPathway = new ProductPathway
            {
                Id = Guid.NewGuid(),
                PathwayId = pathwayInEditor.Id,
                ProductId = productPathway.ProductId,
            };

            inventoryDbContext.ProductPathways.Add(newProductPathway);
            await inventoryDbContext.SaveChangesAsync();
        }
    }
}
