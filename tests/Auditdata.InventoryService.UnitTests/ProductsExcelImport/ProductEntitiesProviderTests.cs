using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Abstractions.Azure;
using Auditdata.InventoryService.Core.Features.ProductsExcelImport.Models.Excel;
using Auditdata.InventoryService.Core.Features.ProductsExcelImport.Services;
using Auditdata.InventoryService.Core.OperationContext;
using Auditdata.InventoryService.UnitTests.Common;
using AutoFixture.Xunit2;
using FluentAssertions;

namespace Auditdata.InventoryService.UnitTests.ProductsExcelImport;

public class ProductEntitiesProviderTests : BaseDbContextTest
{
    private readonly ProductEntitiesProvider _sut;
    private readonly IFixture _fixture = new RecursiveFixture();
    private readonly Mock<IInventoryServiceOperationContext> _operationContextMock = new();
    private readonly Mock<IEventPublisher> _eventPublisher = new();
    private readonly Mock<IAzureServiceBusPublisher> _azureServiceBusPublisher = new();

    public ProductEntitiesProviderTests()
    {
        _sut = new ProductEntitiesProvider(
            DbContext, 
            _operationContextMock.Object, 
            _eventPublisher.Object,
            _azureServiceBusPublisher.Object);
    }

    [Theory]
    [InlineAutoData]
    public async Task GetOrCreateProductsEntitiesAsync_Should_GetOrCreateEntities_When_ImportDataProvided(
        string[] manufacturerNames, string[] supplierNames,
        string[] batteryTypeNames, string[] colorNames)
    {
        // Arrange
        var hearingAidBTE = _fixture.Build<HearingAidType>().With(x => x.Name, "BTE")
            .With(x=> x.IsDeleted,false).Create();
        var hearingAidRIC = _fixture.Build<HearingAidType>().With(x => x.Name, "RIC")
            .With(x=> x.IsDeleted,false).Create();
        var importData = _fixture.CreateMany<HearingAidExcelModel>(6).ToArray();
        var manufacturers = manufacturerNames.Select(x => Manufacturer.Create(x)).ToArray();
        var suppliers = supplierNames.Select(x => Supplier.Create(x)).ToArray();
        var batteryTypes = batteryTypeNames.Select(x => BatteryType.Create(x)).ToArray();
        var colors = colorNames.Select(x => Color.Create(x));
        DbContext.Manufacturers.AddRange(manufacturers);
        DbContext.Suppliers.AddRange(suppliers);
        DbContext.BatteryTypes.AddRange(batteryTypes);
        DbContext.Colors.AddRange(colors);
        DbContext.HearingAidTypes.Add(hearingAidBTE);
        DbContext.HearingAidTypes.Add(hearingAidRIC);
        await DbContext.SaveChangesAsync();
        
        importData[1].Manufacturer = manufacturerNames[1];
        importData[2].Manufacturer = manufacturerNames[1];
        importData[3].Manufacturer = manufacturerNames[2];
        importData[1].Supplier = supplierNames[0];
        importData[4].BatteryTypes = batteryTypeNames[0];
        importData[4].Colors = string.Join("||", colorNames);
        importData[1].Type = "BTE";
        importData[2].Type = "RIC";
        importData[3].Type = "RIC";

        // Act
        var result = await _sut.GetOrCreateProductsEntitiesAsync(importData, CancellationToken.None);

        // Assert
        result.ManufacturerIds.Count.Should().Be(5);
        result.ManufacturerIds.Keys.Should().Contain(new[] { manufacturerNames[1], manufacturerNames[2] });
        result.SupplierIds.Count.Should().Be(6);
        result.SupplierIds.Keys.Should().Contain(new[] { supplierNames[0] });
        result.BatteryTypeIds.Count.Should().Be(6);
        result.BatteryTypeIds.Keys.Should().Contain(new[] { batteryTypeNames[0] });
        result.ColorIds.Count.Should().Be(8);
        result.ColorIds.Keys.Should().Contain(colorNames);
        result.HearingAidTypeIds.Count.Should().Be(2);
        result.HearingAidTypeIds.Keys.Should().Contain(new[] { "BTE", "RIC" });
    }
}
