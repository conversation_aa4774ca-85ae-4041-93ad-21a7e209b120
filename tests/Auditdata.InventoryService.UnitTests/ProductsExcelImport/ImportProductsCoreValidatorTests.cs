using Auditdata.Infrastructure.Contracts.CountryLayers;
using Auditdata.Infrastructure.Excel.Exceptions;
using Auditdata.Infrastructure.Excel.Models;
using Auditdata.InventoryService.Core.Abstractions.Clients;
using Auditdata.InventoryService.Core.Constants;
using Auditdata.InventoryService.Core.Entities.Products;
using Auditdata.InventoryService.Core.Features.ProductsExcelImport.Models.Excel;
using Auditdata.InventoryService.Core.Features.ProductsExcelImport.Models.Excel.Abstractions;
using Auditdata.InventoryService.Core.Features.ProductsExcelImport.Validation;
using Auditdata.InventoryService.UnitTests.Common;
using Auditdata.Transport.Contracts.Exceptions;
using AutoFixture.Xunit2;
using FluentAssertions;
using System.Reflection;

namespace Auditdata.InventoryService.UnitTests.ProductsExcelImport;

public class ImportProductsCoreValidatorTests : BaseSqlLiteDbContextTest
{
    private readonly ImportProductsCoreValidator _sut;

    private readonly IFixture _fixture;
    private readonly Mock<IThirdPartyPayerClient> _clientMock = new();

    public ImportProductsCoreValidatorTests()
    {
        _sut = new ImportProductsCoreValidator(DbContext, _clientMock.Object);
        _fixture = new RecursiveFixture();
    }

    [Theory]
    [InlineAutoData]
    public void ValidateImportResult_Should_ThrowWithFirstExMessage_When_ImportResultNotSuccessful(
    string firstExceptionMessage)
    {
        // Arrange
        var importResult = new ExcelImportResult<LdWarrantyExcelModel>
        {
            IsSuccess = false,
            Exceptions = new ExcelFormatException[]
            {
                new WorksheetCountException(firstExceptionMessage),
                new InvalidRecordsException("second exception"),
            }
        };

        // Act
        // Assert
        var ex = Assert.Throws<BusinessException>(() => _sut.ValidateImportResult(importResult, CountryEnum.UnitedKingdom));

        ex.Message.Should().Be(firstExceptionMessage);
    }

    [Theory]
    [InlineAutoData(25, -74, 36, 26, 678, 0.57, 0.43, 546.5, 734, "abc", "def", "ghi")]
    [InlineAutoData(-25, 74, 36, 26, 678, 0.57, 0.43, 546.5, 734, "abc", "def", "ghi")]
    [InlineAutoData(25, 74, -36, 26, 678, 0.57, 0.43, 546.5, 734, "abc", "def", "ghi")]
    [InlineAutoData(25, 74, 36, -26, 678, 0.57, 0.43, 546.5, 734, "abc", "def", "ghi")]
    [InlineAutoData(25, 74, 36, 26, -678, 0.57, 0.43, 546.5, 734, "abc", "def", "ghi")]
    [InlineAutoData(25, 74, 36, 26, 678, 2, 0.43, 546.5, 734, "abc", "def", "ghi")]
    [InlineAutoData(25, 74, 36, 26, 678, 0.57, 3, 546.5, 734, "abc", "def", "ghi")]
    [InlineAutoData(25, 74, 36, 26, 678, 0.57, 0.44, 546.5, 734, "abc", "def", "ghi")]
    [InlineAutoData(25, 74, 36, 26, 678, 0.57, 0.43, 546.5, -734, "abc", "def", "ghi")]
    [InlineAutoData(25, 74, 36, 26, 678, 0.57, 0.43, 546.5, 734, "ab_c", "def", "ghi")]
    [InlineAutoData(25, 74, 36, 26, 678, 0.57, 0.43, 546.5, 734, "abc", "", "ghi")]
    [InlineAutoData(25, 74, 36, 26, 678, 0.57, 0.43, 546.5, 734, "abc", "def", null)]
    [InlineAutoData(25, 74, 36, 26, 678, 0.57, 0.43, 546.5, 734, "abc", "def", "ghi", "Service", true, false)]
    [InlineAutoData(25, 74, 36, 26, 678, 0.57, 0.43, 546.5, 734, "abc", "def", "ghi", "Repair Service", false, true)]
    public void ValidateImportResult_Should_Throw_When_ImportDataValidationNotSuccessful(
        int quantity, int warranty, decimal retailPrice, decimal cost, decimal maximumDiscount,
        decimal firstVat, decimal secondVat, decimal _, int ldWarranty,
        string vendorProductNumber, string batteryTypes, string? manufacturer, 
        string? category, bool isSerialized, bool controlledByStock, BatteryExcelModel excelModel)
    {
        // Arrange
        excelModel.Quantity = quantity;
        excelModel.Warranty = warranty;
        excelModel.Cost = cost;
        excelModel.RetailPrice = retailPrice;
        excelModel.MaximumDiscount = maximumDiscount;
        excelModel.FirstVAT = firstVat;
        excelModel.SecondVAT = secondVat;
        // TODO: NHS value
        //excelModel.NHSVAT = nhsVat;
        excelModel.LDWarranty = ldWarranty;
        excelModel.VendorProductNumber = vendorProductNumber;
        excelModel.BatteryTypes = batteryTypes;
        excelModel.Manufacturer = manufacturer;
        excelModel.Category = category;
        excelModel.IsSerialized = isSerialized;
        excelModel.ControlledByStock = controlledByStock;
        var excelModels = new ExcelImportData<BatteryExcelModel>[]
        {
            new ExcelImportData<BatteryExcelModel>(1, excelModel)
        };
        var importResult = new ExcelImportResult<BatteryExcelModel>
        {
            IsSuccess = true,
            ImportData = excelModels,
            PropertyColumnLetters = typeof(BatteryExcelModel)
                .GetProperties(BindingFlags.Public | BindingFlags.Instance).ToDictionary(x => x.Name, y => "test")
        };

        // Act
        // Assert
        var ex = Assert.Throws<BusinessException>(() => _sut.ValidateImportResult(importResult, CountryEnum.UnitedKingdom));
        ex.Should().NotBeNull();
    }

    [Theory]
    [InlineAutoData(25, 74, 36, 26, 678, 0.57, 0.43, 546.5, 734, "abc", "def", "ghi")]
    public void ValidateImportResult_Should_NotThrow_When_ImportResultSuccessful(
        int quantity, int warranty, decimal retailPrice, decimal cost, decimal maximumDiscount,
        decimal firstVat, decimal secondVat, decimal _, int ldWarranty,
        string vendorProductNumber, string batteryTypes, string manufacturer, BatteryExcelModel excelModel)
    {
        // Arrange
        excelModel.IsAcc = false;
        excelModel.Quantity = quantity;
        excelModel.Warranty = warranty;
        excelModel.Cost = cost;
        excelModel.RetailPrice = retailPrice;
        excelModel.MaximumDiscount = maximumDiscount;
        excelModel.FirstVAT = firstVat;
        excelModel.SecondVAT = secondVat;
        // TODO: NHS value
        //excelModel.NHSVAT = nhsVat;
        excelModel.LDWarranty = ldWarranty;
        excelModel.VendorProductNumber = vendorProductNumber;
        excelModel.BatteryTypes = batteryTypes;
        excelModel.Manufacturer = manufacturer;
        var excelModels = new ExcelImportData<BatteryExcelModel>[]
        {
            new ExcelImportData<BatteryExcelModel>(1, excelModel)
        };
        var importResult = new ExcelImportResult<BatteryExcelModel>
        {
            IsSuccess = true,
            ImportData = excelModels
        };

        // Act
        var ex = Record.Exception(() => _sut.ValidateImportResult(importResult, CountryEnum.UnitedKingdom));

        // Assert
        ex.Should().BeNull();
    }

    [Fact]
    public void ValidateIsAllIdsFound_Should_Throw_When_SomeProductsWithSomeIdsAreMissing()
    {
        // Arrange
        var existingProducts = _fixture.Build<Product>().With(x => x.IsDeleted, false).CreateMany(6).ToArray();
        var productsWithFilledIds = _fixture.Build<ExcelImportData<LdWarrantyExcelModel>>().CreateMany(5).ToArray();
        for (var i = 0; i < productsWithFilledIds.Length; i++)
        {
            productsWithFilledIds[i].Data.Id = existingProducts[i].Id;
        }

        // Act
        // Assert
        Assert.Throws<BusinessException>(
            () => _sut.ValidateIsAllIdsFound(existingProducts, productsWithFilledIds));
    }

    [Fact]
    public void ValidateIsAllIdsFound_Should_NotThrow_When_ProductsWithAllIdsArePresent()
    {
        // Arrange
        var existingProducts = _fixture.Build<Product>().With(x => x.IsDeleted, false).CreateMany(6).ToArray();
        var productsWithFilledIds = _fixture.Build<ExcelImportData<LdWarrantyExcelModel>>().CreateMany(6).ToArray();
        for (var i = 0; i < productsWithFilledIds.Length; i++)
        {
            productsWithFilledIds[i].Data.Id = existingProducts[i].Id;
        }

        // Act
        var ex = Record.Exception(() => _sut.ValidateIsAllIdsFound(existingProducts, productsWithFilledIds));

        // Assert
        ex.Should().BeNull();
    }

    [Fact]
    public async Task ValidateIfProductsAlreadyExistsAsync_Should_Throw_When_IfSomeProductsAlreadyExistsInDb()
    {
        // Arrange
        var existingProducts = _fixture.Build<Product>().With(x => x.IsDeleted, false).CreateMany(6).ToArray();
        DbContext.Products.AddRange(existingProducts);
        await DbContext.SaveChangesAsync();

        var importData = _fixture.CreateMany<ExcelImportData<LdWarrantyExcelModel>>(6).ToArray();
        importData[4].Data.Id = Guid.Empty;
        importData[3].Data.Id = existingProducts[2].Id;
        importData[3].Data.Name = existingProducts[5].Name;
        var filledIdsSet = importData.Where(x => x.Data.Id != Guid.Empty).Select(x => x.Data.Id).ToHashSet();

        // Act
        // Assert
        await Assert.ThrowsAsync<BusinessException>(
            async () => await _sut.ValidateIfProductsAlreadyExistsAsync(filledIdsSet, importData, CancellationToken.None));
    }

    [Fact]
    public async Task ValidateIfProductsAlreadyExistsAsync_Should_NotThrow_When_NoProductsAlreadyExistsInDbWhichWillNotBeChanged()
    {
        // Arrange
        var existingProducts = _fixture.Build<Product>().With(x => x.IsDeleted, false).CreateMany(6).ToArray();
        DbContext.Products.AddRange(existingProducts);
        await DbContext.SaveChangesAsync();

        var importData = _fixture.CreateMany<ExcelImportData<LdWarrantyExcelModel>>(6).ToArray();
        importData[4].Data.Id = Guid.Empty;
        importData[3].Data.Id = existingProducts[2].Id;
        importData[3].Data.Name = existingProducts[5].Name;
        importData[1].Data.Id = existingProducts[5].Id;
        var filledIdsSet = importData.Where(x => x.Data.Id != Guid.Empty).Select(x => x.Data.Id).ToHashSet();

        // Act
        var ex = await Record.ExceptionAsync(
            async () => await _sut.ValidateIfProductsAlreadyExistsAsync(filledIdsSet, importData, CancellationToken.None));

        // Assert
        ex.Should().BeNull();
    }

    [Fact]
    public void ValidateCategories_Should_Throw_When_CategoryIsNull()
    {
        // Act
        // Assert
        Assert.Throws<BusinessException>(() => _sut.ValidateCategories(
            ProductCategoryCode.LDWarranty, null, new ExcelImportResult<LdWarrantyExcelModel>()));
    }

    [Theory]
    [RecursiveInlineAutoData]
    public void ValidateCategories_Should_Throw_When_CategoriesAreInvalid(
        ProductCategory productCategory, ExcelImportData<LdWarrantyExcelModel>[] excelModels)
    {
        // Arrange
        var importResult = new ExcelImportResult<LdWarrantyExcelModel>
        {
            ImportData = excelModels,
            PropertyColumnLetters =
            new Dictionary<string, string>() { { nameof(LdWarrantyExcelModel.Category), "AB" } }
        };

        // Act
        // Assert
        Assert.Throws<BusinessException>(() => _sut.ValidateCategories(
            ProductCategoryCode.LDWarranty, productCategory.Name, importResult));
    }

    [Theory]
    [RecursiveInlineAutoData]
    public void ValidateCategories_Should_NotThrow_When_CategoriesAreValid(
        ProductCategory productCategory, ExcelImportData<LdWarrantyExcelModel>[] excelModels)
    {
        // Arrange
        foreach (var excelModel in excelModels)
        {
            excelModel.Data.Category = productCategory.Name;
        }

        var importResult = new ExcelImportResult<LdWarrantyExcelModel>
        {
            ImportData = excelModels
        };

        // Act
        var ex = Record.Exception(() => _sut.ValidateCategories(
            ProductCategoryCode.LDWarranty, productCategory.Name, importResult));

        // Assert
        ex.Should().BeNull();
    }

    [Theory]
    [InlineAutoData]
    public async Task ValidateHearingAidsAsync_Should_Throw_When_NotAllHearingAidTypesPresent(
        IReadOnlyDictionary<string, Guid> existingHearingAidTypeIds,
        CountryEnum country,
        ExcelImportData<HearingAidExcelModel>[] excelModels)
    {
        // Arrange
        var importResult = new ExcelImportResult<HearingAidExcelModel>
        {
            ImportData = excelModels,
            PropertyColumnLetters = typeof(HearingAidExcelModel)
                .GetProperties(BindingFlags.Public | BindingFlags.Instance).ToDictionary(x => x.Name, y => "test")
        };

        // Act
        // Assert
        await Assert.ThrowsAsync<BusinessException>(async () => await _sut.ValidateHearingAidsAsync(
            existingHearingAidTypeIds, importResult, country, CancellationToken.None));
    }

    [Theory]
    [InlineAutoData]
    public async Task ValidateHearingAidsAsync_Should_Throw_When_NotAllHSPCategoriesPresent(
        IReadOnlyDictionary<string, Guid> existingHearingAidTypeIds,
        ExcelImportData<HearingAidExcelModel>[] excelModels,
        string[] existingHspCategoryCodes)
    {
        // Arrange
        excelModels[0].Data.IsHSP = true;
        _clientMock.Setup(x => x.GetActiveContractHspCategoryCodesAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(existingHspCategoryCodes);

        var importResult = new ExcelImportResult<HearingAidExcelModel>
        {
            ImportData = excelModels,
            PropertyColumnLetters = typeof(HearingAidExcelModel)
                .GetProperties(BindingFlags.Public | BindingFlags.Instance).ToDictionary(x => x.Name, y => "test")
        };

        // Act
        // Assert
        await Assert.ThrowsAsync<BusinessException>(async () => await _sut.ValidateHearingAidsAsync(
            existingHearingAidTypeIds, importResult, CountryEnum.Australia, CancellationToken.None));
    }

    [Theory]
    [InlineAutoData]
    public async Task ValidateHearingAidsAsync_Should_NotThrow_When_AllHearingAidTypesPresentAndHspValuesAreCorrect(
        ExcelImportData<HearingAidExcelModel>[] excelModels, string[] existingHspCategoryCodes)
    {
        // Arrange
        var existingHearingAidTypeIds = excelModels.ToDictionary(x => x.Data.Type, y => Guid.Empty)!;
        _clientMock.Setup(x => x.GetActiveContractHspCategoryCodesAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(existingHspCategoryCodes);
        foreach (var excelModel in excelModels)
        {
            if (excelModel.Data.IsHSP)
            {
                excelModel.Data.HspCategory = existingHspCategoryCodes[0];
            }
        }

        var importResult = new ExcelImportResult<HearingAidExcelModel>
        {
            ImportData = excelModels,
            PropertyColumnLetters = typeof(HearingAidExcelModel)
                .GetProperties(BindingFlags.Public | BindingFlags.Instance).ToDictionary(x => x.Name, y => "test")
        };

        // Act
        var ex = await Record.ExceptionAsync(async () => await _sut.ValidateHearingAidsAsync(
            existingHearingAidTypeIds, importResult, CountryEnum.Australia, CancellationToken.None));

        // Assert
        ex.Should().BeNull();
    }

    [Theory]
    [InlineAutoData]
    public async Task ValidateServiceNumbersAsync_Should_Throw_When_NotAllServiceNumbersPresent(
    ExcelImportData<ServiceExcelModel>[] excelModels,
    string[] existingServiceNumbers)
    {
        // Arrange
        excelModels[0].Data.IsHSP = true;
        excelModels[0].Data.HspMaintenance = false;
        _clientMock.Setup(x => x.GetActiveContractHspServiceNumbersAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(existingServiceNumbers);

        var importResult = new ExcelImportResult<ServiceExcelModel>
        {
            ImportData = excelModels,
            PropertyColumnLetters = typeof(ServiceExcelModel)
                .GetProperties(BindingFlags.Public | BindingFlags.Instance).ToDictionary(x => x.Name, y => "test")
        };

        // Act
        // Assert
        await Assert.ThrowsAsync<BusinessException>(async () => await _sut.ValidateServiceNumbersAsync(
            importResult, CountryEnum.Australia, CancellationToken.None));
    }

    [Theory]
    [InlineAutoData]
    public async Task ValidateServiceNumbersAsync_Should_NotThrow_When_AllServiceNumbersPresent(
        ExcelImportData<ServiceExcelModel>[] excelModels, string[] existingServiceNumbers)
    {
        // Arrange
        _clientMock.Setup(x => x.GetActiveContractHspServiceNumbersAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(existingServiceNumbers);
        foreach (var excelModel in excelModels)
        {
            if (excelModel.Data.IsHSP)
            {
                excelModel.Data.HspServiceNumber = existingServiceNumbers[0];
            }
        }

        var importResult = new ExcelImportResult<ServiceExcelModel>
        {
            ImportData = excelModels,
            PropertyColumnLetters = typeof(ServiceExcelModel)
                .GetProperties(BindingFlags.Public | BindingFlags.Instance).ToDictionary(x => x.Name, y => "test")
        };

        // Act
        var ex = await Record.ExceptionAsync(async () => await _sut.ValidateServiceNumbersAsync(
            importResult, CountryEnum.Australia, CancellationToken.None));

        // Assert
        ex.Should().BeNull();
    }

    [Theory]
    [InlineAutoData]
    public void ValidateCptCodes_Should_Throw_When_NotAllCPTCodesPresent(
        IReadOnlyDictionary<string, Guid> existingCPTCodeIds,
        ExcelImportData<GeneralProductExcelModel>[] excelModels)
    {
        // Arrange
        var importResult = new ExcelImportResult<GeneralProductExcelModel>
        {
            ImportData = excelModels,
            PropertyColumnLetters = typeof(GeneralProductExcelModel)
                .GetProperties(BindingFlags.Public | BindingFlags.Instance).ToDictionary(x => x.Name, y => "test")
        };

        // Act
        // Assert
        Assert.Throws<BusinessException>(() => _sut.ValidateCptCodes(
            existingCPTCodeIds, importResult, CountryEnum.UnitedStates));
    }

    [Theory]
    [InlineAutoData]
    public void ValidateCptCodes_Should_NotThrow_When_AllCPTCodesPresent(
        IReadOnlyDictionary<string, Guid> existingCPTCodeIds,
        ExcelImportData<GeneralProductExcelModel>[] excelModels)
    {
        // Arrange
        var importResult = new ExcelImportResult<GeneralProductExcelModel>
        {
            ImportData = excelModels,
            PropertyColumnLetters = typeof(GeneralProductExcelModel)
                .GetProperties(BindingFlags.Public | BindingFlags.Instance).ToDictionary(x => x.Name, y => "test")
        };
        foreach (var excelModel in excelModels)
        {
            excelModel.Data.CPTCodes = string.Join("||", existingCPTCodeIds.Keys);
        }

        // Act
        var ex = Record.Exception(() => _sut.ValidateCptCodes(
            existingCPTCodeIds, importResult, CountryEnum.UnitedStates));

        // Assert
        ex.Should().BeNull();
    }

    [Fact]
    public async Task ValidateSupplierUsedInSkuWithExcelResult_Should_Throw_When_SupplierIsUsedInSku()
    {
        // Arrange
        var color = new Color
        {
            Id = Guid.NewGuid(),
            Name = $"name{Guid.NewGuid()}"
        };
        var supplier = new Supplier
        {
            Id = Guid.NewGuid(),
            Name = "test-supplier",
            PhoneNumber = "************",
            Address1 = "Address",
            State = "State",
            City = "City",
            IsDeleted = false
        };
        var existingProduct = _fixture.Build<Product>()
            .With(p => p.Id, Guid.NewGuid())
            .With(p => p.Supplier, supplier)
            .With(x => x.IsDeleted, false)
            .Create();

        var productColor = new ProductColor
        {
            ProductId = existingProduct.Id,
            ColorId = color.Id
        };

        var sku = new Sku
        {
            Id = Guid.NewGuid(),
            SkuValue = "skuValue",
            ProductId = existingProduct.Id,
            Product = existingProduct,
            SupplierId = supplier.Id,
            Supplier = supplier,
            IsDeleted = false,
            ColorId = color.Id
        };

        DbContext.Colors.Add(color);
        DbContext.Products.Add(existingProduct);
        DbContext.ProductColors.Add(productColor);
        DbContext.Suppliers.Add(supplier);
        DbContext.Skus.Add(sku);
        await DbContext.SaveChangesAsync();

        var importData = new ExcelImportData<AccessoryExcelModel>[]
        {
            new ExcelImportData<AccessoryExcelModel>(1, new AccessoryExcelModel { Id = existingProduct.Id, Supplier = "NewSupplier" })
        };

        var importResult = new ExcelImportResult<AccessoryExcelModel>
        {
            IsSuccess = true,
            ImportData = importData,
            PropertyColumnLetters = typeof(AccessoryExcelModel)
                .GetProperties(BindingFlags.Public | BindingFlags.Instance).ToDictionary(x => x.Name, y => "Supplier")
        };

        var existingSupplierIds = new Dictionary<string, Guid> { { "NewSupplier", Guid.NewGuid() } };

        // Act & Assert
        var ex = Assert.Throws<BusinessException>(() =>
            _sut.ValidateSupplierUsedInSkuWithExcelResult(importResult, DbContext.Products.ToList(), existingSupplierIds, CancellationToken.None));
    }

    [Fact]
    public async Task ValidateSupplierUsedInSkuWithExcelResult_Should_NotThrow_When_SupplierIsNotUsedInSku()
    {
        // Arrange
        var supplier = Supplier.Create("testSupplier");
        var existingProduct = _fixture.Build<Product>()
            .With(p => p.Id, Guid.NewGuid())
            .With(p => p.Supplier, supplier)
            .With(p => p.Skus, [])
            .With(x => x.IsDeleted, false)
            .Create();

        DbContext.Products.Add(existingProduct);
        await DbContext.SaveChangesAsync();

        var importData = new ExcelImportData<AccessoryExcelModel>[]
        {
            new(1, new AccessoryExcelModel { Id = existingProduct.Id, Supplier = "NewSupplier" })
        };

        var importResult = new ExcelImportResult<AccessoryExcelModel>
        {
            IsSuccess = true,
            ImportData = importData,
            PropertyColumnLetters = typeof(ProductExcelModelBase)
                .GetProperties(BindingFlags.Public | BindingFlags.Instance)
                .ToDictionary(x => x.Name, y => "Supplier")
        };

        var existingSupplierIds = new Dictionary<string, Guid> { { "NewSupplier", Guid.NewGuid() } };

        // Act
        var ex = Record.Exception(() =>
            _sut.ValidateSupplierUsedInSkuWithExcelResult(
                importResult, DbContext.Products.ToList(), existingSupplierIds, CancellationToken.None));

        // Assert
        ex.Should().BeNull();
    }

    [Fact]
    public async Task ValidateAttributesUsedInSkuWithExcelResult_Should_Throw_When_ColorsAreUsedInSku()
    {
        // Arrange
        var color = new Color
        {
            Id = Guid.NewGuid(),
            Name = $"name{Guid.NewGuid()}"
        };
        var existingProduct = _fixture.Build<Product>()
            .With(p => p.Id, Guid.NewGuid())
            .With(x => x.IsDeleted, false)
            .Create();

        var productColor = new ProductColor
        {
            ProductId = existingProduct.Id,
            ColorId = color.Id
        };
        var supplier = new Supplier
        {
            Id = Guid.NewGuid(),
            Name = "test-supplier",
            PhoneNumber = "************",
            Address1 = "Address",
            State = "State",
            City = "City",
            IsDeleted = false
        };
        var sku = new Sku
        {
            Id = Guid.NewGuid(),
            SkuValue = "skuValue",
            ProductId = existingProduct.Id,
            Product = existingProduct,
            SupplierId = supplier.Id,
            Supplier = supplier,
            IsDeleted = false,
            ColorId = color.Id
        };

        DbContext.Colors.Add(color);
        DbContext.Products.Add(existingProduct);
        DbContext.ProductColors.Add(productColor);
        DbContext.Suppliers.Add(supplier);
        DbContext.Skus.Add(sku);
        await DbContext.SaveChangesAsync();

        var importData = new ExcelImportData<AccessoryExcelModel>[]
        {
            new ExcelImportData<AccessoryExcelModel>(1, new AccessoryExcelModel { Id = existingProduct.Id, Colors = "Red" })
        };

        var importResult = new ExcelImportResult<AccessoryExcelModel>
        {
            IsSuccess = true,
            ImportData = importData,
            PropertyColumnLetters = typeof(AccessoryExcelModel)
                .GetProperties(BindingFlags.Public | BindingFlags.Instance).ToDictionary(x => x.Name, y => "Colors")
        };

        var existingColorIds = new Dictionary<string, Guid> { { "Red", Guid.NewGuid() } };

        // Act & Assert
        var ex = Assert.Throws<BusinessException>(() =>
            _sut.ValidateAttributesUsedInSkuWithExcelResult(importResult, DbContext.Products.ToList(), existingColorIds, new Dictionary<string, Guid>()));

        ex.ErrorCode.Should().Be(ErrorCodes.AttributesUsedInSkuAndCannotUpdate);
    }

    [Fact]
    public async Task ValidateAttributesUsedInSkuWithExcelResult_Should_Throw_When_BatteryTypesAreUsedInSku()
    {
        // Arrange
        var batteryType = new BatteryType
        {
            Id = Guid.NewGuid(),
            Name = $"name{Guid.NewGuid()}"
        };
        var existingProduct = _fixture.Build<Product>()
            .With(p => p.Id, Guid.NewGuid())
            .With(x => x.IsDeleted, false)
            .Create();

        var productBattery = new ProductBatteryType
        {
            ProductId = existingProduct.Id,
            BatteryTypeId = batteryType.Id
        };
        var supplier = new Supplier
        {
            Id = Guid.NewGuid(),
            Name = "test-supplier",
            PhoneNumber = "************",
            Address1 = "Address",
            State = "State",
            City = "City",
            IsDeleted = false
        };
        var sku = new Sku
        {
            Id = Guid.NewGuid(),
            SkuValue = "skuValue",
            ProductId = existingProduct.Id,
            Product = existingProduct,
            SupplierId = supplier.Id,
            Supplier = supplier,
            IsDeleted = false,
            BatteryTypeId = batteryType.Id
        };

        DbContext.BatteryTypes.Add(batteryType);
        DbContext.Products.Add(existingProduct);
        DbContext.ProductBatteryTypes.Add(productBattery);
        DbContext.Suppliers.Add(supplier);
        DbContext.Skus.Add(sku);
        await DbContext.SaveChangesAsync();

        var importData = new ExcelImportData<BatteryExcelModel>[]
        {
            new(1, new BatteryExcelModel { Id = existingProduct.Id, BatteryTypes = "Li-ion" })
        };

        var importResult = new ExcelImportResult<BatteryExcelModel>
        {
            IsSuccess = true,
            ImportData = importData,
            PropertyColumnLetters = typeof(BatteryExcelModel)
                .GetProperties(BindingFlags.Public | BindingFlags.Instance)
                .ToDictionary(x => x.Name, _ => "BatteryTypes")
        };

        var existingBatteryIds = new Dictionary<string, Guid> { { "Li-ion", Guid.NewGuid() } };
        var existingColorIds = new Dictionary<string, Guid>();

        // Act & Assert
        var ex = Assert.Throws<BusinessException>(() =>
            _sut.ValidateAttributesUsedInSkuWithExcelResult(
                importResult, DbContext.Products.ToList(), existingColorIds, existingBatteryIds));

        ex.ErrorCode.Should().Be(ErrorCodes.AttributesUsedInSkuAndCannotUpdate);
    }

    [Fact]
    public async Task ValidateAttributesUsedInSkuWithExcelResult_Should_NotThrow_When_ColorsAndBatteriesAreNotUsedInSku()
    {
        // Arrange
        var existingProduct = _fixture.Build<Product>()
            .With(p => p.Id, Guid.NewGuid())
            .Create();

        DbContext.Products.Add(existingProduct);
        await DbContext.SaveChangesAsync();

        var importData = new ExcelImportData<AccessoryExcelModel>[]
        {
            new ExcelImportData<AccessoryExcelModel>(1, new AccessoryExcelModel { Id = existingProduct.Id, Colors = "Blue" })
        };

        var importResult = new ExcelImportResult<AccessoryExcelModel>
        {
            IsSuccess = true,
            ImportData = importData
        };

        var existingColorIds = new Dictionary<string, Guid> { { "Blue", Guid.NewGuid() } };

        // Act
        var ex = Record.Exception(() =>
            _sut.ValidateAttributesUsedInSkuWithExcelResult(importResult, DbContext.Products.ToList(), existingColorIds, new Dictionary<string, Guid>()));

        // Assert
        ex.Should().BeNull();
    }
    
    [Theory]
    [InlineAutoData(74, 36, 26, 678, 0.57, 0.43, 546.5, 734, "abc", "ghi", "cd", "des", 45, 60)]
    public void ValidateImportResult_Should_NotThrow_When_ImportResultSuccessful_ForNzServices(
        int warranty, decimal retailPrice, decimal cost, decimal maximumDiscount,
        decimal firstVat, decimal secondVat, decimal _, int ldWarranty,
        string vendorProductNumber, string manufacturer,
        string accCode,
        string accDescription,
        decimal accPriceExclGst,
        decimal accPriceInclGst,
        ServiceExcelModel excelModel)
    {
        // Arrange
        excelModel.IsHSP = false;
        excelModel.IsAcc = true;
        excelModel.Warranty = warranty;
        excelModel.Cost = cost;
        excelModel.RetailPrice = retailPrice;
        excelModel.MaximumDiscount = maximumDiscount;
        excelModel.FirstVAT = firstVat;
        excelModel.SecondVAT = secondVat;
        excelModel.LDWarranty = ldWarranty;
        excelModel.VendorProductNumber = vendorProductNumber;
        excelModel.Manufacturer = manufacturer;
        excelModel.AccCode = accCode;
        excelModel.AccDescription = accDescription;
        excelModel.AccPriceExclGst = accPriceExclGst;
        excelModel.AccPriceInclGst = accPriceInclGst;
        var excelModels = new[]
        {
            new ExcelImportData<ServiceExcelModel>(1, excelModel)
        };
        var importResult = new ExcelImportResult<ServiceExcelModel>
        {
            IsSuccess = true,
            ImportData = excelModels
        };

        // Act
        var ex = Record.Exception(() => _sut.ValidateImportResult(importResult, CountryEnum.NewZealand));

        // Assert
        ex.Should().BeNull();
    }
}
