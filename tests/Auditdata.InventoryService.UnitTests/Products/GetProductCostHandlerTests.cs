using Auditdata.InventoryService.Core.Entities.Products;
using Auditdata.InventoryService.Core.Features.Products.Handlers;
using Auditdata.InventoryService.Core.Features.Products.Queries;
using Auditdata.InventoryService.UnitTests.Common;
using FluentAssertions;

namespace Auditdata.InventoryService.UnitTests.Products;

public class GetProductCostHandlerTests : BaseDbContextTest
{
    private readonly Fixture _fixture = new RecursiveFixture();
    private readonly GetProductCostHandler _handler;

    public GetProductCostHandlerTests()
    {
        _handler = new GetProductCostHandler(DbContext);
    }

    [Fact]
    public async Task Consume_ShouldRespond_GetProductCost()
    {
        const decimal productCost = 1234.56M;
        var product = _fixture.Build<Product>()
            .With(x => x.IsDeleted, false)
            .With(x => x.Cost, productCost)
            .Create();

        DbContext.Products.Add(product);
        await DbContext.SaveChangesAsync();
        
        var result = await _handler.Handle(new GetProductCostQuery(product.Id), CancellationToken.None);

        result.Cost.Should().Be(productCost);
    }
}