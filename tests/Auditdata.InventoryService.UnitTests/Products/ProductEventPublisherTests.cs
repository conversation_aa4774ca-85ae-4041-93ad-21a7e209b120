using Auditdata.InventoryService.Contracts.Commands;
using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Abstractions.Azure;
using Auditdata.InventoryService.Core.Entities.Products;
using Auditdata.InventoryService.Core.Features.Products.Events;
using Auditdata.InventoryService.Core.Features.Products.Services;
using Auditdata.InventoryService.UnitTests.Common;
using IMediator = MediatR.IMediator;

namespace Auditdata.InventoryService.UnitTests.Products;

public class ProductEventPublisherTests
{
    private readonly ProductEventPublisher _sut;
    private readonly Mock<IAzureServiceBusPublisher> _publisherMock = new();
    private readonly Mock<IEventPublisher> _eventPublisher = new();
    private readonly Mock<IMediator> _mediatorMock = new();
    private readonly IFixture _fixture = new RecursiveFixture();

    public ProductEventPublisherTests()
    {
        _sut = new ProductEventPublisher(_publisherMock.Object, _eventPublisher.Object, _mediatorMock.Object);
    }

    [Fact]
    public async Task PublishProductCreatedAsync_Should_PublishEvents_When_ProductProvided()
    {
        // Arrange
        var product = _fixture.Build<Product>().With(x => x.IsDeleted, false).Create();

        // Act
        await _sut.PublishProductCreatedAsync(product, CancellationToken.None);

        // Assert
        _eventPublisher.Verify(x => x.ProductCreated(product, It.IsAny<CancellationToken>()), Times.Once);
        _publisherMock.Verify(x => x.PublishAddProductToAllStocksCommand(
            It.IsAny<AddProductToAllStocksCommand>()), Times.Once);
    }

    [Fact]
    public async Task PublishProductsCreatedAsync_Should_PublishEvents_When_ProductsProvided()
    {
        // Arrange
        var products = _fixture.Build<Product>().With(x => x.IsDeleted, false).CreateMany().ToList();

        // Act
        await _sut.PublishProductsCreatedAsync(products, CancellationToken.None);

        // Assert
        _eventPublisher.Verify(x => x.ProductsCreated(products, It.IsAny<CancellationToken>()), Times.Once);
        _publisherMock.Verify(x => x.PublishAddProductsToAllStocksCommand(
            It.IsAny<IEnumerable<AddProductToAllStocksCommand>>()), Times.Once);
    }

    [Theory]
    [InlineData(true)]
    [InlineData(false)]
    public async Task PublishProductUpdatedAsync_Should_PublishEvents_When_ProductProvided(bool isActiveChanged)
    {
        // Arrange
        var product = _fixture.Build<Product>().With(x => x.IsDeleted, false).Create();

        // Act
        await _sut.PublishProductUpdatedAsync(product, isActiveChanged, CancellationToken.None);

        // Assert
        _eventPublisher.Verify(x => x.ProductUpdated(product, It.IsAny<CancellationToken>()), Times.Once);
        _mediatorMock.Verify(x => x.Publish(It.IsAny<ProductIsActiveUpdated>(), It.IsAny<CancellationToken>()),
            isActiveChanged ? Times.Once : Times.Never);
    }
}
