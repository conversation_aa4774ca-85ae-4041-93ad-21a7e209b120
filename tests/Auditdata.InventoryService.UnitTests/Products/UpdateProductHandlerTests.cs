using Auditdata.InventoryService.Core.Features.Products.Commands;
using Auditdata.InventoryService.Core.Features.Products.Mappings;
using Auditdata.InventoryService.Core.Entities.Products;
using Auditdata.InventoryService.Core.Exceptions;
using Auditdata.InventoryService.Core.Features.Products.Handlers;
using Auditdata.InventoryService.Core.OperationContext;
using Auditdata.InventoryService.UnitTests.Common;
using FluentAssertions;
using Attribute = Auditdata.InventoryService.Core.Entities.Attribute;
using Auditdata.InventoryService.Core.Features.Products.Models;
using Auditdata.InventoryService.Core.Features.Products.Validation;
using Auditdata.InventoryService.Core.Abstractions.Services;
using Auditdata.Transport.Contracts.Exceptions;
using Auditdata.InventoryService.Core.Constants;

namespace Auditdata.InventoryService.UnitTests.Products;

public sealed class UpdateProductHandlerTests : BaseDbContextTest
{
    private readonly IFixture _fixture;
    private readonly Mock<IProductEventPublisher> _productEventPublisher;
    private readonly UpdateProductHandler _handler;

    public UpdateProductHandlerTests()
    {
        _fixture = new RecursiveFixture();
        _productEventPublisher = new Mock<IProductEventPublisher>();
        var operationContext = new Mock<IInventoryServiceOperationContext>();
        var mapper = new MapperConfiguration(c => c.AddProfile<ProductMappings>()).CreateMapper();

        _handler = new UpdateProductHandler(
            new ProductUniqueNameValidator(DbContext),
            new UpdateUKProductCommandValidator(DbContext),
            new UpdateAUProductCommandValidator(DbContext),
            new UpdateROIProductCommandValidator(DbContext),
            new UpdateNZProductCommandValidator(DbContext),
            new ProductSkusValidator(DbContext),
            mapper,
            operationContext.Object,
            DbContext,
            _productEventPublisher.Object);
    }

    [Fact]
    public async Task Consume_ShouldUpdate_Product_IfFound()
    {
        // Arrange
        var colors = _fixture.Build<Color>()
            .With(x => x.IsDeleted, false)
            .With(x => x.IsActive, true)
            .CreateMany().ToList();
        DbContext.Colors.AddRange(colors);
        var batteryTypes = _fixture.Build<BatteryType>()
            .With(x => x.IsDeleted, false)
            .With(x => x.IsActive, true)
            .CreateMany().ToList();
        DbContext.BatteryTypes.AddRange(batteryTypes);

        var manufacturer = _fixture.Build<Manufacturer>()
            .With(x => x.IsDeleted, false)
            .Create();
        var category = _fixture.Build<ProductCategory>()
            .With(x => x.IsDeleted, false)
            .Create();
        var hearingAid = _fixture.Build<HearingAidType>()
            .With(x => x.IsDeleted, false)
            .Create();
        var supplier = _fixture.Build<Supplier>()
            .With(x => x.IsDeleted, false)
            .Create();
        var attributes = _fixture.Build<Attribute>()
            .With(x => x.IsDeleted, false)
            .CreateMany().ToList();

        var product2 = new Product
            { Id = Guid.NewGuid(), Name = "test2", Manufacturer = manufacturer, Category = category };
        var product = new UKProduct
        {
            Name = "test",
            Manufacturer = manufacturer,
            Category = category,
            HearingAidType = hearingAid,
            Supplier = supplier
        };

        DbContext.Products.Add(product);
        DbContext.Products.Add(product2);
        DbContext.Attributes.AddRange(attributes);

        await DbContext.SaveChangesAsync();

        var batteryType = batteryTypes.Select(y => y.Id);

        var command = _fixture.Build<UpdateUKProductCommand>()
            .With(x => x.Id, product.Id)
            .With(x => x.ManufacturerId, manufacturer.Id)
            .With(x => x.CategoryId, category.Id)
            .With(x => x.HearingAidTypeId, hearingAid.Id)
            .With(x => x.SupplierId, supplier.Id)
            .With(x => x.IsSerialized, false)
            .With(x => x.Attributes, attributes.Select(x => new ProductAttributeDto
            {
                AttributeId = x.Id,
                Values = new List<AttributeValue>
                {
                    new()
                    {
                        Value = $"test{x.Id}",
                        IsActive = true
                    }
                }
            }))
            .With(x => x.BatteryTypes, new List<Guid>() { batteryType.First()})
            .With(x => x.Colors, colors.Select(y => y.Id))
            .With(x => x.SuggestedProductIds, new List<Guid> { product2.Id })
            .Create();

        // Act
        await _handler.Handle(command, CancellationToken.None);

        product = await DbContext.Set<UKProduct>().FindAsync(product.Id);

        // Assert
        product!.Name.Should().Be(command.Name);
        product.Description.Should().Be(command.Description);
        product.CategoryId.Should().Be(command.CategoryId);
        product.HearingAidTypeId.Should().Be(command.HearingAidTypeId);
        product.ManufacturerId.Should().Be(command.ManufacturerId);
        product.SupplierId.Should().Be(command.SupplierId);
        product.Warranty.Should().Be(command.Warranty);
        product.Quantity.Should().Be(command.Quantity);
        product.RetailPrice.Should().Be(command.RetailPrice);
        product.FirstVAT.Should().Be(command.FirstVAT);
        product.SecondVAT.Should().Be(command.SecondVAT);
        product.IsActive.Should().Be(command.IsActive);
        product.IsSerialized.Should().Be(command.IsSerialized);
        product.IsSellable.Should().Be(command.IsSellable);
        product.AutoDeliver.Should().Be(command.AutoDeliver);
        product.IsFastTrack.Should().Be(command.IsFastTrack);
        product.ProductPathways.Select(x => x.PathwayId).Should().BeEquivalentTo(command.PathwayIds);
        product.SuggestedProducts.Select(x => x.SuggestedProductId)
            .Should().BeEquivalentTo(command.SuggestedProductIds);
        product.BatteryTypes.Select(x => x.BatteryTypeId).Should().BeEquivalentTo(command.BatteryTypes);
        product.Colors.Select(x => x.ColorId).Should().BeEquivalentTo(command.Colors);

        product.Attributes.Select(x => x.AttributeId).Should()
            .Contain(command.Attributes?.Select(a => a.AttributeId));
        product.Attributes.Select(x => x.Value).Should()
            .BeEquivalentTo(command.Attributes?.Select(a => a.Values));

        _productEventPublisher.Verify(x => x.PublishProductUpdatedAsync(
                It.Is<UKProduct>(y =>
                    y.Name == command.Name &&
                    y.CategoryId == command.CategoryId &&
                    y.IsSerialized == command.IsSerialized &&
                    y.Id == command.Id &&
                    y.RetailPrice == command.RetailPrice &&
                    y.AutoDeliver == command.AutoDeliver &&
                    y.IsFastTrack == command.IsFastTrack),
                It.IsAny<bool>(),
                It.IsAny<CancellationToken>()),
            Times.Once);
    }

    //TODO:FIX
    //[Fact]
    // public async Task Consume_ShouldRespond_With_InvalidProduct_IfValidation_NotPassed()
    // {
    //     var command = _fixture.Build<UpdateUKProductCommand>().Create();
    //     var errorMessage = "Test error";
    //     _validator.Setup(x => x.ValidateAsync(It.IsAny<UKProduct>(), CancellationToken.None))
    //         .ReturnsAsync(new ValidationResult
    //         {
    //             Errors = new List<ValidationFailure>
    //             {
    //                 new("Property", errorMessage)
    //             }
    //         });
    //
    //     var product = _fixture.Build<UKProduct>()
    //         .With(x => x.Id, command.Id)
    //         .With(x => x.IsDeleted, false).Create();
    //     DbContext.Products.Add(product);
    //     await DbContext.SaveChangesAsync();
    //
    //     var result = await _handler.Handle(command, CancellationToken.None);
    //
    //     result.IsT2.Should().BeTrue();
    //     result.AsT2.Error.Should().Be(errorMessage);
    // }

    [Fact]
    public async Task Consume_ShouldSkip_IfProductCategory_NotFound()
    {
        Func<Task> act = async () => await _handler.Handle(new UpdateUKProductCommand(), CancellationToken.None);
        await act.Should().ThrowAsync<EntityNotFoundException<ProductCategory>>();
    }

    [Fact]
    public async Task Consume_ShouldSkip_IfProduct_NotFound()
    {
        var category = _fixture.Build<ProductCategory>()
            .With(x => x.IsDeleted, false)
            .Create();

        DbContext.ProductCategories.Add(category);
        await DbContext.SaveChangesAsync();

        Func<Task> act = async () => await _handler.Handle(new UpdateUKProductCommand()
        {
            CategoryId = category.Id,
        }, CancellationToken.None);
        await act.Should().ThrowAsync<EntityNotFoundException<Product>>();
    }

    [Theory]
    [RecursiveInlineAutoData]
    public async Task Handle_ShouldThrow_IfConvertToSerialized_When_WasPlacedOnStock(Product product)
    {
        // Arrange
        var category = _fixture.Build<ProductCategory>()
            .With(x => x.IsDeleted, false)
            .Create();

        product.IsSerialized = false;
        product.ControlledByStock = true;
        product.StockProducts.First().Product = product;
        product.StockProducts.First().AdjustQuantity(1);

        DbContext.Products.Add(product);
        DbContext.ProductCategories.Add(category);
        await DbContext.SaveChangesAsync();

        var command = new UpdateUKProductCommand
        {
            Id = product.Id,
            CategoryId = product.CategoryId,
            IsSerialized = true
        };

        // Act
        var act = async () => await _handler.Handle(command, CancellationToken.None);

        // Assert
        var ex = await act.Should().ThrowAsync<BusinessException>();
        ex.Which.ErrorCode.Should().Be(ErrorCodes.ProductCantBeMadeSerialized);
    }

    [Theory]
    [RecursiveInlineAutoData]
    public async Task Handle_ShouldThrow_IfConvertToSerialized_When_NotControlledByStock(Product product)
    {
        // Arrange
        var category = _fixture.Build<ProductCategory>()
            .With(x => x.IsDeleted, false)
            .Create();

        product.IsSerialized = false;
        product.ControlledByStock = false;
        product.StockProducts.Clear();

        DbContext.Products.Add(product);
        DbContext.ProductCategories.Add(category);
        await DbContext.SaveChangesAsync();

        var command = new UpdateUKProductCommand
        {
            Id = product.Id,
            CategoryId = product.CategoryId,
            IsSerialized = true
        };

        // Act
        var act = async () => await _handler.Handle(command, CancellationToken.None);

        // Assert
        var ex = await act.Should().ThrowAsync<BusinessException>();
        ex.Which.ErrorCode.Should().Be(ErrorCodes.ProductCantBeMadeSerialized);
    }

    [Theory]
    [RecursiveInlineAutoData]
    public async Task Handle_ShouldThrow_IfChangeCategory_When_WasPlacedOnStock(Product product)
    {
        // Arrange
        var category = _fixture.Build<ProductCategory>()
            .With(x => x.IsDeleted, false)
            .Create();

        product.IsSerialized = false;
        product.ControlledByStock = true;
        product.StockProducts.First().Product = product;
        product.StockProducts.First().AdjustQuantity(1);

        DbContext.Products.Add(product);
        DbContext.ProductCategories.Add(category);
        await DbContext.SaveChangesAsync();

        var command = new UpdateUKProductCommand
        {
            Id = product.Id,
            CategoryId = category.Id
        };

        // Act
        var act = async () => await _handler.Handle(command, CancellationToken.None);

        // Assert
        var ex = await act.Should().ThrowAsync<BusinessException>();
        ex.Which.ErrorCode.Should().Be(ErrorCodes.ProductCantChangeCategory);
    }

    [Theory]
    [RecursiveInlineAutoData]
    public async Task Handle_ShouldThrow_IfChangeCategory_When_NotControlledByStock(Product product)
    {
        // Arrange
        product.IsSerialized = false;
        product.ControlledByStock = false;
        product.StockProducts.Clear();

        var category = _fixture.Build<ProductCategory>()
            .With(x => x.IsDeleted, false)
            .Create();

        DbContext.Products.Add(product);
        DbContext.ProductCategories.Add(category);
        await DbContext.SaveChangesAsync();

        var command = new UpdateUKProductCommand
        {
            Id = product.Id,
            CategoryId = category.Id
        };

        // Act
        var act = async () => await _handler.Handle(command, CancellationToken.None);

        // Assert
        var ex = await act.Should().ThrowAsync<BusinessException>();
        ex.Which.ErrorCode.Should().Be(ErrorCodes.ProductCantChangeCategory);
    }

    [Fact]
    public async Task UpdateAsync_ShouldNotThrowException_WhenSupplierNotUsedInSkus()
    {
        // Arrange
        var supplier = _fixture.Build<Supplier>()
            .With(x => x.IsDeleted, false)
            .Create();

        var batteryType = new BatteryType
        {
            Id = Guid.NewGuid(),
            Name = $"name{Guid.NewGuid()}",
            IsActive = true
        };

        var color = new Color
        {
            Id = Guid.NewGuid(),
            Name = $"name{Guid.NewGuid()}",
            IsActive = true
        };

        var product = _fixture.Build<Product>()
            .With(x => x.IsDeleted, false)
            .With(x => x.ControlledByStock, true)
            .With(x => x.IsSerialized, false)
            .With(x => x.SupplierId, supplier.Id)
            .Without(x => x.BatteryTypes)
            .Without(x => x.Colors)
            .Create();

        product.StockProducts.Clear();

        DbContext.BatteryTypes.Add(batteryType);
        DbContext.Colors.Add(color);
        DbContext.Suppliers.Add(supplier);
        DbContext.Products.Add(product);

        await DbContext.SaveChangesAsync();

        var command = _fixture.Build<UpdateProductCommand>()
            .With(x => x.Id, product.Id)
            .With(x => x.SupplierId, Guid.NewGuid())
            .With(x => x.IsSerialized, false)
            .With(x => x.BatteryTypes, [batteryType.Id])
            .With(x => x.Colors, [color.Id])
            .Create();

        // Act
        Func<Task> act = async () => await _handler.UpdateAsync(command, CancellationToken.None);

        // Assert
        await act.Should().NotThrowAsync();
    }

    [Fact]
    public async Task UpdateAsync_ShouldThrowException_WhenSupplierUsedInSkus()
    {
        // Arrange
        var supplier1 = _fixture.Build<Supplier>()
            .With(x => x.IsDeleted, false)
            .Create();

        var supplier2 = _fixture.Build<Supplier>()
            .With(x => x.IsDeleted, false)
            .Create();

        var category = _fixture.Build<ProductCategory>()
            .With(x => x.IsDeleted, false)
            .Create();

        var product = _fixture.Build<Product>()
            .With(x => x.IsDeleted, false)
            .With(x => x.SupplierId, supplier1.Id)
            .With(x => x.CategoryId, category.Id)
            .Create();

        DbContext.Suppliers.AddRange(supplier1, supplier2);
        DbContext.ProductCategories.Add(category);
        DbContext.Products.Add(product);
        await DbContext.SaveChangesAsync();

        var sku = new Sku
        {
            SkuValue = "skuValue",
            ProductId = product.Id,
            SupplierId = product.SupplierId!.Value
        };

        DbContext.Skus.Add(sku);

        await DbContext.SaveChangesAsync();

        var command = _fixture.Build<UpdateProductCommand>()
            .With(x => x.Id, product.Id)
            .With(x => x.SupplierId, supplier2.Id)
            .With(x => x.IsSerialized, product.IsSerialized)
            .With(x => x.CategoryId, product.CategoryId)
            .Create();

        // Act
        var act = async () => await _handler.UpdateAsync(command, CancellationToken.None);

        // Assert
        var ex = await act.Should().ThrowAsync<BusinessException>();
        ex.Which.ErrorCode.Should().Be(ErrorCodes.SupplierUsedInSkuAndCannotUpdate);
    }


    [Fact]
    public async Task UpdateAsync_ShouldThrowException_WhenAttributesUsedInSkus()
    {
        // Arrange
        var supplier = _fixture.Build<Supplier>()
            .With(x => x.IsDeleted, false)
            .Create();

        var category = _fixture.Build<ProductCategory>()
            .With(x => x.IsDeleted, false)
            .Create();

        var attribute1 = _fixture.Build<Attribute>()
            .With(x => x.IsDeleted, false)
            .With(x => x.Name, "Attribute1")
            .Create();

        var attributeValue1 = new AttributeValue
        {
            ValueId = Guid.NewGuid(),
            Value = "Value1",
            IsActive = true
        };

        var product = _fixture.Build<Product>()
            .With(x => x.IsDeleted, false)
            .With(x => x.SupplierId, supplier.Id)
            .With(x => x.CategoryId, category.Id)
            .With(x => x.ControlledByStock, true)
            .With(x => x.IsSerialized, false)
            .With(x => x.Attributes, new List<ProductAttribute>
            {
            ProductAttribute.Create(Guid.NewGuid(), attribute1.Id, new List<AttributeValue> { attributeValue1 })
            })
            .Create();

        DbContext.Products.Add(product);
        DbContext.Attributes.Add(attribute1);
        DbContext.Suppliers.Add(supplier);
        DbContext.ProductCategories.Add(category);

        await DbContext.SaveChangesAsync();

        var sku = new Sku
        {
            SkuValue = "skuValue",
            ProductId = product.Id,
            ColorId = product.Colors.FirstOrDefault()!.ColorId,
            BatteryTypeId = product.BatteryTypes.FirstOrDefault()!.Id,
            Attributes = new List<SkuAttribute>
            {
                SkuAttribute.Create(Guid.NewGuid(), attribute1.Id, attributeValue1)
            }
        };

        DbContext.Skus.Add(sku);

        await DbContext.SaveChangesAsync();

        var command = _fixture.Build<UpdateProductCommand>()
            .With(x => x.Id, product.Id)
            .With(x => x.Attributes, new List<ProductAttributeDto>
            {
            new ProductAttributeDto
            {
                AttributeId = attribute1.Id,
                Values = new List<AttributeValue>
                {
                    new AttributeValue
                    {
                        ValueId = attributeValue1.ValueId,
                        Value = attributeValue1.Value,
                        IsActive = false
                    }
                }
            }
            })
            .With(x => x.IsSerialized, false)
            .With(x => x.CategoryId, product.CategoryId)
            .Create();

        // Act
        var act = async () => await _handler.UpdateAsync(command, CancellationToken.None);

        // Assert
        var ex = await act.Should().ThrowAsync<BusinessException>();
        ex.Which.ErrorCode.Should().Be(ErrorCodes.AttributesUsedInSkuAndCannotUpdate);
    }

    [Fact]
    public async Task Consume_ShouldThrowBusinessException_IfBatteryTypesAreInactive()
    {
        // Arrange
        var activeBatteryTypes = _fixture.Build<BatteryType>()
            .With(x => x.IsDeleted, false)
            .With(x => x.IsActive, true)
            .CreateMany().ToList();

        var inactiveBatteryTypes = _fixture.Build<BatteryType>()
            .With(x => x.IsDeleted, false)
            .With(x => x.IsActive, false)
            .CreateMany().ToList();

        DbContext.BatteryTypes.AddRange(activeBatteryTypes.Concat(inactiveBatteryTypes));

        var supplier = _fixture.Build<Supplier>()
            .With(x => x.IsDeleted, false)
            .Create();

        var category = _fixture.Build<ProductCategory>()
            .With(x => x.IsDeleted, false)
            .Create();

        var product = _fixture.Build<UKProduct>()
            .With(x => x.IsDeleted, false)
            .With(x => x.SupplierId, supplier.Id)
            .With(x => x.CategoryId, category.Id)
            .With(x => x.IsSerialized, true)
            .Create();

        DbContext.Products.Add(product);
        DbContext.Suppliers.Add(supplier);
        DbContext.ProductCategories.Add(category);

        await DbContext.SaveChangesAsync();

        var command = _fixture.Build<UpdateUKProductCommand>()
            .With(x => x.Id, product.Id)
            .With(x => x.CategoryId, product.CategoryId)
            .With(x => x.BatteryTypes, [activeBatteryTypes.First().Id, inactiveBatteryTypes.First().Id])
            .Create();

        // Act & Assert
        var act = async () => await _handler.Handle(command, CancellationToken.None);
        var ex = await act.Should().ThrowAsync<BusinessException>();

        ex.Which.ErrorCode.Should().Be(ErrorCodes.BatteryTypeInactive);
    }

    [Fact]
    public async Task Consume_ShouldThrowBusinessException_IfColorsAreInactive()
    {
        // Arrange
        var activeBatteryTypes = _fixture.Build<BatteryType>()
            .With(x => x.IsDeleted, false)
            .With(x => x.IsActive, true)
            .CreateMany().ToList();

        var activeColors = _fixture.Build<Color>()
            .With(x => x.IsDeleted, false)
            .With(x => x.IsActive, true)
            .CreateMany().ToList();

        var inactiveColors = _fixture.Build<Color>()
            .With(x => x.IsDeleted, false)
            .With(x => x.IsActive, false)
            .CreateMany().ToList();

        DbContext.Colors.AddRange(activeColors.Concat(inactiveColors));
        DbContext.BatteryTypes.AddRange(activeBatteryTypes);

        var supplier = _fixture.Build<Supplier>()
            .With(x => x.IsDeleted, false)
            .Create();

        var category = _fixture.Build<ProductCategory>()
            .With(x => x.IsDeleted, false)
            .Create();

        var product = _fixture.Build<UKProduct>()
            .With(x => x.IsDeleted, false)
            .With(x => x.SupplierId, supplier.Id)
            .With(x => x.CategoryId, category.Id)
            .With(x => x.IsSerialized, true)
            .Create();

        DbContext.Products.Add(product);
        DbContext.Suppliers.Add(supplier);
        DbContext.ProductCategories.Add(category);

        await DbContext.SaveChangesAsync();

        var command = _fixture.Build<UpdateUKProductCommand>()
            .With(x => x.Id, product.Id)
            .With(x => x.CategoryId, product.CategoryId)
            .With(x => x.Colors, [activeColors.First().Id, inactiveColors.First().Id])
            .With(x => x.BatteryTypes, activeBatteryTypes.Select(t => t.Id))
            .Create();

        // Act & Assert
        var act = async () => await _handler.Handle(command, CancellationToken.None);
        var ex = await act.Should().ThrowAsync<BusinessException>();

        ex.Which.ErrorCode.Should().Be(ErrorCodes.ColorInactive);
    }
}
