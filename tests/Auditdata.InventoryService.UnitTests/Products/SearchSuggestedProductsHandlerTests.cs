using Auditdata.InventoryService.Core.Entities.Products;
using Auditdata.InventoryService.Core.Features.Products.Handlers;
using Auditdata.InventoryService.Core.Features.Products.Queries;
using Auditdata.InventoryService.UnitTests.Common;
using FluentAssertions;

namespace Auditdata.InventoryService.UnitTests.Products;

public class SearchSuggestedProductsHandlerTests : BaseSqlLiteDbContextTest
{
    private readonly IFixture _fixture;

    private readonly SearchSuggestedProductsHandler _handler;

    public SearchSuggestedProductsHandlerTests()
    {
        _fixture = new RecursiveFixture();
        _handler = new SearchSuggestedProductsHandler(DbContext);
    }

    [Fact]
    public async Task Handle_Should_ReturnProducts_When_QueryProvided()
    {
        // Arrange
        var query = new SearchSuggestedProductsQuery {
            Page = 1,
            PerPage = 10,
            ProductId = _fixture.Create<Guid>(),
            OrderBy = "Name",
        };

        var productsSearchResult = _fixture.Build<Product>()
            .With(x => x.IsDeleted, false)
            .With(x => x.Bundles, new List<BundleProduct>())
            .With(x => x.IsInBundle, false)
            .With(x => x.Name)
            .Without(x => x.StockProducts)
            .CreateMany(6)
            .ToArray();

        var product1 = _fixture.Build<Product>()
            .With(x => x.Id, query.ProductId)
            .With(x => x.IsDeleted, false)
            .With(x => x.SuggestedProducts,
                new[] { ProductSuggestedProduct.Create(query.ProductId, productsSearchResult[1].Id),
                        ProductSuggestedProduct.Create(query.ProductId, productsSearchResult[2].Id)})
            .Create();

        DbContext.AddRange(productsSearchResult);
        DbContext.Add(product1);
        await DbContext.SaveChangesAsync();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.TotalCount.Should().Be(productsSearchResult.Length);
        result.Products.Take(2).Select(x => x.Name).Should()
            .BeEquivalentTo(productsSearchResult[1].Name, productsSearchResult[2].Name);
        result.Products.Select(x => x.Name).Should().BeEquivalentTo(productsSearchResult.Select(x => x.Name));
    }
}