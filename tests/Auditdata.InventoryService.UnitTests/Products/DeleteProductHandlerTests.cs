using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Constants;
using Auditdata.InventoryService.Core.Entities.Products;
using Auditdata.InventoryService.Core.Exceptions;
using Auditdata.InventoryService.Core.Features.Products.Commands;
using Auditdata.InventoryService.Core.Features.Products.Handlers;
using Auditdata.InventoryService.UnitTests.Common;
using Auditdata.Transport.Contracts.Exceptions;
using FluentAssertions;
using MediatR;

namespace Auditdata.InventoryService.UnitTests.Products;

public class DeleteProductHandlerTests : IClassFixture<BaseDbContextTest>
{
    private readonly IFixture _fixture;
    private readonly DeleteProductHandler _handler;
    private readonly Mock<IEventPublisher> _eventPublisher;
    private readonly IDbContext _dbContext;

    public DeleteProductHandlerTests(BaseDbContextTest dbContextTest)
    {
        _fixture = new RecursiveFixture();
        _eventPublisher = new Mock<IEventPublisher>();
        _dbContext = dbContextTest.DbContext;
        var mediator = new Mock<IMediator>();

        _handler = new DeleteProductHandler(
            _dbContext,
            mediator.Object,
            _eventPublisher.Object);
    }

    [Fact]
    public async Task Consume_ShouldDelete_Product_IfFound()
    {
        var product = _fixture.Build<Product>().With(x=> x.StockProducts, new List<StockProduct>())
            .With(x => x.IsDeleted, false)
            .Create();
        _dbContext.Products.Add(product);
        await _dbContext.SaveChangesAsync();
        
        await _handler.Handle(new DeleteProductCommand(product.Id), CancellationToken.None);

        _eventPublisher.Verify(x => x.ProductDeleted(
            product.Id, It.IsAny<CancellationToken>()), Times.Once());
    }

    [Fact]
    public async Task Consume_ShouldSkip_IfProduct_NotFound()
    {
        await Assert.ThrowsAsync<EntityNotFoundException<Product>>
            (() =>_handler.Handle(new DeleteProductCommand(Guid.Empty), CancellationToken.None));
    }
    
    [Fact]
    public async Task Consume_ShouldReturnCannotBeRemoved_IfQuantityAboveZero()
    {
        //Arrange
        var product = _fixture.Build<Product>()
            .With(x => x.StockProducts)
            .With(x => x.IsDeleted, false)
            .With(x => x.ControlledByStock, true)
            .Create();
        foreach (var stockProduct in product.StockProducts)
        {
            stockProduct.Product = product;
            stockProduct.SetQuantity(1);
            stockProduct.StockProductItems = [];
        }

        _dbContext.Products.Add(product);
        await _dbContext.SaveChangesAsync();
        
        //Act
        //Assert
        var exception = await Assert.ThrowsAsync<BusinessException>(() => _handler.Handle(new DeleteProductCommand(product.Id), CancellationToken.None));
        exception.ErrorCode.Should().Be(ErrorCodes.ProductOnTheStock);
    }
}
