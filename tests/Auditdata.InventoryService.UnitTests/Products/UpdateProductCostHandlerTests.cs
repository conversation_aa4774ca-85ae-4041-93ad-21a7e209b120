using Auditdata.InventoryService.Core.Entities.Products;
using Auditdata.InventoryService.Core.Exceptions;
using Auditdata.InventoryService.Core.Features.Products.Commands;
using Auditdata.InventoryService.Core.Features.Products.Handlers;
using Auditdata.InventoryService.UnitTests.Common;
using FluentAssertions;

namespace Auditdata.InventoryService.UnitTests.Products;

public class UpdateProductCostHandlerTests
{
    private readonly IFixture _fixture;
    private readonly Mock<IProductsRepository> _productsRepository;
    private readonly UpdateProductCostHandler _handler;

    public UpdateProductCostHandlerTests()
    {
        _fixture = new RecursiveFixture();
        _productsRepository = new Mock<IProductsRepository>();
        _handler = new UpdateProductCostHandler(_productsRepository.Object);
    }

    [Fact]
    public async Task Consume_ShouldRespond_UpdatedProductCost()
    {
        var productId = Guid.NewGuid();
        var productCost = 1234.56M;
        var product = _fixture.Build<Product>()
            .With(x => x.Id, productId)
            .Create();

        _productsRepository.Setup(x => x.GetByIdAsync(productId)).ReturnsAsync(product);

         await _handler.Handle(new UpdateProductCostCommand(productId, productCost), CancellationToken.None);

        _productsRepository.Verify(x => x.UpdateAsync(It.Is<Product>(y =>
            y.Id == productId)));
    }

    [Fact]
    public async Task Consume_ShouldRespond_ProductNotFound()
    {
        var productId = Guid.NewGuid();
        var productCost = 1234.56M;

        _productsRepository.Setup(x => x.GetByIdAsync(productId)).ReturnsAsync((Product) null!);

        var act = async () => await _handler.Handle(new UpdateProductCostCommand(productId, productCost), CancellationToken.None);

        await act.Should().ThrowExactlyAsync<EntityNotFoundException<Product>>();

    }
}