using Auditdata.InventoryService.Core.Entities.Products;
using Auditdata.InventoryService.Core.Features.Products.Handlers;
using Auditdata.InventoryService.Core.Features.Products.Queries;
using Auditdata.InventoryService.Core.OperationContext;
using Auditdata.InventoryService.UnitTests.Common;
using FluentAssertions;

namespace Auditdata.InventoryService.UnitTests.Products;

public class GetProductByManufacturerAndCategoryHandlerTests
{
    private readonly IFixture _fixture;
    private readonly Mock<IProductsRepository> _productsRepository;
    private readonly Mock<IInventoryServiceOperationContext> _operationContext;
    private readonly GetProductByManufacturerAndCategoryHandler _handler;

    public GetProductByManufacturerAndCategoryHandlerTests()
    {
        _fixture = new RecursiveFixture();
        _productsRepository = new Mock<IProductsRepository>();
        _operationContext = new Mock<IInventoryServiceOperationContext>();
        _handler = new GetProductByManufacturerAndCategoryHandler(
            _productsRepository.Object,
            _operationContext.Object);
    }

    [Fact]
    public async Task Consume_ShouldRespond_With_GetProductByManufacturerAndTypeResult()
    {
        var productCategoryId = Guid.NewGuid();
        var manufacturerId = Guid.NewGuid();
        var products = _fixture.Build<Product>().CreateMany().ToList();
        
        _productsRepository.Setup(x => x.GetByManufacturerAndCategoryAsync(productCategoryId, manufacturerId))
            .ReturnsAsync(products);
        _operationContext.Setup(x => x.UserCanViewProductCost).Returns(true);

        var result = await _handler.Handle(new GetProductByManufacturerAndCategoryQuery(productCategoryId, manufacturerId), CancellationToken.None);

        result.Products.Should().BeSameAs(products);
    }
}
