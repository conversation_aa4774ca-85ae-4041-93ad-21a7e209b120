using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Entities.Products;
using Auditdata.InventoryService.Core.Exceptions;
using Auditdata.InventoryService.Core.Features.Products.Commands;
using Auditdata.InventoryService.Core.Features.Products.Handlers;
using Auditdata.InventoryService.UnitTests.Common;
using FluentAssertions;
using Microsoft.EntityFrameworkCore;

namespace Auditdata.InventoryService.UnitTests.Products;

public class UpdateIsSellableProductStatusHandlerTest : BaseDbContextTest
{
    private readonly UpdateIsSellableProductStatusHandler _handler;
    
    public UpdateIsSellableProductStatusHandlerTest()
    {
        var eventPublisher = new Mock<IEventPublisher>();
        _handler = new UpdateIsSellableProductStatusHandler(
            eventPublisher.Object,
            DbContext);
    }

    [Fact]
    public async Task Consume_ShouldUpdate_SellableProductStatus_IfFound()
    {
        var category = new ProductCategory()
        {
            Code = ProductCategoryCode.Accessories,
            Name = "test",
        };
        var manufacturer = Manufacturer.Create("TestManufacturer");
        var product = new Product
        {
            Id = Guid.NewGuid(),
            Name = "test",
            Manufacturer = manufacturer,
            Category = category,
            IsDeleted = false
        };
        DbContext.Products.Add(product);
        await DbContext.SaveChangesAsync();

        var isSellable = true;
        await _handler.Handle(new UpdateIsSellableProductStatusCommand(product.Id, isSellable), CancellationToken.None);

        var productResult = await DbContext.Products.FirstAsync(x=> x.Id == product.Id);

        productResult.IsSellable.Should().BeTrue();
    }

    [Fact]
    public async Task Consume_ShouldSkip_IfProduct_NotFound()
    {

       var act = async () =>  await _handler.Handle(new UpdateIsSellableProductStatusCommand(Guid.Empty, It.IsAny<bool>()), CancellationToken.None);
       await act.Should().ThrowExactlyAsync<EntityNotFoundException<Product>>();
    }
}