using Auditdata.InventoryService.Core.Entities.Products;
using Auditdata.InventoryService.Core.Features.Products.Handlers;
using Auditdata.InventoryService.Core.Features.Products.Queries;
using Auditdata.InventoryService.UnitTests.Common;
using Auditdata.Transport.Contracts.PageResults;
using Auditdata.Transport.Contracts.Table;
using FluentAssertions;

namespace Auditdata.InventoryService.UnitTests.Products;

public class GetProductListHandlerTests
{
    private readonly IFixture _fixture;
    private readonly Mock<IProductsRepository> _productsRepository;
    private readonly GetProductListHandler _handler;

    public GetProductListHandlerTests()
    {
        _fixture = new RecursiveFixture();
        _productsRepository = new Mock<IProductsRepository>();
        _handler = new GetProductListHandler(_productsRepository.Object);
    }

    [Fact]
    public async Task Consume_ShouldRespond_ProductTablePageResult()
    {
        var command = _fixture.Build<GetProductListQuery>().Create();

        var products = _fixture.Build<TablePageResult<Product>>().Create();
        _productsRepository.Setup(x => x.GetProductsAsync(It.IsAny<TableQueryBase>())).ReturnsAsync(products);

        var result = await _handler.Handle(command, CancellationToken.None);
        result.Should().BeSameAs(products);
    }
}