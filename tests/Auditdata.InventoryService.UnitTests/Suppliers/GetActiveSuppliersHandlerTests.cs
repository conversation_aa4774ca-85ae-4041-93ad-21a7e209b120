using Auditdata.InventoryService.Core.Features.Suppliers.Handlers;
using Auditdata.InventoryService.Core.Features.Suppliers.Queries;
using Auditdata.InventoryService.UnitTests.Common;
using FluentAssertions;

namespace Auditdata.InventoryService.UnitTests.Suppliers;

public class GetActiveSuppliersHandlerTests : BaseDbContextTest
{
    private readonly Fixture _fixture = new();
    private readonly GetActiveSuppliersHandler _handler;
    
    public GetActiveSuppliersHandlerTests()
    {
        _handler = new GetActiveSuppliersHandler(DbContext);
    }
    
    [Fact]
    public async Task Handle_ShouldRespond_With_GetActiveSuppliersResult()
    {
        const string search = "test";
        
        var suppliers = _fixture.Build<Supplier>()
            .With(x => x.Name, search)
            .With(x => x.IsDeleted, false)
            .With(x => x.IsActive, true).CreateMany().ToList();
        DbContext.Suppliers.AddRange(suppliers);
        await DbContext.SaveChangesAsync();

        var result = await _handler.Handle(new GetActiveSuppliersQuery(search), CancellationToken.None);

        result.Suppliers.Should().BeEquivalentTo(suppliers);
    }
}
