using Auditdata.InventoryService.Core.Features.Suppliers.Handlers;
using Auditdata.InventoryService.Core.Features.Suppliers.Queries;
using Auditdata.InventoryService.UnitTests.Common;
using FluentAssertions;

namespace Auditdata.InventoryService.UnitTests.Suppliers;

public class GetSuppliersByIdsHandlerTests : BaseDbContextTest
{
    private readonly Fixture _fixture = new();
    private readonly GetSuppliersByIdsHandler _handler;

    public GetSuppliersByIdsHandlerTests()
    {
        _handler = new GetSuppliersByIdsHandler(DbContext);
    }

    [Fact]
    public async Task Consume_ShouldRespond_With_GetSuppliersByIdsResult()
    {
        var suppliers = _fixture.Build<Supplier>()
            .With(x => x.IsDeleted, false)
            .CreateMany().ToList();
        DbContext.Suppliers.AddRange(suppliers);
        await DbContext.SaveChangesAsync();

        var result = await _handler.<PERSON>le(new GetSuppliersByIdsQuery(suppliers.Select(x => x.Id)), CancellationToken.None);

        result.Suppliers.Should().BeEquivalentTo(suppliers);
    }
}