using Auditdata.InventoryService.Core.Entities.Products;
using Auditdata.InventoryService.Core.Features.Bundles.Models;
using Auditdata.InventoryService.Core.Features.Bundles.UpdateBundle;
using Auditdata.InventoryService.Core.Features.Bundles.Validation;
using Auditdata.InventoryService.UnitTests.Common;
using Auditdata.Transport.Contracts.Exceptions;
using FluentAssertions;

namespace Auditdata.InventoryService.UnitTests.Bundles
{
    public class UpdateBundleHandlerTests : BaseSqlLiteDbContextTest
    {
        private readonly UpdateBundleHandler _sut;
        private readonly Mock<IMapper> _mapper = new();
        public UpdateBundleHandlerTests()
        {
            _sut = new UpdateBundleHandler(DbContext, _mapper.Object, new UniqueNameValidator(DbContext));
        }

        [Fact]
        public async Task Handle_ShouldThrow_NameAlreadyExistsException_IfBundleNameAlreadyExists()
        {
            // Arrange
            DbContext.Bundles.Add(new Bundle
            {
                Name = "test"
            });
            await DbContext.SaveChangesAsync();

            var request = new UpdateBundleCommand
            {
                Name = "test",
                Products = new List<BundleProductDto>()
            };

            // Assert
            var result = async () => await _sut.Handle(request, CancellationToken.None);
            await result.Should().ThrowAsync<BusinessException>();
        }

        [Fact]
        public async Task Handle_ShouldUpdate_ExistingBundle()
        {
            // Arrange
            var fixture = new RecursiveFixture();
            var bundle = Bundle.CreateWithProducts("test", "desc", 12, true, false, true, true, Enumerable.Empty<BundleProduct>());
            var category = fixture.Build<ProductCategory>().Create();
            var manufacturer = Manufacturer.Create("TestManufacturer");
            var product = new UKProduct
            {
                Id = Guid.NewGuid(),
                Name = "test",
                Manufacturer = manufacturer,
                Category = category,
            };
            DbContext.Products.Add(product);
            DbContext.Bundles.Add(bundle);
            await DbContext.SaveChangesAsync();

            var command = fixture
                .Build<UpdateBundleCommand>()
                .With(x => x.Id, bundle.Id)
                .With(x => x.Name, bundle.Name + "new")
                .Create();

            _mapper.Setup(t => t.Map(command, bundle)).Returns<UpdateBundleCommand, Bundle>((command, bundle) =>
            {
                bundle.Name = command.Name;
                return bundle;
            });

            // Act
            await _sut.Handle(command, CancellationToken.None);

            // Assert
            var updatedBundleResult = DbContext.Bundles.FirstOrDefault();
            updatedBundleResult.Should().NotBeNull();
            updatedBundleResult.Name.Should().BeEquivalentTo(command.Name);
        }
    }
}
