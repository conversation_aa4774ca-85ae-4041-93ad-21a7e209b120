using Auditdata.InventoryService.Core.Exceptions;
using Auditdata.InventoryService.Core.Features.Bundles.GetBundleById;
using Auditdata.InventoryService.UnitTests.Common;
using FluentAssertions;

namespace Auditdata.InventoryService.UnitTests.Bundles;

public class GetBundleByIdHandlerTests : BaseSqlLiteDbContextTest
{
    private readonly GetBundleByIdHandler _sut;
    
    public GetBundleByIdHandlerTests()
    {
        _sut = new GetBundleByIdHandler(DbContext);
    }

    [Fact]
    public async Task Handle_ShouldThrow_EntityNotFoundException_IfNoBundle_Found()
    {
        // Act
        var action = async () => await _sut.Handle(new GetBundleByIdQuery(Guid.Empty), CancellationToken.None);

        // Assert
        await action.Should().ThrowAsync<EntityNotFoundException>();
    }

    [Fact]
    public async Task Handle_ShouldReturn_BundleEntity()
    {
        // Arrange
        var bundle = new RecursiveFixture().Build<Bundle>().With(x => x.IsDeleted, false).Create();
        DbContext.Bundles.Add(bundle);
        await DbContext.SaveChangesAsync();
        
        // Act
        var result = await _sut.Handle(new GetBundleByIdQuery(bundle.Id), CancellationToken.None);
        
        // Assert
        result.Should().BeEquivalentTo(bundle);
    }
}
