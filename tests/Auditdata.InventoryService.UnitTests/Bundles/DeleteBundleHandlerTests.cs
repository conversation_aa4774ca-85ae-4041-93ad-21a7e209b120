using Auditdata.InventoryService.Core.Features.Bundles.DeleteBundle;
using Auditdata.InventoryService.UnitTests.Common;
using FluentAssertions;
using Microsoft.EntityFrameworkCore;

namespace Auditdata.InventoryService.UnitTests.Bundles;

public class DeleteBundleHandlerTests : BaseSqlLiteDbContextTest
{
    private readonly DeleteBundleHandler _sut;
    
    public DeleteBundleHandlerTests()
    {
        _sut = new DeleteBundleHandler(DbContext);
    }
    
    [Fact]
    public async Task Handle_ShouldRemove_Bundle_FromDatabase()
    {
        // Arrange
        var fixture = new RecursiveFixture();
        var bundle = fixture.Create<Bundle>();
        DbContext.Bundles.Add(bundle);
        await DbContext.SaveChangesAsync();

        var command = new DeleteBundleCommand(bundle.Id);
        
        // Act
        await _sut.Handle(command, CancellationToken.None);
        
        // Assert
        var softDeletedBundle = DbContext.Bundles
            .IgnoreQueryFilters()
            .First(x => x.Id == command.Id);
        softDeletedBundle.IsDeleted.Should().BeTrue();
    }
}
