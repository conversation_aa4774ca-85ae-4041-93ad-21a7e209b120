using Auditdata.Infrastructure.Contracts.OperationContext;
using Auditdata.InventoryService.Core.Abstractions.Services;
using Auditdata.InventoryService.Core.Features.ProductsImport.ImportStrategies;
using Auditdata.InventoryService.Core.Services;
using FluentAssertions;
using System.IO;

namespace Auditdata.InventoryService.UnitTests.ProductImport;

public class HimsaProductImportStrategyTests
{
    private readonly HimsaProductImportStrategy _sut;
    private readonly Mock<IOperationContext> _operationContext;
    private readonly IHashingService _hashingService;

    public HimsaProductImportStrategyTests()
    {
        _operationContext = new Mock<IOperationContext>();
        _hashingService = new HashingService();
        _sut = new HimsaProductImportStrategy(_hashingService, _operationContext.Object);
    }

    [Fact]
    public async Task ImportAsync_ShouldThrow_IfCatalogXml_Invalid()
    {
        await using var stream = File.Open("ProductImport/himsa_test_invalid.xml", FileMode.Open);

        await Assert.ThrowsAsync<InvalidOperationException>(
            () => _sut.ImportAsync(Guid.NewGuid(), stream));
    }

    [Fact]
    public async Task ImportAsync_ShouldCreate_ExternalProductOptions()
    {
        // Arrange
        await using var stream = File.Open("ProductImport/himsa_hearing_aids_test_valid.xml", FileMode.Open);
        
        // Act
        var externalProducts = (await _sut.ImportAsync(Guid.NewGuid(), stream)).ToList();

        var bteProducts = externalProducts.Where(x => x.HearingAidType == "BTE").ToList();
        var ricProducts = externalProducts.Where(x => x.HearingAidType == "RIC").ToList();

        // Assert
        bteProducts.Should().HaveCount(1);
        var bteExternalProduct = bteProducts[0];
        bteExternalProduct.Name.Should().Be("Viron 3 BTE");
        var expectedHashKey = $"{bteExternalProduct.Manufacturer}_{bteExternalProduct.Name}_{_operationContext.Object.TenantId}";
        var expectedHash = _hashingService.ToMd5(expectedHashKey);
        bteExternalProduct.ExternalId.Should().Be(expectedHash);
        
        bteExternalProduct.ExternalProductOptions.Should().Satisfy(x =>
            x.Styles!.Color == "Metallic Silver" &&
            x.Name == "BTE" &&
            x.ExternalId == "228480" &&
            x.Styles!.BatteryType == "13");
        
        ricProducts.Should().HaveCount(1);
        var ricExternalProduct = ricProducts[0];
        ricExternalProduct.Name.Should().Be("Viron 3 RIC");
        var expectedRicHashKey = $"{ricExternalProduct.Manufacturer}_{ricExternalProduct.Name}_{_operationContext.Object.TenantId}";
        var expectedRicHash = _hashingService.ToMd5(expectedRicHashKey);
        ricExternalProduct.ExternalId.Should().Be(expectedRicHash);
        
        ricExternalProduct.ExternalProductOptions.Should().Satisfy(
            x =>
                x.Styles!.Color == "Sand Beige" &&
                x.Name == "RIC Mini" &&
                x.ExternalId == "229449" &&
                x.Styles!.BatteryType == "312+",
            x =>
                x.Styles!.Color == "Metallic Anthracite" &&
                x.Name == "RIC Mini" &&
                x.ExternalId == "229447" &&
                x.Styles!.BatteryType == "312+");
    }

    [Fact]
    public async Task ImportAsync_ShouldCreate_RICReceiver_Products()
    {
        //Arrange
        await using var stream = File.Open("ProductImport/himsa_ric_receivers_test.xml", FileMode.Open);
        
        //Act
        var externalProducts = (await _sut.ImportAsync(Guid.NewGuid(), stream)).ToList();
        
        //Assert
        externalProducts.Should().HaveCount(3);
        externalProducts.Should().Satisfy(
            x => x.Name == "SPEAKER UNIT, 4 L" &&
                x.Manufacturer == "Oticon" &&
                x.Category == ExternalProductCategory.RICReceivers,
            x => x.Name == "SPEAKER UNIT, 2 L" &&
                x.Manufacturer == "Oticon" &&
                x.Category == ExternalProductCategory.RICReceivers,
            x => x.Name == "SPEAKER UNIT, 3 L" &&
                x.Manufacturer == "Oticon" &&
                x.Category == ExternalProductCategory.RICReceivers);

        externalProducts.SelectMany(x => x.ExternalProductOptions)
            .Should().Satisfy(
                x => x.Name == "SPEAKER UNIT, 4 L" &&
                     x.ExternalId == "115024" &&
                     x.Styles!.Attributes!.Any(y => y.Code == "SpeakerUnitLength" && y.Value == "4") &&
                     x.Styles!.Attributes!.Any(y => y.Code == "SpeakerUnitPowerLevel" && y.Value == "Standard") &&
                     x.Styles!.Attributes!.Any(y => y.Code == "SpeakerUnitFittingLevel" && y.Value == "N/A"),
                x => x.Name == "SPEAKER UNIT, 2 L" &&
                     x.ExternalId == "115022" &&
                     x.Styles!.Attributes!.Any(y => y.Code == "SpeakerUnitLength" && y.Value == "2") &&
                     x.Styles!.Attributes!.Any(y => y.Code == "SpeakerUnitPowerLevel" && y.Value == "Standard") &&
                     x.Styles!.Attributes!.Any(y => y.Code == "SpeakerUnitFittingLevel" && y.Value == "N/A"),
                x => x.Name == "SPEAKER UNIT, 3 L" &&
                     x.ExternalId == "115023" &&
                     x.Styles!.Attributes!.Any(y => y.Code == "SpeakerUnitLength" && y.Value == "3") &&
                     x.Styles!.Attributes!.Any(y => y.Code == "SpeakerUnitPowerLevel" && y.Value == "Standard") &&
                     x.Styles!.Attributes!.Any(y => y.Code == "SpeakerUnitFittingLevel" && y.Value == "N/A"));
    }

    [Fact]
    public async Task ImportAsync_ShouldCreate_ExternalProductOptions_ForAccessories()
    {
        //Arrange
        await using var stream = File.Open("ProductImport/himsa_accessories_test_valid.xml", FileMode.Open);
        
        //Act
        var externalProducts = (await _sut.ImportAsync(Guid.NewGuid(), stream)).ToList();
        
        //Assert
        externalProducts.Should().HaveCount(3);

        var wirelessGateway = externalProducts[0];
        wirelessGateway.Category.Should().Be(ExternalProductCategory.Accessories);
        wirelessGateway.Manufacturer.Should().Be("Oticon");
        wirelessGateway.HearingAidType.Should().BeNull();
        var wirelessOption = wirelessGateway.ExternalProductOptions.First();
        wirelessOption.Name.Should().Be("STRPR 1.3A, LINKED WH STREAMER PRO 1.3A CL");
        wirelessOption.ExternalId.Should().Be("151240");
        wirelessOption.Styles!.Color.Should().Be("White");
        
        var mediaStreamer = externalProducts[1];
        mediaStreamer.Category.Should().Be(ExternalProductCategory.Accessories);
        mediaStreamer.Manufacturer.Should().Be("Oticon");
        mediaStreamer.HearingAidType.Should().BeNull();
        mediaStreamer.Name.Should().Be("PA2, OTICON JP BL PHONE ADAPTER 2.0");
        var mediaStreamerOption = mediaStreamer.ExternalProductOptions.First();
        mediaStreamerOption.Name.Should().Be("PA2, OTICON JP BL PHONE ADAPTER 2.0");
        mediaStreamerOption.ExternalId.Should().Be("128816");
        mediaStreamerOption.Styles!.Color.Should().Be("Black");

        var otherAccessory = externalProducts[2];
        otherAccessory.Category.Should().Be(ExternalProductCategory.Accessories);
        otherAccessory.Manufacturer.Should().Be("Oticon");
        otherAccessory.HearingAidType.Should().BeNull();
        otherAccessory.Name.Should().Be("EDUMIC, 2.4G OTICON");
        otherAccessory.ExternalProductOptions.First().ExternalId.Should().Be("191009");
    }
}
