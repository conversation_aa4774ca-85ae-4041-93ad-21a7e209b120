using Auditdata.InventoryService.Core.Features.Countries.GetCountries;
using Auditdata.InventoryService.UnitTests.Common;
using FluentAssertions;

namespace Auditdata.InventoryService.UnitTests.Countries;

public class GetCountriesHandlerTests : BaseSqlLiteDbContextTest
{
    private readonly GetCountriesHandler _sut;

    public GetCountriesHandlerTests()
    {
        _sut = new GetCountriesHandler(DbContext);
    }

    [Fact]
    public async Task Handler_Should_Respond_When_CountriesExist()
    {
        var countries = DbContext.Countries.OrderBy(x => x.Name).ToList();
        var result = await _sut.Handle(new GetCountriesQuery(), CancellationToken.None);

        result.Should().BeEquivalentTo(countries);
    }
}