using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Abstractions.Validators;
using Auditdata.InventoryService.Core.Entities.Products;
using Auditdata.InventoryService.Core.Exceptions;
using Auditdata.InventoryService.Core.Features.Skus.CreateSku;
using Auditdata.InventoryService.Core.Features.Skus.Models;
using Auditdata.InventoryService.UnitTests.Common;
using Auditdata.Transport.Contracts.Exceptions;
using FluentAssertions;
using Microsoft.EntityFrameworkCore;

namespace Auditdata.InventoryService.UnitTests.Skus;

public class CreateSkuHandlerTests : BaseSqlLiteDbContextTest
{
    private readonly IFixture _fixture;
    private readonly CreateSkuHandler _sut;
    private readonly Mock<ISkuValidator> _validatorMock;
    private readonly Mock<IEventPublisher> _publisherMock = new();

    public CreateSkuHandlerTests()
    {
        _fixture = new RecursiveFixture();
        _validatorMock = new Mock<ISkuValidator>();

        _sut = new CreateSkuHandler(DbContext, _validatorMock.Object, _publisherMock.Object);
    }

    [Fact]
    public async Task Handle_Should_CreateSku_When_CommandIsValid()
    {
        // Arrange
        var productId = Guid.NewGuid();
        var supplierId = Guid.NewGuid();
        var attributeId = Guid.NewGuid();
        var categoryId = Guid.NewGuid();

        var supplier = new Supplier
        {
            Id = supplierId,
            Name = "Test Supplier",
            PhoneNumber = "************",
            Address1 = "Address",
            State = "State",
            City = "City",
            IsDeleted = false
        };
        DbContext.Suppliers.Add(supplier);

        var category = new ProductCategory
        {
            Id = categoryId,
            Name = "Test Category",
            IsDeleted = false
        };
        DbContext.ProductCategories.Add(category);

        var product = new Product
        {
            Id = productId,
            Name = "Test Product",
            SupplierId = supplierId,
            Supplier = supplier,
            CategoryId = categoryId,
            Category = category,
            IsDeleted = false,
            IsActive = true,
            Attributes = new List<ProductAttribute>(),
            SkuConfigs = new List<SkuConfig>()
        };
        DbContext.Products.Add(product);

        var attribute = new Core.Entities.Attribute
        {
            Id = attributeId,
            Name = "Test Attribute",
            ValueType = AttributeValueType.MultiSelect,
            IsActive = true,
            IsDeleted = false
        };
        DbContext.Attributes.Add(attribute);

        var skuConfig = new SkuConfig
        {
            Id = Guid.NewGuid(),
            ProductId = productId,
            AttributeId = attributeId,
            SkuAttributeType = SkuAttributeType.Attribute,
            Product = product,
            Attribute = attribute
        };
        DbContext.SkuConfigs.Add(skuConfig);

        await DbContext.SaveChangesAsync();

        var command = new CreateSkuCommand
        {
            ProductId = productId,
            SkuValue = "TestSku123",
            Attributes = new List<AttributeDto>
        {
            new AttributeDto
            {
                AttributeId = attributeId,
                Value = new AttributeValueDto(Guid.NewGuid(), "Value1")
            }
        }
        };

        // Act
        var result = await _sut.Handle(command, CancellationToken.None);

        // Assert
        var sku = await DbContext.Skus.Include(s => s.Attributes).FirstOrDefaultAsync(s => s.SkuValue == command.SkuValue);

        sku.Should().NotBeNull();
        result.Should().Be(sku!.Id);
        sku.ProductId.Should().Be(command.ProductId);
        sku.ColorId.Should().Be(command.ColorId);
        sku.BatteryTypeId.Should().Be(command.BatteryTypeId);
        sku.Attributes.Should().HaveCount(command.Attributes!.Count);
        sku.Attributes.Select(a => a.AttributeId).Should().BeEquivalentTo(command.Attributes.Select(a => a.AttributeId));
        sku.Attributes.Select(a => a.Value.Value).Should().BeEquivalentTo(command.Attributes.Select(a => a.Value.Value));
        _publisherMock.Verify(x => x.SkuCreated(It.IsAny<Sku>(), It.IsAny<CancellationToken>()), Times.Once);
    }


    [Fact]
    public async Task Handle_Should_Throw_EntityNotFoundException_When_ProductNotFound()
    {
        // Arrange
        var command = _fixture.Build<CreateSkuCommand>()
            .With(c => c.ProductId, Guid.NewGuid())
            .Create();

        // Act
        var act = async () => await _sut.Handle(command, CancellationToken.None);

        // Assert
        await act.Should().ThrowAsync<EntityNotFoundException<Product>>();
    }

    [Fact]
    public async Task Handle_Should_Throw_BusinessException_When_SupplierNotAssigned()
    {
        // Arrange
        var productId = Guid.NewGuid();

        var product = new Product
        {
            Id = productId,
            Name = "Test Product",
            SupplierId = null,
            IsDeleted = false,
            IsActive = true,
            Attributes = new List<ProductAttribute>(),
            SkuConfigs = new List<SkuConfig>()
        };

        var category = new ProductCategory
        {
            Id = Guid.NewGuid(),
            Name = "Test Category",
            IsDeleted = false
        };
        DbContext.ProductCategories.Add(category);

        product.CategoryId = category.Id;
        product.Category = category;

        DbContext.Products.Add(product);

        await DbContext.SaveChangesAsync();

        var command = new CreateSkuCommand
        {
            ProductId = productId,
            SkuValue = "TestSku123"
        };

        // Act
        var act = async () => await _sut.Handle(command, CancellationToken.None);

        // Assert
        await act.Should().ThrowAsync<BusinessException>()
            .WithMessage("Assign the Supplier before the SKU creation.");
    }

    [Fact]
    public async Task Handle_Should_CallValidator_Validate()
    {
        // Arrange
        var productId = Guid.NewGuid();
        var supplierId = Guid.NewGuid();
        var attributeId = Guid.NewGuid();
        var categoryId = Guid.NewGuid();

        // Create and add Supplier
        var supplier = new Supplier
        {
            Id = supplierId,
            Name = "Test Supplier",
            PhoneNumber = "************",
            Address1 = "Address",
            State = "State",
            City = "City",
            IsDeleted = false
        };
        DbContext.Suppliers.Add(supplier);

        var category = new ProductCategory
        {
            Id = categoryId,
            Name = "Test Category",
            IsDeleted = false
        };
        DbContext.ProductCategories.Add(category);

        var product = new Product
        {
            Id = productId,
            Name = "Test Product",
            SupplierId = supplierId,
            Supplier = supplier,
            CategoryId = categoryId,
            Category = category,
            IsDeleted = false,
            IsActive = true,
            Attributes = new List<ProductAttribute>(),
            SkuConfigs = new List<SkuConfig>()
        };
        DbContext.Products.Add(product);

        var attribute = new Core.Entities.Attribute
        {
            Id = attributeId,
            Name = "Test Attribute",
            ValueType = AttributeValueType.MultiSelect,
            IsActive = true,
            IsDeleted = false
        };
        DbContext.Attributes.Add(attribute);

        var skuConfig = new SkuConfig
        {
            Id = Guid.NewGuid(),
            ProductId = productId,
            AttributeId = attributeId,
            SkuAttributeType = SkuAttributeType.Attribute,
            Product = product,
            Attribute = attribute
        };
        DbContext.SkuConfigs.Add(skuConfig);

        await DbContext.SaveChangesAsync();

        var command = new CreateSkuCommand
        {
            ProductId = productId,
            SkuValue = "TestSku123",
            Attributes = new List<AttributeDto>
        {
            new AttributeDto
            {
                AttributeId = attributeId,
                Value = new AttributeValueDto(Guid.NewGuid(), "Value1")
            }
        }
        };

        // Act
        await _sut.Handle(command, CancellationToken.None);

        // Assert
        _validatorMock.Verify(v => v.Validate(
            command.SkuValue,
            product,
            command.ColorId,
            command.BatteryTypeId,
            command.Attributes,
            It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact]
    public async Task Handle_Should_AddAttributes_When_AttributesProvided()
    {
        // Arrange
        var productId = Guid.NewGuid();
        var supplierId = Guid.NewGuid();
        var attributeId = Guid.NewGuid();
        var categoryId = Guid.NewGuid();
        var hearingAidTypeId = Guid.NewGuid();

        var supplier = new Supplier
        {
            Id = supplierId,
            Name = "Test Supplier",
            PhoneNumber = "************",
            Address1 = "add1",
            State = "state",
            City = "city",
            IsDeleted = false
        };
        DbContext.Suppliers.Add(supplier);

        var category = new ProductCategory
        {
            Id = categoryId,
            Name = "Test Category",
            IsDeleted = false
        };
        DbContext.ProductCategories.Add(category);

        var hearingAidType = new HearingAidType
        {
            Id = hearingAidTypeId,
            Name = "Test Hearing Aid Type",
            IsDeleted = false
        };
        DbContext.HearingAidTypes.Add(hearingAidType);

        var product = new Product
        {
            Id = productId,
            Name = "Test Product",
            SupplierId = supplierId,
            CategoryId = categoryId,
            HearingAidTypeId = hearingAidTypeId,
            Supplier = supplier,
            Category = category,
            HearingAidType = hearingAidType,
            IsDeleted = false,
            IsActive = true,
            SkuConfigs = new List<SkuConfig>(),
            Attributes = new List<ProductAttribute>()
        };
        DbContext.Products.Add(product);

        var attribute = new Core.Entities.Attribute
        {
            Id = attributeId,
            Name = "Test Attribute",
            ValueType = AttributeValueType.MultiSelect,
            IsActive = true,
            IsDeleted = false
        };
        DbContext.Attributes.Add(attribute);

        var skuConfig = new SkuConfig
        {
            Id = Guid.NewGuid(),
            ProductId = productId,
            AttributeId = attributeId,
            SkuAttributeType = SkuAttributeType.Attribute,
            Product = product,
            Attribute = attribute
        };
        DbContext.SkuConfigs.Add(skuConfig);

        var productAttribute = new ProductAttribute
        {
            Id = Guid.NewGuid(),
            ProductId = productId,
            AttributeId = attributeId,
            Attribute = attribute,
            Product = product
        };
        DbContext.ProductAttributes.Add(productAttribute);

        await DbContext.SaveChangesAsync();

        var attributes = new List<AttributeDto>
        {
            new AttributeDto
            {
                AttributeId = attributeId,
                Value = new AttributeValueDto(Guid.NewGuid(), "Value1")
            }
        };

        var command = new CreateSkuCommand
        {
            ProductId = productId,
            SkuValue = "TestSku123",
            Attributes = attributes
        };

        // Act
        await _sut.Handle(command, CancellationToken.None);

        // Assert
        var sku = await DbContext.Skus.Include(s => s.Attributes).FirstOrDefaultAsync(s => s.SkuValue == command.SkuValue);

        sku.Should().NotBeNull();
        sku!.ProductId.Should().Be(productId);
        sku.Attributes.Should().HaveCount(attributes.Count);
        sku.Attributes.Select(a => a.AttributeId).Should().BeEquivalentTo(attributes.Select(a => a.AttributeId));
        sku.Attributes.Select(a => a.Value.Value).Should().BeEquivalentTo(attributes.Select(a => a.Value.Value));
    }
}
