using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Abstractions.Validators;
using Auditdata.InventoryService.Core.Entities.Products;
using Auditdata.InventoryService.Core.Exceptions;
using Auditdata.InventoryService.Core.Features.Skus.Models;
using Auditdata.InventoryService.Core.Features.Skus.UpdateSku;
using Auditdata.InventoryService.UnitTests.Common;
using FluentAssertions;
using Microsoft.EntityFrameworkCore;

namespace Auditdata.InventoryService.UnitTests.Skus;

public class UpdateSkuHandlerTests : BaseSqlLiteDbContextTest
{
    private readonly Mock<ISkuValidator> _validatorMock;
    private readonly UpdateSkuHandler _sut;
    private readonly Mock<IEventPublisher> _publisherMock = new();

    public UpdateSkuHandlerTests()
    {
        _validatorMock = new Mock<ISkuValidator>();
        _sut = new UpdateSkuHandler(DbContext, _validatorMock.Object, _publisherMock.Object);
    }

    [Fact]
    public async Task Handle_Should_UpdateSku_When_CommandIsValid()
    {
        // Arrange
        await SeedDatabaseAsync();
        var existingSku = await DbContext.Skus
            .Include(s => s.Attributes)
            .FirstAsync();

        var command = new UpdateSkuCommand
        {
            SkuId = existingSku.Id,
            SkuValue = "NewSkuValue",
            Attributes = new List<AttributeDto>
            {
                new AttributeDto
                {
                    AttributeId = existingSku.Attributes.First().AttributeId,
                    Value = new AttributeValueDto(Guid.NewGuid(), "NewValue")
                }
            }
        };

        // Act
        await _sut.Handle(command, CancellationToken.None);

        // Assert
        var updatedSku = await DbContext.Skus
            .Include(s => s.Attributes)
            .ThenInclude(a => a.Value)
            .FirstOrDefaultAsync(s => s.Id == existingSku.Id);

        updatedSku.Should().NotBeNull();
        updatedSku!.SkuValue.Should().Be(command.SkuValue);
        updatedSku.Attributes.Should().HaveCount(command.Attributes!.Count);
        updatedSku.Attributes.First().Value.Value.Should().Be("NewValue");
        _publisherMock.Verify(x => x.SkuUpdated(It.IsAny<Sku>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Should_Throw_EntityNotFoundException_When_SkuNotFound()
    {
        // Arrange
        var command = new UpdateSkuCommand
        {
            SkuId = Guid.NewGuid(),
            SkuValue = "NewSkuValue"
        };

        // Act
        var act = async () => await _sut.Handle(command, CancellationToken.None);

        // Assert
        await act.Should().ThrowAsync<EntityNotFoundException<Sku>>();
    }

    [Fact]
    public async Task Handle_Should_CallValidator_Validate()
    {
        // Arrange
        await SeedDatabaseAsync();
        var existingSku = await DbContext.Skus
            .Include(s => s.Attributes)
            .FirstAsync();

        var command = new UpdateSkuCommand
        {
            SkuId = existingSku.Id,
            SkuValue = "NewSkuValue",
            Attributes = new List<AttributeDto>
            {
                new AttributeDto
                {
                    AttributeId = existingSku.Attributes.First().AttributeId,
                    Value = new AttributeValueDto(Guid.NewGuid(), "NewValue")
                }
            }
        };

        // Act
        await _sut.Handle(command, CancellationToken.None);

        // Assert
        _validatorMock.Verify(v => v.Validate(
            existingSku.Product,
            existingSku,
            command.SkuValue,
            command.ColorId,
            command.BatteryTypeId,
            command.Attributes,
            It.IsAny<CancellationToken>()),
            Times.Once);
    }

    private async Task SeedDatabaseAsync()
    {
        // Seed Supplier
        var supplier = new Supplier
        {
            Id = Guid.NewGuid(),
            Name = "Test Supplier",
            PhoneNumber = "************",
            Address1 = "Address",
            State = "State",
            City = "City",
            IsDeleted = false
        };
        DbContext.Suppliers.Add(supplier);
        await DbContext.SaveChangesAsync(); // Save supplier first

        // Seed Product Category
        var category = new ProductCategory
        {
            Id = Guid.NewGuid(),
            Name = "Test Category",
            IsDeleted = false
        };
        DbContext.ProductCategories.Add(category);
        await DbContext.SaveChangesAsync(); // Save category next

        // Seed Product
        var product = new Product
        {
            Id = Guid.NewGuid(),
            Name = "Test Product",
            CategoryId = category.Id,
            SupplierId = supplier.Id,
            IsActive = true,
            IsDeleted = false
        };
        DbContext.Products.Add(product);
        await DbContext.SaveChangesAsync(); // Save product next

        // Seed Attribute
        var attribute = new Core.Entities.Attribute
        {
            Id = Guid.NewGuid(),
            Name = "Test Attribute",
            ValueType = AttributeValueType.MultiSelect,
            IsActive = true,
            IsDeleted = false,
            Values = new List<AttributeValue>
            {
                new AttributeValue { ValueId = Guid.NewGuid(), Value = "Value1" }
            }
        };
        DbContext.Attributes.Add(attribute);
        await DbContext.SaveChangesAsync(); // Save attribute

        // Seed SKU
        var sku = new Sku
        {
            Id = Guid.NewGuid(),
            SkuValue = "OldSkuValue",
            ProductId = product.Id,
            SupplierId = supplier.Id,
            Attributes = new List<SkuAttribute>
            {
                new SkuAttribute
                {
                    AttributeId = attribute.Id,
                    Value = new AttributeValue
                    {
                        ValueId = Guid.NewGuid(),
                        Value = "OldValue"
                    }
                }
            },
            IsDeleted = false
        };
        DbContext.Skus.Add(sku);
        await DbContext.SaveChangesAsync(); // Save SKU
    }
}
