using Auditdata.InventoryService.Core.Entities.Products;
using Auditdata.Transport.Contracts.Exceptions;
using FluentAssertions;
using Auditdata.InventoryService.Core.Features.Skus.Validation;
using Auditdata.InventoryService.UnitTests.Common;
using Microsoft.EntityFrameworkCore;
using Auditdata.InventoryService.Core.Features.Skus.Models;
using Auditdata.InventoryService.Core.Constants;
using Attribute = Auditdata.InventoryService.Core.Entities.Attribute;

namespace Auditdata.InventoryService.UnitTests.Skus.Validation;

public class SkuValidatorTests : BaseSqlLiteDbContextTest
{
    private readonly SkuValidator _sut;

    public SkuValidatorTests()
    {
        _sut = new SkuValidator(DbContext);
    }

    [Theory]
    [RecursiveInlineAutoData]
    public async Task Validate_Should_Throw_When_DuplicatesInCommand(Product product)
    {
        // Arrange
        product.SkuConfigs.Clear();
        product.Skus.Clear();
        product.SkuConfigs.Add(new SkuConfig
        {
            Id = Guid.NewGuid(),
            ProductId = product.Id,
            SkuAttributeType = SkuAttributeType.Color,
        });
        product.SkuConfigs.Add(new SkuConfig
        {
            Id = Guid.NewGuid(),
            ProductId = product.Id,
            SkuAttributeType = SkuAttributeType.BatteryType,
        });
        product.SkuConfigs.Add(new SkuConfig
        {
            Id = Guid.NewGuid(),
            ProductId = product.Id,
            SkuAttributeType = SkuAttributeType.Attribute,
            AttributeId = product.Attributes.First().Attribute!.Id,
            Attribute = product.Attributes.First().Attribute,
        });
        var sku = Sku.Create("TestSku", product.Id, product.Supplier!.Id);
        sku.BatteryType = product.BatteryTypes.First().BatteryType;
        sku.Color = product.Colors.First().Color;
        sku.Attributes.Add(new SkuAttribute
        {
            Id = Guid.NewGuid(),
            SkuId = sku.Id,
            AttributeId = product.Attributes.First().Attribute!.Id,
            Value = new AttributeValue { ValueId = product.Attributes.First().Value[0].ValueId }
        });
        product.Skus.Add(sku);
        DbContext.Products.Add(product);
        await DbContext.SaveChangesAsync();

        // Act
        Func<Task> act = async () => await _sut.ValidateAssignSkus(
            product,
            [
                new()
                {
                    SkuValue = "TestSku2",
                    BatteryTypeId = sku.BatteryType!.Id,
                    ColorId = sku.Color!.Id,
                    Attributes =
                    [
                        new()
                        {
                            AttributeId = sku.Attributes.First().Attribute!.Id,
                            Value = new AttributeValueDto(
                                sku.Attributes.First().Value.ValueId, sku.Attributes.First().Value.Value),
                        }
                    ]
                },
                new()
                {
                    SkuValue = "TestSku3",
                    BatteryTypeId = sku.BatteryType!.Id,
                    ColorId = sku.Color!.Id,
                    Attributes =
                    [
                        new()
                        {
                            AttributeId = sku.Attributes.First().Attribute!.Id,
                            Value = new AttributeValueDto(
                                sku.Attributes.First().Value.ValueId, sku.Attributes.First().Value.Value),
                        }
                    ]
                }
            ],
            CancellationToken.None);

        // Assert
        var ex = await act.Should().ThrowAsync<BusinessException>();
        ex.Which.ErrorCode.Should().Be(ErrorCodes.SkusHasDuplicates);
    }

    [Theory]
    [RecursiveInlineAutoData]
    public async Task Validate_Should_Throw_When_DuplicateAttributesInOneSkuInCommand(Product product)
    {
        // Arrange
        product.SkuConfigs.Clear();
        product.Skus.Clear();
        product.SkuConfigs.Add(new SkuConfig
        {
            Id = Guid.NewGuid(),
            ProductId = product.Id,
            SkuAttributeType = SkuAttributeType.Color,
        });
        product.SkuConfigs.Add(new SkuConfig
        {
            Id = Guid.NewGuid(),
            ProductId = product.Id,
            SkuAttributeType = SkuAttributeType.BatteryType,
        });
        product.SkuConfigs.Add(new SkuConfig
        {
            Id = Guid.NewGuid(),
            ProductId = product.Id,
            SkuAttributeType = SkuAttributeType.Attribute,
            AttributeId = product.Attributes.First().Attribute!.Id,
            Attribute = product.Attributes.First().Attribute,
        });
        var sku = Sku.Create("TestSku", product.Id, product.Supplier!.Id);
        sku.BatteryType = product.BatteryTypes.First().BatteryType;
        sku.Color = product.Colors.First().Color;
        sku.Attributes.Add(new SkuAttribute
        {
            Id = Guid.NewGuid(),
            SkuId = sku.Id,
            AttributeId = product.Attributes.First().Attribute!.Id,
            Value = new AttributeValue { ValueId = product.Attributes.First().Value[0].ValueId }
        });
        product.Skus.Add(sku);
        DbContext.Products.Add(product);
        await DbContext.SaveChangesAsync();

        // Act
        Func<Task> act = async () => await _sut.ValidateAssignSkus(
            product,
            [
                new()
                {
                    SkuValue = "TestSku3",
                    BatteryTypeId = sku.BatteryType!.Id,
                    ColorId = sku.Color!.Id,
                    Attributes =
                    [
                        new()
                        {
                            AttributeId = sku.Attributes.First().Attribute!.Id,
                            Value = new AttributeValueDto(
                                sku.Attributes.First().Value.ValueId, sku.Attributes.First().Value.Value),
                        },
                        new()
                        {
                            AttributeId = sku.Attributes.First().Attribute!.Id,
                            Value = new AttributeValueDto(
                                sku.Attributes.First().Value.ValueId, sku.Attributes.First().Value.Value),
                        }
                    ]
                }
            ],
            CancellationToken.None);

        // Assert
        var ex = await act.Should().ThrowAsync<BusinessException>();
        ex.Which.ErrorCode.Should().Be(ErrorCodes.SkusHasDuplicates);
    }

    [Theory]
    [RecursiveInlineAutoData]
    public async Task Validate_Should_Throw_When_SkuCombinationExistsForProduct(Product product)
    {
        // Arrange
        product.SkuConfigs.Clear();
        product.Skus.Clear();
        product.SkuConfigs.Add(new SkuConfig
        {
            Id = Guid.NewGuid(),
            ProductId = product.Id,
            SkuAttributeType = SkuAttributeType.Color,
        });
        product.SkuConfigs.Add(new SkuConfig
        {
            Id = Guid.NewGuid(),
            ProductId = product.Id,
            SkuAttributeType = SkuAttributeType.BatteryType,
        });
        product.SkuConfigs.Add(new SkuConfig
        {
            Id = Guid.NewGuid(),
            ProductId = product.Id,
            SkuAttributeType = SkuAttributeType.Attribute,
            AttributeId = product.Attributes.First().Attribute!.Id,
            Attribute = product.Attributes.First().Attribute,
        });
        var sku = Sku.Create("TestSku", product.Id, product.Supplier!.Id);
        sku.BatteryType = product.BatteryTypes.First().BatteryType;
        sku.Color = product.Colors.First().Color;
        sku.Attributes.Add(new SkuAttribute
        {
            Id = Guid.NewGuid(),
            SkuId = sku.Id,
            AttributeId = product.Attributes.First().Attribute!.Id,
            Value = new AttributeValue { ValueId = product.Attributes.First().Value[0].ValueId }
        });
        product.Skus.Add(sku);
        DbContext.Products.Add(product);
        await DbContext.SaveChangesAsync();
        var attributes = new List<AttributeDto>
        {
            new()
            {
                AttributeId = sku.Attributes.First().Attribute!.Id,
                Value = new AttributeValueDto(
                    sku.Attributes.First().Value.ValueId, sku.Attributes.First().Value.Value),
            }
        };

        // Act
        var act = async () => await _sut
            .Validate("TestSku2", product, sku.Color!.Id, sku.BatteryType!.Id, attributes, CancellationToken.None);

        // Assert
        var ex = await act.Should().ThrowAsync<BusinessException>();
        ex.Which.ErrorCode.Should().Be(ErrorCodes.SkuCombinationExistsForProduct);
    }

    [Theory]
    [RecursiveInlineAutoData]
    public async Task Validate_Should_Throw_When_SkuCombinationExistsForProductWithEmptyAttributeValueIds(Product product)
    {
        // Arrange
        product.SkuConfigs.Clear();
        product.Skus.Clear();
        product.SkuConfigs.Add(new SkuConfig
        {
            Id = Guid.NewGuid(),
            ProductId = product.Id,
            SkuAttributeType = SkuAttributeType.Color,
        });
        product.SkuConfigs.Add(new SkuConfig
        {
            Id = Guid.NewGuid(),
            ProductId = product.Id,
            SkuAttributeType = SkuAttributeType.BatteryType,
        });
        product.SkuConfigs.Add(new SkuConfig
        {
            Id = Guid.NewGuid(),
            ProductId = product.Id,
            SkuAttributeType = SkuAttributeType.Attribute,
            AttributeId = product.Attributes.First().Attribute!.Id,
            Attribute = product.Attributes.First().Attribute,
        });
        var sku = Sku.Create("TestSku", product.Id, product.Supplier!.Id);
        sku.BatteryType = product.BatteryTypes.First().BatteryType;
        sku.Color = product.Colors.First().Color;
        sku.Attributes.Add(new SkuAttribute
        {
            Id = Guid.NewGuid(),
            SkuId = sku.Id,
            AttributeId = product.Attributes.First().Attribute!.Id,
            Value = new AttributeValue { ValueId = Guid.Empty, Value = product.Attributes.First().Value[0].Value }
        });
        product.Skus.Add(sku);
        DbContext.Products.Add(product);
        await DbContext.SaveChangesAsync();
        var attributes = new List<AttributeDto>
        {
            new()
            {
                AttributeId = sku.Attributes.First().Attribute!.Id,
                Value = new AttributeValueDto(
                    Guid.Empty, sku.Attributes.First().Value.Value),
            }
        };

        // Act
        var act = async () => await _sut
            .Validate("TestSku2", product, sku.Color!.Id, sku.BatteryType!.Id, attributes, CancellationToken.None);

        // Assert
        var ex = await act.Should().ThrowAsync<BusinessException>();
        ex.Which.ErrorCode.Should().Be(ErrorCodes.SkuCombinationExistsForProduct);
    }

    [Fact]
    public async Task Validate_Should_Throw_When_BatteryTypeIsRequired_ButNotProvided()
    {
        // Arrange
        var product = await SeedProductWithSkuConfigAsync(SkuAttributeType.BatteryType);

        // Act
        Func<Task> act = async () => await _sut.Validate(
            "TestSku",
            product,
            colorId: null,
            batteryTypeId: null,
            attributes: null,
            CancellationToken.None);

        // Assert
        await act.Should().ThrowAsync<BusinessException>()
            .WithMessage("Battery type is required.");
    }

    [Fact]
    public async Task Validate_Should_Throw_When_ColorIsRequired_ButNotProvided()
    {
        // Arrange
        var product = await SeedProductWithSkuConfigAsync(SkuAttributeType.Color);

        // Act
        Func<Task> act = async () => await _sut.Validate(
            "TestSku",
            product,
            colorId: null,
            batteryTypeId: null,
            attributes: null,
            CancellationToken.None);

        // Assert
        await act.Should().ThrowAsync<BusinessException>()
            .WithMessage("Color is required.");
    }

    [Fact]
    public async Task Validate_Should_Throw_When_AttributeIsRequired_ButNotProvided()
    {
        // Arrange
        var product = await SeedProductWithSkuConfigAsync(SkuAttributeType.Attribute);
        var attribute = await SeedAttributeAsync("RequiredAttribute");

        product.SkuConfigs.First().AttributeId = attribute.Id;
        await DbContext.SaveChangesAsync();

        // Act
        Func<Task> act = async () => await _sut.Validate(
            "TestSku",
            product,
            colorId: null,
            batteryTypeId: null,
            attributes: null,
            CancellationToken.None);

        // Assert
        await act.Should().ThrowAsync<BusinessException>()
            .WithMessage("Attribute is required.");
    }

    [Fact]
    public async Task Validate_Should_Throw_When_AttributeValueIsInvalid()
    {
        // Arrange
        var product = await SeedProductWithSkuConfigAsync(SkuAttributeType.Attribute);
        var attribute = await SeedAttributeAsync("RequiredAttribute");
        product.Attributes = [new()
        {
            AttributeId = attribute.Id,
            Value = [new() { Value = "test2" }]
        }];

        product.SkuConfigs.First().AttributeId = attribute.Id;
        await DbContext.SaveChangesAsync();

        // Act
        Func<Task> act = async () => await _sut.Validate(
            "TestSku",
            product,
            colorId: null,
            batteryTypeId: null,
            attributes: [new() { AttributeId = attribute.Id, Value = new(Guid.Empty, "test") }],
            CancellationToken.None);

        // Assert
        var ex = await act.Should().ThrowAsync<BusinessException>();
        ex.Which.ErrorCode.Should().Be(ErrorCodes.AttributeValueIsInvalid);
    }

    [Fact]
    public async Task Validate_Should_Throw_When_DuplicateSkuForSupplierExists()
    {
        // Arrange
        var product = await SeedProductWithSkuConfigAsync(SkuAttributeType.Color);
        await SeedSkuAsync(product.Id, "DuplicateSku");

        // Act
        Func<Task> act = async () => await _sut.Validate(
            "DuplicateSku",
            product,
            colorId: product.Colors.First().ColorId,
            batteryTypeId: null,
            attributes: null,
            CancellationToken.None);

        // Assert
        await act.Should().ThrowAsync<BusinessException>()
            .WithMessage("SKU must be unique for the supplier.");
    }

    [Fact]
    public async Task Validate_Should_Throw_When_AttributeIsNotAssignedToProduct()
    {
        // Arrange
        var product = await SeedProductWithSkuConfigAsync(SkuAttributeType.Color);

        // Act
        Func<Task> act = async () => await _sut.Validate(
            "TestSku",
            product,
            colorId: null,
            batteryTypeId: null,
            attributes: [new() { AttributeId = Guid.NewGuid() }],
            CancellationToken.None);

        // Assert
        var ex = await act.Should().ThrowAsync<BusinessException>();
        ex.Which.ErrorCode.Should().Be(ErrorCodes.AttributeIsInvalid);
    }

    private async Task<Product> SeedProductWithSkuConfigAsync(SkuAttributeType skuAttributeType)
    {
        var supplier = await SeedSupplierAsync();
        var productCategory = await SeedProductCategoryAsync();

        var product = new Product
        {
            Id = Guid.NewGuid(),
            Name = "TestProduct",
            SupplierId = supplier.Id,
            CategoryId = productCategory.Id,
            IsActive = true,
            SkuConfigs =
            [
                new SkuConfig
                {
                    Id = Guid.NewGuid(),
                    SkuAttributeType = skuAttributeType
                }
            ],
        };
        var color = Color.Create("TestColor");
        product.Colors = [ProductColor.Create(product.Id, color.Id)];
        var attribute = Attribute.Create("TestAttribute", AttributeValueType.SingleSelect, true, [productCategory]);
        product.Attributes = [ProductAttribute.Create(product.Id, attribute.Id, [])];

        DbContext.Colors.Add(color);
        DbContext.Attributes.Add(attribute);
        DbContext.Products.Add(product);
        await DbContext.SaveChangesAsync();
        return product;
    }

    private async Task<Supplier> SeedSupplierAsync()
    {
        var supplier = new Supplier
        {
            Id = Guid.NewGuid(),
            Name = "Test Supplier",
            PhoneNumber = "************",
            Address1 = "Address",
            State = "State",
            City = "City",
            IsDeleted = false
        };

        DbContext.Suppliers.Add(supplier);
        await DbContext.SaveChangesAsync();
        return supplier;
    }

    private async Task<ProductCategory> SeedProductCategoryAsync()
    {
        var category = new ProductCategory
        {
            Id = Guid.NewGuid(),
            Name = "TestCategory",
            IsDeleted = false
        };

        DbContext.ProductCategories.Add(category);
        await DbContext.SaveChangesAsync();
        return category;
    }

    private async Task<Core.Entities.Attribute> SeedAttributeAsync(string attributeName)
    {
        var attribute = new Core.Entities.Attribute
        {
            Id = Guid.NewGuid(),
            Name = attributeName,
            ValueType = AttributeValueType.SingleSelect,
            IsActive = true,
            IsDeleted = false
        };

        DbContext.Attributes.Add(attribute);
        await DbContext.SaveChangesAsync();
        return attribute;
    }

    private async Task SeedSkuAsync(Guid productId, string skuValue)
    {
        var supplier = await DbContext.Suppliers.FirstOrDefaultAsync();

        var sku = new Sku
        {
            Id = Guid.NewGuid(),
            SkuValue = skuValue,
            ProductId = productId,
            SupplierId = supplier!.Id,
            IsDeleted = false
        };

        DbContext.Skus.Add(sku);
        await DbContext.SaveChangesAsync();
    }
}