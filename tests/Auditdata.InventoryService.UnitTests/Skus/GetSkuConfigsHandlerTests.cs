using Auditdata.InventoryService.Core.Entities.Products;
using Auditdata.InventoryService.Core.Features.SKUs.GetSkuConfigs;
using Auditdata.InventoryService.UnitTests.Common;
using FluentAssertions;

namespace Auditdata.InventoryService.UnitTests.Skus;

public class GetSkuConfigsHandlerTests : BaseSqlLiteDbContextTest
{
    private readonly GetSkuConfigsHandler _sut;

    public GetSkuConfigsHandlerTests()
    {
        _sut = new GetSkuConfigsHandler(DbContext);
    }

    [Fact]
    public async Task Handle_Should_ReturnSkuAttributes_When_ProductIdIsValid()
    {
        // Arrange
        await SeedDatabaseAsync();
        var productId = DbContext.Products.First().Id;
        var attributeId = DbContext.Attributes.First().Id;

        var query = new GetSkuConfigsQuery(productId);

        // Act
        var result = await _sut.Handle(query, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Count.Should().Be(3);
        result.Should().Contain(x => x.SkuAttributeType == Core.Features.Skus.Models.SkuAttributeTypeDto.BatteryType);
        result.Should().Contain(x => x.SkuAttributeType == Core.Features.Skus.Models.SkuAttributeTypeDto.Color);
        result.Should().Contain(x => x.SkuAttributeType == Core.Features.Skus.Models.SkuAttributeTypeDto.Attribute
            && x.AttributeId == attributeId);
    }

    private async Task SeedDatabaseAsync()
    {
        // Seed Supplier
        var supplier = new Supplier
        {
            Id = Guid.NewGuid(),
            Name = "Test Supplier",
            PhoneNumber = "************",
            Address1 = "Address",
            State = "State",
            City = "City",
            IsDeleted = false
        };
        DbContext.Suppliers.Add(supplier);

        // Seed Product Category
        var category = new ProductCategory
        {
            Id = Guid.NewGuid(),
            Name = "Test Category",
            IsDeleted = false
        };
        DbContext.ProductCategories.Add(category);

        // Seed Product
        var product = new Product
        {
            Id = Guid.NewGuid(),
            Name = "Test Product",
            CategoryId = category.Id,
            SupplierId = supplier.Id,
            IsActive = true,
            BatteryTypes = new List<ProductBatteryType>(),
            Colors = new List<ProductColor>(),
            Attributes = new List<ProductAttribute>(),
            SkuConfigs = new List<SkuConfig>()
        };
        DbContext.Products.Add(product);

        // Seed Battery Type
        var batteryType = new BatteryType
        {
            Id = Guid.NewGuid(),
            Name = "Test Battery",
            IsDeleted = false
        };
        DbContext.BatteryTypes.Add(batteryType);

        // Seed Color
        var color = new Color
        {
            Id = Guid.NewGuid(),
            Name = "Test Color",
            IsDeleted = false
        };
        DbContext.Colors.Add(color);

        // Seed Attribute
        var attribute = new Core.Entities.Attribute
        {
            Id = Guid.NewGuid(),
            Name = "Test Attribute",
            ValueType = AttributeValueType.MultiSelect,
            IsActive = true,
            IsDeleted = false,
            Values = new List<AttributeValue>
            {
                new AttributeValue { ValueId = Guid.NewGuid(), Value = "Value1" },
                new AttributeValue { ValueId = Guid.NewGuid(), Value = "Value2" }
            }
        };
        DbContext.Attributes.Add(attribute);

        // Seed ProductAttribute
        var productAttribute = new ProductAttribute
        {
            ProductId = product.Id,
            AttributeId = attribute.Id,
            Value = attribute.Values.ToList(),
            Attribute = attribute
        };
        DbContext.ProductAttributes.Add(productAttribute);
        var productColor = ProductColor.Create(product.Id, color.Id);
        DbContext.ProductColors.Add(productColor);
        var productBatteryType = ProductBatteryType.Create(batteryType.Id);
        productBatteryType.ProductId = product.Id;
        DbContext.ProductBatteryTypes.Add(productBatteryType);

        // Seed SkuConfig
        var skuConfig = new SkuConfig
        {
            Id = Guid.NewGuid(),
            ProductId = product.Id,
            AttributeId = attribute.Id,
            SkuAttributeType = SkuAttributeType.Attribute,
            Product = product,
            Attribute = attribute
        };
        var skuConfig2 = new SkuConfig
        {
            Id = Guid.NewGuid(),
            ProductId = product.Id,
            SkuAttributeType = SkuAttributeType.BatteryType,
        };
        var skuConfig3 = new SkuConfig
        {
            Id = Guid.NewGuid(),
            ProductId = product.Id,
            SkuAttributeType = SkuAttributeType.Color,
        };
        DbContext.SkuConfigs.AddRange(skuConfig, skuConfig2, skuConfig3);

        // Save Changes
        await DbContext.SaveChangesAsync();
    }
}
