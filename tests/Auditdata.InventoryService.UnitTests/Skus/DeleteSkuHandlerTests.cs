using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Entities.Products;
using Auditdata.InventoryService.Core.Exceptions;
using Auditdata.InventoryService.Core.Features.Skus.DeleteSku;
using Auditdata.InventoryService.UnitTests.Common;
using FluentAssertions;
using Microsoft.EntityFrameworkCore;

namespace Auditdata.InventoryService.UnitTests.Skus;

public class DeleteSkuHandlerTests : BaseSqlLiteDbContextTest
{
    private readonly DeleteSkuHandler _sut;
    private readonly Mock<IEventPublisher> _publisherMock = new();

    public DeleteSkuHandlerTests()
    {
        _sut = new DeleteSkuHandler(DbContext, _publisherMock.Object);
    }

    [Fact]
    public async Task Handle_Should_DeleteSku_When_SkuExists()
    {
        // Arrange
        var skuId = await SeedDatabaseAsync();

        var command = new DeleteSkuCommand(skuId);

        // Act
        await _sut.Handle(command, CancellationToken.None);

        // Assert
        var deletedSku = await DbContext.Skus
            .Include(s => s.Attributes)
            .FirstOrDefaultAsync(s => s.Id == skuId);

        deletedSku.Should().BeNull();
        var remainingAttributes = await DbContext.SkuAttributes.ToListAsync();
        remainingAttributes.Should().BeEmpty();
        _publisherMock.Verify(x => x.SkuDeleted(It.IsAny<Sku>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Should_Throw_EntityNotFoundException_When_SkuDoesNotExist()
    {
        // Arrange
        var command = new DeleteSkuCommand(Guid.NewGuid());

        // Act
        var act = async () => await _sut.Handle(command, CancellationToken.None);

        // Assert
        await act.Should().ThrowAsync<EntityNotFoundException<Sku>>();
    }

    private async Task<Guid> SeedDatabaseAsync()
    {
        var supplier = new Supplier
        {
            Id = Guid.NewGuid(),
            Name = "Test Supplier",
            PhoneNumber = "************",
            Address1 = "Address",
            State = "State",
            City = "City",
            IsDeleted = false
        };
        DbContext.Suppliers.Add(supplier);
        await DbContext.SaveChangesAsync(); 

        var category = new ProductCategory
        {
            Id = Guid.NewGuid(),
            Name = "Test Category",
            IsDeleted = false
        };
        DbContext.ProductCategories.Add(category);
        await DbContext.SaveChangesAsync(); 

        var product = new Product
        {
            Id = Guid.NewGuid(),
            Name = "Test Product",
            CategoryId = category.Id,
            SupplierId = supplier.Id,
            IsActive = true,
            IsDeleted = false
        };
        DbContext.Products.Add(product);
        await DbContext.SaveChangesAsync(); 

        var attribute = new Core.Entities.Attribute
        {
            Id = Guid.NewGuid(),
            Name = "Test Attribute",
            ValueType = AttributeValueType.MultiSelect,
            IsActive = true,
            IsDeleted = false,
            Values = new List<AttributeValue>
            {
                new AttributeValue { ValueId = Guid.NewGuid(), Value = "Value1" }
            }
        };
        DbContext.Attributes.Add(attribute);
        await DbContext.SaveChangesAsync(); 

        var sku = new Sku
        {
            Id = Guid.NewGuid(),
            SkuValue = "TestSkuValue",
            ProductId = product.Id,
            SupplierId = supplier.Id,
            Attributes = new List<SkuAttribute>
            {
                new SkuAttribute
                {
                    AttributeId = attribute.Id,
                    Value = new AttributeValue
                    {
                        ValueId = Guid.NewGuid(),
                        Value = "TestValue"
                    }
                }
            },
            IsDeleted = false
        };
        DbContext.Skus.Add(sku);
        await DbContext.SaveChangesAsync(); 

        return sku.Id;
    }
}
