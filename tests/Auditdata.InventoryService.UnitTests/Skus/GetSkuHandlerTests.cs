using Auditdata.InventoryService.Core.Entities.Products;
using Auditdata.InventoryService.Core.Exceptions;
using Auditdata.InventoryService.Core.Features.Skus.GetById;
using Auditdata.InventoryService.UnitTests.Common;
using FluentAssertions;

namespace Auditdata.InventoryService.UnitTests.Skus;

public class GetSkuHandlerTests : BaseSqlLiteDbContextTest
{
    private readonly GetSkuHandler _sut;

    public GetSkuHandlerTests()
    {
        _sut = new GetSkuHandler(DbContext);
    }

    [Fact]
    public async Task Handle_Should_ReturnSkuDto_When_SkuIdIsValid()
    {
        // Arrange
        var skuId = await SeedDatabaseAsync();
        var query = new GetSkuQuery(skuId);

        // Act
        var result = await _sut.Handle(query, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Id.Should().Be(skuId);
        result.SkuValue.Should().Be("TestSkuValue");
        result.SkuAttributes.Should().HaveCount(1);
        result.SkuAttributes!.First().AttributeInfo.Should().NotBeNull();
        result.SkuAttributes!.First().Value!.Value.Should().Be("TestValue");
    }

    [Fact]
    public async Task Handle_Should_Throw_EntityNotFoundException_When_SkuIdIsInvalid()
    {
        // Arrange
        var query = new GetSkuQuery(Guid.NewGuid());

        // Act
        Func<Task> act = async () => await _sut.Handle(query, CancellationToken.None);

        // Assert
        await act.Should().ThrowAsync<EntityNotFoundException<Sku>>();
    }

    [Fact]
    public async Task Handle_Should_IncludeAllSkuRelations()
    {
        // Arrange
        var skuId = await SeedDatabaseAsync();
        var query = new GetSkuQuery(skuId);

        // Act
        var result = await _sut.Handle(query, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.BatteryType.Should().BeNull();
        result.Color.Should().BeNull();
        result.SkuAttributes.Should().HaveCount(1);
        var attribute = result.SkuAttributes!.First();
        attribute.AttributeInfo.Should().NotBeNull();
        attribute.AttributeInfo!.Name.Should().Be("Test Attribute");
        attribute.Value.Should().NotBeNull();
        attribute.Value!.Value.Should().Be("TestValue");
    }

    private async Task<Guid> SeedDatabaseAsync()
    {
        var supplier = new Supplier
        {
            Id = Guid.NewGuid(),
            Name = "Test Supplier",
            PhoneNumber = "************",
            Address1 = "Address",
            State = "State",
            City = "City",
            IsDeleted = false
        };
        DbContext.Suppliers.Add(supplier);
        await DbContext.SaveChangesAsync();

        var category = new ProductCategory
        {
            Id = Guid.NewGuid(),
            Name = "Test Category",
            IsDeleted = false
        };
        DbContext.ProductCategories.Add(category);
        await DbContext.SaveChangesAsync();

        var product = new Product
        {
            Id = Guid.NewGuid(),
            Name = "Test Product",
            CategoryId = category.Id,
            SupplierId = supplier.Id,
            IsActive = true,
            IsDeleted = false
        };
        DbContext.Products.Add(product);
        await DbContext.SaveChangesAsync();

        var attribute = new Core.Entities.Attribute
        {
            Id = Guid.NewGuid(),
            Name = "Test Attribute",
            ValueType = AttributeValueType.MultiSelect,
            IsActive = true,
            IsDeleted = false,
            Values = new List<AttributeValue>
            {
                new AttributeValue { ValueId = Guid.NewGuid(), Value = "Value1" }
            }
        };
        DbContext.Attributes.Add(attribute);
        await DbContext.SaveChangesAsync();

        var sku = new Sku
        {
            Id = Guid.NewGuid(),
            SkuValue = "TestSkuValue",
            ProductId = product.Id,
            SupplierId = supplier.Id,
            Attributes = new List<SkuAttribute>
            {
                new SkuAttribute
                {
                    AttributeId = attribute.Id,
                    Value = new AttributeValue
                    {
                        ValueId = Guid.NewGuid(),
                        Value = "TestValue"
                    }
                }
            },
            IsDeleted = false
        };
        DbContext.Skus.Add(sku);
        await DbContext.SaveChangesAsync();

        return sku.Id;
    }
}
