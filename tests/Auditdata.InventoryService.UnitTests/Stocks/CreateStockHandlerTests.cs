using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Abstractions.Azure;
using Auditdata.InventoryService.Core.Entities.Products;
using Auditdata.InventoryService.Core.Features.Products.Mappings;
using Auditdata.InventoryService.Core.Features.Stocks.Commands;
using Auditdata.InventoryService.Core.Features.Stocks.Handlers;
using Auditdata.InventoryService.UnitTests.Common;
using FluentAssertions;
using Microsoft.EntityFrameworkCore;

namespace Auditdata.InventoryService.UnitTests.Stocks;

public class CreateStockHandlerTests : BaseDbContextTest
{
    private readonly CreateStockHandler _sut;
    private readonly Fixture _fixture = new RecursiveFixture();
    private readonly Mock<IEventPublisher> _eventPublisherMock = new();

    public CreateStockHandlerTests()
    {
        var mapper = new MapperConfiguration(cfg => cfg.AddProfile<ProductMappings>()).CreateMapper();
        _sut = new CreateStockHandler(
            new Mock<IAzureServiceBusPublisher>().Object,
            DbContext,
            mapper,
            _eventPublisherMock.Object);
    }

    [Fact]
    public async Task Handle_ShouldCreate_Stock_With_StockProducts()
    {
        // Arrange
        var locationId = Guid.NewGuid();
        var locationName = "test_location";
        var regionId = Guid.NewGuid();
        var parentLocationId = Guid.NewGuid();

        var productCategory = _fixture.Build<ProductCategory>().With(x => x.IsDeleted, false).Create();
        var manufacturer = _fixture.Build<Manufacturer>().With(x => x.IsDeleted, false).Create();
        var products = _fixture.Build<Product>()
            .With(x => x.IsDeleted, false)
            .With(x => x.Category, productCategory)
            .With(x => x.Manufacturer, manufacturer)
            .Without(x => x.StockProducts)
            .CreateMany().ToList();
        DbContext.Products.AddRange(products);
        await DbContext.SaveChangesAsync();
        
        // Act
        await _sut.Handle(
            new CreateStockCommand(locationName, locationId, parentLocationId, regionId),
            CancellationToken.None);
        
        // Assert
        var stock = await DbContext.Stocks.FirstAsync();
        stock.Name.Should().Be(locationName);
        stock.LocationId.Should().Be(locationId);
        stock.RegionId.Should().Be(regionId);
        stock.ParentLocationId.Should().Be(parentLocationId);

        var stockProducts = await DbContext.StockProducts.ToListAsync();
        stockProducts.Should().HaveCount(products.Count);

        foreach (var product in products)
        {
            stockProducts.Should().Contain(x => x.Product == product);
        }
    }
}
