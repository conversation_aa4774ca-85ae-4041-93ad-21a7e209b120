using Auditdata.Infrastructure.Contracts.CountryLayers;
using Auditdata.Infrastructure.Contracts.OperationContext;
using Auditdata.Infrastructure.Excel.Abstractions;
using Auditdata.InventoryService.Core.Entities.Products;
using Auditdata.InventoryService.Core.Features.Products.Mappings;
using Auditdata.InventoryService.Core.Features.ProductsExcelExport.Handlers;
using Auditdata.InventoryService.Core.Features.ProductsExcelExport.Queries;
using Auditdata.InventoryService.Core.Features.ProductsExcelImport.Models.Excel;
using Auditdata.InventoryService.UnitTests.Common;
using AutoFixture.Xunit2;
using FluentAssertions;
using System.IO;

namespace Auditdata.InventoryService.UnitTests.ProductsExport
{
    public class ProductsExportQueryHandlerTests : BaseSqlLiteDbContextTest
    {
        private readonly Mock<IExcelExporter> _excelExporterMock;
        private readonly Mock<IOperationContext> _operationContextMock;
        private readonly ProductsExportQueryHandler _sut;

        private readonly Fixture _fixture;

        public ProductsExportQueryHandlerTests()
        {
            _excelExporterMock = new Mock<IExcelExporter>();
            _operationContextMock = new Mock<IOperationContext>();
            var mapper = new MapperConfiguration(c => c.AddProfile<ProductMappings>()).CreateMapper();

            _sut = new ProductsExportQueryHandler(
                _excelExporterMock.Object,
                DbContext,
                _operationContextMock.Object,
                mapper);

            _fixture = new RecursiveFixture();
            _fixture.Behaviors.Add(new OmitOnRecursionBehavior());
        }

        [Theory]
        [InlineAutoData(false)]
        [InlineAutoData(true)]
        public async Task Handle_Should_ExportHearingAids_When_QueryProvided(
            bool activeOnly, string searchText, byte[] excelData)
        {
            // Arrangestream
            var stream = new MemoryStream(excelData);
            var query = new ProductsExportQuery() 
            { 
                ActiveOnly = activeOnly, 
                SearchText = searchText,
                CategoryCode = ProductCategoryCode.HearingAids,
            };

            var productCategory = new ProductCategory()
            {
                Code = ProductCategoryCode.HearingAids,
                Name = "test",
                IsDeleted = false,
            };

            var products = _fixture.Build<AUProduct>()
                .With(x => x.IsDeleted, false)
                .With(x => x.Category, productCategory)
                .With(x=> x.StockProducts, new List<StockProduct>())
                .CreateMany(6).ToArray();
            products[0].IsHSP = true;
            products[0].IsActive = true;
            var searchedProducts = products.Take(3).ToArray();
            foreach (var product in searchedProducts)
            {
                product.Name += searchText;
            }

            DbContext.ProductCategories.Add(productCategory);
            DbContext.Products.AddRange(products);
            await DbContext.SaveChangesAsync();
            _excelExporterMock.Setup(x => x.ToXlsxStream(It.IsAny<IReadOnlyList<HearingAidExcelModel>>(), CountryEnum.Australia, It.IsAny<string?>()))
                .Returns(stream);
            _operationContextMock.Setup(x => x.CountryId).Returns(CountryEnum.Australia);

            // Act
            var result = await _sut.Handle(query, CancellationToken.None);

            // Assert
            var resultBuffer = ((MemoryStream)result.ExcelFileStream).ToArray();
            resultBuffer.Should().BeEquivalentTo(stream.ToArray());
            var correctProducts = searchedProducts.Where(x => !activeOnly || x.IsActive);
            var hspProduct = searchedProducts.First(x => x.Name == products[0].Name);
            _excelExporterMock.Verify(x => x.ToXlsxStream(It.Is<IReadOnlyList<HearingAidExcelModel>>(
                x => x.Should().BeEquivalentTo(correctProducts, opt => opt.Excluding(x => x.Colors)
                .Excluding(x => x.Supplier)
                .Excluding(x => x.Manufacturer)
                .Excluding(x => x.ProductPathways)
                .Excluding(x => x.BatteryTypes)
                .Excluding(x => x.HspCode)
                .Excluding(x => x.HspCategory)
                .Excluding(x => x.HspTopUp)
                .Excluding(x => x.HspClientPrice)
                .Excluding(x => x.Description)
                .Excluding(x => x.SuggestedProducts)
                .ExcludingMissingMembers(), "") != null
                && x.Select(y => y.Description).Should().BeEquivalentTo(correctProducts.Select(y => y.DescriptionValue), "") != null
                && x.First(y => y.Name == hspProduct.Name).HspCategory.Should().BeEquivalentTo(hspProduct.HspCategory, "") != null
                && x.First(y => y.Name == hspProduct.Name).HspCode.Should().BeEquivalentTo(hspProduct.HspCode, "") != null
                && x.First(y => y.Name == hspProduct.Name).HspTopUp.Should().Be(hspProduct.HspTopUp, "") != null
                && x.First(y => y.Name == hspProduct.Name).HspClientPrice.Should().Be(hspProduct.HspClientPrice, "") != null),
                CountryEnum.Australia, It.IsAny<string?>()), Times.AtMostOnce());
        }

        [Theory]
        [InlineAutoData(false)]
        [InlineAutoData(true)]
        public async Task Handle_Should_ExportServices_When_QueryProvided(
            bool activeOnly, string searchText, byte[] excelData)
        {
            // Arrange
            var stream = new MemoryStream(excelData);
            var query = new ProductsExportQuery()
            {
                ActiveOnly = activeOnly,
                SearchText = searchText,
                CategoryCode = ProductCategoryCode.Service,
            };

            var productCategory = new ProductCategory()
            {
                Code = ProductCategoryCode.Service,
                Name = "test",
                IsDeleted = false,
            };

            var products = _fixture.Build<AUProduct>()
                .With(x => x.IsDeleted, false)
                .With(x => x.Category, productCategory)
                .CreateMany(6).ToArray();
            products[0].IsHSP = true;
            products[0].IsActive = true;
            products[0].HspMaintenance = false;
            var searchedProducts = products.Take(3).ToArray();
            foreach (var product in searchedProducts)
            {
                product.Name += searchText;
            }

            DbContext.ProductCategories.Add(productCategory);
            DbContext.Products.AddRange(products);
            await DbContext.SaveChangesAsync();
            _excelExporterMock.Setup(x => x.ToXlsxStream(It.IsAny<IReadOnlyList<ServiceExcelModel>>(), CountryEnum.Australia, It.IsAny<string?>()))
                .Returns(stream);
            _operationContextMock.Setup(x => x.CountryId).Returns(CountryEnum.Australia);

            // Act
            var result = await _sut.Handle(query, CancellationToken.None);

            // Assert
            var resultBuffer = ((MemoryStream)result.ExcelFileStream).ToArray();
            resultBuffer.Should().BeEquivalentTo(stream.ToArray());
            var correctProducts = searchedProducts.Where(x => !activeOnly || x.IsActive);
            var hspProduct = searchedProducts.First(x => x.Name == products[0].Name);
            _excelExporterMock.Verify(x => x.ToXlsxStream(It.Is<IReadOnlyList<ServiceExcelModel>>(
                x => x.Should().BeEquivalentTo(correctProducts, opt => opt.Excluding(x => x.Colors)
                .Excluding(x => x.Supplier)
                .Excluding(x => x.Manufacturer)
                .Excluding(x => x.ProductPathways)
                .Excluding(x => x.BatteryTypes)
                .Excluding(x => x.HspMaintenance)
                .Excluding(x => x.HspServiceType)
                .Excluding(x => x.HspServiceNumber)
                .Excluding(x => x.Description)
                .Excluding(x => x.SuggestedProducts)
                .ExcludingMissingMembers(), "") != null
                && x.Select(y => y.Description).Should().BeEquivalentTo(correctProducts.Select(y => y.DescriptionValue), "") != null
                && x.First(y => y.Name == hspProduct.Name).HspMaintenance.Should().Be(hspProduct.HspMaintenance, "") != null
                && x.First(y => y.Name == hspProduct.Name).HspServiceType.Should().BeEquivalentTo(hspProduct.HspServiceType.ToString(), "") != null
                && x.First(y => y.Name == hspProduct.Name).HspServiceNumber.Should().BeEquivalentTo(hspProduct.HspServiceNumber, "") != null),
                CountryEnum.Australia, It.IsAny<string?>()), Times.AtMostOnce());
        }

        [Theory]
        [InlineAutoData(false)]
        [InlineAutoData(true)]
        public async Task Handle_Should_ExportNull_When_ProductCategoryOther(
            bool activeOnly, string searchText)
        {
            // Arrange
            MemoryStream stream = null!;
            var query = new ProductsExportQuery()
            {
                ActiveOnly = activeOnly,
                SearchText = searchText,
                CategoryCode = ProductCategoryCode.Other,
            };
            var productCategory = _fixture.Build<ProductCategory>().With(
                x => x.Code, ProductCategoryCode.Other).With(x=> x.IsDeleted, false).Create();
            
            var products = _fixture.Build<Product>()
                .With(x => x.IsDeleted, false)
                .With(x => x.Category, productCategory)
                .CreateMany(6);
            var searchedProducts = products.Take(3).ToArray();
            foreach (var product in searchedProducts)
            {
                product.Name += searchText;
            }

            DbContext.ProductCategories.Add(productCategory);
            DbContext.Products.AddRange(products);
            await DbContext.SaveChangesAsync();
            _excelExporterMock.Setup(x => x.ToXlsxStream(It.IsAny<IReadOnlyList<AccessoryExcelModel>>(), CountryEnum.UnitedKingdom, It.IsAny<string?>()))
                .Returns(stream!);
            _operationContextMock.Setup(x => x.CountryId).Returns(CountryEnum.UnitedKingdom);

            // Act
            var result = await _sut.Handle(query, CancellationToken.None);

            // Assert
            var resultBuffer = ((MemoryStream)result.ExcelFileStream);
            resultBuffer.Should().BeNull();
        }
    }
}
