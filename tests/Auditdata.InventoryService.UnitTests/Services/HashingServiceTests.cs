using Auditdata.InventoryService.Core.Services;
using FluentAssertions;
using System.Security.Cryptography;
using System.Text;

namespace Auditdata.InventoryService.UnitTests.Services;

public class HashingServiceTests
{
    private readonly HashingService _sut;
    
    public HashingServiceTests()
    {
        _sut = new HashingService();
    }

    [Fact]
    public void ToMD5_ShouldReturn_UTF8_Hash()
    {
        const string testKey = "ad_test_key";
        
        using var md5 = MD5.Create();
        var inputBytes = Encoding.UTF8.GetBytes(testKey);
        var hashBytes = md5.ComputeHash(inputBytes);

        var expectedHash = Convert.ToHexString(hashBytes);
        var actualHash = _sut.ToMd5(testKey);

        actualHash.Should().Be(expectedHash);
    }
}
