using Auditdata.InventoryService.Core.Abstractions.Azure;
using Auditdata.InventoryService.Core.Features.Attributes.Commands;
using Auditdata.InventoryService.Core.Features.Attributes.Handlers;
using Auditdata.InventoryService.Core.Features.Attributes.Mappings;
using Auditdata.InventoryService.Core.Features.Attributes.Validators;
using Auditdata.InventoryService.UnitTests.Common;
using FluentAssertions;
using ValidationException = Auditdata.InventoryService.Core.Exceptions.ValidationException;

namespace Auditdata.InventoryService.UnitTests.Attributes;

public class CreateAttributeHandlerTests : BaseSqlLiteDbContextTest
{
    private readonly CreateAttributeHandler _sut;
    private readonly Mock<IAttributesValidator> _validator;
    private readonly Mock<IAzureServiceBusPublisher> _publisherMock = new();
    private readonly Fixture _fixture;
    
    public CreateAttributeHandlerTests()
    {
        _validator = new Mock<IAttributesValidator>();
        var mapper = new MapperConfiguration(c => c.AddProfile<AttributeMappings>()).CreateMapper();
        _sut = new CreateAttributeHandler(DbContext, _validator.Object, _publisherMock.Object, mapper);
        _fixture = new RecursiveFixture();
    }
    
    [Fact]
    public async Task Handle_ShouldThrow_IfCommand_Invalid()
    {
        //Arrange
        var command = new CreateAttributeCommand("test", new List<Guid>());
        _validator.Setup(x => x.ValidateUniqueNameAsync("test", It.IsAny<Guid>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new ValidationException("Some test error"));
        
        //Act/Assert
        var action = () => _sut.Handle(command, CancellationToken.None);
        await action.Should().ThrowAsync<Exception>();
    }

    [Fact]
    public async Task Handle_ShouldCreate_NewAttribute_WithCategories()
    {
        //Arrange
        var productCategories = _fixture
            .Build<ProductCategory>()
            .Without(x => x.Attributes)
            .With(x => x.IsDeleted, false)
            .CreateMany().ToList();
        DbContext.ProductCategories.AddRange(productCategories);
        await DbContext.SaveChangesAsync();
        DbContext.ChangeTracker.Clear();
        
        //Act
        var command = _fixture.Build<CreateAttributeCommand>()
            .With(x => x.ProductCategories, productCategories.Select(x => x.Id))
            .Create();
        var result = await _sut.Handle(command, CancellationToken.None);
        
        //Assert
        result.Should().NotBeEmpty();
        var attribute = await DbContext.Attributes.FindAsync(result);
        attribute.Should().NotBeNull();
    }
}
