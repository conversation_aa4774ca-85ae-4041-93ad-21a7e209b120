using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Features.Attributes.Handlers;
using Auditdata.InventoryService.Core.Features.Attributes.Queries;
using Auditdata.InventoryService.UnitTests.Common;
using FluentAssertions;
using Attribute = Auditdata.InventoryService.Core.Entities.Attribute;

namespace Auditdata.InventoryService.UnitTests.Attributes;

public class GetAttributesHandlerTests : IClassFixture<BaseDbContextTest>
{
    private readonly IDbContext _dbContext;
    private readonly GetAttributesHandler _sut;

    public GetAttributesHandlerTests(BaseDbContextTest baseDbContextTest)
    {
        _dbContext = baseDbContextTest.DbContext;
        _sut = new GetAttributesHandler(_dbContext);
    }

    [Theory]
    [RecursiveInlineAutoData]
    public async Task Handle_ShouldReturn_AttributesResult_When_QueryProvided(
        GetAttributesQuery query, IEnumerable<Attribute> attributes)
    {
        // Arrange
        _dbContext.Attributes.AddRange(attributes);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _sut.Handle(query, CancellationToken.None);

        // Assert
        result.Attributes.Should().BeEquivalentTo(attributes.Where(a => !a.IsDeleted));
    }
}
