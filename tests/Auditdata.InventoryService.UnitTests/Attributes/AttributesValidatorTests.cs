using Auditdata.InventoryService.Core.Entities.Products;
using Auditdata.InventoryService.Core.Features.Attributes.Validators;
using Auditdata.InventoryService.UnitTests.Common;
using Auditdata.Transport.Contracts.Exceptions;
using FluentAssertions;
using Attribute = Auditdata.InventoryService.Core.Entities.Attribute;
using ValidationException = Auditdata.InventoryService.Core.Exceptions.ValidationException;

namespace Auditdata.InventoryService.UnitTests.Attributes;

public class AttributesValidatorTests : BaseSqlLiteDbContextTest
{
    private readonly AttributesValidator _validator;
    private readonly Fixture _fixture = new RecursiveFixture();
    
    public AttributesValidatorTests()
    {
        _validator = new AttributesValidator(DbContext);
    }

    [Fact]
    public async Task ValidateUniqueNameAsync_ShouldThrow_IfDuplicateName_Exists()
    {
        //Arrange
        const string name = "test";
        DbContext.Attributes.Add(new Attribute
        {
            Name = name
        });
        await DbContext.SaveChangesAsync();
        
        //Act|Assert
        var action = () => _validator.ValidateUniqueNameAsync(name, Guid.Empty, CancellationToken.None);
        await action.Should().ThrowAsync<ValidationException>();
    }

    [Fact]
    public void ValidateUniqueValue_ShouldThrow_IfDuplicateValue_Exists()
    {
        //Arrange
        var values = new List<AttributeValue>
        {
            new()
            {
                Value = "test"
            },
            new()
            {
                Value = "test"
            }
        };
        
        //Act|Assert
        var action = () => _validator.ValidateUniqueValue(values);
        action.Should().Throw<ValidationException>();
    }

    [Fact]
    public async Task ValidateCanDeleteAsync_ShouldThrow_IfProductAttribute_Exists()
    {
        //Arrange
        var attribute = new Attribute
        {
            Id = Guid.NewGuid(),
            Name = "test"
        };
        var productAttribute = _fixture
            .Build<ProductAttribute>()
            .With(x => x.IsDeleted, false)
            .With(x => x.Attribute, attribute)
            .Create();
        DbContext.ProductAttributes.Add(productAttribute);
        await DbContext.SaveChangesAsync();

        //Act|Assert
        var action = () => _validator.ValidateCanDeleteAsync(attribute.Id, CancellationToken.None);
        await action.Should().ThrowAsync<ValidationException>();
    }
    
    [Fact]
    public async Task ValidateRemovingCategories_ShouldThrow_WhenProductUsesAttributeInCategory()
    {
        // Arrange
        var categoryId = Guid.NewGuid();
        var attributeId = Guid.NewGuid();

        var category = new ProductCategory { Id = categoryId, Name = "test" };
        var attribute = new Attribute
        {
            Id = attributeId,
            Name = "Attribute1"
        };

        var product = new Product
        {
            Id = Guid.NewGuid(),
            Name = "test",
            Category = category,
            Attributes = new List<ProductAttribute>
            {
                new ProductAttribute
                {
                    AttributeId = attributeId,
                    Attribute = attribute
                }
            }
        };

        DbContext.ProductCategories.Add(category);
        DbContext.Attributes.Add(attribute);
        DbContext.Products.Add(product);
        await DbContext.SaveChangesAsync();

        // Act
        var action = () => _validator.ValidateRemovingCategories([category], attributeId, CancellationToken.None);

        // Assert
        await action.Should().ThrowAsync<BusinessException>();
    }

    [Fact]
    public async Task ValidateRemovingCategories_ShouldNotThrow_WhenNoProductsUseAttributeInCategories()
    {
        // Arrange
        var categoryId = Guid.NewGuid();
        var attributeId = Guid.NewGuid();

        var category = new ProductCategory { Id = categoryId, Name = "test" };
        var attribute = new Attribute
        {
            Id = attributeId,
            Name = "Attribute2"
        };

        var product = new Product
        {
            Id = Guid.NewGuid(),
            Name = "test",
            Category = category,
            Attributes = []
        };

        DbContext.ProductCategories.Add(category);
        DbContext.Attributes.Add(attribute);
        DbContext.Products.Add(product);
        await DbContext.SaveChangesAsync();

        // Act
        var action = () => _validator.ValidateRemovingCategories([category], attributeId, CancellationToken.None);

        // Assert
        await action.Should().NotThrowAsync();
    }
}
