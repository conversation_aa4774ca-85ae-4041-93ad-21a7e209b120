using Auditdata.InventoryService.Core.Exceptions;
using Auditdata.InventoryService.Core.Features.Attributes.Handlers;
using Auditdata.InventoryService.Core.Features.Attributes.Queries;
using Auditdata.InventoryService.UnitTests.Common;
using FluentAssertions;
using Attribute = Auditdata.InventoryService.Core.Entities.Attribute;

namespace Auditdata.InventoryService.UnitTests.Attributes;

public class GetAttributeByIdHandlerTests : BaseSqlLiteDbContextTest
{
    private readonly GetAttributeByIdHandler _sut;
    private readonly Fixture _fixture = new RecursiveFixture();
    
    public GetAttributeByIdHandlerTests()
    {
        _sut = new GetAttributeByIdHandler(DbContext);
    }

    [Fact]
    public async Task Handle_ShouldThrow_EntityNotFoundException_IfEntity_NotFound()
    {
        //Arrange
        var recordId = Guid.NewGuid();
        var query = new GetAttributeByIdQuery(recordId);

        //Act|Assert
        var action = () => _sut.Handle(query, CancellationToken.None);
        await action.Should().ThrowAsync<EntityNotFoundException<Attribute>>();
    }

    [Fact]
    public async Task Handle_ShouldReturn_Attribute_FromDatabase()
    {
        //Arrange
        var attribute = _fixture.Build<Attribute>().With(x => x.IsDeleted, false).Create();
        
        var query = new GetAttributeByIdQuery(attribute.Id);
        DbContext.Attributes.Add(attribute);
        await DbContext.SaveChangesAsync();

        //Act
        var result = await _sut.Handle(query, CancellationToken.None);
        
        //Assert
        result.Should().BeSameAs(attribute);
    }
}
