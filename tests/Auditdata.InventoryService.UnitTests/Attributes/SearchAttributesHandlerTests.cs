using Auditdata.InventoryService.Core.Features.Attributes.Handlers;
using Auditdata.InventoryService.Core.Features.Attributes.Queries;
using Auditdata.InventoryService.UnitTests.Common;
using Auditdata.Transport.Contracts.Table;
using FluentAssertions;
using Attribute = Auditdata.InventoryService.Core.Entities.Attribute;

namespace Auditdata.InventoryService.UnitTests.Attributes;

public class SearchAttributesHandlerTests : BaseSqlLiteDbContextTest
{
    private readonly SearchAttributesHandler _sut;
    private readonly RecursiveFixture _fixture;
    
    public SearchAttributesHandlerTests()
    {
        _sut = new SearchAttributesHandler(DbContext);
        _fixture = new RecursiveFixture();
    }

    [Fact]
    public async Task Handle_ShouldReturn_FoundAttributes()
    {
        //Arrange
        var attributes = _fixture.Build<Attribute>()
            .With(x => x.IsDeleted, false)
            .CreateMany().ToList();
        
        var tableQuery = new TableQueryBase
        {
            OrderBy = "Name",
            Page = 1,
            PerPage = 10
        };
        var query = new SearchAttributesQuery(tableQuery, null, null);
        DbContext.Attributes.AddRange(attributes);
        await DbContext.SaveChangesAsync();

        //Act
        var result = await _sut.Handle(query, CancellationToken.None);

        //Assert
        result.Rows.Should().HaveCount(attributes.Count);
        result.Pagination!.OrderBy.Should().Be(tableQuery.OrderBy);
        result.Pagination.PerPage.Should().Be(tableQuery.PerPage);
        result.Pagination.Total.Should().Be(attributes.Count);
    }
}
