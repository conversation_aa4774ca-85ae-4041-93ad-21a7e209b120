using Auditdata.Infrastructure.Contracts.CountryLayers;
using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Entities.Products;
using Auditdata.InventoryService.Core.Features.Repairs.Handlers;
using Auditdata.InventoryService.Core.Features.Repairs.Models;
using Auditdata.InventoryService.Core.Features.Repairs.Queries;
using Auditdata.InventoryService.UnitTests.Common;
using FluentAssertions;

namespace Auditdata.InventoryService.UnitTests.RepairOrders;

public class GetRepairAvailableStockProductItemsHandlerTests : IClassFixture<BaseDbContextTest>
{
    private readonly GetRepairAvailableStockProductItemsHandler _handler;
    private readonly IDbContext _dbContext;

    public GetRepairAvailableStockProductItemsHandlerTests(BaseDbContextTest baseDbContextTest)
    {
        _dbContext = baseDbContextTest.DbContext;

        _handler = new GetRepairAvailableStockProductItemsHandler(_dbContext);
    }

    [Fact]
    public async Task Handle_ShouldReturnAvailableRepairProductItems_WhenItemsExist()
    {
        // Arrange
        var locationId = Guid.NewGuid();

        var stock = Stock.New("name", locationId, null, null);
        var product = Product.Create(CountryEnum.UnitedKingdom);
        product.Name = "producttest";

        var stockProduct = StockProduct.Create(stock, product);

        await _dbContext.Stocks.AddAsync(stock);
        await _dbContext.Products.AddAsync(product);
        await _dbContext.StockProducts.AddAsync(stockProduct);
        await _dbContext.SaveChangesAsync();

        var stockProductItems = new List<StockProductItem>()
        {
            new()
            {
                Id = Guid.NewGuid(),
                Status = StockProductItemStatus.Available,
                IsDeleted = false,
                SerialNumber = "SN12345",
                StockProduct = stockProduct,
                StockProductId = stockProduct.Id
            },
            new()
            {
                Id = Guid.NewGuid(),
                Status = StockProductItemStatus.Available,
                IsDeleted = false,
                SerialNumber = "SN12346",
                StockProduct = stockProduct,
                StockProductId = stockProduct.Id
            }
        };
                
        await _dbContext.StockProductItems.AddRangeAsync(stockProductItems);
        await _dbContext.SaveChangesAsync();

        var request = new GetRepairAvailableStockProductItemsQuery
        {
            ProductId = product.Id,
            LocationId = locationId,
            PerPage = int.MaxValue
        };

        // Act
        var result = await _handler.Handle(request, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Rows.Should().HaveCount(2);
        result.Rows!.Select(x => x.Model).Should().AllBeOfType<AvailableRepairProductItem>();
        result.Rows!.Select(x => x.Model)
            .Should().OnlyContain(item => item!.SerialNumber == "SN12345" || item.SerialNumber == "SN12346");
    }

    [Fact]
    public async Task Handle_ShouldReturnEmptyList_WhenNoAvailableItems()
    {
        // Arrange
        var locationId = Guid.NewGuid();

        var stock = Stock.New("name", locationId, null, null);
        var product = Product.Create(CountryEnum.UnitedKingdom);
        product.Name = "producttest";

        var stockProduct = StockProduct.Create(stock, product);

        await _dbContext.Stocks.AddAsync(stock);
        await _dbContext.Products.AddAsync(product);
        await _dbContext.StockProducts.AddAsync(stockProduct);
        await _dbContext.SaveChangesAsync();

        var stockProductItems = new StockProductItem()
        {
            Id = Guid.NewGuid(),
            Status = StockProductItemStatus.UnderRepair,
            IsDeleted = false,
            SerialNumber = "SN12345",
            StockProduct = stockProduct,
            StockProductId = stockProduct.Id
        };

        await _dbContext.StockProductItems.AddRangeAsync(stockProductItems);
        await _dbContext.SaveChangesAsync();

        var request = new GetRepairAvailableStockProductItemsQuery
        {
            ProductId = product.Id,
            LocationId = locationId,
            PerPage = int.MaxValue
        };

        // Act
        var result = await _handler.Handle(request, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Rows.Should().BeEmpty();
    }

    [Fact]
    public async Task Handle_ShouldReturnAvailableRepairProductItems_WhenItemsExistWithSearch()
    {
        // Arrange
        var locationId = Guid.NewGuid();

        var stock = Stock.New("name", locationId, null, null);
        var product = Product.Create(CountryEnum.UnitedKingdom);
        product.Name = "producttest";

        var stockProduct = StockProduct.Create(stock, product);

        await _dbContext.Stocks.AddAsync(stock);
        await _dbContext.Products.AddAsync(product);
        await _dbContext.StockProducts.AddAsync(stockProduct);
        await _dbContext.SaveChangesAsync();

        var stockProductItems = new List<StockProductItem>()
        {
            new()
            {
                Id = Guid.NewGuid(),
                Status = StockProductItemStatus.Available,
                IsDeleted = false,
                SerialNumber = "SN12345Test",
                StockProduct = stockProduct,
                StockProductId = stockProduct.Id
            },
            new()
            {
                Id = Guid.NewGuid(),
                Status = StockProductItemStatus.Available,
                IsDeleted = false,
                SerialNumber = "SN12346Test",
                StockProduct = stockProduct,
                StockProductId = stockProduct.Id
            },
            new()
            {
                Id = Guid.NewGuid(),
                Status = StockProductItemStatus.Available,
                IsDeleted = false,
                SerialNumber = "SN12347",
                StockProduct = stockProduct,
                StockProductId = stockProduct.Id
            }
        };

        await _dbContext.StockProductItems.AddRangeAsync(stockProductItems);
        await _dbContext.SaveChangesAsync();

        var request = new GetRepairAvailableStockProductItemsQuery
        {
            RepairOrderStockProductItemId = stockProductItems[0].Id,
            ProductId = product.Id,
            LocationId = locationId,
            PerPage = int.MaxValue,
            SearchText = "Test",
        };

        // Act
        var result = await _handler.Handle(request, CancellationToken.None);

        // Assert
        result.Rows.Should().HaveCount(1);
    }
}
