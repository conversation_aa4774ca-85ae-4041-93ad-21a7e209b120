using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Abstractions.Validators;
using Auditdata.InventoryService.Core.Entities.Products;
using Auditdata.InventoryService.Core.Exceptions;
using Auditdata.InventoryService.Core.Features.Repairs.Commands;
using Auditdata.InventoryService.Core.Features.Repairs.Handlers;
using Auditdata.InventoryService.UnitTests.Common;
using Auditdata.Microservice.Messages.OriginEntities.RepairOrders;
using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Auditdata.InventoryService.UnitTests.RepairOrders;

public class MarkStockProductItemUnderRepairHandlerTests : IClassFixture<BaseDbContextTest>
{
    private readonly IFixture _fixture;
    private readonly Mock<ISerialNumbersValidator> _serialNumbersValidator;
    private readonly MarkStockProductItemUnderRepairHandler _handler;
    private readonly IDbContext _dbContext;

    public MarkStockProductItemUnderRepairHandlerTests(BaseDbContextTest baseDbContextTest)
    {
        _fixture = new RecursiveFixture();
        _serialNumbersValidator = new Mock<ISerialNumbersValidator>();
        var logger = new Mock<ILogger<MarkStockProductItemUnderRepairHandler>>();

        _dbContext = baseDbContextTest.DbContext;

        _handler = new MarkStockProductItemUnderRepairHandler(
            logger.Object,
            _dbContext,
            new Mock<IEventPublisher>().Object,
            _serialNumbersValidator.Object);
    }

    [Fact]
    public async Task Consume_ShouldRespond_MarkedStockProductItemUnderRepair()
    {
        // Arrange
        var productId = Guid.NewGuid();
        var stockProductId = Guid.NewGuid();
        var serialNumber = "SN78901";
        var repairOrderId = Guid.NewGuid();

        var command = _fixture.Build<MarkStockProductItemUnderRepairCommand>()
            .With(x => x.ProductId, productId)
            .With(x => x.SerialNumber, serialNumber)
            .With(x => x.RepairOrderId, repairOrderId)
            .With(x => x.Status, StockProductItemStatus.UnderRepair)
            .Create();

        var stockProductItem = _fixture.Build<StockProductItem>()
            .With(x => x.Id, Guid.NewGuid())
            .With(x => x.StockProduct, new StockProduct
            {
                Id = stockProductId,
                Product = new Product
                {
                    Id = productId,
                    Name = "productName",
                    ManufacturerId = Guid.NewGuid()
                },
                Stock = new Stock
                {
                    Id = Guid.NewGuid(),
                    Name = "stockName"
                }
            })
            .With(x => x.SerialNumber, serialNumber)
            .With(x => x.Status, StockProductItemStatus.Available)
            .With(x => x.IsDeleted, false)
            .Create();

        _dbContext.StockProductItems.Add(stockProductItem);
        await _dbContext.SaveChangesAsync();

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        var updatedStockProductItem = await _dbContext.StockProductItems.FirstOrDefaultAsync(x => x.SerialNumber == serialNumber);
        updatedStockProductItem.Should().NotBeNull();
        updatedStockProductItem!.Status.Should().Be(command.Status);
        updatedStockProductItem.RepairOrderId.Should().Be(repairOrderId);

        if (stockProductItem.Status == StockProductItemStatus.Available)
        {
            var stockProduct = await _dbContext.StockProducts.FirstOrDefaultAsync(x => x.Id == stockProductItem.StockProduct.Id);
            stockProduct.Should().NotBeNull();
            stockProduct!.Quantity.Should().Be(-1);
        }
    }

    [Fact]
    public async Task Consume_InStoreRepair_ShouldRespond_MarkedStockProductItemUnderRepair()
    {
        // Arrange
        var productId = Guid.NewGuid();
        var stockProductId = Guid.NewGuid();
        var locationId = Guid.NewGuid();
        var repairOrderId = Guid.NewGuid();
        var serialNumber = "SN12345";

        var command = _fixture.Build<MarkStockProductItemUnderRepairCommand>()
            .With(x => x.ProductId, productId)
            .With(x => x.SerialNumber, serialNumber)
            .With(x => x.RepairOrderId, repairOrderId)
            .With(x => x.Status, StockProductItemStatus.UnderRepair)
            .With(x => x.RepairMethod, RepairMethod.InStore)
            .With(x => x.LocationId, locationId)
            .Create();

        var stockProductItem = _fixture.Build<StockProductItem>()
            .With(x => x.Id, Guid.NewGuid())
            .With(x => x.StockProduct, new StockProduct
            {
                Id = stockProductId,
                Product = new Product
                {
                    Id = productId,
                    Name = "name",
                    ManufacturerId = Guid.NewGuid()
                },
                Stock = new Stock 
                { 
                    LocationId = locationId,
                    Name = "name"
                }
            })
            .With(x => x.SerialNumber, serialNumber)
            .With(x => x.Status, StockProductItemStatus.Available)
            .With(x => x.IsDeleted, false)
            .Create();

        _dbContext.StockProductItems.Add(stockProductItem);
        await _dbContext.SaveChangesAsync();

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        var updatedStockProductItem = await _dbContext.StockProductItems.FirstOrDefaultAsync(x => x.SerialNumber == serialNumber);
        updatedStockProductItem.Should().NotBeNull();
        updatedStockProductItem!.Status.Should().Be(command.Status);
        updatedStockProductItem.RepairInLocationId.Should().Be(command.LocationId);
        updatedStockProductItem.RepairOrderId.Should().Be(command.RepairOrderId);

        var stockLog = await _dbContext.StockProductItemLogs
            .FirstOrDefaultAsync(x => x.StockProductItemId == updatedStockProductItem.Id);
        stockLog.Should().NotBeNull();
    }

    [Fact]
    public async Task Consume_SendToSupplierRepair_ShouldRespond_MarkedStockProductItemUnderRepair()
    {
        // Arrange
        var productId = Guid.NewGuid();
        var stockProductId = Guid.NewGuid();
        var serialNumber = "SN67890";
        var repairOrderId = Guid.NewGuid();
        var productName = "Test Product";
        var stockName = "Test Stock";

        var command = _fixture.Build<MarkStockProductItemUnderRepairCommand>()
            .With(x => x.ProductId, productId)
            .With(x => x.SerialNumber, serialNumber)
            .With(x => x.RepairOrderId, repairOrderId)
            .With(x => x.Status, StockProductItemStatus.UnderRepair)
            .With(x => x.RepairMethod, RepairMethod.SendToSupplier)
            .Create();

        var stockProductItem = _fixture.Build<StockProductItem>()
            .With(x => x.Id, Guid.NewGuid())
            .With(x => x.StockProduct, new StockProduct
            {
                Id = stockProductId,
                Product = new Product
                {
                    Id = productId,
                    Name = productName,
                    ManufacturerId = Guid.NewGuid()
                },
                Stock = new Stock
                {
                    Id = Guid.NewGuid(),
                    LocationId = Guid.NewGuid(),
                    Name = stockName
                }
            })
            .With(x => x.SerialNumber, serialNumber)
            .With(x => x.Status, StockProductItemStatus.Available)
            .With(x => x.IsDeleted, false)
            .Create();

        _dbContext.StockProductItems.Add(stockProductItem);
        await _dbContext.SaveChangesAsync();

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        var updatedStockProductItem = await _dbContext.StockProductItems.FirstOrDefaultAsync(x => x.SerialNumber == serialNumber);
        updatedStockProductItem.Should().NotBeNull();
        updatedStockProductItem!.Status.Should().Be(command.Status);
        updatedStockProductItem.RepairInLocationId.Should().BeNull();
        updatedStockProductItem.RepairOrderId.Should().Be(repairOrderId);

        var stockLog = await _dbContext.StockProductItemLogs
            .FirstOrDefaultAsync(x => x.StockProductItemId == updatedStockProductItem.Id);
        stockLog.Should().NotBeNull();
    }

    [Fact]
    public async Task Consume_ShouldThrow_EntityNotFoundException()
    {
        var stockProductId = Guid.NewGuid();

        var command = _fixture.Build<MarkStockProductItemUnderRepairCommand>()
            //.With(x => x.StockProductItemId, stockProductId)
            .With(x => x.Status, StockProductItemStatus.UnderRepair)
            .Create();

        await Assert.ThrowsAsync<EntityNotFoundException<StockProductItem>>(() => _handler.Handle(command, CancellationToken.None));
    }

    [Fact]
    public async Task Consume_ShouldThrow_DuplicatedSerialNumbersException()
    {
        // Arrange
        var productId = Guid.NewGuid();
        var stockProductId = Guid.NewGuid();
        var serialNumber = "12345";
        var message = $"The serial number {serialNumber} is not unique";

        var command = _fixture.Build<MarkStockProductItemUnderRepairCommand>()
            .With(x => x.ProductId, productId)
            .With(x => x.SerialNumber, serialNumber)
            .With(x => x.NewSerialNumber, serialNumber) 
            .With(x => x.Status, StockProductItemStatus.UnderRepair)
            .Create();

        var stockProductItem = _fixture.Build<StockProductItem>()
            .With(x => x.Id, Guid.NewGuid())
            .With(x => x.StockProduct, new StockProduct
            {
                Id = stockProductId,
                Product = new Product
                {
                    Id = productId,
                    Name = "productName",
                    ManufacturerId = Guid.NewGuid()
                },
                Stock = new Stock
                {
                    Id = Guid.NewGuid(),
                    Name = "stockName",
                    LocationId = Guid.NewGuid()
                }
            })
            .With(x => x.SerialNumber, serialNumber)
            .With(x => x.Status, StockProductItemStatus.Available)
            .With(x => x.IsDeleted, false)
            .Create();

        _dbContext.StockProductItems.Add(stockProductItem);
        await _dbContext.SaveChangesAsync();

        _serialNumbersValidator
            .Setup(x => x.CheckDuplicatedSerialNumbers(It.IsAny<Guid>(), It.Is<IEnumerable<string>>(sn => sn.Contains(serialNumber)), CancellationToken.None))
            .ThrowsAsync(new DuplicatedSerialNumbersException(message));

        // Act & Assert
        var exception = await Assert.ThrowsAsync<DuplicatedSerialNumbersException>(() => _handler.Handle(command, CancellationToken.None));
    }
}