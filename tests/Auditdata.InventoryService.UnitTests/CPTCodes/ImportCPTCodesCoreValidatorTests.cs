using Auditdata.Infrastructure.Excel.Exceptions;
using Auditdata.Infrastructure.Excel.Models;
using Auditdata.InventoryService.Core.Features.CPTCodes.Models;
using Auditdata.InventoryService.Core.Features.CPTCodes.Validators;
using Auditdata.InventoryService.UnitTests.Common;
using Auditdata.Transport.Contracts.Exceptions;
using AutoFixture.Xunit2;
using FluentAssertions;

namespace Auditdata.InventoryService.UnitTests.CPTCodes;

public class ImportCPTCodesCoreValidatorTests : BaseSqlLiteDbContextTest
{
    private readonly ImportCPTCodesCoreValidator _sut;

    private readonly Fixture _fixture;

    public ImportCPTCodesCoreValidatorTests()
    {
        _sut = new ImportCPTCodesCoreValidator(DbContext);
        _fixture = new Fixture();
    }

    [Theory]
    [InlineAutoData]
    public void ValidateImportResult_Should_ThrowWithFirstExMessage_When_ImportResultNotSuccessful(
        string firstExceptionMessage)
    {
        // Arrange
        var importResult = new ExcelImportResult<CPTCodeExcelModel>
        {
            IsSuccess = false,
            Exceptions = new ExcelFormatException[]
            {
                new WorksheetCountException(firstExceptionMessage),
                new InvalidRecordsException("second exception"),
            }
        };

        // Act
        // Assert
        var ex = Assert.Throws<BusinessException>(() => _sut.ValidateImportResult(importResult));

        ex.Message.Should().Be(firstExceptionMessage);
    }

    [Fact]
    public void ValidateImportResult_Should_NotThrow_When_ImportResultSuccessful()
    {
        // Arrange
        var importResult = new ExcelImportResult<CPTCodeExcelModel>
        {
            IsSuccess = true
        };

        // Act
        var ex = Record.Exception(() => _sut.ValidateImportResult(importResult));

        // Assert
        ex.Should().BeNull();
    }

    [Fact]
    public void ValidateIsAllIdsFound_Should_Throw_When_SomeCPTCodesWithSomeIdsAreMissing()
    {
        // Arrange
        var existingCptCodes = _fixture.Build<CPTCode>().With(x => x.IsDeleted, false).CreateMany(6).ToArray();
        var codesWithFilledIds = _fixture.Build<ExcelImportData<CPTCodeExcelModel>>().CreateMany(5).ToArray();
        for (var i = 0; i < codesWithFilledIds.Length; i++)
        {
            codesWithFilledIds[i].Data.Id = existingCptCodes[i].Id;
        }

        // Act
        // Assert
        var ex = Assert.Throws<BusinessException>(
            () => _sut.ValidateIsAllIdsFound(existingCptCodes, codesWithFilledIds));
    }

    [Fact]
    public void ValidateIsAllIdsFound_Should_NotThrow_When_CPTCodesWithAllIdsArePresent()
    {
        // Arrange
        var existingCptCodes = _fixture.Build<CPTCode>().With(x => x.IsDeleted, false).CreateMany(6).ToArray();
        var codesWithFilledIds = _fixture.Build<ExcelImportData<CPTCodeExcelModel>>().CreateMany(6).ToArray();
        for (var i = 0; i < codesWithFilledIds.Length; i++)
        {
            codesWithFilledIds[i].Data.Id = existingCptCodes[i].Id;
        }

        // Act
        var ex = Record.Exception(() => _sut.ValidateIsAllIdsFound(existingCptCodes, codesWithFilledIds));

        // Assert
        ex.Should().BeNull();
    }

    [Fact]
    public async Task ValidateIfCodesAlreadyExistsAsync_Should_Throw_When_IfSomeCodesAlreadyExistsInDb()
    {
        // Arrange
        var existingCptCodes = _fixture.Build<CPTCode>().With(x => x.IsDeleted, false).CreateMany(6).ToArray();
        DbContext.CPTCodes.AddRange(existingCptCodes);
        await DbContext.SaveChangesAsync();

        var importData = _fixture.CreateMany<ExcelImportData<CPTCodeExcelModel>>(6).ToArray();
        importData[4].Data.Id = default;
        importData[3].Data.Id = existingCptCodes[2].Id;
        importData[3].Data.Code = existingCptCodes[5].Code;
        var filledIdsSet = importData.Where(x => x.Data.Id != default).Select(x => x.Data.Id).ToHashSet();

        // Act
        // Assert
        var ex = await Assert.ThrowsAsync<BusinessException>(
            async() => await _sut.ValidateIfCodesAlreadyExistsAsync(filledIdsSet, importData, CancellationToken.None));
    }

    [Fact]
    public async Task ValidateIfCodesAlreadyExistsAsync_Should_NotThrow_When_NoCodesAlreadyExistsInDbWhichWillNotBeChanged()
    {
        // Arrange
        var existingCptCodes = _fixture.Build<CPTCode>().With(x => x.IsDeleted, false).CreateMany(6).ToArray();
        DbContext.CPTCodes.AddRange(existingCptCodes);
        await DbContext.SaveChangesAsync();

        var importData = _fixture.CreateMany<ExcelImportData<CPTCodeExcelModel>>(6).ToArray();
        importData[4].Data.Id = default;
        importData[3].Data.Id = existingCptCodes[2].Id;
        importData[3].Data.Code = existingCptCodes[5].Code;
        importData[1].Data.Id = existingCptCodes[5].Id;
        var filledIdsSet = importData.Where(x => x.Data.Id != default).Select(x => x.Data.Id).ToHashSet();

        // Act
        var ex = await Record.ExceptionAsync(
            async () => await _sut.ValidateIfCodesAlreadyExistsAsync(filledIdsSet, importData, CancellationToken.None));

        // Assert
        ex.Should().BeNull();
    }
}
