using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Events.Transfers;
using Auditdata.InventoryService.Core.Exceptions;
using Auditdata.InventoryService.Core.Features.Transfers.Commands;
using Auditdata.InventoryService.Core.Features.Transfers.Handlers;
using Auditdata.InventoryService.UnitTests.Common;
using FluentAssertions;
using MassTransit.Clients;
using MediatR;

namespace Auditdata.InventoryService.UnitTests.Transfers;

public class AcceptTransferHandlerTests : BaseDbContextTest
{
    private readonly IFixture _fixture = new RecursiveFixture();
    private readonly AcceptTransferHandler _sut;
    private readonly Mock<IRequestClient<GetTransferStatus>> _getTransferStatusClient = new();

    public AcceptTransferHandlerTests()
    {
        Mock<IRequestClient<AcceptTransfer>> acceptTransferClient = new();
        acceptTransferClient.Setup(c => c.GetResponse<TransferAccepted>(It.IsAny<AcceptTransfer>(), default, default));

        _sut = new AcceptTransferHandler(
            new Mock<IMediator>().Object,
            acceptTransferClient.Object,
            _getTransferStatusClient.Object,
            DbContext,
            new Mock<IEventPublisher>().Object);
    }

    [Fact]
    public async Task Should_Accept_Transfer_Successfully()
    {
        // Arrange
        var transfer = _fixture.Create<TransfersState>();
        var command = _fixture.Build<AcceptTransferCommand>()
            .With(c => c.TransferId, transfer.TransferId)
            .Create();

        var consumeContext = new Mock<ConsumeContext<TransfersState>>();
        consumeContext.Setup(x => x.Message).Returns(transfer);
        _getTransferStatusClient.Setup(x => x.GetResponse<TransfersState>(
                It.IsAny<GetTransferStatus>(), It.IsAny<CancellationToken>(), It.IsAny<RequestTimeout>()))
            .ReturnsAsync(new MessageResponse<TransfersState>(consumeContext.Object));

        var oldStockProduct = _fixture.Build<StockProduct>()
            .With(x => x.IsDeleted, false)
            .Create();
        oldStockProduct.Product.ControlledByStock = true;

        var newStock = _fixture.Build<Stock>().With(x => x.Id, transfer.ToStockId).Create();
        var newStockProduct = _fixture.Build<StockProduct>()
            .With(x => x.IsDeleted, false)
            .With(x => x.Stock, newStock)
            .With(x => x.Product, oldStockProduct.Product)
            .Create();
        newStockProduct.SetQuantity(5);

        var stockProductItem = _fixture.Build<StockProductItem>()
            .With(sp => sp.StockProduct, oldStockProduct)
            .With(x => x.IsDeleted, false)
            .With(x => x.TransferId, transfer.TransferId)
            .With(x => x.Id, transfer.StockProductItemId)
            .Create();
        DbContext.StockProductItems.Add(stockProductItem);
        DbContext.StockProducts.Add(newStockProduct);

        await DbContext.SaveChangesAsync();

        // Act
        await _sut.Handle(command, CancellationToken.None);

        // Assert
        newStockProduct.Quantity.Should().Be(6);
        stockProductItem.StockProduct.Should().Be(newStockProduct);
        stockProductItem.Status.Should().Be(StockProductItemStatus.Available);
        stockProductItem.TransferId.Should().BeNull();
    }

    [Fact]
    public async Task Should_Throw_EntityNotFound_For_Non_Existing_Transfer()
    {
        var command = _fixture.Create<AcceptTransferCommand>();

        _getTransferStatusClient.Setup(x => x.GetResponse<TransfersState>(
                It.IsAny<GetTransferStatus>(), It.IsAny<CancellationToken>(), It.IsAny<RequestTimeout>()))
            .ReturnsAsync(new MessageResponse<TransfersState>(new Mock<ConsumeContext<TransfersState>>().Object));

        await Assert.ThrowsAsync<EntityNotFoundException<TransfersState>>(() => _sut.Handle(command, CancellationToken.None));
    }

    [Fact]
    public async Task Should_Throw_EntityNotFoundException_for_Non_Existing_StockProductItem()
    {
        var command = _fixture.Create<AcceptTransferCommand>();
        var transfer = _fixture.Create<TransfersState>();

        var consumeContext = new Mock<ConsumeContext<TransfersState>>();
        consumeContext.Setup(x => x.Message).Returns(transfer);
        _getTransferStatusClient.Setup(x => x.GetResponse<TransfersState>(
                It.IsAny<GetTransferStatus>(), It.IsAny<CancellationToken>(), It.IsAny<RequestTimeout>()))
            .ReturnsAsync(new MessageResponse<TransfersState>(consumeContext.Object));

        await Assert.ThrowsAsync<EntityNotFoundException<StockProductItem>>(() => _sut.Handle(command, CancellationToken.None));
    }

    [Fact]
    public async Task Should_NotSendAcceptTransfer_When_SagaInAcceptedState()
    {
        var transfer = _fixture.Build<TransfersState>().With(x => x.CurrentState, "Accepted").Create();
        var consumeContext = new Mock<ConsumeContext<TransfersState>>();
        consumeContext.Setup(x => x.Message).Returns(transfer);
        _getTransferStatusClient.Setup(x => x.GetResponse<TransfersState>(
                It.IsAny<GetTransferStatus>(), It.IsAny<CancellationToken>(), It.IsAny<RequestTimeout>()))
            .ReturnsAsync(new MessageResponse<TransfersState>(consumeContext.Object));
        var command = _fixture.Create<AcceptTransferCommand>();

        await _sut.Handle(command, CancellationToken.None);

        _getTransferStatusClient
            .Verify(x => x.GetResponse<TransferAccepted>(
                It.IsAny<AcceptTransfer>(), It.IsAny<CancellationToken>(), It.IsAny<RequestTimeout>()), Times.Never);
    }
}