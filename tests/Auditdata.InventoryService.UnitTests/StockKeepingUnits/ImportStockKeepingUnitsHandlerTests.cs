using Auditdata.Infrastructure.Contracts.CountryLayers;
using Auditdata.Infrastructure.Excel.Abstractions;
using Auditdata.Infrastructure.Excel.Models;
using Auditdata.InventoryService.Core.Abstractions.Services;
using Auditdata.InventoryService.Core.Entities.Products;
using Auditdata.InventoryService.Core.Features.StockKeepingUnits.Commands;
using Auditdata.InventoryService.Core.Features.StockKeepingUnits.Handlers;
using Auditdata.InventoryService.Core.Features.StockKeepingUnits.Models.Excel;
using Auditdata.InventoryService.Core.Features.StockKeepingUnits.Validation.Interfaces;
using Auditdata.InventoryService.UnitTests.Common;
using AutoFixture.Xunit2;
using FluentAssertions;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using System.IO;

namespace Auditdata.InventoryService.UnitTests.StockKeepingUnits;

public class ImportStockKeepingUnitsHandlerTests : BaseSqlLiteDbContextTest
{
    private readonly IFixture _fixture = new RecursiveFixture();
    private readonly ImportStockKeepingUnitsHandler _sut;
    private readonly Mock<IImportSkusCoreValidator> _validatorMock = new();
    private readonly Mock<IExcelImporter> _excelImporterMock = new();
    private readonly Mock<IDateTimeService> _dateTimeServiceMock = new();

    public ImportStockKeepingUnitsHandlerTests()
    {
        _sut = new ImportStockKeepingUnitsHandler(
            DbContext, _excelImporterMock.Object, _validatorMock.Object, _dateTimeServiceMock.Object);
    }

    [Theory(Skip = "Feature become obsolete")]
    [InlineAutoData]
    public async Task Handle_Should_CreateAndUpdateHearingAidSkus_When_ExcelFileWithCategoryProvided(
        Mock<IFormFile> formFileMock, byte[] excelFileData, DateTimeOffset utcNow)
    {
        // Arrange
        var importData = _fixture.CreateMany<ExcelImportData<SkuHearingAidExcelModel>>(2).ToList();
        var excelFileStream = new MemoryStream(excelFileData);
        formFileMock.Setup(x => x.OpenReadStream()).Returns(excelFileStream);
        formFileMock.Setup(x => x.FileName).Returns("TestFilename");
        _dateTimeServiceMock.Setup(x => x.UtcNow).Returns(utcNow);
        var importResult = new ExcelImportResult<SkuHearingAidExcelModel> { IsSuccess = true, ImportData = importData };

        _excelImporterMock.Setup(x => x.FromXlsxStream<SkuHearingAidExcelModel>(
            excelFileStream, CountryEnum.Undefined, It.IsAny<int?>()))
            .Returns(importResult);

        var command = new ImportStockKeepingUnitsCommand(formFileMock.Object, ProductCategoryCode.HearingAids);
        var category = new ProductCategory() { Name = "TestCategory", Code = ProductCategoryCode.HearingAids };
        var supplier = Supplier.Create("TestSupplier");
        var colors = _fixture.Build<Color>().With(x => x.IsDeleted, false).CreateMany(3).ToList();
        var batteryTypes = _fixture.Build<BatteryType>().With(x => x.IsDeleted, false).CreateMany(3).ToList();

        DbContext.Suppliers.Add(supplier);
        DbContext.Colors.AddRange(colors);
        DbContext.BatteryTypes.AddRange(batteryTypes);
        var products = _fixture.Build<AUProduct>()
            .With(x => x.IsDeleted, false)
            .With(x => x.Category, category)
            .CreateMany(2).ToArray();
        products[0].Supplier = supplier;
        products[0].Colors = new ProductColor[] {
            new() { Product = products[0], Color = colors[0] },
            new() { Product = products[0], Color = colors[1] },
        };
        products[0].BatteryTypes = new ProductBatteryType[] {
            new() { Product = products[0], BatteryType = batteryTypes[0] },
            new() { Product = products[0], BatteryType = batteryTypes[1] },
        };

        products[1].Colors = new ProductColor[] {
            new() { Product = products[1], Color = colors[2] },
        };
        products[1].BatteryTypes = new ProductBatteryType[] {
            new() { Product = products[1], BatteryType = batteryTypes[2] },
        };
        products[1].SupplierId = null;
        products[1].Supplier = null;

        DbContext.Products.AddRange(products);

        var sku = new StockKeepingUnit
        {
            Name = "testSku",
            Product = products[0],
            Supplier = supplier,
            BatteryType = batteryTypes[0],
            Color = colors[0],
        };
        DbContext.StockKeepingUnits.Add(sku);

        await DbContext.SaveChangesAsync();

        var otherCategories = DbContext.ProductCategories.Where(x => x.Id != category.Id && x.Code == ProductCategoryCode.HearingAids);
        DbContext.ProductCategories.RemoveRange(otherCategories);
        await DbContext.SaveChangesAsync();

        importData[0].Data.Id = sku.Id;
        importData[0].Data.ProductName = products[0].Name;
        importData[0].Data.Color = colors[1].Name;
        importData[0].Data.BatteryType = batteryTypes[1].Name;
        importData[0].Data.Supplier = supplier.Name;

        importData[1].Data.Id = Guid.Empty;
        importData[1].Data.ProductName = products[1].Name;
        importData[1].Data.Color = colors[2].Name;
        importData[1].Data.BatteryType = batteryTypes[2].Name;
        importData[1].Data.Supplier = null;

        // Act
        var result = await _sut.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        var newDbContext = GetDbContext();
        var savedSkus = await newDbContext.StockKeepingUnits.ToListAsync();
        savedSkus.Should().HaveCount(2);
        var savedChangedSku = savedSkus.First(x => x.Id == importData[0].Data.Id);
        savedChangedSku.Name.Should().Be(importData[0].Data.Name);
        savedChangedSku.ProductId.Should().Be(products[0].Id);
        savedChangedSku.SupplierId.Should().Be(supplier.Id);
        savedChangedSku.ColorId.Should().Be(colors[1].Id);
        savedChangedSku.BatteryTypeId.Should().Be(batteryTypes[1].Id);
        var savedNewSku = savedSkus.First(x => x.ProductId == products[1].Id);
        savedNewSku.Name.Should().Be(importData[1].Data.Name);
        savedNewSku.ProductId.Should().Be(products[1].Id);
        savedNewSku.SupplierId.Should().BeNull();
        savedNewSku.ColorId.Should().Be(colors[2].Id);
        savedNewSku.BatteryTypeId.Should().Be(batteryTypes[2].Id);
        var importMetadata = await newDbContext.ImportMetadatas.FirstAsync(x => x.CategoryId == category.Id);
        importMetadata.Filename.Should().Be("TestFilename");
        importMetadata.ImportDate.Should().BeSameDateAs(utcNow);
        importMetadata.AddedObjectsCount.Should().Be(1);
        importMetadata.UpdatedObjectsCount.Should().Be(1);
    }

    [Theory(Skip = "Feature become obsolete")]
    [InlineAutoData]
    public async Task Handle_Should_ThrowNotSupportedException_When_NotSupportedCategoryProvided(Mock<IFormFile> formFileMock)
    {
        // Arrange
        var command = new ImportStockKeepingUnitsCommand(formFileMock.Object, (ProductCategoryCode)100);

        // Act
        var ex = await Assert.ThrowsAsync<NotSupportedException>(async () => await _sut.Handle(command, CancellationToken.None));

        // Assert
        ex.Should().NotBeNull();
    }
}
