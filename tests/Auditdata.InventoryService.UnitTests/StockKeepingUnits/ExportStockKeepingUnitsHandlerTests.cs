using Auditdata.Infrastructure.Contracts.CountryLayers;
using Auditdata.Infrastructure.Excel.Abstractions;
using Auditdata.InventoryService.Core.Entities.Products;
using Auditdata.InventoryService.Core.Features.StockKeepingUnits.Commands;
using Auditdata.InventoryService.Core.Features.StockKeepingUnits.Handlers;
using Auditdata.InventoryService.Core.Features.StockKeepingUnits.Models;
using Auditdata.InventoryService.Core.Features.StockKeepingUnits.Models.Excel;
using Auditdata.InventoryService.UnitTests.Common;
using AutoFixture.Xunit2;
using FluentAssertions;
using System.IO;

namespace Auditdata.InventoryService.UnitTests.StockKeepingUnits;

public class ExportStockKeepingUnitsHandlerTests : BaseSqlLiteDbContextTest
{
    private readonly IFixture _fixture = new RecursiveFixture();
    private readonly ExportStockKeepingUnitsHandler _sut;
    private readonly Mock<IExcelExporter> _excelExporterMock = new();
    private readonly Mock<IMapper> _mapperMock = new();

    public ExportStockKeepingUnitsHandlerTests()
    {
        _sut = new ExportStockKeepingUnitsHandler(DbContext, _excelExporterMock.Object, _mapperMock.Object);
    }

    [Theory(Skip = "Feature become obsolete")]
    [AutoData]
    public async Task Handle_Should_ExportStockKeepingUnits_When_ValidCategoryProvided(int skipProducts, byte[] excelData)
    {
        // Arrange
        var productCategory = _fixture.Build<ProductCategory>()
            .With(p => p.Code, ProductCategoryCode.HearingAids)
            .Create();

        var products = _fixture.Build<Product>()
            .With(p => p.CategoryId, productCategory.Id)
            .CreateMany(5)
            .ToList();

        var skus = products.Select(p => new StockKeepingUnit { ProductId = p.Id,  Name = $"sku{p.Id}"}).ToList();

        var skuDtos = skus.Select(s => new StockKeepingUnitDto()).ToList();

        var command = new ExportStockKeepingUnitsCommand(productCategory.Code, skipProducts);
        var category = new ProductCategory { Id = Guid.NewGuid(), Code = productCategory.Code };

        DbContext.ProductCategories.Add(productCategory);
        DbContext.Products.AddRange(products);
        DbContext.StockKeepingUnits.AddRange(skus);
        await DbContext.SaveChangesAsync();

        _mapperMock.Setup(m => m.Map<IReadOnlyList<StockKeepingUnitDto>>(It.IsAny<HashSet<StockKeepingUnitDto>>()))
            .Returns(skuDtos);

        _excelExporterMock.Setup(e => e.ToXlsxStream(It.IsAny<IReadOnlyList<SkuHearingAidExcelModel>>(), It.IsAny<CountryEnum>(), It.IsAny<string?>()))
            .Returns(new MemoryStream(excelData));

        // Act
        var result = await _sut.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();

        _excelExporterMock.Verify(
            e => e.ToXlsxStream(It.IsAny<IReadOnlyList<SkuHearingAidExcelModel>>(), It.IsAny<CountryEnum>(), It.IsAny<string?>()), Times.Once);
    }

    [Theory(Skip = "Feature become obsolete")]
    [AutoData]
    public async Task Handle_Should_ThrowException_When_InvalidCategoryProvided(
        ProductCategoryCode categoryCode, int skipProducts)
    {
        // Arrange
        var command = new ExportStockKeepingUnitsCommand(categoryCode, skipProducts);

        // Act
        Func<Task> action = async () => await _sut.Handle(command, CancellationToken.None);

        // Assert
        await action.Should().ThrowAsync<NotSupportedException>();
    }

    [Theory(Skip = "Feature become obsolete")]
    [AutoData]
    public async Task Handle_Should_ReturnResultWithAllSkusExportedFalse_When_CombinationProcessedExceedsMaxPageLength(
            byte[] excelData)
    {
        // Arrange
        var category = new ProductCategory() { Name = "TestCategory", Code = ProductCategoryCode.HearingAids };
        var supplier = Supplier.Create("TestSupplier");
        var colors = _fixture.Build<Color>().With(x => x.IsDeleted, false).CreateMany(30).ToList();
        var batteryTypes = _fixture.Build<BatteryType>().With(x => x.IsDeleted, false).CreateMany(40).ToList();
        var command = new ExportStockKeepingUnitsCommand(category.Code, 0);

        DbContext.Suppliers.Add(supplier);
        DbContext.Colors.AddRange(colors);
        DbContext.BatteryTypes.AddRange(batteryTypes);
        var products = _fixture.Build<AUProduct>()
            .With(x => x.IsDeleted, false)
            .With(x => x.Category, category)
            .CreateMany(5).ToArray();

        foreach (var product in products)
        {
            product.Supplier = supplier;
            product.Colors = colors.Select(color => 
                new ProductColor
                {
                    Product = product,
                    Color = color,
                }).ToArray();
            product.BatteryTypes = batteryTypes.Select(batteryType =>
                new ProductBatteryType
                {
                    Product = product,
                    BatteryType = batteryType,
                }).ToArray();
        }
        DbContext.Products.AddRange(products);
        await DbContext.SaveChangesAsync();

        var otherCategories = DbContext.ProductCategories.Where(x => x.Id != category.Id && x.Code == ProductCategoryCode.HearingAids);
        DbContext.ProductCategories.RemoveRange(otherCategories);
        await DbContext.SaveChangesAsync();

        _excelExporterMock.Setup(e => e.ToXlsxStream(
            It.IsAny<IReadOnlyList<SkuHearingAidExcelModel>>(),
            It.IsAny<CountryEnum>(), It.IsAny<string?>()))
            .Returns(new MemoryStream(excelData));

        // Act
        var result = await _sut.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.AllSkusExported.Should().BeFalse();
    }

    [Theory(Skip = "Feature become obsolete")]
    [AutoData]
    public async Task Handle_Should_ExportWithSkipProducts_When_SkipProductsGreaterThanZero(int skipProducts, byte[] excelData)
    {
        // Arrange
        var category = new ProductCategory() { Name = "TestCategory", Code = ProductCategoryCode.HearingAids };
        var supplier = Supplier.Create("TestSupplier");
        var colors = _fixture.Build<Color>().With(x => x.IsDeleted, false).CreateMany(30).ToList();
        var batteryTypes = _fixture.Build<BatteryType>().With(x => x.IsDeleted, false).CreateMany(40).ToList();
        var command = new ExportStockKeepingUnitsCommand(category.Code, skipProducts);

        DbContext.Suppliers.Add(supplier);
        DbContext.Colors.AddRange(colors);
        DbContext.BatteryTypes.AddRange(batteryTypes);
        var products = _fixture.Build<AUProduct>()
            .With(x => x.IsDeleted, false)
            .With(x => x.Category, category)
            .CreateMany(5).ToArray();

        foreach (var product in products)
        {
            product.Supplier = supplier;
            product.Colors = colors.Select(color =>
                new ProductColor
                {
                    Product = product,
                    Color = color,
                }).ToArray();
            product.BatteryTypes = batteryTypes.Select(batteryType =>
                new ProductBatteryType
                {
                    Product = product,
                    BatteryType = batteryType,
                }).ToArray();
        }
        DbContext.Products.AddRange(products);
        await DbContext.SaveChangesAsync();

        var otherCategories = DbContext.ProductCategories.Where(x => x.Id != category.Id && x.Code == ProductCategoryCode.HearingAids);
        DbContext.ProductCategories.RemoveRange(otherCategories);
        await DbContext.SaveChangesAsync();

        _excelExporterMock.Setup(e => e.ToXlsxStream(
            It.IsAny<IReadOnlyList<SkuHearingAidExcelModel>>(),
            It.IsAny<CountryEnum>(), It.IsAny<string?>()))
            .Returns(new MemoryStream(excelData));

        // Act
        var result = await _sut.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.ProcessedProducts.Should().BeGreaterThan(0);

        _excelExporterMock.Verify(
            e => e.ToXlsxStream(It.IsAny<IReadOnlyList<SkuHearingAidExcelModel>>(), It.IsAny<CountryEnum>(), It.IsAny<string?>()), Times.Once);
    }

    [Theory(Skip = "Feature become obsolete")]
    [AutoData]
    public async Task Handle_Should_ExportPagedProducts_When_SkipProductsZero(byte[] excelData)
    {
        // Arrange
        var category = new ProductCategory() { Name = "TestCategory", Code = ProductCategoryCode.Accessories };
        var supplier = Supplier.Create("TestSupplier");
        var colors = _fixture.Build<Color>().With(x => x.IsDeleted, false).CreateMany(3).ToList();
        var batteryTypes = _fixture.Build<BatteryType>().With(x => x.IsDeleted, false).CreateMany(4).ToList();
        var command = new ExportStockKeepingUnitsCommand(category.Code, 0);

        DbContext.Suppliers.Add(supplier);
        DbContext.Colors.AddRange(colors);
        DbContext.BatteryTypes.AddRange(batteryTypes);
        var products = _fixture.Build<AUProduct>()
            .With(x => x.IsDeleted, false)
            .With(x => x.Category, category)
            .CreateMany(2).ToArray();

        foreach (var product in products)
        {
            product.Supplier = supplier;
            product.Colors = colors.Select(color =>
                new ProductColor
                {
                    Product = product,
                    Color = color,
                }).ToArray();
            product.BatteryTypes = batteryTypes.Select(batteryType =>
                new ProductBatteryType
                {
                    Product = product,
                    BatteryType = batteryType,
                }).ToArray();
        }
        DbContext.Products.AddRange(products);
        await DbContext.SaveChangesAsync();

        var otherCategories = DbContext.ProductCategories.Where(x => x.Id != category.Id && x.Code == ProductCategoryCode.Accessories);
        DbContext.ProductCategories.RemoveRange(otherCategories);
        await DbContext.SaveChangesAsync();

        _excelExporterMock.Setup(e => e.ToXlsxStream(
            It.IsAny<IReadOnlyList<SkuAccessoryExcelModel>>(),
            It.IsAny<CountryEnum>(), It.IsAny<string?>()))
            .Returns(new MemoryStream(excelData));

        // Act
        var result = await _sut.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.ProcessedProducts.Should().Be(products.Length); 
        result.AllSkusExported.Should().BeTrue(); 

        _excelExporterMock.Verify(
            e => e.ToXlsxStream(It.IsAny<IReadOnlyList<SkuHearingAidExcelModel>>(), It.IsAny<CountryEnum>(), It.IsAny<string?>()), Times.AtMostOnce);
    }

    [Theory(Skip = "Feature become obsolete")]
    [AutoData]
    public async Task Handle_Should_ExportCorrectCategorySkus(byte[] excelData)
    {
        // Arrange
        var category = new ProductCategory() { Name = "TestCategory", Code = ProductCategoryCode.Accessories };
        var supplier = Supplier.Create("TestSupplier");
        var colors = _fixture.Build<Color>().With(x => x.IsDeleted, false).CreateMany(3).ToList();
        var batteryTypes = _fixture.Build<BatteryType>().With(x => x.IsDeleted, false).CreateMany(4).ToList();
        var command = new ExportStockKeepingUnitsCommand(category.Code, 0);

        DbContext.Suppliers.Add(supplier);
        DbContext.Colors.AddRange(colors);
        DbContext.BatteryTypes.AddRange(batteryTypes);
        var products = _fixture.Build<AUProduct>()
            .With(x => x.IsDeleted, false)
            .With(x => x.Category, category)
            .CreateMany(2).ToArray();

        foreach (var product in products)
        {
            product.Supplier = supplier;
            product.Colors = colors.Select(color =>
                new ProductColor
                {
                    Product = product,
                    Color = color,
                }).ToArray();
            product.BatteryTypes = batteryTypes.Select(batteryType =>
                new ProductBatteryType
                {
                    Product = product,
                    BatteryType = batteryType,
                }).ToArray();
        }
        DbContext.Products.AddRange(products);
        await DbContext.SaveChangesAsync();

        var otherCategories = DbContext.ProductCategories.Where(x => x.Id != category.Id && x.Code == ProductCategoryCode.Accessories);
        DbContext.ProductCategories.RemoveRange(otherCategories);
        await DbContext.SaveChangesAsync();

        _excelExporterMock.Setup(e => e.ToXlsxStream(
            It.IsAny<IReadOnlyList<SkuAccessoryExcelModel>>(),
            It.IsAny<CountryEnum>(), It.IsAny<string?>()))
            .Returns(new MemoryStream(excelData));

        // Act
        var result = await _sut.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.ProcessedProducts.Should().Be(products.Length);
        result.AllSkusExported.Should().BeTrue(); 

        _excelExporterMock.Verify(
            e => e.ToXlsxStream(It.IsAny<IReadOnlyList<SkuAccessoryExcelModel>>(), It.IsAny<CountryEnum>(), It.IsAny<string?>()), Times.Once);
    }
}
