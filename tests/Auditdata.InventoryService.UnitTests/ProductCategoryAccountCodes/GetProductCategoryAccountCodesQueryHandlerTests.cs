using Auditdata.InventoryService.Core.Features.ProductCategoryAccountCodes.Handlers;
using Auditdata.InventoryService.Core.Features.ProductCategoryAccountCodes.Queries;
using Auditdata.InventoryService.UnitTests.Common;
using FluentAssertions;

namespace Auditdata.InventoryService.UnitTests.ProductCategoryAccountCodes;

public class GetProductCategoryAccountCodesQueryHandlerTests
{
    private readonly Fixture _fixture = new RecursiveFixture();
    private readonly Mock<IProductCategoriesRepository> _productCategoriesRepository = new();
    private readonly Mock<IProductCategoryAccountCodesRepository> _accountRepositoryMock = new();

    [Fact]
    public async Task Consume_ShouldRespond_With_AccountCodesResult_IfAccountCodesSetup()
    {
        //Arrange
        var accountCodes = _fixture.CreateMany<ProductCategoryAccountCode>(5).ToList();
        var productCategories = _fixture.CreateMany<ProductCategory>(5).ToList();

        for (var i = 0; i < 5; i++)
        {
            accountCodes[i].ProductCategoryId = productCategories[i].Id;
        }
        
        _accountRepositoryMock.Setup(x => x.GetAsync()).ReturnsAsync(accountCodes);
        _productCategoriesRepository.Setup(x => x.GetAsync()).ReturnsAsync(productCategories);

        var query = new GetProductCategoryAccountCodesQuery();
        var handler =
            new GetProductCategoryAccountCodesQueryHandler(_accountRepositoryMock.Object,
                _productCategoriesRepository.Object);

        //Act
        var result = await handler.Handle(query, CancellationToken.None);

        //Assert
        result.Should().HaveSameCount(productCategories);
       
        for (var i = 0; i < 5; i++)
        {
            result.Should().Contain(x => x.ProductCategoryId == accountCodes[i].ProductCategoryId);
            result.Should().Contain(x => x.AccountCode == accountCodes[i].AccountCode);
        }
    }
}