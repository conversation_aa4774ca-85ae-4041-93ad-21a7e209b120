using Auditdata.InventoryService.Api.Mappings;
using Auditdata.InventoryService.Core.Entities.Products;
using Auditdata.InventoryService.Core.Features.StockProductItems.SearchStockProductItems;
using Auditdata.InventoryService.UnitTests.Common;
using FluentAssertions;

namespace Auditdata.InventoryService.UnitTests.StockProductItems;

public class SearchStockProductItemsHandlerTests : BaseDbContextTest
{
    private readonly SearchStockProductItemsHandler _sut;
    private readonly Fixture _fixture = new RecursiveFixture();
    
    public SearchStockProductItemsHandlerTests()
    {
        _sut = new SearchStockProductItemsHandler(
            DbContext,
            new MapperConfiguration(conf => conf.AddProfile<StocksMapping>()).CreateMapper());
    }
    
    [Fact]
    public async Task Handler_ShouldReturn_PendingTransferAcceptance_True_ForTransfer_Items()
    {
        //Arrange
        var product = _fixture.Build<Product>()
            .With(x => x.IsDeleted, false)
            .With(x => x.IsSerialized, true)
            .Create();
        var batteryType = _fixture.Build<BatteryType>()
            .With(x => x.IsDeleted, false)
            .Create();
        var color = _fixture.Build<Color>()
            .With(x => x.IsDeleted, false)
            .Create();
        
        var toStockId = Guid.NewGuid();
        var transfer = new TransfersState
        {
            TransferId = Guid.NewGuid(),
            FromStockId = Guid.NewGuid(),
            ToStockId = toStockId
        };

        var stockProductItem = new StockProductItem
        {
            Id = Guid.NewGuid(),
            Status = StockProductItemStatus.Available,
            StockProduct = new StockProduct
            {
                Stock = new Stock
                {
                    Id = toStockId,
                    Name = "Stock1",
                    LocationId = Guid.NewGuid()
                },
                Product = product
            },
            BatteryType = batteryType,
            Color = color
        };
        var transferredItem = new StockProductItem
        {
            Id = Guid.NewGuid(),
            Status = StockProductItemStatus.TransferInProcess,
            StockProduct = new StockProduct
            {
                Stock = new Stock
                {
                    Id = transfer.FromStockId,
                    Name = "Stock2"
                },
                Product = product
            },
            BatteryType = batteryType,
            Color = color,
            TransferId = transfer.TransferId
        };
        transfer.StockProductItem = transferredItem;
        
        DbContext.StockProductItems.Add(stockProductItem);
        DbContext.Transfers.Add(transfer);
        await DbContext.SaveChangesAsync();

        var command = new SearchStockProductItemsQuery
        {
            StockIds = [toStockId],
            ProductId = product.Id,
            Page = 1,
            PerPage = 10
        };
        //Act
        var result = await _sut.Handle(command, CancellationToken.None);

        //Assert

        result.Rows.Should().HaveCount(2);
        result.Rows![1].Model!.PendingTransferAcceptance.Should().BeTrue();
    }
}
