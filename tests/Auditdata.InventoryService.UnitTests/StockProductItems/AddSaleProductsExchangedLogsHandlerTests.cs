using Auditdata.InventoryService.Core.Entities.Products;
using Auditdata.InventoryService.Core.Features.Products.Models;
using Auditdata.InventoryService.Core.Features.StockProductItems.Commands;
using Auditdata.InventoryService.Core.Features.StockProductItems.Handlers;
using Auditdata.InventoryService.Core.Features.StockProductItems.Models;
using Auditdata.InventoryService.UnitTests.Common;
using FluentAssertions;
using Microsoft.EntityFrameworkCore;

namespace Auditdata.InventoryService.UnitTests.StockProductItems;

public class AddSaleProductsExchangedLogsHandlerTests : BaseSqlLiteDbContextTest
{
    private readonly AddSaleProductsExchangedLogsHandler _sut;
    private readonly RecursiveFixture _fixture = new();

    public AddSaleProductsExchangedLogsHandlerTests()
    {
        _sut = new AddSaleProductsExchangedLogsHandler(DbContext);
    }

    [Theory]
    [RecursiveInlineAutoData]
    public async Task Handle_Should_AddExchangeProductsLogs_When_CommandProvided(
        AddSaleProductsExchangedLogsCommand command)
    {
        // Arrange
        var stock = _fixture.Create<Stock>();
        var product = _fixture.Build<Product>().With(x => x.IsDeleted, false)
            .With(x => x.IsSerialized, true)
            .Without(x => x.StockProducts).Create();
        var stockProduct = _fixture.Build<StockProduct>().With(x => x.IsDeleted, false)
            .Without(x => x.StockProductItems)
            .With(x => x.Product, product).With(x => x.ProductId, product.Id)
            .With(x => x.Stock, stock).With(x => x.StockId, stock.Id)
            .Create();
        var items = _fixture.Build<StockProductItem>().With(x => x.IsDeleted, false)
            .With(x => x.StockProduct, stockProduct).With(x => x.StockProductId, stockProduct.Id)
            .With(x => x.Status, StockProductItemStatus.Available)
            .CreateMany()
            .ToList();
        var newDbContext = GetDbContext();
        newDbContext.Stocks.Add(stock);
        newDbContext.Products.Add(product);
        newDbContext.StockProducts.Add(stockProduct);
        newDbContext.StockProductItems.AddRange(items);
        await newDbContext.SaveChangesAsync();
        var returnedSpi = items[0];
        var newSpi = items[1];
        command.ReturnedItems =
            [new ProductReturnedItem(returnedSpi.StockProduct.Id, 1, returnedSpi.Id, CreditNoteAction.ToStock)];
        command.NewItems = [new ReserveProductItem(newSpi.StockProduct.Id, 1, newSpi.Id, "abc")];

        // Act
        await _sut.Handle(command, CancellationToken.None);

        // Assert
        var savedLogs = await DbContext.StockProductItemLogs.ToListAsync();
        savedLogs.Select(x => x.StockProductItemId).Should().Contain(returnedSpi.Id);
        savedLogs.Select(x => x.StockProductItemId).Should().Contain(newSpi.Id);
    }
}
