using Auditdata.InventoryService.Contracts.Commands;
using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Abstractions.Azure;
using Auditdata.InventoryService.Core.Constants;
using Auditdata.InventoryService.Core.Exceptions;
using Auditdata.InventoryService.Core.Features.StockProductItems.Commands;
using Auditdata.InventoryService.Core.Features.StockProductItems.Handlers;
using Auditdata.InventoryService.UnitTests.Common;
using Auditdata.Transport.Contracts.Exceptions;
using FluentAssertions;
using StockTransactionType = Auditdata.InventoryService.Contracts.StockTransactionType;

namespace Auditdata.InventoryService.UnitTests.StockProductItems;

public class MarkProductItemAvailableHandlerTests : BaseDbContextTest
{
    private readonly RecursiveFixture _fixture = new();
    private readonly Mock<IAzureServiceBusPublisher> _azureServiceBusPublisher;
    private readonly Mock<IEventPublisher> _eventPublisherMock = new();
    private readonly MarkProductItemAvailableHandler _handler;

    public MarkProductItemAvailableHandlerTests()
    {
        _azureServiceBusPublisher = new Mock<IAzureServiceBusPublisher>();
        _handler = new MarkProductItemAvailableHandler(
            _azureServiceBusPublisher.Object,
            _eventPublisherMock.Object,
            DbContext);
    }

    [Fact]
    public async Task Handle_Should_MarkStockProductItemAvailable_When_ItemAvailable()
    {
        // Arrange
        var stockProductItem = _fixture.Build<StockProductItem>()
            .With(x => x.IsDeleted, false)
            .With(x => x.Status, StockProductItemStatus.Reserved)
            .With(x => x.AddToStockAllowed, true).Create();
        DbContext.StockProductItems.Add(stockProductItem);
        await DbContext.SaveChangesAsync();

        // Act
        await _handler.Handle(new MarkProductItemAvailableCommand(stockProductItem.Id), CancellationToken.None);

        // Assert
        stockProductItem.Status.Should().Be(StockProductItemStatus.Available);
        stockProductItem.AddToStockAllowed.Should().BeFalse();
        _azureServiceBusPublisher.Verify(x => x.PublishAddStockTransactionCommand(It.Is<AddStockTransactionCommand>(y =>
            y.Type == StockTransactionType.StockAdjustment &&
            y.Quantity == 1)));
        _eventPublisherMock.Verify(
            x => x.SerializedStockAdjusted(
                stockProductItem.StockProduct, 1, new List<StockProductItem>() { stockProductItem },
                It.IsAny<CancellationToken>()),
            Times.Once);
    }
    
    [Fact]
    public async Task Handle_Should_ThrowEntityNotFound_When_ItemNotFound()
    {
        // Act
        var act = async () => await _handler
            .Handle(new MarkProductItemAvailableCommand(Guid.NewGuid()), CancellationToken.None);

        // Assert
        await act.Should().ThrowAsync<EntityNotFoundException>();
    }
    
    [Fact]
    public async Task Handle_Should_ThrowBusinessException_When_ItemReservedAndNotAllowedToAddToStock()
    {
        // Arrange
        var stockProductItem = _fixture.Build<StockProductItem>()
            .With(x => x.Status, StockProductItemStatus.Reserved)
            .With(x => x.AddToStockAllowed, false)
            .With(x => x.IsDeleted, false).Create();
        DbContext.StockProductItems.Add(stockProductItem);
        await DbContext.SaveChangesAsync();

        // Act
        var act = async () => await _handler
            .Handle(new MarkProductItemAvailableCommand(stockProductItem.Id), CancellationToken.None);

        // Assert
        var ex = await act.Should().ThrowAsync<BusinessException>();
        ex.Which.ErrorCode.Should().Be(ErrorCodes.StockProductItemAddToStockNotAllowed);
    }
}