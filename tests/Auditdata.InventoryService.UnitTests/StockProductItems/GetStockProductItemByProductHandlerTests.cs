using Auditdata.InventoryService.Core.Features.StockProductItems.Handlers;
using Auditdata.InventoryService.Core.Features.StockProductItems.Queries;
using Auditdata.InventoryService.UnitTests.Common;
using FluentAssertions;

namespace Auditdata.InventoryService.UnitTests.StockProductItems;

public class GetStockProductItemByProductHandlerTests
{
    private readonly IFixture _fixture;
    private readonly GetStockProductItemByProductHandler _handler;
    private readonly Mock<IStockProductItemsRepository> _stockProductItemsRepository;

    public GetStockProductItemByProductHandlerTests()
    {
        _fixture = new RecursiveFixture();
        _stockProductItemsRepository = new Mock<IStockProductItemsRepository>();
        _handler = new GetStockProductItemByProductHandler(_stockProductItemsRepository.Object);
    }

    [Fact]
    public async Task Consume_ShouldReturn_StockProductItems()
    {
        var searchText = "searchText";
        var productId = Guid.NewGuid();
        var stockId = Guid.NewGuid();
        var stockProduct = _fixture.Build<StockProduct>()
            .With(x => x.StockId, stockId)
            .With(x => x.ProductId, productId)
            .Create();
        var stockProductItems = _fixture.Build<StockProductItem>()
            .With(x => x.StockProductId, stockProduct.Id)
            .With(x => x.StockProduct, stockProduct)
            .CreateMany()
            .ToList();

        _stockProductItemsRepository.Setup(x => x.GetByProductIdWithStockAsync(productId, searchText, stockId, null))
            .ReturnsAsync(stockProductItems);

        var result = await _handler.Handle(new GetStockProductItemByProductQuery(productId, searchText, stockId, null), CancellationToken.None);
    }

    [Fact]
    public async Task Consume_ShouldCombine_PendingTransferAcceptanceStockProductItems()
    {
        var searchText = "searchText";
        var productId = Guid.NewGuid();
        var stockId = Guid.NewGuid();
        var stockProduct = _fixture.Build<StockProduct>()
            .With(x => x.StockId, stockId)
            .With(x => x.ProductId, productId)
            .Create();
        var stockProductItems = _fixture.Build<StockProductItem>()
            .With(x => x.StockProductId, stockProduct.Id)
            .With(x => x.StockProduct, stockProduct)
            .With(x => x.PendingTransferAcceptance, false)
            .CreateMany()
            .ToList();
        var pendingTransferAcceptanceStockProductItems = _fixture.Build<StockProductItem>()
            .With(x => x.StockProductId, stockProduct.Id)
            .With(x => x.StockProduct, stockProduct)
            .With(x => x.PendingTransferAcceptance, true)
            .CreateMany()
            .ToList();

        _stockProductItemsRepository.Setup(x => x.GetByProductIdWithStockAsync(productId, searchText, stockId, null))
            .ReturnsAsync(stockProductItems);
        _stockProductItemsRepository.Setup(x => x.GetStockProductItemsPendingTransferToStockAsync(productId, stockId, searchText, null))
            .ReturnsAsync(stockProductItems);


        var result = await _handler.Handle(new GetStockProductItemByProductQuery(productId, searchText, stockId, null), CancellationToken.None);

        result.StockProductItems.Should().HaveCount(stockProductItems.Count + pendingTransferAcceptanceStockProductItems.Count);
    }
}
