using System.IO;
using Auditdata.InventoryService.Core.Entities.Products;
using Auditdata.InventoryService.Core.Features.StockProductItems.Commands;
using Auditdata.InventoryService.Core.Features.StockProductItems.Events;
using Auditdata.InventoryService.Core.Features.StockProductItems.Handlers;
using Auditdata.InventoryService.Core.Features.StockProductItems.Models;
using Auditdata.InventoryService.UnitTests.Common;
using FluentAssertions;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Auditdata.InventoryService.UnitTests.StockProductItems;

public class ProductTrialCompletedHandlerTests : BaseDbContextTest
{
    private static readonly IFixture Fixture = new RecursiveFixture();
    private readonly Mock<IMediator> _mediator = new();
    private readonly ProductTrialCompletedHandler _sut;

    public ProductTrialCompletedHandlerTests()
    {
        _sut = new ProductTrialCompletedHandler(DbContext, _mediator.Object);
    }

    [Fact]
    public async Task Handle_ShouldThrow_InvalidDataException_IfDuplicateSerialNumbers_AreFound()
    {
        //Arrange
        var serialNumber = Fixture.Create<string>();
        var command = new ProductTrialCompletedCommand(
            Guid.NewGuid(),
            new List<TrialStockProductItem>
            {
                new(Guid.NewGuid(), serialNumber),
                new(Guid.NewGuid(), serialNumber)
            },
            null);

        //Act
        var action = () => _sut.Handle(command, CancellationToken.None);

        //Assert
        (await action.Should().ThrowAsync<InvalidDataException>()).Which.Message.Should().Be("Stock product items with duplicated serial number.");
    }

    [Fact]
    public async Task Handle_ShouldCompleteTrial_And_PublishEvent()
    {
        //Arrange
        var locationId = Guid.NewGuid();
        var serialNumber = Guid.NewGuid().ToString();
        var stock = Fixture.Build<Stock>().With(x => x.LocationId, locationId).Create();
        var product = GetSerializedProduct();
        var stockProductItem = new StockProductItem()
        {
            Id = Guid.NewGuid(),
            SerialNumber = serialNumber,
            Status = StockProductItemStatus.OnTrial,
            SaleId = Guid.NewGuid(),
            PatientId = Guid.NewGuid(),
        };

        var stockProduct = new StockProduct
        {
            Stock = stock,
            Product = product,
            StockProductItems = new List<StockProductItem>
            {
                stockProductItem
            },
        };
        stockProduct.SetQuantity(1);
        DbContext.StockProducts.Add(stockProduct);
        await DbContext.SaveChangesAsync();

        var command = new ProductTrialCompletedCommand(
            locationId,
            new List<TrialStockProductItem>
            {
                new(product.Id, serialNumber),
            },
            "patientName");

        //Act
        await _sut.Handle(command, CancellationToken.None);

        //Assert
        var updatedStockProductItem = await DbContext.StockProductItems.FirstOrDefaultAsync(x => x.Id == stockProductItem.Id);
        updatedStockProductItem.Should().NotBeNull();
        updatedStockProductItem!.Status.Should().Be(StockProductItemStatus.Reserved);

        _mediator.Verify(x => x.Publish(It.Is<StockProductItemsReservedEvent>(notification => 
                notification.SaleId == stockProductItem.SaleId!.Value &&
                notification.Patient == command.PatientName &&
                notification.StockProductItems.Count() == 1 &&
                notification.StockProductItems.ElementAt(0).Id == stockProductItem.Id), It.IsAny<CancellationToken>()),
            Times.Once);
    }

    private static Product GetSerializedProduct() => Fixture
        .Build<Product>()
        .With(x => x.IsDeleted, false)
        .With(x => x.IsSerialized, true)
        .With(x => x.IsActive, true)
        .With(x => x.IsSellable, true)
        .With(x => x.ControlledByStock, true)
        .Without(x => x.StockProducts)
        .Create();
}
