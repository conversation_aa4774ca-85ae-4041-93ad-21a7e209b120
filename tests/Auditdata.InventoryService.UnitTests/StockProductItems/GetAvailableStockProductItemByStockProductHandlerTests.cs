using Auditdata.InventoryService.Core.Features.StockProductItems.Handlers;
using Auditdata.InventoryService.Core.Features.StockProductItems.Queries;
using Auditdata.InventoryService.UnitTests.Common;
using FluentAssertions;

namespace Auditdata.InventoryService.UnitTests.StockProductItems;

public class GetAvailableStockProductItemByStockProductHandlerTests
{
    private readonly IFixture _fixture;
    private readonly GetAvailableStockProductItemByStockProductHandler _handler;
    private readonly Mock<IStockProductItemsRepository> _stockProductItemsRepository;

    public GetAvailableStockProductItemByStockProductHandlerTests()
    {
        _fixture = new RecursiveFixture();
        _stockProductItemsRepository = new Mock<IStockProductItemsRepository>();
        _handler = new GetAvailableStockProductItemByStockProductHandler(_stockProductItemsRepository.Object);
    }
    
    [Fact]
    public async Task Consume_ShouldReturn_Available_ProductItems()
    {
        var stockProductItems = _fixture.Build<StockProductItem>().CreateMany().ToList();
        _stockProductItemsRepository.Setup(x => x.GetAvailableAsync(It.IsAny<Guid>(), It.IsAny<Guid?>()))
            .ReturnsAsync(stockProductItems);
        
        var result = await _handler.Handle(new GetAvailableStockProductItemByStockProductQuery(Guid.NewGuid()), CancellationToken.None);

        result.StockProductItems.Should().HaveCount(stockProductItems.Count);
    }
}
