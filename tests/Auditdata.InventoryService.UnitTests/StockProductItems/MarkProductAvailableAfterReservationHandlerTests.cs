using Auditdata.InventoryService.Contracts.Commands;
using Auditdata.InventoryService.Core.Abstractions.Azure;
using Auditdata.InventoryService.Core.Entities.Products;
using Auditdata.InventoryService.Core.Features.StockProductItems.Commands;
using Auditdata.InventoryService.Core.Features.StockProductItems.Handlers;
using Auditdata.InventoryService.Core.Features.StockProductItems.Models;
using Auditdata.InventoryService.UnitTests.Common;
using FluentAssertions;
using StockTransactionType = Auditdata.InventoryService.Contracts.StockTransactionType;

namespace Auditdata.InventoryService.UnitTests.StockProductItems;

public class MarkProductAvailableAfterReservationHandlerTests : BaseDbContextTest
{
    private readonly IFixture _fixture = new RecursiveFixture();
    private readonly Mock<IStockProductItemsRepository> _stockProductItemsRepository;
    private readonly Mock<IAzureServiceBusPublisher> _azureServiceBusPublisher;
    private readonly MarkProductAvailableAfterReservationHandler _handler;

    public MarkProductAvailableAfterReservationHandlerTests()
    {
        _stockProductItemsRepository = new Mock<IStockProductItemsRepository>();
        _azureServiceBusPublisher = new Mock<IAzureServiceBusPublisher>();
        _handler = new MarkProductAvailableAfterReservationHandler(
            _stockProductItemsRepository.Object,
            _azureServiceBusPublisher.Object,
            DbContext);
    }

    [Fact]
    public async Task Consume_ShouldMarkAvailable_SerializedReservedItem()
    {
        var stockProductId = Guid.NewGuid();
        var stockProductItemId = Guid.NewGuid();
        var saleId = Guid.NewGuid();
        var productReturnedItems = _fixture.Build<UnreservedProductItem>()
            .With(x => x.StockProductId, stockProductId)
            .With(x => x.Quantity, 1)
            .With(x => x.StockProductItemId, stockProductItemId)
            .CreateMany(1).ToList();
        var command = _fixture.Build<MarkProductAvailableAfterReservationCommand>()
            .With(x => x.Items, productReturnedItems)
            .With(x => x.SaleId, saleId)
            .Create();
        var stockProductItem = _fixture.Build<StockProductItem>()
            .With(x => x.Id, stockProductItemId)
            .With(x => x.StockProductId, stockProductId)
            .With(x => x.Status, StockProductItemStatus.Reserved)
            .With(x => x.SaleId, saleId)
            .With(x => x.IsDeleted, false)
            .Create();
        var product = _fixture.Build<Product>()
            .With(x => x.IsSerialized, true)
            .With(x => x.IsDeleted, false)
            .Create();
        var stockProduct = _fixture.Build<StockProduct>()
            .With(x => x.Id, stockProductId)
            .With(x => x.Product, product)
            .With(x => x.IsDeleted, false)
            .Create();
        DbContext.StockProducts.Add(stockProduct);
        await DbContext.SaveChangesAsync();

        _stockProductItemsRepository.Setup(x => x.GetByIdWithStockProductAsync(stockProductItemId)).ReturnsAsync(stockProductItem);

        await _handler.Handle(command, CancellationToken.None);

        stockProductItem.SaleId.Should().BeNull();
        stockProductItem.Status.Should().Be(StockProductItemStatus.Available);
        _azureServiceBusPublisher.Verify(x => x.PublishAddStockTransactionCommand(It.Is<AddStockTransactionCommand>(y =>
            y.Type == StockTransactionType.Unreserved &&
            y.StockId == stockProduct.StockId &&
            y.ProductId == stockProduct.ProductId &&
            y.Quantity == productReturnedItems.Count &&
            y.SaleId == command.SaleId &&
            y.ChangeRunningTotal)), Times.Once);
    }
    
    [Fact]
    public async Task Consume_ShouldNotMarkAvailable_ProductsWhichReturnedToSupplier()
    {
        var stockProductId = Guid.NewGuid();
        var stockProductItemId = Guid.NewGuid();
        var saleId = Guid.NewGuid();
        var productReturnedItems = _fixture.Build<UnreservedProductItem>()
            .With(x => x.StockProductId, stockProductId)
            .With(x => x.Quantity, 1)
            .With(x => x.StockProductItemId, stockProductItemId)
            .CreateMany(1).ToList();
        var command = _fixture.Build<MarkProductAvailableAfterReservationCommand>()
            .With(x => x.Items, productReturnedItems)
            .With(x => x.SaleId, saleId)
            .Create();
        var stockProductItem = _fixture.Build<StockProductItem>()
            .With(x => x.Id, stockProductItemId)
            .With(x => x.StockProductId, stockProductId)
            .With(x => x.Status, StockProductItemStatus.ReturnedToSupplier)
            .With(x => x.SaleId, saleId)
            .With(x => x.IsDeleted, false)
            .Create();
        var product = _fixture.Build<Product>()
            .With(x => x.IsSerialized, true)
            .With(x => x.IsDeleted, false)
            .Create();
        var stockProduct = _fixture.Build<StockProduct>()
            .With(x => x.Id, stockProductId)
            .With(x => x.Product, product)
            .With(x => x.IsDeleted, false)
            .Create();
        DbContext.StockProducts.Add(stockProduct);
        await DbContext.SaveChangesAsync();

        _stockProductItemsRepository.Setup(x => x.GetByIdWithStockProductAsync(stockProductItemId)).ReturnsAsync(stockProductItem);

        await _handler.Handle(command, CancellationToken.None);

        stockProductItem.Status.Should().Be(StockProductItemStatus.ReturnedToSupplier);
        _azureServiceBusPublisher.Verify(x => x.PublishAddStockTransactionCommand(It.IsAny<AddStockTransactionCommand>()), Times.Never());
    }
    
    [Fact]
    public async Task Consume_ShouldDelete_NonSerializedReservedItem()
    {
        var stockProductId = Guid.NewGuid();
        var stockProductItemId = Guid.NewGuid();
        var saleId = Guid.NewGuid();
        var productReturnedItems = _fixture.Build<UnreservedProductItem>()
            .With(x => x.StockProductId, stockProductId)
            .With(x => x.Quantity, 1)
            .With(x => x.StockProductItemId, stockProductItemId)
            .CreateMany(1).ToList();
        var command = _fixture.Build<MarkProductAvailableAfterReservationCommand>()
            .With(x => x.Items, productReturnedItems)
            .With(x => x.SaleId, saleId)
            .Create();
        var stockProductItem = _fixture.Build<StockProductItem>()
            .With(x => x.Id, stockProductItemId)
            .With(x => x.StockProductId, stockProductId)
            .With(x => x.Status, StockProductItemStatus.Reserved)
            .With(x => x.SaleId, saleId)
            .With(x => x.IsDeleted, false)
            .Create();
        var product = _fixture.Build<Product>()
            .With(x => x.IsSerialized, false)
            .With(x => x.IsDeleted, false)
            .Create();
        var stockProduct = _fixture.Build<StockProduct>()
            .With(x => x.Id, stockProductId)
            .With(x => x.Product, product)
            .With(x => x.IsDeleted, false)
            .Create();
        DbContext.StockProducts.Add(stockProduct);
        await DbContext.SaveChangesAsync();

        _stockProductItemsRepository.Setup(x => x.GetReservedAsync(stockProductId, saleId)).ReturnsAsync(stockProductItem);

        await _handler.Handle(command, CancellationToken.None);
        
        _azureServiceBusPublisher.Verify(x => x.PublishAddStockTransactionCommand(It.Is<AddStockTransactionCommand>(y =>
            y.Type == StockTransactionType.Unreserved &&
            y.StockId == stockProduct.StockId &&
            y.ProductId == stockProduct.ProductId &&
            y.Quantity == productReturnedItems.Count &&
            y.SaleId == command.SaleId &&
            y.ChangeRunningTotal)), Times.Never);
    }
}