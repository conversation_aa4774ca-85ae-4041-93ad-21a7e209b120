using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Abstractions.Validators;
using Auditdata.InventoryService.Core.Entities.Products;
using Auditdata.InventoryService.Core.Exceptions;
using Auditdata.InventoryService.Core.Features.StockProductItems.Commands;
using Auditdata.InventoryService.Core.Features.StockProductItems.Dtos;
using Auditdata.InventoryService.Core.Features.StockProductItems.Handlers;
using Auditdata.InventoryService.UnitTests.Common;
using FluentAssertions;

namespace Auditdata.InventoryService.UnitTests.StockProductItems;

public class AssignProductSerialNumberHandlerTests : BaseSqlLiteDbContextTest
{
    private readonly IFixture _fixture;
    private readonly Mock<ISerialNumbersValidator> _serialNumbersValidator;
    private readonly AssignProductSerialNumberHandler _handler;

    public AssignProductSerialNumberHandlerTests()
    {
        _fixture = new RecursiveFixture();
        _serialNumbersValidator = new Mock<ISerialNumbersValidator>();

        _handler = new AssignProductSerialNumberHandler(
            DbContext,
            _serialNumbersValidator.Object,
            new Mock<IEventPublisher>().Object);
    }

    [Fact]
    public async Task Handle_ShouldUpdate_StockProductItems()
    {
        // Arrange
        var category = _fixture.Build<ProductCategory>()
            .With(x=> x.Code, ProductCategoryCode.HearingAids)
            .With(x=> x.IsDeleted, false)
            .Without(x=> x.Attributes).Create();
        var attributes = _fixture.Build<Core.Entities.Attribute>()
            .With(x => x.IsDeleted, false)
            .With(x => x.ProductCategories, new List<ProductCategory> { category })
            .CreateMany().ToList();
        var batteryTypes = _fixture.Build<BatteryType>()
            .With(x => x.IsDeleted, false)
            .CreateMany().ToList();
        var colors = _fixture.Build<Color>()
            .With(x => x.IsDeleted, false)
            .CreateMany().ToList();
        var productBatteryTypes = batteryTypes
            .Select(x => new ProductBatteryType { BatteryTypeId = x.Id, BatteryType = x }).ToList();
        var productColors = colors
            .Select(x => new ProductColor { ColorId = x.Id, Color = x }).ToList();
        var product = _fixture.Build<Product>()
            .With(x => x.IsDeleted, false).With(x => x.IsSerialized, true)
            .With(x => x.Category, category)
            .With(x => x.BatteryTypes, productBatteryTypes)
            .With(x => x.Colors, productColors)
            .Create();
        var productAttributes = attributes
        .Select(attribute => new ProductAttribute
        {
            ProductId = product.Id,
            Product = product,
            AttributeId = attribute.Id,
            Attribute = attribute,
            IsDeleted = false,
            Value = attribute.Values.ToList()
        }).ToList();
        var stockProduct = _fixture.Build<StockProduct>()
            .With(x => x.IsDeleted, false)
            .With(x => x.Product, product)
            .Create();
        var stockProductItems = _fixture.Build<StockProductItem>()
            .With(x => x.Status, StockProductItemStatus.Available)
            .With(x => x.IsDeleted, false)
            .With(x => x.StockProduct, stockProduct)
            .Without(x => x.SerialNumber)
            .CreateMany().ToList();

        DbContext.ProductCategories.Add(category);
        DbContext.StockProductItems.AddRange(stockProductItems);
        DbContext.BatteryTypes.AddRange(batteryTypes);
        DbContext.Colors.AddRange(colors);
        DbContext.Attributes.AddRange(attributes);
        DbContext.Products.Add(product);
        DbContext.ProductAttributes.AddRange(productAttributes);

        await DbContext.SaveChangesAsync();

        var serialNumbers = _fixture.CreateMany<string>().ToList();

        var command = new AssignProductSerialNumberCommand(
            stockProduct.ProductId, stockProduct.Stock.LocationId, serialNumbers, new List<StockProductItemAttributeDto>
            {
                new(attributes[1].Id, new AttributeValueDto(attributes[1].Values[1].ValueId, "AttributeValue"))
            }, batteryTypes[0].Id, colors[0].Id);

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        Assert.True(DbContext.StockProductItems.Any(x => !string.IsNullOrEmpty(x.SerialNumber)));
    }

    [Fact]
    public async Task Handle_ShouldRespond_ProductNotFound()
    {
        // Arrange
        var command = _fixture.Build<AssignProductSerialNumberCommand>().Create();

        // Act
        var act = async () => await _handler.Handle(command, CancellationToken.None);

        // Assert
        await act.Should().ThrowExactlyAsync<EntityNotFoundException<Product>>();
    }

    [Theory]
    [RecursiveInlineAutoData]
    public async Task Handle_ShouldRespond_DuplicatedSerialNumbers(Product product, AssignProductSerialNumberCommand command)
    {
        // Arrange
        product.Id = command.ProductId;
        product.IsDeleted = false;
        var manufacturer = _fixture.Build<Manufacturer>().With(x => x.IsDeleted, false).Create();
        var category = _fixture.Build<ProductCategory>().With(x => x.IsDeleted, false).Create();
        product.Manufacturer = manufacturer;
        product.Category = category;

        DbContext.Manufacturers.Add(manufacturer);
        DbContext.ProductCategories.Add(category);
        DbContext.Products.Add(product);
        await DbContext.SaveChangesAsync();

        var serialNumbers = _fixture.Create<IEnumerable<string>>();
        _serialNumbersValidator.Setup(x =>
                x.CheckDuplicatedSerialNumbers(It.IsAny<Guid>(), It.IsAny<IEnumerable<string>>(), CancellationToken.None))
            .ThrowsAsync(new DuplicatedSerialNumbersException($"Serial numbers {string.Join(", ", serialNumbers)} are not unique"));

        // Act
        var act = async () => await _handler.Handle(command, CancellationToken.None);

        // Assert
        await act.Should().ThrowExactlyAsync<DuplicatedSerialNumbersException>();
    }
}