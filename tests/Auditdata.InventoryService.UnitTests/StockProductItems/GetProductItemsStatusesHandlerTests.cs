using Auditdata.InventoryService.Core.Features.StockProductItems.Handlers;
using Auditdata.InventoryService.Core.Features.StockProductItems.Queries;
using Auditdata.InventoryService.UnitTests.Common;
using FluentAssertions;

namespace Auditdata.InventoryService.UnitTests.StockProductItems;

public class GetProductItemsStatusesHandlerTests
{
    private readonly IFixture _fixture;
    private readonly GetProductItemsStatusesHandler _handler;
    private readonly Mock<IStockProductItemsRepository> _stockProductItemsRepository;

    public GetProductItemsStatusesHandlerTests()
    {
        _fixture = new RecursiveFixture();
        _stockProductItemsRepository = new Mock<IStockProductItemsRepository>();
        _handler = new GetProductItemsStatusesHandler(_stockProductItemsRepository.Object);
    }

    [Fact]
    public async Task Consume_ShouldReturn_GetProductItemsStatusesResult()
    {
        var stockProductItems = _fixture.Build<StockProductItem>()
            .CreateMany()
            .ToList();
        var stockProductItemIds = stockProductItems.Select(x => x.Id).ToList();

        _stockProductItemsRepository.Setup(x => x.GetByIdsAsync(stockProductItemIds))
            .ReturnsAsync(stockProductItems);

        var result = await _handler.Handle(new GetProductItemsStatusesQuery(stockProductItemIds), CancellationToken.None);

        result.Items.Should().BeSameAs(stockProductItems);
    }
}