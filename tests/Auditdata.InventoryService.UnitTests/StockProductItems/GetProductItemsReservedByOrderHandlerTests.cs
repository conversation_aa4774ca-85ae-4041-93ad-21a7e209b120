using Auditdata.InventoryService.Core.Features.StockProductItems.Handlers;
using Auditdata.InventoryService.Core.Features.StockProductItems.Queries;
using Auditdata.InventoryService.UnitTests.Common;
using FluentAssertions;

namespace Auditdata.InventoryService.UnitTests.StockProductItems;

public class GetProductItemsReservedByOrderHandlerTests
{
    private readonly IFixture _fixture;
    private readonly GetProductItemsReservedByOrderHandler _handler;
    private readonly Mock<IStockProductItemsRepository> _stockProductItemsRepository;

    public GetProductItemsReservedByOrderHandlerTests()
    {
        _fixture = new RecursiveFixture();
        _stockProductItemsRepository = new Mock<IStockProductItemsRepository>();
        _handler = new GetProductItemsReservedByOrderHandler(_stockProductItemsRepository.Object);
    }

    [Fact]
    public async Task Consume_ShouldReturn_ReservedByOrder_ProductItems()
    {
        var locationId = Guid.NewGuid();
        var orderIds = new List<Guid> { Guid.NewGuid(), Guid.NewGuid(), Guid.NewGuid() };
        
        var stockProductItems = _fixture.Build<StockProductItem>()
            .CreateMany()
            .ToList();

        _stockProductItemsRepository.Setup(x => x.GetReservedByOrderAsync(locationId, orderIds, CancellationToken.None))
            .ReturnsAsync(stockProductItems);

        var result = await _handler.Handle(new GetProductItemsReservedByOrderQuery(locationId, orderIds), CancellationToken.None);

        result.StockProductItems.Should().BeEquivalentTo(stockProductItems);
    }
}