using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Abstractions.Azure;
using Auditdata.InventoryService.Core.Abstractions.Validators;
using Auditdata.InventoryService.Core.Entities.Products;
using Auditdata.InventoryService.Core.Features.StockProductItems.Commands;
using Auditdata.InventoryService.Core.Features.StockProductItems.Handlers;
using Auditdata.InventoryService.Core.Features.StockProductItems.Models;
using Auditdata.InventoryService.UnitTests.Common;
using FluentAssertions;
using Microsoft.EntityFrameworkCore;

namespace Auditdata.InventoryService.UnitTests.StockProductItems;

public class CreateStockProductItemHandlerTests : BaseSqlLiteDbContextTest
{
    private readonly Mock<IAzureServiceBusPublisher> _azureServiceBusPublisher = new();
    private readonly Mock<ISerialNumbersValidator> _serialNumbersValidator = new();
    private readonly CreateStockProductItemHandler _sut;
    private readonly IFixture _fixture = new RecursiveFixture();

    public CreateStockProductItemHandlerTests()
    {
        _sut = new CreateStockProductItemHandler(
            DbContext,
            _azureServiceBusPublisher.Object,
            _serialNumbersValidator.Object,
            new Mock<IEventPublisher>().Object);
    }

    [Fact]
    public async Task Handle_Should_CreateAndLogHistory_When_CreateSingleStockProductItem()
    {
        // Arrange
        var stockProductId = Guid.NewGuid();
        var productId = Guid.NewGuid();
        var locationId = Guid.NewGuid();
        var request = new CreateStockProductItemsCommand
        (
            new List<CreateStockProductItem>
            {
                new(stockProductId, "SN123", Guid.NewGuid().ToString())
            }
        );

        var stock = Stock.New("stock", locationId, null, null);

        var category = _fixture.Build<ProductCategory>().Create();
        var manufacturer = Manufacturer.Create("Test");
        var product = new Product
        {
            Id = productId,
            Name = "product",
            Manufacturer = manufacturer,
            Category = category,
            IsSerialized = true
        };
        var stockProduct = new StockProduct
        {
            Id = stockProductId,
            ProductId = productId,
            Stock =  stock,
        };

        DbContext.Set<Stock>().Add(stock);
        DbContext.Set<Product>().Add(product);
        DbContext.Set<StockProduct>().Add(stockProduct);
        await DbContext.SaveChangesAsync();

        // Act
        var result = await _sut.Handle(request, default);

        // Assert
        result.Should().HaveCount(1);
        result.First().StockProductItemId.Should().NotBeEmpty();

        var createdItem = await DbContext.StockProductItems.FindAsync(result.First().StockProductItemId);
        createdItem.Should().NotBeNull();
        createdItem.Status.Should().Be(StockProductItemStatus.Available);
    }

    [Fact]
    public async Task Handle_Should_CreateAndLogHistories_When_CreateMultipleStockProductItems()
    {
        // Arrange
        var stockProductId = Guid.NewGuid();
        var productId = Guid.NewGuid();
        var locationId = Guid.NewGuid();

        var request = new CreateStockProductItemsCommand
        (
            new List<CreateStockProductItem>
            {
                new(stockProductId, "SN123", Guid.NewGuid().ToString()),
                new(stockProductId, "SN124", Guid.NewGuid().ToString())
            }
        );
        
        var stock = Stock.New("name", locationId, null, null);

        var category = _fixture.Build<ProductCategory>().Create();
        var manufacturer = Manufacturer.Create("Test");
        var product = new Product
        {
            Id = productId,
            Name = "product",
            Manufacturer = manufacturer,
            Category = category,
            IsSerialized = true
        };
        var stockProduct = new StockProduct
        {
            Id = stockProductId,
            ProductId = productId,
            Stock = stock,
        };

        DbContext.Set<Stock>().Add(stock);
        DbContext.Set<Product>().Add(product);
        DbContext.Set<StockProduct>().Add(stockProduct);
        await DbContext.SaveChangesAsync();

        // Act
        var result = await _sut.Handle(request, default);

        // Assert
        result.Should().HaveCount(2);
        result.All(r => r.StockProductItemId != Guid.Empty).Should().BeTrue();

        var stockProductItemIds = result.Select(r => r.StockProductItemId);
        var createdItems = await DbContext.StockProductItems
            .Where(item => stockProductItemIds.Contains(item.Id))
            .ToListAsync();
        createdItems.Should().HaveCount(2);
        createdItems.All(item => item.Status == StockProductItemStatus.Available).Should().BeTrue();
    }

    [Fact]
    public async Task Handle_Should_ThrowException_When_DuplicateSerialNumber()
    {
        // Arrange
        var stock = _fixture.Create<Stock>();
        var stockProductId = Guid.NewGuid();
        var productId = Guid.NewGuid();
        var serialNumber = "SN123";
        var request = new CreateStockProductItemsCommand
        (
            new List<CreateStockProductItem>
            {
                new(stockProductId, serialNumber, Guid.NewGuid().ToString())
            }
        );

        var category = _fixture.Build<ProductCategory>().Create();
        var manufacturer = Manufacturer.Create("Test");
        var product = new Product
        {
            Id = productId,
            Name = "product",
            Manufacturer = manufacturer,
            Category = category,
        };
        var stockProduct = new StockProduct
        {
            Id = stockProductId,
            ProductId = productId,
            Stock = stock,
        };

        var existingItem = new StockProductItem
        {
            StockProductId = stockProductId,
            SerialNumber = serialNumber,
        };

        DbContext.Set<Product>().Add(product);
        DbContext.Set<StockProduct>().Add(stockProduct);
        DbContext.Set<StockProductItem>().Add(existingItem);
        await DbContext.SaveChangesAsync();

        _serialNumbersValidator.Setup(validator =>
                validator.CheckDuplicatedSerialNumbers(It.IsAny<Guid>(), It.IsAny<string[]>(), default))
            .Throws(new DuplicatedSerialNumbersException("Serial number already exists"));

        // Act
        // Assert
        await Assert.ThrowsAsync<DuplicatedSerialNumbersException>(async () => await _sut.Handle(request, default));
    }
}
