using Auditdata.InventoryService.UnitTests.Common;
using FluentAssertions;
using Auditdata.InventoryService.Core.Entities.Products;
using Auditdata.InventoryService.Core.Features.StockProductItems.Handlers;
using Auditdata.InventoryService.Core.Features.StockProductItems.Queries;
using Auditdata.InventoryService.Core.Exceptions;

namespace Auditdata.InventoryService.UnitTests.StockProductItems
{
    public class GetStockProductItemByIdHandlerTests : BaseSqlLiteDbContextTest
    {
        private readonly GetStockProductItemByIdHandler _sut;
        private readonly IFixture _fixture = new RecursiveFixture();

        public GetStockProductItemByIdHandlerTests()
        {
            _sut = new GetStockProductItemByIdHandler(DbContext);
        }

        [Fact]
        public async Task Handle_ShouldReturnItemWithRelatedEntities_WhenItemExists()
        {
            // Arrange
            var locationId = Guid.NewGuid();
            var productId = Guid.NewGuid();
            var serialNumber = "SN123";
            var itemId = Guid.NewGuid();

            var batteryType = _fixture.Build<BatteryType>()
                                      .With(bt => bt.Id, Guid.NewGuid())
                                      .With(bt => bt.Name, "AA")
                                      .Create();

            var color = _fixture.Build<Color>()
                                .With(c => c.Id, Guid.NewGuid())
                                .With(c => c.Name, "Red")
                                .Create();

            var stock = new Stock
            {
                Id = Guid.NewGuid(),
                LocationId = locationId,
                Name = "Main Stock"
            };

            var category = _fixture.Build<ProductCategory>()
                                   .With(pc => pc.Id, Guid.NewGuid())
                                   .With(pc => pc.IsDeleted, false)
                                   .With(pc => pc.Code, ProductCategoryCode.HearingAids)
                                   .Create();

            var manufacturer = Manufacturer.Create("Test");

            var product = new Product
            {
                Id = productId,
                Name = "product",
                Manufacturer = manufacturer,
                Category = category,
                IsSerialized = true,
                Colors = [ProductColor.Create(productId, color.Id)],
                BatteryTypes = [ProductBatteryType.Create(batteryType.Id)]
            };

            var stockProduct = new StockProduct
            {
                Id = Guid.NewGuid(),
                Stock = stock,
                Product = product,
                ProductId = productId
            };

            var stockProductItem = new StockProductItem
            {
                Id = itemId,
                StockProduct = stockProduct,
                BatteryType = batteryType,
                Color = color,
                SerialNumber = serialNumber
            };

            DbContext.Set<BatteryType>().Add(batteryType);
            DbContext.Set<Color>().Add(color);
            DbContext.Set<Stock>().Add(stock);
            DbContext.Set<ProductCategory>().Add(category);
            DbContext.Set<Manufacturer>().Add(manufacturer);
            DbContext.Set<Product>().Add(product);
            DbContext.Set<StockProduct>().Add(stockProduct);
            DbContext.Set<StockProductItem>().Add(stockProductItem);

            await DbContext.SaveChangesAsync();

            // Act
            var result = await _sut.Handle(new GetStockProductItemByIdQuery(itemId), CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.Id.Should().Be(itemId);

            result.BatteryType.Should().NotBeNull();
            result.BatteryType.Id.Should().Be(batteryType.Id);
            result.BatteryType.Name.Should().Be("AA");

            result.Color.Should().NotBeNull();
            result.Color.Id.Should().Be(color.Id);
            result.Color.Name.Should().Be("Red");

            result.StockProduct.Should().NotBeNull();
            result.StockProduct.Id.Should().Be(stockProduct.Id);

            result.StockProduct.Stock.Should().NotBeNull();
            result.StockProduct.Stock.Id.Should().Be(stock.Id);
            result.StockProduct.Stock.Name.Should().Be("Main Stock");
        }

        [Fact]
        public async Task Handle_ShouldThrowEntityNotFoundException_WhenItemDoesNotExist()
        {
            // Arrange
            var nonExistentId = Guid.NewGuid();

            // Act
            Func<Task> act = () => _sut.Handle(new GetStockProductItemByIdQuery(nonExistentId), CancellationToken.None);

            // Assert
            await act.Should().ThrowAsync<EntityNotFoundException<StockProductItem>>();
        }
    }
}
