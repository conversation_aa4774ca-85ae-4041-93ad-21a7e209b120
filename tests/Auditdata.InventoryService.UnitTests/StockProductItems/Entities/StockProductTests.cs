using Auditdata.InventoryService.Core.Constants;
using Auditdata.InventoryService.Core.Entities.Products;
using Auditdata.InventoryService.Core.Features.StockProductItems.Dtos;
using Auditdata.InventoryService.UnitTests.Common;
using Auditdata.Transport.Contracts.Exceptions;
using FluentAssertions;

namespace Auditdata.InventoryService.UnitTests.StockProductItems.Entities;

public class StockProductTests
{
    
    private readonly IFixture _fixture = new RecursiveFixture();

    [Fact]
    public void AssignSerialNumbers_Should_AssignProperties_When_CommandProvided()
    {
        // Arrange
        var serialNumber = "serialNumber";
        var category = _fixture.Build<ProductCategory>().With(x => x.Code, ProductCategoryCode.HearingAids).Create();
        var stockProductItems = _fixture.Build<StockProductItem>()
            .With(x => x.Status, StockProductItemStatus.Available)
            .Without(x => x.SerialNumber)
            .CreateMany().ToList();
        var stockProduct = _fixture.Build<StockProduct>()
            .With(x => x.StockProductItems, stockProductItems)
            .Create();
        var attributes = _fixture.Build<Core.Entities.Attribute>()
            .With(x => x.IsDeleted, false)
            .With(x=> x.Values)
            .With(x => x.ProductCategories, new List<ProductCategory> { category })
            .CreateMany().ToList();
        var batteryTypes = _fixture.Build<BatteryType>()
            .With(x => x.IsDeleted, false)
            .CreateMany().ToList();
        var colors = _fixture.Build<Color>()
            .With(x => x.IsDeleted, false)
            .CreateMany().ToList();
        var productAttributes = attributes
            .Select(x => new ProductAttribute { Value = x.Values, AttributeId = x.Id, Attribute = x, IsDeleted = false }).ToList();
        var productBatteryTypes = batteryTypes
            .Select(x => new ProductBatteryType { BatteryTypeId = x.Id, BatteryType = x }).ToList();
        var productColors = colors
            .Select(x => new ProductColor { ColorId = x.Id, Color = x }).ToList();
        var product = _fixture.Build<Product>()
            .With(x => x.StockProducts, new List<StockProduct> { stockProduct })
            .With(x => x.Category, category)
            .With(x => x.Attributes, productAttributes)
            .With(x => x.BatteryTypes, productBatteryTypes)
            .With(x=> x.IsSerialized, true)
            .With(x => x.Colors, productColors)
            .Create();
        stockProduct.Product = product;
        var attributeValue = _fixture.Build<AttributeValueDto>()
            .With(x => x.Id, product.Attributes.First().Attribute!.Values[0].ValueId)
            .Create();
        var attributeDto = _fixture.Build<StockProductItemAttributeDto>()
            .With(x => x.AttributeId, product.Attributes.First().AttributeId)
            .With(x => x.Value, attributeValue)
            .Create();
        stockProductItems.ForEach(x => x.StockProduct = stockProduct);
        
        var assignedStockProductItems = stockProduct.AssignSerialNumbers(new List<string>() { serialNumber}, product.Colors.First().Color,
             product.BatteryTypes.First().BatteryType
            , new List<StockProductItemAttributeDto> { attributeDto });

       assignedStockProductItems.Count.Should().Be(1);
       assignedStockProductItems[0].SerialNumber.Should().Be(serialNumber);

    }

    [Fact]
    public void AssignSerialNumbers_Should_Throw_NotEnoughStockProductItemsAvailable_When_NotEnoughAvailableWithNoSerialNumber()
    {
        // Arrange
        var category = _fixture.Build<ProductCategory>().With(x => x.Code, ProductCategoryCode.HearingAids).Create();
        var stockProductItems = _fixture.Build<StockProductItem>()
            .With(x => x.Status, StockProductItemStatus.Available)
            .Without(x => x.SerialNumber)
            .CreateMany().ToList();
        var stockProduct = _fixture.Build<StockProduct>()
            .With(x => x.StockProductItems, stockProductItems)
            .Create();
        var product = _fixture.Build<Product>()
            .With(x => x.StockProducts, new List<StockProduct> { stockProduct })
            .With(x => x.Category, category)
            .With(x=> x.IsSerialized,true)
            .Create();
        stockProduct.Product = product;
        var attributeValue = _fixture.Build<AttributeValueDto>()
            .With(x => x.Id, product.Attributes.First().Attribute!.Values[0].ValueId)
            .Create();
        var attributeDto = _fixture.Build<StockProductItemAttributeDto>()
            .With(x => x.AttributeId, product.Attributes.First().AttributeId)
            .With(x => x.Value, attributeValue)
            .Create();
        stockProductItems.ForEach(x => x.StockProduct = stockProduct);

        var serialNumbers = _fixture.CreateMany<string>(6);

        // Act
         var act = () => stockProduct.AssignSerialNumbers(serialNumbers.ToList(),
             null, null, new List<StockProductItemAttributeDto>());

        // Assert
        act.Should().Throw<BusinessException>().Which.ErrorCode.Should().Be(ErrorCodes.NotEnoughStockProductItemsAvailable);
    }

    [Fact]
    public void AdjustSerializedItems_ShouldThrow_BusinessException_IfProduct_NotSerialized()
    {
        // Arrange
        var stockProduct = new StockProduct
        {
            Product = new Product { IsSerialized = false }
        };
        
        // Act
        var action = () => stockProduct.AdjustSerializedItems(
            new List<string>(), null, null, new List<StockProductItemAttributeDto>());
        
        // Assert
        action.Should().Throw<BusinessException>().Which.ErrorCode.Should().Be(ErrorCodes.ProductNotSerialized);
    }

    [Fact]
    public void AdjustSerializedItems_ShouldCreate_NewItems_AddIncrease_Quantity()
    {
        var category = _fixture.Build<ProductCategory>()
            .With(x => x.Code, ProductCategoryCode.HearingAids).Create();
        var stockProductItems = _fixture.Build<StockProductItem>()
            .With(x => x.Status, StockProductItemStatus.Available)
            .Without(x => x.SerialNumber)
            .CreateMany().ToList();
        var stockProduct = _fixture.Build<StockProduct>()
            .With(x => x.StockProductItems, stockProductItems)
            .Create();
        var attributes = _fixture.Build<Core.Entities.Attribute>()
            .With(x => x.IsDeleted, false)
            .With(x=> x.Values)
            .With(x => x.ProductCategories, new List<ProductCategory> { category })
            .CreateMany().ToList();
        var batteryTypes = _fixture.Build<BatteryType>()
            .With(x => x.IsDeleted, false)
            .CreateMany().ToList();
        var colors = _fixture.Build<Color>()
            .With(x => x.IsDeleted, false)
            .CreateMany().ToList();
        var productAttributes = attributes
            .Select(x => new ProductAttribute { Value = x.Values, AttributeId = x.Id, Attribute = x, IsDeleted = false }).ToList();
        var productBatteryTypes = batteryTypes
            .Select(x => new ProductBatteryType { BatteryTypeId = x.Id, BatteryType = x }).ToList();
        var productColors = colors
            .Select(x => new ProductColor { ColorId = x.Id, Color = x }).ToList();
        var product = _fixture.Build<Product>()
            .With(x => x.StockProducts, new List<StockProduct> { stockProduct })
            .With(x => x.Category, category)
            .With(x => x.Attributes, productAttributes)
            .With(x => x.BatteryTypes, productBatteryTypes)
            .With(x=> x.IsSerialized, true)
            .With(x => x.Colors, productColors)
            .Create();
        stockProduct.Product = product;
        var attributeValue = _fixture.Build<AttributeValueDto>()
            .With(x => x.Id, product.Attributes.First().Attribute!.Values[0].ValueId)
            .Create();
        var attributeDto = _fixture.Build<StockProductItemAttributeDto>()
            .With(x => x.AttributeId, product.Attributes.First().AttributeId)
            .With(x => x.Value, attributeValue)
            .Create();
        stockProductItems.ForEach(x => x.StockProduct = stockProduct);

        var newSerialNumbers = new List<string> { "test1", "test2" };
        var color = colors[0];
        var batteryType = batteryTypes[0];
        
        // Act
        var items = stockProduct.AdjustSerializedItems(
            newSerialNumbers, color, batteryType, new List<StockProductItemAttributeDto>
            {
                attributeDto
            });
        
        // Assert
        items.Should().HaveCount(2);
        items.Should().Match(x => x.All(y => y.Status == StockProductItemStatus.Available));
        items.Should().Contain(x => x.SerialNumber == newSerialNumbers[0]);
        items.Should().Contain(x => x.SerialNumber == newSerialNumbers[1]);
    }
}