using Auditdata.Infrastructure.Dictionary.Exceptions;
using Auditdata.InventoryService.Contracts.Requests.Colors;
using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Entities.Products;
using Auditdata.InventoryService.Dictionaries.Colors;
using Auditdata.InventoryService.Dictionaries.Constants;
using Auditdata.InventoryService.UnitTests.Common;
using FluentAssertions;
using Microsoft.Extensions.Logging.Abstractions;

namespace Auditdata.InventoryService.UnitTests.Dictionaries;

public class ColorsServiceTests : BaseDbContextTest
{
    private readonly ColorsService _sut;

    public ColorsServiceTests()
    {
        _sut = new ColorsService(DbContext, new Mock<IEventPublisher>().Object, new NullLogger<ColorsService>());
    }
    
    [Fact]
    public async Task Create_Update_ShouldThrow_ValidationException_IfName_IsEmpty()
    {
        //Arrange
        var request = new ColorRequest();

        //Act
        var createAction = async() => await _sut.Create(request);
        var updateAction = async() => await _sut.Update(Guid.Empty, request);
        
        //Assert
        await createAction.Should().ThrowAsync<DictionaryValidationException<ColorRequest>>();
        await updateAction.Should().ThrowAsync<DictionaryValidationException<ColorRequest>>();
    }
    
    [Fact]
    public async Task Create_Update_ShouldThrow_ValidationException_IfName_Length_Overflow()
    {
        //Arrange
        var request = new ColorRequest
        {
            Name = new string('-', 101)
        };

        //Act
        var createAction = async() => await _sut.Create(request);
        var updateAction = async() => await _sut.Update(Guid.Empty, request);
        
        //Assert
        await createAction.Should().ThrowAsync<DictionaryValidationException<ColorRequest>>();
        await updateAction.Should().ThrowAsync<DictionaryValidationException<ColorRequest>>();
    }

    [Fact]
    public async Task Create_Update_ShouldThrow_ValidationException_IfDuplicateName_Found()
    {
        //Arrange
        var colorId = Guid.NewGuid();
        var existingProductColor = new Color
        {
            Id = colorId,
            IsActive = true,
            Name = "Test"
        };
        var colorRequest = new ColorRequest
        {
            Id = new Guid(),
            Name = "Test"
        };
        DbContext.Colors.Add(existingProductColor);
        await DbContext.SaveChangesAsync();

        //Act
        var createAction = async() => await _sut.Create(colorRequest);
        var updateAction = async() => await _sut.Update(Guid.Empty, colorRequest);
        
        //Assert
        await createAction.Should().ThrowAsync<DictionaryValidationException<ColorRequest>>();
        await updateAction.Should().ThrowAsync<DictionaryValidationException<ColorRequest>>();
    }

    [Theory]
    [RecursiveInlineAutoData]
    public async Task Update_ShouldThrow_ValidationException_Color_InUse(Product product)
    {
        //Arrange
        var colorId = Guid.NewGuid();
        var request = new ColorRequest
        {
            Id = colorId,
            Name = "TestUpdated",
            IsActive = false,
        };
        var existingColor = new Color
        {
            Id = colorId,
            IsActive = true,
            Name = "Test"
        };

        product.IsDeleted = false;
        product.Colors.Add(new ProductColor()
        {
            ColorId = colorId
        });

        DbContext.Colors.Add(existingColor);
        DbContext.Products.Add(product);
        await DbContext.SaveChangesAsync();
        
        //Act
        var updateAction = async () => await _sut.Update(colorId, request);

        //Assert
        await updateAction.Should().ThrowAsync<DictionaryValidationException<ColorRequest>>()
            .Where(x => x.Errors.Any(failure => failure.ErrorCode == ValidatorErrorCodes.Colors.CantUpdate)); ;
    }
    [Theory]
    [RecursiveInlineAutoData]
    public async Task Delete_ShouldThrow_ValidationException_Color_InUse(Product product)
    {
        //Arrange
        var colorId = Guid.NewGuid();
        var existingColor = new Color
        {
            Id = colorId,
            IsActive = true,
            Name = "Test"
        };

        product.IsDeleted = false;
        product.Colors.Add(new ProductColor()
        {
            ColorId = colorId
        });

        DbContext.Colors.Add(existingColor);
        DbContext.Products.Add(product);
        await DbContext.SaveChangesAsync();

        //Act
        var deleteAction = async () => await _sut.Delete(colorId);

        //Assert
        await deleteAction.Should().ThrowAsync<DictionaryValidationException<ColorRequest>>()
            .Where(x => x.Errors.Any(failure => failure.ErrorCode == ValidatorErrorCodes.Colors.CantDelete)); ; ;
    }
    [Theory]
    [RecursiveInlineAutoData]
    public async Task Update_ShouldNotThrow_ValidationException_Color_InUse(Product product)
    {
        //Arrange
        var colorId = Guid.NewGuid();
        var request = new ColorRequest
        {
            Id = colorId,
            Name = "TestUpdated",
            IsActive = false,
        };
        var existingColor = new Color
        {
            Id = colorId,
            IsActive = true,
            Name = "Test"
        };

        DbContext.Colors.Add(existingColor);
        await DbContext.SaveChangesAsync();

        //Act
        var updateAction = async () => await _sut.Update(colorId, request);

        //Assert
        await updateAction.Should().NotThrowAsync<DictionaryValidationException<ColorRequest>>();
    }
    [Theory]
    [RecursiveInlineAutoData]
    public async Task Create_ShouldNotThrow_ValidationException_Color_InUse(Product product)
    {
        //Arrange
        var request = new ColorRequest
        {
            Id = new Guid(),
            Name = "Test",
            IsActive = false,
        };

        //Act
        var createAction = async () => await _sut.Create(request);

        //Assert
        await createAction.Should().NotThrowAsync<DictionaryValidationException<ColorRequest>>();
    }
    [Theory]
    [RecursiveInlineAutoData]
    public async Task Delete_ShouldNotThrow_ValidationException_Color_InUse(Product product)
    {
        //Arrange
        var colorId = new Guid();
        var request = new ColorRequest
        {
            Id = colorId,
            Name = "Test",
            IsActive = false,
        };
        var existingColor = new Color
        {
            Id = colorId,
            IsActive = true,
            Name = "Test"
        };

        DbContext.Colors.Add(existingColor);
        await DbContext.SaveChangesAsync();

        //Act
        var deleteAction = async () => await _sut.Delete(colorId);

        //Assert
        await deleteAction.Should().NotThrowAsync<DictionaryValidationException<ColorRequest>>();
    }
}
