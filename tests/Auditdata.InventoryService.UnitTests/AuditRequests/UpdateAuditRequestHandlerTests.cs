using Auditdata.InventoryService.Core.Entities.AuditRequests;
using Auditdata.InventoryService.Core.Entities.Products;
using Auditdata.InventoryService.Core.Features.AuditRequests.Update;
using Auditdata.InventoryService.Core.Features.AuditRequests.Models;
using Auditdata.InventoryService.UnitTests.Common;
using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using Auditdata.InventoryService.Core.Features.AuditRequests.Services;
using Auditdata.InventoryService.UnitTests.Common.Mocks;
using Auditdata.Transport.Contracts.Exceptions;
using MediatR;
using Auditdata.InventoryService.Core.Constants;

namespace Auditdata.InventoryService.UnitTests.AuditRequests;

public class UpdateAuditRequestHandlerTests : BaseDbContextTest
{
    private readonly UpdateAuditRequestHandler _sut;
    private readonly Mock<IMediator> _mediatorMock = new();

    public UpdateAuditRequestHandlerTests()
    {
        var auditRequestService = new AuditRequestService(DbContext, new OperationContextMock());
        _sut = new UpdateAuditRequestHandler(
            DbContext, auditRequestService, _mediatorMock.Object);
    }

    private async Task<AuditRequest> SetupAuditRequestAsync(
        AuditRequestStatus status, bool addNotCalculated = false, bool addInOtheLocation = false)
    {
        var manufacturerId = Guid.NewGuid();
        var supplier = new Supplier
        {
            Id = Guid.NewGuid(),
            Name = "test-supplier",
            PhoneNumber = "************",
            Address1 = "Address",
            State = "State",
            City = "City",
            IsDeleted = false
        };

        var manufacturer = new Manufacturer
        {
            Id = manufacturerId,
            Name = "ManufacturerName",
            City = "ManufacturerCity",
            PhoneNumber = "ManufacturerPhoneNumber",
            State = "ManufacturerState",
            Address1 = "ManufacturerAddress1"
        };

        var category = new ProductCategory
        {
            Id = Guid.NewGuid(),
            Name = "Test Category",
            IsDeleted = false
        };
        DbContext.ProductCategories.Add(category);

        var product = new Product
        {
            Id = Guid.NewGuid(),
            Name = "test-product",
            SupplierId = supplier.Id,
            Supplier = supplier,
            Category = category,
            CategoryId = category.Id,
            Manufacturer = manufacturer,
            ManufacturerId = manufacturerId,
            ControlledByStock = true,
            IsActive = true,
            IsSerialized = true,
            IsDeleted = false
        };

        var locationId = Guid.NewGuid();
        var stock = new Stock
        {
            Id = Guid.NewGuid(),
            LocationId = locationId,
            Name = "Test Stock"
        };

        var locationId1 = Guid.NewGuid();

        if (addInOtheLocation)
        {
            var stock1 = new Stock
            {
                Id = Guid.NewGuid(),
                LocationId = locationId1,
                Name = "Test Stock1"
            };
            var stockProduct1 = new StockProduct
            {
                Id = Guid.NewGuid(),
                Product = product,
                Stock = stock1,
            };
            stockProduct1.SetQuantity(10);

            var stockProductItem1 = new StockProductItem
            {
                Id = Guid.NewGuid(),
                Status = StockProductItemStatus.Available,
                StockProduct = stockProduct1,
                LnDOrderId = Guid.NewGuid(),
                LnDInLocationId = locationId1,
                SerialNumber = "SN123"
            };

            DbContext.Stocks.Add(stock1);
            DbContext.StockProducts.Add(stockProduct1);
            DbContext.StockProductItems.Add(stockProductItem1);
        }

        if (addNotCalculated)
        {
            var product1 = new Product
            {
                Id = Guid.NewGuid(),
                Name = "dzg",
                SupplierId = supplier.Id,
                Supplier = supplier,
                Category = category,
                CategoryId = category.Id,
                Manufacturer = manufacturer,
                ManufacturerId = manufacturerId,
                ControlledByStock = true,
                IsActive = true,
                IsSerialized = true,
                IsDeleted = false
            };

            var stockProduct1 = new StockProduct
            {
                Id = Guid.NewGuid(),
                Product = product1,
                Stock = stock,
            };
            stockProduct1.SetQuantity(10);

            var stockProductItem1 = new StockProductItem
            {
                Id = Guid.NewGuid(),
                Status = StockProductItemStatus.Available,
                StockProduct = stockProduct1,
                LnDOrderId = Guid.NewGuid(),
                LnDInLocationId = locationId1,
                SerialNumber = "SN123"
            };

            DbContext.Products.Add(product1);
            DbContext.StockProducts.Add(stockProduct1);
            DbContext.StockProductItems.Add(stockProductItem1);
        }

        var auditRequest = AuditRequest.Create(
            status,
            DateTime.UtcNow,
            "Initial Notes",
            false, false, false, true);
        auditRequest.AuditLocations.Add(AuditLocation.Create(locationId, stock.Name, auditRequest));

        var auditProduct = AuditProduct.Create(product.Id, auditRequest, false, true, [])
            with
        { Product = product };
        auditProduct.SerialNumbers.Add(
            AuditProductSerialNumber.Create("SN124", auditProduct, AuditProductSNStatus.Unverified, true, true, ""));

        auditRequest.AuditProducts.Add(auditProduct);

        DbContext.ProductCategories.Add(category);
        DbContext.Manufacturers.Add(manufacturer);
        DbContext.Suppliers.Add(supplier);
        DbContext.Stocks.Add(stock);
        DbContext.Products.Add(product);
        DbContext.AuditRequests.Add(auditRequest);
        DbContext.AuditProducts.AddRange(auditRequest.AuditProducts);
        DbContext.AuditProductSerialNumbers.AddRange(auditProduct.SerialNumbers);
        DbContext.AuditLocations.AddRange(auditRequest.AuditLocations);

        await DbContext.SaveChangesAsync();

        return auditRequest;
    }

    [Fact]
    public async Task Handle_ShouldUpdateDraft_WhenStatusIsDraft()
    {
        // Arrange
        var auditRequest = await SetupAuditRequestAsync(AuditRequestStatus.Draft);

        var request = new UpdateAuditRequestCommand
        {
            AuditRequestId = auditRequest.Id,
            Products = new List<AuditProductDto>
            {
                new() { ProductId = auditRequest.AuditProducts.First().ProductId, SerialNumbers = [] }
            },
            Locations = new List<AuditLocationDto> { new AuditLocationDto(auditRequest.AuditLocations.First().LocationId) },
            DateTimeDue = DateTime.UtcNow.AddDays(1),
            Notes = "Updated Notes"
        };

        // Act
        await _sut.Handle(request, CancellationToken.None);

        // Assert
        var updatedAuditRequest = await DbContext.AuditRequests.FirstOrDefaultAsync();
        updatedAuditRequest.Should().NotBeNull();
        updatedAuditRequest!.Status.Should().Be(AuditRequestStatus.Draft);
        updatedAuditRequest.Notes.Should().Be("Updated Notes");
        updatedAuditRequest.DateTimeDue.Should().BeCloseTo(request.DateTimeDue, TimeSpan.FromSeconds(1));
    }

    [Fact]
    public async Task Handle_ShouldUpdateReadyForCalculation_WhenStatusIsReadyForCalculation()
    {
        // Arrange
        var auditRequest = await SetupAuditRequestAsync(AuditRequestStatus.ReadyForCalculation);

        var request = new UpdateAuditRequestCommand
        {
            AuditRequestId = auditRequest.Id,
            Products = new List<AuditProductDto>
            {
                new() { ProductId = auditRequest.AuditProducts.First().ProductId, SerialNumbers = new List<AuditProductSerialNumberDto>() }
            },
            Locations = new List<AuditLocationDto> { new AuditLocationDto(auditRequest.AuditLocations.First().LocationId) },
            DateTimeDue = DateTime.UtcNow.AddDays(2),
            Notes = "Updated Notes for ReadyForCalculation"
        };

        // Act
        await _sut.Handle(request, CancellationToken.None);

        // Assert
        var updatedAuditRequest = await DbContext.AuditRequests.FirstOrDefaultAsync();
        updatedAuditRequest.Should().NotBeNull();
        updatedAuditRequest!.Status.Should().Be(AuditRequestStatus.ReadyForCalculation);
        updatedAuditRequest.Notes.Should().Be("Initial Notes");
    }

    [Fact]
    public async Task Handle_ShouldUpdateFinalized_WhenStatusIsFinalized()
    {
        // Arrange
        var auditRequest = await SetupAuditRequestAsync(AuditRequestStatus.Finalized);

        var request = new UpdateAuditRequestCommand
        {
            AuditRequestId = auditRequest.Id,
            Products =
            [
                new() { ProductId = auditRequest.AuditProducts.First().ProductId, SerialNumbers = [] }
            ],
            Locations = [new(auditRequest.AuditLocations.First().LocationId)],
            DateTimeDue = DateTime.UtcNow.AddDays(3),
            Notes = "Initial Notes"
        };

        // Act
        await _sut.Handle(request, CancellationToken.None);

        // Assert
        var updatedAuditRequest = await DbContext.AuditRequests.FirstOrDefaultAsync();
        updatedAuditRequest.Should().NotBeNull();
        updatedAuditRequest!.Status.Should().Be(AuditRequestStatus.Finalized);
        updatedAuditRequest.Notes.Should().Be("Initial Notes");
    }

    [Fact]
    public async Task Handle_ShouldCheckForDuplicateSerialNumbers()
    {
        // Arrange
        var auditRequest = await SetupAuditRequestAsync(AuditRequestStatus.ReadyForCalculation);

        var request = new UpdateAuditRequestCommand
        {
            AuditRequestId = auditRequest.Id,
            Products = new List<AuditProductDto>
        {
            new()
            {
                ProductId = auditRequest.AuditProducts.First().ProductId,
                SerialNumbers = new List<AuditProductSerialNumberDto>
                {
                    new AuditProductSerialNumberDto("SN123", AuditProductSNStatus.Verified, ""),
                    new AuditProductSerialNumberDto("SN124", AuditProductSNStatus.Unverified, ""),
                    new AuditProductSerialNumberDto("SN124", AuditProductSNStatus.Unverified, ""),
                }
            }
        },
            Locations = new List<AuditLocationDto> { new AuditLocationDto(auditRequest.AuditLocations.First().LocationId) },
            DateTimeDue = DateTime.UtcNow.AddDays(1),
            Notes = "Updated Notes"
        };

        // Act
        var result = async () => await _sut.Handle(request, CancellationToken.None);

        // Assert
        await result.Should().ThrowAsync<DuplicatedSerialNumbersException>();
    }

    [Fact]
    public async Task Handle_ShouldThrowException_WhenUserIsAuditRequestSpecialistUpdatingDraft()
    {
        // Arrange
        var auditRequest = await SetupAuditRequestAsync(AuditRequestStatus.Draft);

        var sut = new UpdateAuditRequestHandler(
            DbContext, new AuditRequestService(
                DbContext, new OperationContextMock { UserAuditRequestManager = false, UserAuditRequestSpecialist = true }),
            _mediatorMock.Object);
        var request = new UpdateAuditRequestCommand
        {
            AuditRequestId = auditRequest.Id,
            Products = new List<AuditProductDto>
            {
                new() { ProductId = auditRequest.AuditProducts.First().ProductId, SerialNumbers = [] }
            },
            Locations = new List<AuditLocationDto> { new AuditLocationDto(auditRequest.AuditLocations.First().LocationId) },
            DateTimeDue = DateTime.UtcNow.AddDays(1),
            Notes = "Updated Notes"
        };

        // Act
        var result = async () => await sut.Handle(request, CancellationToken.None);

        // Assert
        await result.Should().ThrowAsync<BusinessException>()
            .WithMessage("Specialist cannot update Audit request in Draft status");
    }

    [Fact]
    public async Task Handle_ShouldThrowException_WhenSpecialistTryingToRemoveProductNotCreatedByHim()
    {
        // Arrange
        var auditRequest = await SetupAuditRequestAsync(AuditRequestStatus.ReadyForCalculation);

        var sut = new UpdateAuditRequestHandler(
            DbContext,
            new AuditRequestService(
                DbContext, new OperationContextMock
                {
                    UserAuditRequestManager = false,
                    UserAuditRequestSpecialist = true,
                    Login = "stas-test"
                }),
            _mediatorMock.Object);
        var request = new UpdateAuditRequestCommand
        {
            AuditRequestId = auditRequest.Id,
            Locations = [new(auditRequest.AuditLocations.First().LocationId)],
            DateTimeDue = DateTime.UtcNow.AddDays(1),
            Notes = "Updated Notes"
        };

        // Act
        var result = async () => await sut.Handle(request, CancellationToken.None);

        // Assert
        var ex = await result.Should().ThrowAsync<BusinessException>();
        ex.Which.ErrorCode.Should().Be(ErrorCodes.SpecialistCannotDeleteProductAddedByOthers);
    }

    [Fact]
    public async Task Handle_ShouldCheckForDuplicateSerialNumbers_WhenFromNonCalculatedProducts()
    {
        // Arrange
        var auditRequest = await SetupAuditRequestAsync(AuditRequestStatus.ReadyForCalculation, true);

        var request = new UpdateAuditRequestCommand
        {
            AuditRequestId = auditRequest.Id,
            Products = new List<AuditProductDto>
        {
            new()
            {
                ProductId = auditRequest.AuditProducts.First().ProductId,
                SerialNumbers = new List<AuditProductSerialNumberDto>
                {
                    new AuditProductSerialNumberDto("SN123", AuditProductSNStatus.Verified, ""),
                    new AuditProductSerialNumberDto("SN126", AuditProductSNStatus.Unverified, "")
                }
            }
        },
            Locations = new List<AuditLocationDto> { new AuditLocationDto(auditRequest.AuditLocations.First().LocationId) },
            DateTimeDue = DateTime.UtcNow.AddDays(1),
            Notes = "Updated Notes"
        };

        // Act
        var result = async () => await _sut.Handle(request, CancellationToken.None);

        // Assert
        await result.Should().ThrowAsync<DuplicatedSerialNumbersException>()
            .WithMessage("S/N assigned to a different product"); 
    }

    [Fact]
    public async Task Handle_ShouldCheckForDuplicateSerialNumbers_WhenFromOtherStocks()
    {
        // Arrange
        var auditRequest = await SetupAuditRequestAsync(AuditRequestStatus.ReadyForCalculation, false, true);

        var request = new UpdateAuditRequestCommand
        {
            AuditRequestId = auditRequest.Id,
            Products = new List<AuditProductDto>
        {
            new()
            {
                ProductId = auditRequest.AuditProducts.First().ProductId,
                SerialNumbers = new List<AuditProductSerialNumberDto>
                {
                    new AuditProductSerialNumberDto("SN123", AuditProductSNStatus.Verified, ""),
                    new AuditProductSerialNumberDto("SN125", AuditProductSNStatus.Unverified, "")
                }
            }
        },
            Locations = new List<AuditLocationDto> { new AuditLocationDto(auditRequest.AuditLocations.First().LocationId) },
            DateTimeDue = DateTime.UtcNow.AddDays(1),
            Notes = "Updated Notes"
        };

        // Act
        var result = async () => await _sut.Handle(request, CancellationToken.None);

        // Assert
        await result.Should().ThrowAsync<DuplicatedSerialNumbersException>()
            .WithMessage("S/N assigned to another location"); 
    }
}
