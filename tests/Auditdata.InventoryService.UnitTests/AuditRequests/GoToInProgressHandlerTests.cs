using Auditdata.InventoryService.Core.Entities.AuditRequests;
using Auditdata.InventoryService.Core.Entities.Products;
using Auditdata.InventoryService.Core.Features.AuditRequests.GoToInProgess;
using Auditdata.InventoryService.Core.Features.AuditRequests.Services;
using Auditdata.InventoryService.UnitTests.Common;
using Auditdata.InventoryService.UnitTests.Common.Mocks;
using Auditdata.Transport.Contracts.Exceptions;
using FluentAssertions;
using MediatR;

namespace Auditdata.InventoryService.UnitTests.AuditRequests
{
    public class GoToInProgressHandlerTests : BaseSqlLiteDbContextTest
    {
        private readonly GoToInProgressHandler _sut;
        private readonly AuditRequestService _auditRequestService;
        private readonly Mock<IMediator> _mediatorMock = new();

        public GoToInProgressHandlerTests()
        {
            _auditRequestService = new AuditRequestService(DbContext, new OperationContextMock());
            _sut = new GoToInProgressHandler(DbContext, _auditRequestService, _mediatorMock.Object);
        }

        private async Task<AuditRequest> SetupAuditRequestAsync(AuditRequestStatus status)
        {
            var manufacturerId = Guid.NewGuid();
            var supplier = new Supplier
            {
                Id = Guid.NewGuid(),
                Name = "test-supplier",
                PhoneNumber = "************",
                Address1 = "Address",
                State = "State",
                City = "City",
                IsDeleted = false
            };

            var manufacturer = new Manufacturer
            {
                Id = manufacturerId,
                Name = "ManufacturerName",
                City = "ManufacturerCity",
                PhoneNumber = "ManufacturerPhoneNumber",
                State = "ManufacturerState",
                Address1 = "ManufacturerAddress1"
            };

            var category = new ProductCategory
            {
                Id = Guid.NewGuid(),
                Name = "Test Category",
                IsDeleted = false
            };
            DbContext.ProductCategories.Add(category);

            var product = new Product
            {
                Id = Guid.NewGuid(),
                Name = "test-product",
                SupplierId = supplier.Id,
                Supplier = supplier,
                Category = category,
                CategoryId = category.Id,
                Manufacturer = manufacturer,
                ManufacturerId = manufacturerId,
                IsActive = true,
                IsDeleted = false
            };

            var locationId = Guid.NewGuid();
            var stock = new Stock
            {
                Id = Guid.NewGuid(),
                LocationId = locationId,
                Name = "Test Stock"
            };

            var auditRequest = AuditRequest.Create(
                status,
                DateTime.UtcNow,
                "Initial Notes",
                false, false, false, true);
            auditRequest.AuditLocations.Add(AuditLocation.Create(locationId, stock.Name, auditRequest));

            var auditProduct = AuditProduct.Create(product.Id, auditRequest, false, true, [])
                with
            { Product = product };

            auditRequest.AuditProducts.Add(auditProduct);

            DbContext.ProductCategories.Add(category);
            DbContext.Manufacturers.Add(manufacturer);
            DbContext.Suppliers.Add(supplier);
            DbContext.Stocks.Add(stock);
            DbContext.Products.Add(product);
            DbContext.AuditRequests.Add(auditRequest);
            DbContext.AuditProducts.AddRange(auditRequest.AuditProducts);
            DbContext.AuditLocations.AddRange(auditRequest.AuditLocations);

            await DbContext.SaveChangesAsync();

            return auditRequest;
        }

        [Fact]
        public async Task Handle_ShouldChangeStatusToInProgress_WhenStatusIsReadyForCalculation()
        {
            // Arrange
            var auditRequest = await SetupAuditRequestAsync(AuditRequestStatus.ReadyForCalculation);

            // Act
            await _sut.Handle(new GoToInProgressCommand(auditRequest.Id), CancellationToken.None);

            // Assert
            var updatedAuditRequest = await DbContext.AuditRequests.FindAsync(auditRequest.Id);
            updatedAuditRequest.Should().NotBeNull();
            updatedAuditRequest!.Status.Should().Be(AuditRequestStatus.InProgress);
        }

        [Fact]
        public async Task Handle_ShouldThrowBusinessException_WhenStatusIsNotReadyForCalculation()
        {
            // Arrange
            var auditRequest = await SetupAuditRequestAsync(AuditRequestStatus.Draft);

            // Act
            var result = async () => await _sut.Handle(new GoToInProgressCommand(auditRequest.Id), CancellationToken.None);

            // Assert
            await result.Should().ThrowAsync<BusinessException>()
                .WithMessage("Only requests in 'ReadyForCalculation' status can be moved to 'InProgress'.");
        }
    }
}
