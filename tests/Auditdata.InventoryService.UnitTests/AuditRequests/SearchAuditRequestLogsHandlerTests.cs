using Auditdata.InventoryService.Core.Entities.AuditRequests;
using Auditdata.InventoryService.UnitTests.Common.Mocks;
using Auditdata.InventoryService.UnitTests.Common;
using Auditdata.InventoryService.Core.Exceptions;
using FluentAssertions;
using Auditdata.InventoryService.Core.Features.AuditRequests.AddLog;
using Auditdata.InventoryService.Core.Features.AuditRequests.SearchLogs;

namespace Auditdata.InventoryService.UnitTests.AuditRequests;

public class SearchAuditRequestLogsHandlerTests : BaseDbContextTest
{
    private readonly SearchAuditRequestLogsHandler _sut;

    public SearchAuditRequestLogsHandlerTests()
    {
        _sut = new SearchAuditRequestLogsHandler(DbContext, new OperationContextMock() { UserAuditRequestManager = false });
    }

    [Theory]
    [RecursiveInlineAutoData]
    public async Task Handle_Should_GetAuditRequestLogs_When_DraftRequestFound(AuditRequest auditRequest, List<AuditRequestLog> logs)
    {
        // Arrange
        auditRequest.BlindStockCalculation = true;
        DbContext.AuditRequests.Add(auditRequest);
        logs.ForEach(x => x.AuditRequest = auditRequest);
        DbContext.AuditRequestLogs.AddRange(logs);
        await DbContext.SaveChangesAsync();

        var query = new SearchAuditRequestLogsQuery
        {
            AuditRequestId = auditRequest.Id
        };

        // Act
        var result = await _sut.Handle(query, CancellationToken.None);

        // Assert
        result.Logs.ToList()
            .ForEach(x => x.AuditRequestChanges!.AuditProductsChanges
                .ForEach(y => y.Changes
                    .Find(z => z.PropertyName == AddAuditRequestLogHandler.ExpectedLogName).Should().BeNull()));
        logs
            .ForEach(x =>
            {
                x.AuditRequest = null;
                x.AuditRequestChanges!.AuditProductsChanges
                    .ForEach(y => y.Changes.RemoveAll(z => z.PropertyName == AddAuditRequestLogHandler.ExpectedLogName));
            });
        result.Logs.Should().BeEquivalentTo(logs);
    }

    [Fact]
    public async Task Handle_Should_ThrowEntityNotFound_When_RequestNotFound()
    {
        // Arrange
        var query = new SearchAuditRequestLogsQuery
        {
            AuditRequestId = Guid.NewGuid()
        };

        // Act
        var act = async () => await _sut.Handle(query, CancellationToken.None);

        // Assert
        await act.Should().ThrowAsync<EntityNotFoundException<AuditRequest>>();
    }
}
