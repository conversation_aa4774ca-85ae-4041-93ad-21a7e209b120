using Auditdata.InventoryService.Core.Abstractions.Services;
using Auditdata.InventoryService.Core.Entities.AuditRequests;
using Auditdata.InventoryService.Core.Entities.Products;
using Auditdata.InventoryService.Core.Features.AuditRequests.GoToReadyForCalculation;
using Auditdata.InventoryService.Core.Features.Models;
using Auditdata.InventoryService.UnitTests.Common;
using Auditdata.InventoryService.UnitTests.Common.Mocks;
using FluentAssertions;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Auditdata.InventoryService.UnitTests.AuditRequests;

public class CreateReadyForCalculationHandlerTests : BaseSqlLiteDbContextTest
{
    private readonly CreateReadyForCalculationHandler _sut;
    private readonly OperationContextMock _operationContextMock = new();
    private readonly Mock<IMediator> _mediatorMock = new();
    private readonly Mock<IAuditRequestService> _serviceMock = new();
    private readonly Mock<TimeProvider> _timeProviderMock = new();

    public CreateReadyForCalculationHandlerTests()
    {
        _operationContextMock.UserAuditRequestManager = true;
        _sut = new CreateReadyForCalculationHandler(
            DbContext,
            _serviceMock.Object,
            _operationContextMock,
            _mediatorMock.Object,
            _timeProviderMock.Object);
    }

    [Theory]
    [RecursiveInlineAutoData]
    public async Task Handle_Should_CreateAuditRequestInReadyForCalculation_When_CommandReceived(
        Guid locationId, AuditRequest auditRequest, List<AuditProductInfo> auditProductInfos, Product[] products,
        AuditRequestLog draftLog)
    {
        // Arrange
        auditRequest.RequestNumber = null;
        auditRequest.Status = AuditRequestStatus.Draft;
        auditRequest.IncludeOnTrialProducts = true;
        auditRequest.IndicateSerialNumber = true;
        auditRequest.BlindStockCalculation = false;
        auditRequest.AuditLocations.First().LocationId = locationId;
        auditRequest.AuditLocations.First().Stock!.LocationId = locationId;
        draftLog.AuditRequestId = auditRequest.Id;
        draftLog.AuditRequest = auditRequest;
        draftLog.Action = AuditRequestLogActionType.Created;
        auditProductInfos[0].IsSerialized = true;
        auditProductInfos[1].IsSerialized = false;
        auditProductInfos[0].ProductId = products[0].Id;
        auditProductInfos[1].ProductId = products[1].Id;
        auditProductInfos[2].ProductId = products[2].Id;
        DbContext.Products.AddRange(products);
        DbContext.AuditRequests.Add(auditRequest);
        DbContext.AuditRequestLogs.Add(draftLog);
        await DbContext.SaveChangesAsync();
        var command = new CreateReadyForCalculationCommand(auditRequest.Id, locationId);
        _serviceMock.Setup(x => x.GetAuditRequestAsync(auditRequest.Id, It.IsAny<bool>(), It.IsAny<CancellationToken>()))
             .ReturnsAsync(auditRequest);
        _serviceMock.Setup(x => x.GetAuditRequestNumberAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(25);
        _serviceMock.Setup(x => x.GetProductsInfoWithStockAsync(
            It.IsAny<AuditRequest>(), It.IsAny<Guid>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(auditProductInfos);

        // Act
        await _sut.Handle(command, default);

        // Assert
        var newDbContext = GetDbContext();
        var createdAuditRequest = await newDbContext.AuditRequests
            .Include(x => x.AuditProducts).ThenInclude(x => x.SerialNumbers)
            .FirstOrDefaultAsync(x => x.Status == AuditRequestStatus.ReadyForCalculation);
        createdAuditRequest.Should().NotBeNull();
        var auditProduct = createdAuditRequest!.AuditProducts.First(x => x.ProductId == products[0].Id);
        auditProduct.SerialNumbers.Should().NotBeNullOrEmpty();
        auditProduct.SupplierName.Should().NotBeNullOrEmpty();
        var logs = await newDbContext.AuditRequestLogs
            .Where(x => x.AuditRequestId == createdAuditRequest.Id)
            .ToListAsync();
        logs.Should().ContainSingle(x => x.Action == AuditRequestLogActionType.Created);
    }
}
