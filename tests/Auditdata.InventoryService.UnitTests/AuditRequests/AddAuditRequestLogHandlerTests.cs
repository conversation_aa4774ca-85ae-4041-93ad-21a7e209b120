using Auditdata.InventoryService.Core.Entities.AuditRequests;
using Auditdata.InventoryService.UnitTests.Common;
using FluentAssertions;
using Auditdata.InventoryService.Core.Features.AuditRequests.AddLog;
using Microsoft.EntityFrameworkCore;

namespace Auditdata.InventoryService.UnitTests.AuditRequests;

public class AddAuditRequestLogHandlerTests : BaseSqlLiteDbContextTest
{
    private readonly AddAuditRequestLogHandler _sut;

    public AddAuditRequestLogHandlerTests()
    {
        _sut = new AddAuditRequestLogHandler(DbContext);
    }

    [Theory]
    [RecursiveInlineAutoData]
    public async Task Handle_Should_AddAuditRequestLog_When_DraftRequestFound(AuditRequest auditRequest)
    {
        // Arrange
        var firstDbContext = GetDbContext();
        firstDbContext.AddRange(auditRequest.AuditLocations.Select(x => x.Stock!));
        await firstDbContext.SaveChangesAsync();
        foreach (var auditLocation in auditRequest.AuditLocations)
        {
            auditLocation.LocationId = auditLocation.Stock!.LocationId;
            auditLocation.Stock = null;
        }

        auditRequest.Status = AuditRequestStatus.Draft;
        DbContext.AuditRequests.Add(auditRequest);
        var command = new AddAuditRequestLogCommand(AuditRequestLogActionType.Created, auditRequest.Id);

        // Act
        await _sut.Handle(command, CancellationToken.None);
        await DbContext.SaveChangesAsync();

        // Assert
        var savedAuditLog = await GetDbContext().AuditRequestLogs.FirstAsync(x => x.AuditRequestId == auditRequest.Id);
        savedAuditLog.Action.Should().Be(AuditRequestLogActionType.Created);
        savedAuditLog.AuditRequestChanges!.Changes.Should().Contain(x => x.PropertyName == "Locations"
            && !string.IsNullOrWhiteSpace(x.UpdatedValue));
    }

    [Theory]
    [RecursiveInlineAutoData]
    public async Task Handle_Should_AddAuditRequestLog_When_NonDraftRequestsFound(AuditRequest auditRequest)
    {
        // Arrange
        auditRequest.Status = AuditRequestStatus.ReadyForCalculation;
        DbContext.AuditRequests.Add(auditRequest);


        var command = new AddAuditRequestLogCommand(
            AuditRequestLogActionType.Created, auditRequest.Id);

        // Act
        await _sut.Handle(command, CancellationToken.None);
        await DbContext.SaveChangesAsync();

        // Assert
        var savedAuditLogs = await GetDbContext().AuditRequestLogs
            .Where(x => x.AuditRequestId == auditRequest.Id)
            .ToListAsync();
        savedAuditLogs.Should().HaveCount(1);
    }
}
