using Auditdata.InventoryService.Core.Constants;
using Auditdata.InventoryService.Core.Entities.AuditRequests;
using Auditdata.InventoryService.Core.Exceptions;
using Auditdata.InventoryService.Core.Features.AuditRequests.Delete;
using Auditdata.InventoryService.UnitTests.Common;
using Auditdata.Transport.Contracts.Exceptions;
using FluentAssertions;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Auditdata.InventoryService.UnitTests.AuditRequests;

public class DeleteAuditRequestHandlerTests : BaseSqlLiteDbContextTest
{
    private readonly DeleteAuditRequestHandler _sut;
    private readonly Mock<IMediator> _mediatorMock = new();

    public DeleteAuditRequestHandlerTests()
    {
        _sut = new DeleteAuditRequestHandler(DbContext, _mediatorMock.Object);
    }

    [Theory]
    [RecursiveInlineAutoData]
    public async Task Handle_Should_DeleteAuditRequest_When_RequestFound(AuditRequest auditRequest)
    {
        // Arrange
        DbContext.AuditRequests.Add(auditRequest);
        await DbContext.SaveChangesAsync();

        var command = new DeleteAuditRequestCommand(auditRequest.Id);

        // Act
        await _sut.Handle(command, CancellationToken.None);

        // Assert
        var savedRequest = await DbContext.AuditRequests
            .FirstOrDefaultAsync(ar => ar.Id == auditRequest.Id);

        savedRequest.Should().BeNull();
    }

    [Theory]
    [RecursiveInlineAutoData(AuditRequestStatus.InProgress)]
    [RecursiveInlineAutoData(AuditRequestStatus.Finalized)]
    [RecursiveInlineAutoData(AuditRequestStatus.Done)]
    public async Task Handle_Should_ThrowBusinessException_When_RequestIsInWrongStatus(
        AuditRequestStatus status,
        AuditRequest auditRequest)
    {
        // Arrange
        auditRequest.Status = status;
        DbContext.AuditRequests.Add(auditRequest);
        await DbContext.SaveChangesAsync();

        var command = new DeleteAuditRequestCommand(auditRequest.Id);

        // Act
        var act = async () => await _sut.Handle(command, CancellationToken.None);

        // Assert
        var ex = await act.Should().ThrowAsync<BusinessException>();
        ex.Which.ErrorCode.Should().Be(ErrorCodes.CannotBeDeleted);
    }

    [Fact]
    public async Task Handle_Should_ThrowEntityNotFound_When_RequestNotFound()
    {
        // Arrange
        var command = new DeleteAuditRequestCommand(Guid.NewGuid());

        // Act
        var act = async () => await _sut.Handle(command, CancellationToken.None);

        // Assert
        await act.Should().ThrowAsync<EntityNotFoundException<AuditRequest>>();
    }
}
