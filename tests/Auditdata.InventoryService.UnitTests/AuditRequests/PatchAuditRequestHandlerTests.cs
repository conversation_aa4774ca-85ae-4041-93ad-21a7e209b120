using Auditdata.InventoryService.Core.Entities.AuditRequests;
using Auditdata.InventoryService.Core.Entities.Products;
using Auditdata.InventoryService.UnitTests.Common;
using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using Auditdata.InventoryService.Core.Features.AuditRequests.Services;
using Auditdata.InventoryService.UnitTests.Common.Mocks;
using Auditdata.Transport.Contracts.Exceptions;
using MediatR;
using Auditdata.InventoryService.Core.Features.AuditRequests.Patch;
using Morcatko.AspNetCore.JsonMergePatch.NewtonsoftJson.Builders;
using Auditdata.InventoryService.Core.Constants;
using Auditdata.InventoryService.Core.Features.AuditRequests.Models;

namespace Auditdata.InventoryService.UnitTests.AuditRequests;

public class PatchAuditRequestHandlerTests : BaseDbContextTest
{
    private readonly PatchAuditRequestHandler _sut;
    private readonly Mock<IMediator> _mediatorMock = new();
    private readonly PatchBuilder<PatchAuditRequestDto> _patchBuilder = new();

    public PatchAuditRequestHandlerTests()
    {
        var auditRequestService = new AuditRequestService(DbContext, new OperationContextMock { UserAuditRequestManager = true });
        _sut = new PatchAuditRequestHandler(
            DbContext, auditRequestService, _mediatorMock.Object);
    }

    private async Task<AuditRequest> SetupAuditRequestAsync(
        AuditRequestStatus status, bool addNotCalculated = false, bool addInOtheLocation = false)
    {
        var manufacturerId = Guid.NewGuid();
        var supplier = new Supplier
        {
            Id = Guid.NewGuid(),
            Name = "test-supplier",
            PhoneNumber = "************",
            Address1 = "Address",
            State = "State",
            City = "City",
            IsDeleted = false
        };

        var manufacturer = new Manufacturer
        {
            Id = manufacturerId,
            Name = "ManufacturerName",
            City = "ManufacturerCity",
            PhoneNumber = "ManufacturerPhoneNumber",
            State = "ManufacturerState",
            Address1 = "ManufacturerAddress1"
        };

        var category = new ProductCategory
        {
            Id = Guid.NewGuid(),
            Name = "Test Category",
            IsDeleted = false
        };
        DbContext.ProductCategories.Add(category);

        var product = new Product
        {
            Id = Guid.NewGuid(),
            Name = "test-product",
            SupplierId = supplier.Id,
            Supplier = supplier,
            Category = category,
            CategoryId = category.Id,
            Manufacturer = manufacturer,
            ManufacturerId = manufacturerId,
            ControlledByStock = true,
            IsActive = true,
            IsSerialized = true,
            IsDeleted = false
        };

        var locationId = Guid.NewGuid();
        var stock = new Stock
        {
            Id = Guid.NewGuid(),
            LocationId = locationId,
            Name = "Test Stock"
        };

        var locationId1 = Guid.NewGuid();

        if (addInOtheLocation)
        {
            var stock1 = new Stock
            {
                Id = Guid.NewGuid(),
                LocationId = locationId1,
                Name = "Test Stock1"
            };
            var stockProduct1 = new StockProduct
            {
                Id = Guid.NewGuid(),
                Product = product,
                Stock = stock1,
            };
            stockProduct1.SetQuantity(10);

            var stockProductItem1 = new StockProductItem
            {
                Id = Guid.NewGuid(),
                Status = StockProductItemStatus.Available,
                StockProduct = stockProduct1,
                LnDOrderId = Guid.NewGuid(),
                LnDInLocationId = locationId1,
                SerialNumber = "SN123"
            };

            DbContext.Stocks.Add(stock1);
            DbContext.StockProducts.Add(stockProduct1);
            DbContext.StockProductItems.Add(stockProductItem1);
        }

        if (addNotCalculated)
        {
            var product1 = new Product
            {
                Id = Guid.NewGuid(),
                Name = "dzg",
                SupplierId = supplier.Id,
                Supplier = supplier,
                Category = category,
                CategoryId = category.Id,
                Manufacturer = manufacturer,
                ManufacturerId = manufacturerId,
                ControlledByStock = true,
                IsActive = true,
                IsSerialized = true,
                IsDeleted = false
            };

            var stockProduct1 = new StockProduct
            {
                Id = Guid.NewGuid(),
                Product = product1,
                Stock = stock,
            };
            stockProduct1.SetQuantity(10);

            var stockProductItem1 = new StockProductItem
            {
                Id = Guid.NewGuid(),
                Status = StockProductItemStatus.Available,
                StockProduct = stockProduct1,
                LnDOrderId = Guid.NewGuid(),
                LnDInLocationId = locationId1,
                SerialNumber = "SN123"
            };

            DbContext.Products.Add(product1);
            DbContext.StockProducts.Add(stockProduct1);
            DbContext.StockProductItems.Add(stockProductItem1);
        }

        var auditRequest = AuditRequest.Create(
            status,
            DateTime.UtcNow,
            "Initial Notes",
            false, false, false, true);
        auditRequest.AuditLocations.Add(AuditLocation.Create(locationId, stock.Name, auditRequest));

        var auditProduct = AuditProduct.Create(product.Id, auditRequest, false, true, [])
            with
        { Product = product };

        auditProduct.SerialNumbers.Add(
            AuditProductSerialNumber.Create("SN124", auditProduct, AuditProductSNStatus.Unverified, true, true, ""));
        auditRequest.AuditProducts.Add(auditProduct);

        DbContext.ProductCategories.Add(category);
        DbContext.Manufacturers.Add(manufacturer);
        DbContext.Suppliers.Add(supplier);
        DbContext.Stocks.Add(stock);
        DbContext.Products.Add(product);
        DbContext.AuditRequests.Add(auditRequest);
        DbContext.AuditProducts.AddRange(auditRequest.AuditProducts);
        DbContext.AuditProductSerialNumbers.AddRange(auditProduct.SerialNumbers);
        DbContext.AuditLocations.AddRange(auditRequest.AuditLocations);

        await DbContext.SaveChangesAsync();

        return auditRequest;
    }

    [Theory]
    [RecursiveInlineAutoData]
    public async Task Handle_ShouldPatchDraft_WhenStatusIsDraft(Product product)
    {
        // Arrange
        var auditRequest = await SetupAuditRequestAsync(AuditRequestStatus.Draft);
        var stock2 = new Stock
        {
            Id = Guid.NewGuid(),
            LocationId = Guid.NewGuid(),
            Name = "Test Stock"
        };
        DbContext.Stocks.Add(stock2);
        var auditLocation = auditRequest.AuditLocations.First();
        var auditLocation2 = AuditLocation.Create(stock2.LocationId, stock2.Name, auditRequest);
        auditRequest.AuditLocations.Add(auditLocation2);
        DbContext.AuditLocations.Add(auditLocation2);
        product.IsActive = true;
        var auditProduct = auditRequest.AuditProducts.First();
        DbContext.Products.Add(product);
        await DbContext.SaveChangesAsync();
        await DbContext.SaveChangesAsync();

        var patchDto = new PatchAuditRequestDto
        {
            Products = new Dictionary<Guid, PatchAuditProductDto?>
            {
                { auditProduct.ProductId, new PatchAuditProductDto { SerialNumbers = [] } },
                { product.Id, new PatchAuditProductDto { SerialNumbers = [] } }
            },
            Locations = [new AuditLocationDto(auditLocation.LocationId)],
            DateTimeDue = DateTime.UtcNow.AddDays(1),
            Notes = "Updated Notes"
        };
        var request = new PatchAuditRequestCommand(auditRequest.Id, _patchBuilder.Build(patchDto));

        // Act
        await _sut.Handle(request, CancellationToken.None);

        // Assert
        var updatedAuditRequest = await DbContext.AuditRequests
            .Include(x => x.AuditLocations)
            .Include(x => x.AuditProducts)
            .FirstOrDefaultAsync();
        updatedAuditRequest.Should().NotBeNull();
        updatedAuditRequest!.AuditLocations.Should().HaveCount(1);
        updatedAuditRequest.Status.Should().Be(AuditRequestStatus.Draft);
        updatedAuditRequest.Notes.Should().Be("Updated Notes");
        updatedAuditRequest.DateTimeDue.Should().BeCloseTo(patchDto.DateTimeDue, TimeSpan.FromSeconds(1));
        updatedAuditRequest.AuditProducts.First(x => x.ProductId == product.Id).IsManual
            .Should().BeTrue();
        updatedAuditRequest.AuditProducts.First(x => x.ProductId == product.Id).AddedByManager
            .Should().BeTrue();
    }

    [Theory]
    [RecursiveInlineAutoData]
    public async Task Handle_ShouldPatchReadyForCalculation_WhenStatusIsReadyForCalculation(
        AuditRequest auditRequest, Product product)
    {
        // Arrange
        product.IsActive = true;
        auditRequest.Status = AuditRequestStatus.ReadyForCalculation;
        auditRequest.AuditLocations = [auditRequest.AuditLocations.First()];
        product.StockProducts.First().Stock = auditRequest.AuditLocations.First().Stock!;
        auditRequest.AuditProducts.First().SerialNumbers.First().Status = AuditProductSNStatus.Undefined;
        var auditProduct1 = auditRequest.AuditProducts.First();
        foreach (var auditSerialNumber in auditProduct1.SerialNumbers)
        {
            auditSerialNumber.IsManual = true;
            auditSerialNumber.AddedByManager = true;
        }

        var auditProduct2 = auditRequest.AuditProducts.Skip(1).First();
        DbContext.AuditRequests.Add(auditRequest);
        DbContext.Products.Add(product);
        await DbContext.SaveChangesAsync();

        var patchDto = new PatchAuditRequestDto
        {
            Products = new()
            {
                {
                    auditProduct1.ProductId,
                    new()
                    {
                        Counted = 3,
                        SerialNumbers =
                        [
                            new("SNNew1", AuditProductSNStatus.Unverified, "abc"),
                            new("SNNew3", AuditProductSNStatus.Verified, "cde"),
                        ]
                    }
                },
                {
                    auditProduct2.ProductId,
                    null
                },
                {
                    product.Id,
                    new()
                    {
                        Counted = 1,
                        SerialNumbers = [new("SNNew2", AuditProductSNStatus.Missing, null)]
                    }
                }
            }
        };
        var request = new PatchAuditRequestCommand(auditRequest.Id, _patchBuilder.Build(patchDto));

        // Act
        await _sut.Handle(request, CancellationToken.None);

        // Assert
        var updatedAuditRequest = await DbContext
            .AuditRequests.Include(x => x.AuditProducts).ThenInclude(x => x.SerialNumbers).FirstOrDefaultAsync();
        updatedAuditRequest.Should().NotBeNull();
        updatedAuditRequest!.Status.Should().Be(AuditRequestStatus.ReadyForCalculation);
        updatedAuditRequest.AuditProducts.Should().HaveCount(3);
        updatedAuditRequest.AuditProducts.First(x => x.ProductId == auditProduct1.ProductId)
            .SerialNumbers.Should().HaveCount(2);
        updatedAuditRequest.AuditProducts.Should().NotContain(x => x.ProductId == auditProduct2.ProductId);
        updatedAuditRequest.AuditProducts.First(x => x.ProductId == product.Id)
            .SerialNumbers.Should().HaveCount(1);
        updatedAuditRequest.AuditProducts.First(x => x.ProductId == product.Id)
            .IsManual.Should().BeTrue();
        updatedAuditRequest.AuditProducts.First(x => x.ProductId == product.Id)
            .AddedByManager.Should().BeTrue();
    }

    [Fact]
    public async Task Handle_ShouldPatchFinalized_WhenStatusIsFinalized()
    {
        // Arrange
        var auditRequest = await SetupAuditRequestAsync(AuditRequestStatus.Finalized);

        var patchDto = new PatchAuditRequestDto
        {
            Products = new()
            {
                { auditRequest.AuditProducts.First().ProductId, new() { SerialNumbers = [] } }
            },
            Locations = [new(auditRequest.AuditLocations.First().LocationId)],
            DateTimeDue = DateTime.UtcNow.AddDays(3),
            Notes = "Initial Notes 2"
        };
        var request = new PatchAuditRequestCommand(auditRequest.Id, _patchBuilder.Build(patchDto));

        // Act
        await _sut.Handle(request, CancellationToken.None);

        // Assert
        var updatedAuditRequest = await DbContext.AuditRequests.FirstOrDefaultAsync();
        updatedAuditRequest.Should().NotBeNull();
        updatedAuditRequest!.Status.Should().Be(AuditRequestStatus.Finalized);
        updatedAuditRequest.Notes.Should().Be("Initial Notes");
    }

    [Fact]
    public async Task Handle_ShouldCheckForDuplicateSerialNumbers()
    {
        // Arrange
        var auditRequest = await SetupAuditRequestAsync(AuditRequestStatus.ReadyForCalculation);

        var patchDto = new PatchAuditRequestDto
        {
            Products = new()
            {
                {
                    auditRequest.AuditProducts.First().ProductId,
                    new()
                    {
                        SerialNumbers =
                        [
                            new("SN123", AuditProductSNStatus.Verified, ""),
                            new("SN124", AuditProductSNStatus.Unverified, ""),
                            new("SN124", AuditProductSNStatus.Unverified, "")
                        ]
                    }
                }
            },
            Locations = [new(auditRequest.AuditLocations.First().LocationId)],
            DateTimeDue = DateTime.UtcNow.AddDays(1),
            Notes = "Updated Notes"
        };
        var request = new PatchAuditRequestCommand(auditRequest.Id, _patchBuilder.Build(patchDto));

        // Act
        var result = async () => await _sut.Handle(request, CancellationToken.None);

        // Assert
        await result.Should().ThrowAsync<DuplicatedSerialNumbersException>()
            .WithMessage("S/N has already been added to this request");
    }

    [Fact]
    public async Task Handle_ShouldThrowException_WhenUserIsAuditRequestSpecialistUpdatingDraft()
    {
        // Arrange
        var auditRequest = await SetupAuditRequestAsync(AuditRequestStatus.Draft);

        var sut = new PatchAuditRequestHandler(
            DbContext, new AuditRequestService(
                DbContext, new OperationContextMock { UserAuditRequestManager = false, UserAuditRequestSpecialist = true }),
            _mediatorMock.Object);

        var patchDto = new PatchAuditRequestDto
        {
            Products = new()
            {
                { Guid.NewGuid(), new() { SerialNumbers = [] } }
            },
            Locations = [new(auditRequest.AuditLocations.First().LocationId)],
            DateTimeDue = DateTime.UtcNow.AddDays(1),
            Notes = "Updated Notes"
        };
        var request = new PatchAuditRequestCommand(auditRequest.Id, _patchBuilder.Build(patchDto));

        // Act
        var result = async () => await sut.Handle(request, CancellationToken.None);

        // Assert
        await result.Should().ThrowAsync<BusinessException>()
            .WithMessage("Specialist cannot update Audit request in Draft status");
    }

    [Fact]
    public async Task Handle_ShouldThrowException_WhenSpecialistTryingToRemoveProductNotCreatedByHim()
    {
        // Arrange
        var auditRequest = await SetupAuditRequestAsync(AuditRequestStatus.ReadyForCalculation);

        var sut = new PatchAuditRequestHandler(
            DbContext,
            new AuditRequestService(
                DbContext, new OperationContextMock
                {
                    UserAuditRequestManager = false,
                    UserAuditRequestSpecialist = true,
                    Login = "stas-test",
                }),
            _mediatorMock.Object);

        var patchDto = new PatchAuditRequestDto
        {
            Products = new()
            {
                { auditRequest.AuditProducts.First().Product.Id, null }
            },
            Locations = [new(auditRequest.AuditLocations.First().LocationId)],
            DateTimeDue = DateTime.UtcNow.AddDays(1),
            Notes = "Updated Notes"
        };
        var request = new PatchAuditRequestCommand(auditRequest.Id, _patchBuilder.Build(patchDto));

        // Act
        var result = async () => await sut.Handle(request, CancellationToken.None);

        // Assert
        var ex = await result.Should().ThrowAsync<BusinessException>();
        ex.Which.ErrorCode.Should().Be(ErrorCodes.SpecialistCannotDeleteProductAddedByOthers);
    }

    [Fact]
    public async Task Handle_ShouldCheckForDuplicateSerialNumbers_WhenFromNonCalculatedProducts()
    {
        // Arrange
        var auditRequest = await SetupAuditRequestAsync(AuditRequestStatus.ReadyForCalculation, true);

        var patchDto = new PatchAuditRequestDto
        {
            Products = new()
        {
            {
                auditRequest.AuditProducts.First().ProductId,
                new()
                {
                    SerialNumbers =
                    [
                        new("SN123", AuditProductSNStatus.Verified, ""),
                        new("SN125", AuditProductSNStatus.Unverified, "")
                    ]
                }
            }
        },
            Locations = [new(auditRequest.AuditLocations.First().LocationId)],
            DateTimeDue = DateTime.UtcNow.AddDays(1),
            Notes = "Updated Notes"
        };

        var request = new PatchAuditRequestCommand(auditRequest.Id, _patchBuilder.Build(patchDto));

        var result = async () => await _sut.Handle(request, CancellationToken.None);

        await result.Should().ThrowAsync<DuplicatedSerialNumbersException>()
            .WithMessage("S/N assigned to a different product");
    }


    [Fact]
    public async Task Handle_ShouldCheckForDuplicateSerialNumbers_WhenFromOtherStocks()
    {
        // Arrange
        var auditRequest = await SetupAuditRequestAsync(AuditRequestStatus.ReadyForCalculation, false, true);

        var patchDto = new PatchAuditRequestDto
        {
            Products = new()
        {
            {
                auditRequest.AuditProducts.First().ProductId,
                new()
                {
                    SerialNumbers =
                    [
                        new("SN123", AuditProductSNStatus.Verified, ""),
                        new("SN125", AuditProductSNStatus.Unverified, "")
                    ]
                }
            }
        },
            Locations = [new(auditRequest.AuditLocations.First().LocationId)],
            DateTimeDue = DateTime.UtcNow.AddDays(1),
            Notes = "Updated Notes"
        };

        var request = new PatchAuditRequestCommand(auditRequest.Id, _patchBuilder.Build(patchDto));

        var result = async () => await _sut.Handle(request, CancellationToken.None);

        // Assert
        await result.Should().ThrowAsync<DuplicatedSerialNumbersException>()
            .WithMessage("S/N assigned to another location");
    }
}
