using Auditdata.InventoryService.Core.Entities.AuditRequests;
using Auditdata.InventoryService.Core.Entities.Products;
using Auditdata.InventoryService.Core.Features.AuditRequests.GoToReadyForCalculation;
using Auditdata.InventoryService.Core.Features.AuditRequests.Services;
using Auditdata.InventoryService.UnitTests.Common;
using Auditdata.Transport.Contracts.Exceptions;
using FluentAssertions;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Auditdata.InventoryService.UnitTests.Common.Mocks;
using Auditdata.InventoryService.Contracts.Commands;

namespace Auditdata.InventoryService.UnitTests.AuditRequests;

public class GoToReadyForCalculationHandlerTests : BaseDbContextTest
{
    private readonly GoToReadyForCalculationHandler _sut;
    private readonly AuditRequestService _auditRequestService;
    private readonly Mock<IMediator> _mediatorMock = new();
    private readonly Mock<TimeProvider> _timeProviderMock = new();
    private readonly Mock<IPublishEndpoint> _publishMock = new();
    private readonly DateTimeOffset _utcNow = new(2025, 3, 13, 10, 0, 0, TimeSpan.Zero);

    public GoToReadyForCalculationHandlerTests()
    {
        _timeProviderMock.Setup(x => x.GetUtcNow()).Returns(_utcNow);
        _auditRequestService = new AuditRequestService(DbContext, new OperationContextMock());
        _sut = new GoToReadyForCalculationHandler(
            DbContext,
            _auditRequestService,
            new OperationContextMock(),
            _mediatorMock.Object,
            _timeProviderMock.Object,
            _publishMock.Object);
    }

    private async Task<AuditRequest> SetupAuditRequestAsync(AuditRequestStatus status)
    {
        var manufacturerId = Guid.NewGuid();
        var supplier = new Supplier
        {
            Id = Guid.NewGuid(),
            Name = "test-supplier",
            PhoneNumber = "************",
            Address1 = "Address",
            State = "State",
            City = "City",
            IsDeleted = false
        };

        var manufacturer = new Core.Entities.Manufacturer
        {
            Id = manufacturerId,
            Name = "ManufacturerName",
            City = "ManufacturerCity",
            PhoneNumber = "ManufacturerPhoneNumber",
            State = "ManufacturerState",
            Address1 = "ManufacturerAddress1"
        };

        var category = new Core.Entities.ProductCategory
        {
            Id = Guid.NewGuid(),
            Name = "Test Category",
            IsDeleted = false
        };
        DbContext.ProductCategories.Add(category);

        var product = new Product
        {
            Id = Guid.NewGuid(),
            Name = "test-product",
            SupplierId = supplier.Id,
            Supplier = supplier,
            Category = category,
            CategoryId = category.Id,
            Manufacturer = manufacturer,
            ManufacturerId = manufacturerId,
            IsActive = true,
            IsDeleted = false,
            IsSerialized = true,
            ControlledByStock = true,
        };
        product.Skus = [Sku.Create("testSku", product.Id, supplier.Id)];

        var locationId = Guid.NewGuid();
        var stock = new Stock
        {
            Id = Guid.NewGuid(),
            LocationId = locationId,
            Name = "Test Stock"
        };

        var stockProduct = StockProduct.Create(stock, product);
        stockProduct.StockProductItems
            .Add(new StockProductItem { SerialNumber = "SNTest123", Status = StockProductItemStatus.Available });
        product.StockProducts = [stockProduct];

        var auditRequest = AuditRequest.Create(
            status,
            DateTime.UtcNow,
            "Initial Notes",
            false, false, false, true);
        auditRequest.AuditLocations.Add(AuditLocation.Create(locationId, stock.Name, auditRequest));

        var auditProduct = AuditProduct.Create(product.Id, auditRequest, false, true, [])
            with
        { Product = product };

        auditRequest.AuditProducts.Add(auditProduct);

        DbContext.ProductCategories.Add(category);
        DbContext.Manufacturers.Add(manufacturer);
        DbContext.Suppliers.Add(supplier);
        DbContext.Stocks.Add(stock);
        DbContext.Products.Add(product);
        DbContext.AuditRequests.Add(auditRequest);
        DbContext.AuditProducts.AddRange(auditRequest.AuditProducts);
        DbContext.AuditLocations.AddRange(auditRequest.AuditLocations);

        await DbContext.SaveChangesAsync();

        return auditRequest;
    }

    [Fact]
    public async Task Handle_ShouldThrowException_WhenNoProductsAdded()
    {
        // Arrange
        var auditRequest = await SetupAuditRequestAsync(AuditRequestStatus.Draft);
        auditRequest.DateTimeDue = DateTimeOffset.UtcNow.AddDays(1);
        auditRequest.AuditProducts.Clear();

        var request = new GoToReadyForCalculationCommand(auditRequest.Id);

        // Act
        var result = async () => await _sut.Handle(request, CancellationToken.None);

        // Assert
        await result.Should().ThrowAsync<BusinessException>()
            .WithMessage("Status cannot be changed because no products have been added.");
    }

    [Fact]
    public async Task Handle_ShouldMoveReturnedToDraftToReadyForCalculation()
    {
        // Arrange
        var auditRequest = await SetupAuditRequestAsync(AuditRequestStatus.Draft);
        auditRequest.ReturnedToDraft = true;
        auditRequest.DateTimeDue= DateTimeOffset.UtcNow.AddDays(1);
        auditRequest.RequestNumber = "testAbc";

        var request = new GoToReadyForCalculationCommand(auditRequest.Id);

        // Act
        await _sut.Handle(request, CancellationToken.None);

        // Assert
        var updatedAuditRequest = await DbContext.AuditRequests.Include(x => x.AuditProducts)
            .ThenInclude(y => y.SerialNumbers).FirstAsync();
        updatedAuditRequest.Should().NotBeNull();
        updatedAuditRequest!.Status.Should().Be(AuditRequestStatus.ReadyForCalculation);
        updatedAuditRequest!.MovedToReadyForCalculationDateTime.Should().Be(_utcNow);
        updatedAuditRequest.RequestNumber.Should().NotBeNullOrEmpty();
        updatedAuditRequest.AuditProducts.Should().NotBeEmpty();
        updatedAuditRequest.AuditProducts.First().SerialNumbers.Should().NotBeNullOrEmpty();
        updatedAuditRequest.AuditProducts.First().IsManual.Should().BeFalse();
        updatedAuditRequest.AuditProducts.First().AddedByManager.Should().BeTrue();
    }

    [Fact]
    public async Task Handle_ShouldChangeStatusToReadyForCalculation_WhenStatusIsFinalized()
    {
        // Arrange
        var auditRequest = await SetupAuditRequestAsync(AuditRequestStatus.Finalized);
        auditRequest.MovedToReadyForCalculationDateTime = DateTimeOffset.MinValue;

        var request = new GoToReadyForCalculationCommand(auditRequest.Id);

        // Act
        await _sut.Handle(request, CancellationToken.None);

        // Assert
        var updatedAuditRequest = await DbContext.AuditRequests.FirstOrDefaultAsync();
        updatedAuditRequest.Should().NotBeNull();
        updatedAuditRequest!.Status.Should().Be(AuditRequestStatus.ReadyForCalculation);
        updatedAuditRequest!.MovedToReadyForCalculationDateTime.Should().Be(DateTimeOffset.MinValue);
    }

    [Fact]
    public async Task Handle_ShouldThrowException_WhenStatusIsNotDraftOrFinalized()
    {
        // Arrange
        var auditRequest = await SetupAuditRequestAsync(AuditRequestStatus.ReadyForCalculation);

        var request = new GoToReadyForCalculationCommand(auditRequest.Id);

        // Act
        var result = async () => await _sut.Handle(request, CancellationToken.None);

        // Assert
        await result.Should().ThrowAsync<BusinessException>()
            .WithMessage("Only requests in 'Draft' or 'Finalized' statuses can be moved to 'Ready for Calculation'.");
    }

    [Fact]
    public async Task Handle_ShouldPublishCommands_WhenUserIsAuditRequestManager()
    {
        // Arrange
        var auditRequest = await SetupAuditRequestAsync(AuditRequestStatus.Draft);
        auditRequest.DateTimeDue = DateTimeOffset.UtcNow.AddDays(1);
        var sut = new GoToReadyForCalculationHandler(
            DbContext,
            _auditRequestService,
            new OperationContextMock { UserAuditRequestManager = true, UserAuditRequestSpecialist = false },
            _mediatorMock.Object,
            _timeProviderMock.Object,
            _publishMock.Object);
        var request = new GoToReadyForCalculationCommand(auditRequest.Id);

        // Act
        await sut.Handle(request, CancellationToken.None);

        // Assert
        var removed = await DbContext.AuditRequests.FirstOrDefaultAsync(x => x.Id == auditRequest.Id);
        removed.Should().BeNull();

        _publishMock
            .Verify(x => x.Publish(
                It.Is<CreateAuditRequestReadyForCalculationCommand>(x => x.AuditRequestId == auditRequest.Id),
                It.IsAny<CancellationToken>()),
                Times.Exactly(auditRequest.AuditLocations.Count));
    }

    [Fact]
    public async Task Handle_ShouldThrowException_WhenUserIsAuditRequestSpecialist()
    {
        // Arrange
        var auditRequest = await SetupAuditRequestAsync(AuditRequestStatus.Draft);
        var sut = new GoToReadyForCalculationHandler(
            DbContext,
            _auditRequestService,
            new OperationContextMock { UserAuditRequestManager = false, UserAuditRequestSpecialist = true },
            _mediatorMock.Object,
            _timeProviderMock.Object,
            _publishMock.Object);
        var request = new GoToReadyForCalculationCommand(auditRequest.Id);

        // Act
        var result = async () => await sut.Handle(request, CancellationToken.None);

        // Assert
        await result.Should().ThrowAsync<BusinessException>()
            .WithMessage("Specialist cannot move Audit request from Draft to ReadyForCalculation status");
    }
}
