using Auditdata.InventoryService.Core.Entities.AuditRequests;
using Auditdata.InventoryService.Core.Entities.Products;
using Auditdata.InventoryService.Core.Exceptions;
using Auditdata.InventoryService.Core.Features.AuditRequests.SearchProducts;
using Auditdata.InventoryService.UnitTests.Common;
using Auditdata.InventoryService.UnitTests.Common.Mocks;
using FluentAssertions;

namespace Auditdata.InventoryService.UnitTests.AuditRequests;

public class SearchAuditProductsHandlerTests : BaseSqlLiteDbContextTest
{
    private readonly SearchAuditProductsHandler _sut;
    private readonly OperationContextMock _operationContextMock = new();

    public SearchAuditProductsHandlerTests()
    {
        _sut = new SearchAuditProductsHandler(DbContext, _operationContextMock);
    }

    [Theory]
    [RecursiveInlineAutoData]
    public async Task Handle_Should_ReturnAuditProducts_When_AuditRequestInDraft(
        AuditRequest auditRequest, SearchAuditProductsQuery query, Product[] products,
        Supplier supplier, ProductCategory productCategory)
    {
        // Arrange
        auditRequest.Status = AuditRequestStatus.Draft;
        auditRequest.IncludeOnTrialProducts = true;
        query.Page = 1;
        query.PerPage = 10;
        query.OrderBy = "Name";
        query.OnlyDiscrepancies = true;
        query.OnlyStockProducts = true;
        var i = 0;
        supplier.Id = query.SupplierIds.FirstOrDefault();
        productCategory.Id = query.CategoryIds.FirstOrDefault();
        auditRequest.Id = query.AuditRequestId;
        foreach (var product in products.Take(2))
        {
            product.IsActive = true;
            product.IsSerialized = true;
            product.ControlledByStock = true;
            product.StockProducts.Skip(i).First().StockProductItems.Skip(i).First().Status = StockProductItemStatus.OnTrial;
            product.Name += query.SearchText;
            product.Supplier = supplier;
            product.Category = productCategory;
            product.StockProducts.Skip(i).First().Stock =
                auditRequest.AuditLocations.Select(x => x.Stock!).Skip(i)!.First();
            auditRequest.AuditProducts.Skip(i).First().Product = product;
            auditRequest.AuditProducts.Skip(i).First().CountedQuantity = 1;
            auditRequest.AuditProducts.Skip(i).First().ExpectedQuantity = 2;
            i++;
        }

        DbContext.Add(auditRequest);
        DbContext.AddRange(products);
        await DbContext.SaveChangesAsync();

        // Act
        var result = await _sut.Handle(query, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Products.Select(x => x.Name).Should()
            .BeEquivalentTo(auditRequest.AuditProducts.Take(2).Select(x => x.Product.Name));
        result.Products.Select(x => x.Category)
            .Should().BeEquivalentTo(auditRequest.AuditProducts.Take(2).Select(x => x.Product.Category!.Name));
        result.Products.Select(x => x.SupplierName)
            .Should().BeEquivalentTo(auditRequest.AuditProducts.Take(2).Select(x => x.Product.Supplier!.Name));
        result.Products.SelectMany(x => x.Skus)
            .Should().BeEquivalentTo(auditRequest.AuditProducts.Take(2)
            .SelectMany(x => x.Product.Skus.Select(y => y.SkuValue)));
        result.Products.SelectMany(x => x.SerialNumbers.Select(y => y.SerialNumber))
            .Should().BeEquivalentTo(auditRequest.AuditProducts.Take(2)
            .SelectMany(x => x.SerialNumbers.Select(y => y.SerialNumber)));
    }

    [Theory]
    [RecursiveInlineAutoData]
    public async Task Handle_Should_ReturnAuditProducts_When_AuditRequestNotInDraft(
    AuditRequest auditRequest, SearchAuditProductsQuery query, Product[] products,
    Supplier supplier, ProductCategory productCategory)
    {
        // Arrange
        _operationContextMock.UserAuditRequestManager = false;
        _operationContextMock.UserAuditRequestSpecialist = true;
        auditRequest.Status = AuditRequestStatus.ReadyForCalculation;
        auditRequest.IncludeOnTrialProducts = false;
        query.Page = 1;
        query.PerPage = 10;
        query.OrderBy = "Name";
        query.OnlyDiscrepancies = true;
        query.OnlyStockProducts = true;

        query.SupplierIds = [ supplier.Id, Guid.NewGuid() ]; 
        query.CategoryIds = [ productCategory.Id, Guid.NewGuid() ]; 

        var i = 0;
        supplier.Id = query.SupplierIds.FirstOrDefault();
        productCategory.Id = query.CategoryIds.FirstOrDefault();
        auditRequest.Id = query.AuditRequestId;

        foreach (var product in products.Take(2))
        {
            product.IsActive = true;
            product.IsSerialized = false;
            product.ControlledByStock = true;
            product.StockProducts.Skip(i).First().Product = product;
            product.StockProducts.Skip(i).First().SetQuantity(3);
            auditRequest.AuditProducts.Skip(i).First().Skus[i] += query.SearchText;
            product.Supplier = supplier;
            product.Category = productCategory;
            product.StockProducts.Skip(i).First().Stock =
                auditRequest.AuditLocations.Select(x => x.Stock!).Skip(i)!.First();
            auditRequest.AuditProducts.Skip(i).First().Product = product;
            auditRequest.AuditProducts.Skip(i).First().CountedQuantity = 1;
            auditRequest.AuditProducts.Skip(i).First().ExpectedQuantity = 2;
            i++;
        }

        DbContext.Add(auditRequest);
        DbContext.AddRange(products);
        await DbContext.SaveChangesAsync();

        // Act
        var result = await _sut.Handle(query, CancellationToken.None);

        // Assert
        var isSpecialist = _operationContextMock.UserAuditRequestSpecialist && !_operationContextMock.UserAuditRequestManager;
        var login = _operationContextMock.Login;
        result.Products.Select(x => x.Name).Should()
            .BeEquivalentTo(auditRequest.AuditProducts.Take(2).Select(x => x.ProductName));
        result.Products.Select(x => x.Category)
            .Should().BeEquivalentTo(auditRequest.AuditProducts.Take(2).Select(x => x.ProductCategoryName));
        result.Products.Select(x => x.SupplierName)
            .Should().BeEquivalentTo(auditRequest.AuditProducts.Take(2).Select(x => x.SupplierName));
        result.Products.SelectMany(x => x.Skus)
            .Should().BeEquivalentTo(auditRequest.AuditProducts.Take(2).SelectMany(x => x.Skus));
        result.Products.SelectMany(x => x.SerialNumbers.Select(y => y.SerialNumber))
            .Should().BeEquivalentTo(auditRequest.AuditProducts.Take(2)
            .SelectMany(x => x.SerialNumbers.Select(y => y.SerialNumber)));
        result.Products.Select(x => x.IsManual)
            .Should().BeEquivalentTo(auditRequest.AuditProducts.Take(2).Select(x => x.IsManual));
        result.Products.Select(x => x.AddedByManager)
           .Should().BeEquivalentTo(auditRequest.AuditProducts.Take(2).Select(x => x.AddedByManager));
        result.Products.Select(x => x.AddedBy)
            .Should().BeEquivalentTo(auditRequest.AuditProducts.Take(2).Select(x => x.CreatedBy));
        result.Products.Select(x => x.IsDisabledToDelete)
        .Should().BeEquivalentTo(auditRequest.AuditProducts.Take(2)
            .Select(x => (x.AddedByManager && isSpecialist) || (!x.AddedByManager && isSpecialist && x.CreatedBy != login)));
        result.Products.Select(x => x.IsSerialized)
        .Should().BeEquivalentTo(auditRequest.AuditProducts.Take(2).Select(x => x.Product.IsSerialized));

        result.Products.Should().Contain(x => x.SupplierId.HasValue && query.SupplierIds.Contains(x.SupplierId.Value));
        result.Products.Should().Contain(x => query.CategoryIds.Contains(x.CategoryId));
    }

    [Theory]
    [RecursiveInlineAutoData]
    public async Task Handle_Should_ReturnExpectedAsNull_When_BlindStockAndNoManagerPermission(AuditRequest auditRequest)
    {
        // Arrange
        auditRequest.BlindStockCalculation = true;
        DbContext.AuditRequests.Add(auditRequest);
        await DbContext.SaveChangesAsync();
        var sut = new SearchAuditProductsHandler(DbContext, new OperationContextMock
        {
            UserAuditRequestManager = false,
            UserAuditRequestSpecialist = true
        });

        var query = new SearchAuditProductsQuery { AuditRequestId = auditRequest.Id };

        // Act
        var result = await sut.Handle(query, CancellationToken.None);

        // Assert
        result.Products.Should().NotBeNullOrEmpty();
        result.Products.Select(x => x.Expected)
            .Should().AllBeEquivalentTo<int?>(null);
    }

    [Fact]
    public async Task Handle_Should_ThrowEntityNotFound_When_RequestNotFound()
    {
        // Arrange
        var query = new SearchAuditProductsQuery { AuditRequestId = Guid.NewGuid() };

        // Act
        var act = async () => await _sut.Handle(query, CancellationToken.None);

        // Assert
        await act.Should().ThrowAsync<EntityNotFoundException<AuditRequest>>();
    }
}
