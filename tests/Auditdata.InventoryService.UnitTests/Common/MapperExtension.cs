using AutoMapper.Internal;

namespace Auditdata.InventoryService.UnitTests.Common
{
    public static class MapperExtension
    {
        public static void IgnoreUnmappedProperties(this IMapperConfigurationExpression cfg)
        {
            InternalApi.Internal(cfg).ForAllMaps((typeMap, mapperExpression) =>
            {
                foreach (var propName in typeMap.GetUnmappedPropertyNames())
                {
                    try
                    {
                        mapperExpression.ForMember(propName, opt => opt.Ignore());
                    }
                    catch { }
                }
            });
        }
    }
}
