using Auditdata.Infrastructure.Contracts.OperationContext;
using Microsoft.EntityFrameworkCore.InMemory.Infrastructure.Internal;
using Microsoft.Extensions.DependencyInjection;

namespace Auditdata.InventoryService.UnitTests.Common;

public class InMemoryOptionsExtensions(IOperationContextAccessor operationContextAccessor) : InMemoryOptionsExtension
{
    public override void ApplyServices(IServiceCollection services)
    {
        services.AddSingleton(operationContextAccessor);
        services.AddScoped<IOperationContext>(sp => sp.GetRequiredService<IOperationContextAccessor>().OperationContext);
    }
}
