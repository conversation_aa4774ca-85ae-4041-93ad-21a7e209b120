using Auditdata.Infrastructure.EntityFrameworkCore.Interceptors;
using Auditdata.InventoryService.Infrastructure.Persistence;
using Auditdata.InventoryService.UnitTests.Common.Mocks;
using Microsoft.EntityFrameworkCore;

namespace Auditdata.InventoryService.UnitTests.Common;

public class BaseDbContextTest : IDisposable
{
    public InventoryDbContextMock DbContext;

    public BaseDbContextTest()
    {
        // Build DbContextOptions
        var operationAccessor = new OperationContextAccessorMock();
        var dbContextOptions = new DbContextOptionsBuilder<InventoryDbContext>()
            .AddInterceptors(
                new TenancyDataModelInterceptor(operationAccessor.OperationContext),
                new AuditableDataModelInterceptor(),
                new AuditablePersonalizedDataModelInterceptor(operationAccessor.OperationContext),
                new SoftDeleteDataModelInterceptor())
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options
            .WithExtension(new InMemoryOptionsExtensions(operationAccessor));

        DbContext = new InventoryDbContextMock((DbContextOptions<InventoryDbContext>)dbContextOptions, operationAccessor);
        DbContext.Database.EnsureDeleted();
        DbContext.Database.EnsureCreated();
    }

    public void Dispose()
    {
        DbContext.Dispose();
    }
}