using Auditdata.Infrastructure.Contracts.DataModel;
using Auditdata.Infrastructure.Contracts.OperationContext;
using Auditdata.InventoryService.Core.Entities.AuditRequests;
using Auditdata.InventoryService.Core.Entities.StockProductItems;
using Auditdata.InventoryService.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using System.Linq.Expressions;

namespace Auditdata.InventoryService.UnitTests.Common.Mocks;

public class InventoryDbContextMock : InventoryDbContext
{
    public InventoryDbContextMock(DbContextOptions<InventoryDbContext> options, IOperationContextAccessor operationContext)
        : base(options, operationContext)
    {
    }

    public override void SetOperationTimeout(TimeSpan timeout)
    {
    }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);
        modelBuilder.Entity<Manufacturer>().Ignore(t => t.AccountReceivableContact);
        modelBuilder.Entity<Manufacturer>().Ignore(t => t.SalesContact);
        modelBuilder.Entity<Supplier>().Ignore(t => t.AccountReceivableContact);
        modelBuilder.Entity<Supplier>().Ignore(t => t.SalesContact);
        modelBuilder.Entity<ExternalProductOption>().Ignore(t => t.Styles);
        modelBuilder.Entity<StockProductItemLog>().Ignore(t => t.Data);
        modelBuilder.Entity<StockProductItem>().Ignore(t => t.Version);
        modelBuilder.Entity<StockProductItemAttribute>().Ignore(t => t.Value);
        modelBuilder.Entity<StockKeepingUnit>().Ignore(t => t.Version);
        modelBuilder.Entity<StockProduct>().Ignore(t => t.Version);
        modelBuilder.Entity<StockSetting>().Ignore(t => t.Version);
        modelBuilder.Entity<Sku>().Ignore(t => t.Version);
        modelBuilder.Entity<AuditRequest>().Ignore(t => t.Version);
        modelBuilder.Entity<AuditLocation>().Ignore(t => t.Version);
        modelBuilder.Entity<AuditProduct>().Ignore(t => t.Version);
        modelBuilder.Entity<AuditProductSerialNumber>().Ignore(t => t.Version);

        modelBuilder.Entity<ImportMetadata>().Property(t => t.ImportDate)
            .HasConversion(new DateTimeOffsetToBinaryConverter());

        var entities = modelBuilder.Model.GetEntityTypes()
            .Where(e => typeof(ITenancyDataModel).IsAssignableFrom(e.ClrType) && e.BaseType == null)
            .Select(e => e.ClrType);
        foreach (var entity in entities)
        {
            var parameterType = Expression.Parameter(entity);
            if (typeof(ISoftDeleteDataModel).IsAssignableFrom(entity))
            {
                var property = Expression.Property(parameterType, nameof(ISoftDeleteDataModel.IsDeleted));
                var condition = Expression.Equal(property, Expression.Constant(false));
                modelBuilder.Entity(entity).HasQueryFilter(Expression.Lambda(condition, parameterType));
            }
            else
            {
                modelBuilder.Entity(entity).HasQueryFilter(Expression.Lambda(Expression.Constant(true), parameterType));
            }
        }
    }

    public override Task<IAsyncDisposable> AcquireLockAsync(string name, CancellationToken cancellationToken = default)
    {
        return Task.FromResult(new Mock<IAsyncDisposable>().Object);
    }
}
