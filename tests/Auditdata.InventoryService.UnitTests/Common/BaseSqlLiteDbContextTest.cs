using Auditdata.Infrastructure.EntityFrameworkCore.Interceptors;
using Auditdata.InventoryService.Infrastructure.Persistence;
using Microsoft.Data.Sqlite;
using Microsoft.EntityFrameworkCore;
using System.Data.Common;
using Auditdata.InventoryService.UnitTests.Common.Mocks;
using System.Diagnostics;

namespace Auditdata.InventoryService.UnitTests.Common;

public abstract class BaseSqlLiteDbContextTest : IDisposable
{
    public readonly InventoryDbContext DbContext;

    private readonly DbConnection _dbConnection;
    private bool _disposedValue;

    public BaseSqlLiteDbContextTest()
    {
        _dbConnection = new SqliteConnection("Data Source=:memory:");
        _dbConnection.Open();

        var operationAccessor = new OperationContextAccessorMock();
        var options = new DbContextOptionsBuilder<InventoryDbContext>()
            .EnableSensitiveDataLogging()
            .LogTo(message =>
            {
                Debug.WriteLine(message);
            }, Microsoft.Extensions.Logging.LogLevel.Information)
            .AddInterceptors(
                new TenancyDataModelInterceptor(operationAccessor.OperationContext),
                new AuditableDataModelInterceptor(),
                new AuditablePersonalizedDataModelInterceptor(operationAccessor.OperationContext),
                new SoftDeleteDataModelInterceptor())
            .UseSqlite(_dbConnection)
            .Options
            .WithExtension(new InMemoryOptionsExtensions(operationAccessor));

        DbContext = new InventoryDbContextMock((DbContextOptions<InventoryDbContext>)options, operationAccessor);
        DbContext.Database.EnsureCreated();
    }

    public InventoryDbContextMock GetDbContext()
    {
        var operationAccessor = new OperationContextAccessorMock();
        var options = new DbContextOptionsBuilder<InventoryDbContext>()
            .AddInterceptors(
                new TenancyDataModelInterceptor(operationAccessor.OperationContext),
                new AuditableDataModelInterceptor(),
                new AuditablePersonalizedDataModelInterceptor(operationAccessor.OperationContext),
                new SoftDeleteDataModelInterceptor())
            .UseSqlite(_dbConnection)
            .Options
            .WithExtension(new InMemoryOptionsExtensions(operationAccessor));
        return new InventoryDbContextMock((DbContextOptions<InventoryDbContext>)options, operationAccessor);
    }

    protected virtual void Dispose(bool disposing)
    {
        if (!_disposedValue)
        {
            if (disposing)
            {
                _dbConnection.Close();
            }

            _disposedValue = true;
        }
    }

    public void Dispose()
    {
        Dispose(disposing: true);
        GC.SuppressFinalize(this);
    }
}