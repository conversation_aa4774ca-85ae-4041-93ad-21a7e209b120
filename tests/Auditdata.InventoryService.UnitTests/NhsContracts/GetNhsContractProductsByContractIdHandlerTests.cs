using Auditdata.InventoryService.Core.Entities.Products;
using Auditdata.InventoryService.Core.Features.NhsContracts.Handlers;
using Auditdata.InventoryService.Core.Features.NhsContracts.Queries;
using Auditdata.InventoryService.UnitTests.Common;
using FluentAssertions;

namespace Auditdata.InventoryService.UnitTests.NhsContracts;

public class GetNhsContractProductsByContractIdHandlerTests : BaseDbContextTest
{
    private readonly IFixture _fixture = new RecursiveFixture();
    private readonly GetNhsContractProductsByContractIdHandler _handler;

    public GetNhsContractProductsByContractIdHandlerTests()
    {
        _handler = new GetNhsContractProductsByContractIdHandler(DbContext);
    }
    
    [Fact]
    public async Task Consume_ShouldRespond_With_NhsContractProductsResult()
    {
        var contractId = Guid.NewGuid();
        var testProduct = _fixture.Build<UKProduct>()
            .With(x => x.IsDeleted, false)
            .With(x => x.Name, "test")
            .With(x => x.IsNHS, true)
            .Create();
        var contractProducts = _fixture.Build<NhsContractProduct>()
            .With(x => x.ContractId, contractId)
            .With(x => x.Product, testProduct)
            .CreateMany()
            .ToList();
        DbContext.NhsContractProducts.AddRange(contractProducts);
        await DbContext.SaveChangesAsync();

        var result = await _handler.Handle(new GetNhsContractProductsByContractIdQuery(contractId, string.Empty), CancellationToken.None);

        result.ProductIds.Count().Should().Be(contractProducts.Count);
    }
}
