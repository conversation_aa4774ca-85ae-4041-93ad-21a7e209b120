using Auditdata.InventoryService.Core.Features.StockAdjustmentReasons.Handlers;
using Auditdata.InventoryService.Core.Features.StockAdjustmentReasons.Queries;
using FluentAssertions;

namespace Auditdata.InventoryService.UnitTests.StockAdjustmentReasonTests;

public class GetStockAdjustmentReasonHandlerTests
{
    private readonly Fixture _fixture;
    private readonly Mock<IStockAdjustmentReasonsRepository> _stockAdjustmentReasonsRepository;
    private readonly GetStockAdjustmentReasonHandler _handler;

    public GetStockAdjustmentReasonHandlerTests()
    {
        _fixture = new Fixture();
        _stockAdjustmentReasonsRepository = new Mock<IStockAdjustmentReasonsRepository>();
        _handler = new GetStockAdjustmentReasonHandler(_stockAdjustmentReasonsRepository.Object);
    }

    [Fact]
    public async Task Consume_ShouldRespond_WithReasons()
    {
        var reasons = _fixture.Build<StockAdjustmentReason>().CreateMany().ToList();
        _stockAdjustmentReasonsRepository.Setup(x => x.GetReasonsAsync(It.IsAny<bool?>())).ReturnsAsync(reasons);

        var result = await _handler.Handle(new GetStockAdjustmentReasonQuery(), CancellationToken.None);

        result.Reasons.Should().BeSameAs(reasons);
    }
}
