using Auditdata.InventoryService.Core.Features.StockAdjustmentReasons.Commands;
using Auditdata.InventoryService.Core.Features.StockAdjustmentReasons.Handlers;

namespace Auditdata.InventoryService.UnitTests.StockAdjustmentReasonTests;

public class DeleteStockAdjustmentReasonHandlerTests
{
    private readonly Fixture _fixture;
    private readonly Mock<IStockAdjustmentReasonsRepository> _stockAdjustmentReasonsRepository;
    private readonly DeleteStockAdjustmentReasonHandler _handler;

    public DeleteStockAdjustmentReasonHandlerTests()
    {
        _fixture = new Fixture();
        _stockAdjustmentReasonsRepository = new Mock<IStockAdjustmentReasonsRepository>();
        _handler = new DeleteStockAdjustmentReasonHandler(_stockAdjustmentReasonsRepository.Object);
    }
    
    [Fact]
    public async Task Consume_ShouldDelete()
    {
        var stockAdjustmentReason = _fixture.Build<StockAdjustmentReason>().Create();
        var command = new DeleteStockAdjustmentReasonCommand(stockAdjustmentReason.Id);

        _stockAdjustmentReasonsRepository.Setup(x => x.GetByIdAsync(stockAdjustmentReason.Id))
            .ReturnsAsync(stockAdjustmentReason);

        await _handler.Handle(command, CancellationToken.None);
        
        _stockAdjustmentReasonsRepository.Verify(x => x.DeleteAsync(stockAdjustmentReason), Times.Once);
    }
}
