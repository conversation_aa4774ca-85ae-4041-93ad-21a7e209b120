using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Abstractions.Validators;
using Auditdata.InventoryService.Core.Constants;
using Auditdata.InventoryService.Core.Exceptions;
using Auditdata.InventoryService.Core.Features.Manufacturers.Commands;
using Auditdata.InventoryService.Core.Features.Manufacturers.Handlers;
using FluentAssertions;
using ValidationException = Auditdata.InventoryService.Core.Exceptions.ValidationException;

namespace Auditdata.InventoryService.UnitTests.Manufacturers;

public class UpdateManufacturerHandlerTests
{
    private readonly Fixture _fixture;
    private readonly Mock<IMapper> _mapper;
    private readonly UpdateManufacturerHandler _handler;
    private readonly Mock<IDbContext> _dbContext;
    private readonly Mock<IManufacturerValidator> _validator;

    public UpdateManufacturerHandlerTests()
    {
        _fixture = new Fixture();
        _mapper = new Mock<IMapper>();
        var eventPublisher = new Mock<IEventPublisher>();
        _dbContext = new Mock<IDbContext>();
        _validator = new Mock<IManufacturerValidator>();
        _handler = new UpdateManufacturerHandler(_mapper.Object, eventPublisher.Object,_dbContext.Object, _validator.Object);
    }

    [Fact]
    public async Task Handle_ShouldThrow_ValidationException_IfManufacturerNameAlreadyExists()
    {
        // Arrange
        var manufacturer = _fixture.Create<Manufacturer>();
        _dbContext.Setup(x => x.Manufacturers.FindAsync(It.IsAny<object[]>(), CancellationToken.None)).ReturnsAsync(manufacturer);
        _validator.Setup(x => x.Validate(It.IsAny<Guid>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
                .ThrowsAsync(new ValidationException(ErrorCodes.ManufacturerNameAlreadyExists));

        var request = new UpdateManufacturerCommand
        {
            Name = "test",
        };

        // Assert
        var result = async () => await _handler.Handle(request, CancellationToken.None);
        await result.Should().ThrowAsync<ValidationException>();
    }

    [Fact]
    public async Task Consume_ShouldRespond_With_UpdatedManufacturer()
    {
        var manufacturerId = Guid.NewGuid();
        var manufacturer = _fixture.Build<Manufacturer>().Create();
        var updateManufacturerCommand = new UpdateManufacturerCommand { Id = manufacturerId };
        _dbContext.Setup(x => x.Manufacturers.FindAsync(It.IsAny<object[]>(), CancellationToken.None)).ReturnsAsync(manufacturer);
        _mapper.Setup(x => x.Map(updateManufacturerCommand, manufacturer))
            .Returns(manufacturer);

        await _handler.Handle(updateManufacturerCommand, CancellationToken.None);

        _dbContext.Verify(x => x.SaveChangesAsync(CancellationToken.None));
    }

    [Fact]
    public async Task Consume_ShouldRespond_With_EntityNotFoundResult_IfEntity_NotFound()
    {
        var manufacturerId = Guid.NewGuid();
        _dbContext.Setup(x => x.Manufacturers.FindAsync(It.IsAny<object[]>())).ReturnsAsync((Manufacturer?)null);

        await Assert.ThrowsAsync<EntityNotFoundException<Manufacturer>>(async () =>
            await _handler.Handle(new UpdateManufacturerCommand { Id = manufacturerId }, CancellationToken.None));
    }
}