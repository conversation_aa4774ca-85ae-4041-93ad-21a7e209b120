using Auditdata.InventoryService.Core.Features.Manufacturers.Handlers;
using Auditdata.InventoryService.Core.Features.Manufacturers.Queries;
using Auditdata.InventoryService.UnitTests.Common;
using FluentAssertions;

namespace Auditdata.InventoryService.UnitTests.Manufacturers;

public class GetActiveManufacturersHandlerTests : BaseSqlLiteDbContextTest
{
    private readonly Fixture _fixture = new();
    private readonly GetActiveManufacturersHandler _handler;

    public GetActiveManufacturersHandlerTests()
    {
        _handler = new GetActiveManufacturersHandler(DbContext);
    }

    [Fact]
    public async Task Handle_ShouldRespond_With_GetActiveManufacturersResult()
    {
        var manufacturers = _fixture.Build<Manufacturer>()
            .With(x => x.IsDeleted, false)
            .With(x => x.IsActive, true).CreateMany().ToList();
        DbContext.Manufacturers.AddRange(manufacturers);
        await DbContext.SaveChangesAsync();

        var result = await _handler.Handle(new GetActiveManufacturersQuery(), CancellationToken.None);

        result.Manufacturers.Should().BeEquivalentTo(manufacturers);
    }
}