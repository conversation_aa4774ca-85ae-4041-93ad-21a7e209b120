using Auditdata.InventoryService.Core.Features.Manufacturers.Handlers;
using Auditdata.InventoryService.Core.Features.Manufacturers.Queries;
using Auditdata.InventoryService.UnitTests.Common;
using FluentAssertions;

namespace Auditdata.InventoryService.UnitTests.Manufacturers;

public class GetManufacturersHandlerTests : BaseDbContextTest
{
    private readonly Fixture _fixture = new();
    private readonly GetManufacturersHandler _handler;

    public GetManufacturersHandlerTests()
    {
        _handler = new GetManufacturersHandler(DbContext);
    }
    
    [Fact]
    public async Task Handle_ShouldRespond_With_GetManufacturerByProductCategoriesResult()
    {
        var manufacturers = _fixture.Build<Manufacturer>()
            .With(x => x.IsDeleted, false)
            .With(x => x.IsActive, true)
            .CreateMany().ToList();
        DbContext.Manufacturers.AddRange(manufacturers);
        await DbContext.SaveChangesAsync();

        var result = await _handler.Handle(new GetManufacturersQuery(), CancellationToken.None);

        result.Manufacturers.Should().BeEquivalentTo(manufacturers);
    }
}
