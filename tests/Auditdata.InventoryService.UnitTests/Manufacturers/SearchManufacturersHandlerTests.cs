using Auditdata.InventoryService.Core.Features.Manufacturers.Handlers;
using Auditdata.InventoryService.Core.Features.Manufacturers.Queries;
using Auditdata.InventoryService.UnitTests.Common;
using Auditdata.Transport.Contracts.PageResults;
using Auditdata.Transport.Contracts.Table;
using FluentAssertions;

namespace Auditdata.InventoryService.UnitTests.Manufacturers;

public class SearchManufacturersHandlerTests : BaseDbContextTest
{
    private readonly Fixture _fixture = new();
    private readonly SearchManufacturersHandler _handler;

    public SearchManufacturersHandlerTests()
    {
        _handler = new SearchManufacturersHandler(DbContext);
    }

    [Fact]
    public async Task Handle_ShouldRespond_With_GetManufacturerResult()
    {
        var tableQuery = new TableQueryBase { Page = 1, PerPage = 10 };
        const string name = "test";
        var manufacturers = _fixture.Build<Manufacturer>()
            .With(x => x.IsDeleted, false)
            .With(x => x.Name, name).CreateMany(tableQuery.PerPage).ToList();
        var pagedResult = new TablePageResult<Manufacturer>(manufacturers.Select(x => new TableRow<Manufacturer>(x)).ToArray(),
            tableQuery, tableQuery.PerPage);
        DbContext.Manufacturers.AddRange(manufacturers);
        await DbContext.SaveChangesAsync();

        var result = await _handler.Handle(new SearchManufacturersQuery(tableQuery, name), CancellationToken.None);

        result.Manufacturers.Should().BeEquivalentTo(pagedResult);
    }
}