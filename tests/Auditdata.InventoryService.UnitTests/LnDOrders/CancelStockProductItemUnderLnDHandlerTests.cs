using Auditdata.InventoryService.Core.Exceptions;
using Auditdata.InventoryService.Core.Features.LnDOrders.Actions.Cancel;
using Auditdata.InventoryService.UnitTests.Common;
using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Auditdata.InventoryService.UnitTests.LnDOrders;

public class CancelStockProductItemUnderLnDHandlerTests : BaseSqlLiteDbContextTest
{
    private readonly IFixture _fixture;
    private readonly CancelStockProductItemUnderLnDHandler _sut;

    public CancelStockProductItemUnderLnDHandlerTests()
    {
        _fixture = new RecursiveFixture();
        var logger = new Mock<ILogger<CancelStockProductItemUnderLnDHandler>>();
        _sut = new CancelStockProductItemUnderLnDHandler(
            logger.Object,
            DbContext);
    }

    [Fact]
    public async Task Handle_Should_CancelLnDAndSetSpiToAvailable_When_CommandProvided()
    {
        // Arrange
        var stockProductItemId = Guid.NewGuid();
        var stockProductItem = _fixture.Build<StockProductItem>()
            .With(x => x.Id, stockProductItemId)
            .With(x => x.Status, StockProductItemStatus.UnderLnD)
            .With(x => x.SerialNumber, "sn123")
            .With(x => x.IsDeleted, false)
            .Create();

        DbContext.StockProductItems.Add(stockProductItem);
        await DbContext.SaveChangesAsync();

        var savedSpi = await DbContext.StockProductItems.FirstAsync(x => x.Id == stockProductItem.Id);

        var command = _fixture.Build<CancelStockProductItemUnderLnDCommand>()
            .With(x => x.ProductId, savedSpi!.StockProduct.ProductId)
            .With(x => x.SerialNumber, "sn123")
            .With(x => x.Status, StockProductItemStatus.Available)
            .With(x => x.PatientName, "John Doe")
            .Create();

        // Act
        await _sut.Handle(command, CancellationToken.None);

        // Assert
        savedSpi.LnDOrderId.Should().BeNull();
        savedSpi.LnDInLocationId.Should().BeNull();
        savedSpi.Status.Should().Be(command.Status);
    }

    [Fact]
    public async Task Handle_Should_Throw_EntityNotFoundException_When_NotFound()
    {
        // Arrange
        var command = _fixture.Build<CancelStockProductItemUnderLnDCommand>()
            .With(x => x.ProductId, Guid.NewGuid)
            .With(x => x.SerialNumber, "sn")
            .With(x => x.Status, StockProductItemStatus.UnderLnD)
            .Create();

        // Act
        var act = async () => await _sut.Handle(command, CancellationToken.None);

        // Assert
        await act.Should().ThrowAsync<EntityNotFoundException<StockProductItem>>();
    }
}
