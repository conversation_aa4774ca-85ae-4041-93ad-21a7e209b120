using Auditdata.InventoryService.Core.Abstractions.Validators;
using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Features.LnDOrders.Actions.Exchange;
using Auditdata.InventoryService.UnitTests.Common;
using Microsoft.Extensions.Logging;
using FluentAssertions;
using Auditdata.InventoryService.Core.Exceptions;
using Auditdata.InventoryService.Core.Entities.Products;
using Auditdata.InventoryService.Core.Features.StockProductItems.Dtos;
using Attribute = Auditdata.InventoryService.Core.Entities.Attribute;
using Microsoft.EntityFrameworkCore;
using AutoFixture.Xunit2;

namespace Auditdata.InventoryService.UnitTests.LnDOrders;

public class ExchangeStockProductItemOnLnDHandlerTests : BaseSqlLiteDbContextTest
{
    private readonly IFixture _fixture;
    private readonly ExchangeStockProductItemOnLnDHandler _sut;
    private readonly Mock<IEventPublisher> _eventPublisherMock = new();
    private readonly Mock<ISerialNumbersValidator> _serialNumbersValidatorMock = new();

    public ExchangeStockProductItemOnLnDHandlerTests()
    {
        _fixture = new RecursiveFixture();
        var logger = new Mock<ILogger<ExchangeStockProductItemOnLnDHandler>>();
        _sut = new ExchangeStockProductItemOnLnDHandler(
            logger.Object,
            DbContext,
            _serialNumbersValidatorMock.Object,
            _eventPublisherMock.Object);
    }

    [Fact]
    public async Task Handle_Should_ExchangeWithExistingItem_When_NewStockProductItemIdProvided()
    {
        // Arrange
        var stock = _fixture.Create<Stock>();
        var originalItemId = Guid.NewGuid();
        var stockProductId = Guid.NewGuid();
        var productId = Guid.NewGuid();
        var newItemId = Guid.NewGuid();
        var category = new ProductCategory
        {
            Name = "Hearing Aids",
            Code = ProductCategoryCode.HearingAids
        };
        var attributeId = Guid.NewGuid();
        var manufacturer = Manufacturer.Create("Test Manufacturer");
        var attributeValues = new List<AttributeValue>
        {
            new()
            {
                ValueId = Guid.NewGuid(),
                Value = "value"
            }
        };
        var product = new Product
        {
            Id = productId,
            Name = "Test Product",
            Manufacturer = manufacturer,
            Category = category,
            IsSerialized = true,
            ControlledByStock = true,
        };
        var stockProduct = new StockProduct
        {
            Id = stockProductId,
            ProductId = productId,
            Product = product,
            Stock = stock,
        };
        stockProduct.SetQuantity(_fixture.Create<int>());
        var attribute = new Core.Entities.Attribute
        {
            Id = attributeId,
            Name = "Attribute Name",
            ValueType = AttributeValueType.SingleSelect,
            ProductCategories = new List<ProductCategory> { category },
            Values = attributeValues
        };
        var productAttribute = new ProductAttribute
        {
            ProductId = productId,
            Product = product,
            AttributeId = attributeId,
            Value = attributeValues
        };
        var attributeValue = attributeValues[0];
        var command = _fixture.Build<ExchangeStockProductItemOnLnDCommand>()
            .With(x => x.SerialNumber, "SN124")
            .With(x => x.NewStockProductItemId, newItemId)
            .With(x => x.NewProductId, stockProduct.ProductId)
            .With(x => x.ProductId, product.Id)
            .With(x => x.LocationId, stock.LocationId)
            .With(x => x.Attributes, new List<StockProductItemAttributeDto>
            {
                new(attributeId, new AttributeValueDto(attributeValue.ValueId, attributeValue.Value))
            })
            .Without(x => x.NewSerialNumber)
            .Create();

        var originalItem = new StockProductItem
        {
            Id = originalItemId,
            Status = StockProductItemStatus.UnderLnD,
            StockProductId = stockProduct.Id,
            SerialNumber = "SN124"
        };

        var newItem = new StockProductItem
        {
            Id = newItemId,
            Status = StockProductItemStatus.Available,
            StockProductId = stockProduct.Id,
            SerialNumber = "SN125"
        };

        DbContext.Set<Product>().Add(product);
        DbContext.Set<ProductCategory>().Add(category);
        DbContext.Set<Stock>().Add(stock);
        DbContext.Set<StockProduct>().Add(stockProduct);
        DbContext.Set<StockProductItem>().AddRange(originalItem, newItem);
        DbContext.Set<Attribute>().Add(attribute);
        DbContext.Set<ProductAttribute>().Add(productAttribute);
        await DbContext.SaveChangesAsync();

        // Act
        var result = await _sut.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.StockProductItemId.Should().Be(newItemId);

        var updatedOriginalItem = await DbContext.StockProductItems.FirstOrDefaultAsync(x => x.SerialNumber == "SN124");
        updatedOriginalItem.Should().NotBeNull();
        updatedOriginalItem!.Status.Should().Be(StockProductItemStatus.Exchanged);

        var updatedNewItem = await DbContext.StockProductItems.FirstOrDefaultAsync(x => x.SerialNumber == "SN125");
        updatedNewItem.Should().NotBeNull();
        updatedNewItem!.Status.Should().Be(StockProductItemStatus.Available);
        _eventPublisherMock.Verify(
            x => x.StockProductItemExchanged(
                It.Is<Guid>(id => id == originalItemId),
                It.Is<Guid>(id => id == newItemId),
                It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Theory]
    [InlineAutoData(StockProductItemStatus.Reserved)]
    [InlineAutoData(StockProductItemStatus.Sold)]
    public async Task Handle_Should_ExchangeWithExistingItem_When_NewStockProductItemIdProvidedWithNewSaleId(
        StockProductItemStatus newSpiStatus, Guid newSaleId)
    {
        // Arrange
        var stock = _fixture.Create<Stock>();
        var originalItemId = Guid.NewGuid();
        var stockProductId = Guid.NewGuid();
        var productId = Guid.NewGuid();
        var newItemId = Guid.NewGuid();
        var category = new ProductCategory
        {
            Name = "Hearing Aids",
            Code = ProductCategoryCode.HearingAids
        };
        var attributeId = Guid.NewGuid();
        var color = _fixture.Build<Color>().Create();
        var batteryType = _fixture.Build<BatteryType>().Create();
        var manufacturer = Manufacturer.Create("Test Manufacturer");
        var attributeValues = new List<AttributeValue>
        {
            new()
            {
                ValueId = Guid.NewGuid(),
                Value = "value"
            }
        };
        var product = new Product
        {
            Id = productId,
            Name = "Test Product",
            Manufacturer = manufacturer,
            Category = category,
            IsSerialized = true,
            ControlledByStock = true,
        };
        var stockProduct = new StockProduct
        {
            Id = stockProductId,
            Product = product,
            ProductId = productId,
            Stock = stock,
        };
        stockProduct.SetQuantity(_fixture.Create<int>());
        var productColor = new ProductColor
        {
            ProductId = productId,
            Product = product,
            Color = color,
            ColorId = color.Id
        };
        var productBatteryType = new ProductBatteryType
        {
            ProductId = productId,
            Product = product,
            BatteryType = batteryType,
            BatteryTypeId = batteryType.Id
        };
        var attribute = new Attribute
        {
            Id = attributeId,
            Name = "Attribute Name",
            ValueType = AttributeValueType.SingleSelect,
            ProductCategories = new List<ProductCategory> { category },
            Values = attributeValues
        };
        var productAttribute = new ProductAttribute
        {
            ProductId = productId,
            Product = product,
            AttributeId = attributeId,
            Value = attributeValues
        };
        var attributeValue = attributeValues[0];
        var command = _fixture.Build<ExchangeStockProductItemOnLnDCommand>()
            .With(x => x.SerialNumber, "SN124")
            .With(x => x.NewStockProductItemId, newItemId)
            .With(x => x.NewSaleId, newSaleId)
            .With(x => x.NewProductId, stockProduct.ProductId)
            .With(x => x.ProductBatteryTypeId, batteryType.Id)
            .With(x => x.ProductColorId, color.Id)
            .With(x => x.ProductId, product.Id)
            .With(x => x.LocationId, stock.LocationId)
            .With(x => x.Attributes, new List<StockProductItemAttributeDto>
            {
                new(attributeId, new AttributeValueDto(attributeValue.ValueId, attributeValue.Value))
            })
            .Without(x => x.NewSerialNumber)
            .Create();

        var originalItem = new StockProductItem
        {
            Id = originalItemId,
            Status = StockProductItemStatus.UnderLnD,
            StockProductId = stockProduct.Id,
            SerialNumber = "SN124"
        };

        var newItem = new StockProductItem
        {
            Id = newItemId,
            Status = newSpiStatus,
            SaleId = newSaleId,
            StockProductId = stockProduct.Id,
            SerialNumber = "SN125"
        };

        DbContext.Set<Product>().Add(product);
        DbContext.Set<ProductCategory>().Add(category);
        DbContext.Set<Stock>().Add(stock);
        DbContext.Set<StockProduct>().Add(stockProduct);
        DbContext.Set<StockProductItem>().AddRange(originalItem, newItem);
        DbContext.Set<Color>().Add(color);
        DbContext.Set<BatteryType>().Add(batteryType);
        DbContext.Set<ProductColor>().Add(productColor);
        DbContext.Set<ProductBatteryType>().Add(productBatteryType);
        DbContext.Set<Attribute>().Add(attribute);
        DbContext.Set<ProductAttribute>().Add(productAttribute);
        await DbContext.SaveChangesAsync();

        // Act
        var result = await _sut.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.StockProductItemId.Should().Be(newItemId);

        var updatedOriginalItem = await DbContext.StockProductItems.FirstOrDefaultAsync(x => x.SerialNumber == "SN124");
        updatedOriginalItem.Should().NotBeNull();
        updatedOriginalItem!.Status.Should().Be(StockProductItemStatus.Exchanged);

        var updatedNewItem = await DbContext.StockProductItems.FirstOrDefaultAsync(x => x.SerialNumber == "SN125");
        updatedNewItem.Should().NotBeNull();
        updatedNewItem!.Status.Should().Be(newSpiStatus);
        _eventPublisherMock.Verify(
            x => x.StockProductItemExchanged(
                It.Is<Guid>(id => id == originalItemId),
                It.Is<Guid>(id => id == newItemId),
                It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact]
    public async Task Handle_Should_Throw_NotSupportedException_When_BothExchangeMethodsAreSpecified()
    {
        // Arrange
        var command = _fixture.Build<ExchangeStockProductItemOnLnDCommand>()
            .With(x => x.NewStockProductItemId, Guid.NewGuid())
            .With(x => x.NewSerialNumber, "SN123")
            .Create();

        // Act
        var act = async () => await _sut.Handle(command, CancellationToken.None);

        // Assert
        await act.Should().ThrowAsync<NotSupportedException>()
            .WithMessage("Unable to exchange item: both or neither exchange methods are specified.");
    }

    [Fact]
    public async Task Handle_Should_Throw_EntityNotFoundException_When_OriginalStockProductItemNotFound()
    {
        // Arrange
        var command = _fixture.Build<ExchangeStockProductItemOnLnDCommand>()
            .With(x => x.NewStockProductItemId, Guid.NewGuid())
            .With(x => x.NewSerialNumber, string.Empty)
            .Create();

        // Act
        var act = async () => await _sut.Handle(command, CancellationToken.None);

        // Assert
        await act.Should().ThrowAsync<EntityNotFoundException<StockProductItem>>();
    }

    [Fact]
    public async Task Handle_Should_ExchangeWithNewItem_When_NewSerialNumberProvided()
    {
        // Arrange
        var stock = _fixture.Create<Stock>();
        var originalItemId = Guid.NewGuid();
        var stockProductId = Guid.NewGuid();
        var productId = Guid.NewGuid();
        var category = new ProductCategory
        {
            Name = "Hearing Aids",
            Code = ProductCategoryCode.HearingAids
        };
        var attributeId = Guid.NewGuid();
        var color = _fixture.Build<Color>().Create();
        var batteryType = _fixture.Build<BatteryType>().Create();
        var manufacturer = Manufacturer.Create("Test Manufacturer");
        var attributeValues = new List<AttributeValue>
        {
            new()
            {
                ValueId = Guid.NewGuid(),
                Value = "value"
            }
        };
        var product = new Product
        {
            Id = productId,
            Name = "Test Product",
            Manufacturer = manufacturer,
            Category = category,
            IsSerialized = true,
            ControlledByStock = true,
        };
        var stockProduct = new StockProduct
        {
            Id = stockProductId,
            ProductId = productId,
            Product = product,
            Stock = stock,
        };
        stockProduct.SetQuantity(_fixture.Create<int>());
        var productColor = new ProductColor
        {
            ProductId = productId,
            Product = product,
            Color = color,
            ColorId = color.Id
        };
        var productBatteryType = new ProductBatteryType
        {
            ProductId = productId,
            Product = product,
            BatteryType = batteryType,
            BatteryTypeId = batteryType.Id
        };
        var attribute = new Core.Entities.Attribute
        {
            Id = attributeId,
            Name = "Attribute Name",
            ValueType = AttributeValueType.SingleSelect,
            ProductCategories = new List<ProductCategory> { category },
            Values = attributeValues
        };
        var productAttribute = new ProductAttribute
        {
            ProductId = productId,
            Product = product,
            AttributeId = attributeId,
            Value = attributeValues
        };
        var attributeValue = attributeValues[0];
        var command = _fixture.Build<ExchangeStockProductItemOnLnDCommand>()
            .With(x => x.NewSerialNumber, "SN123")
            .With(x => x.SerialNumber, "SN124")
            .With(x => x.NewProductId, stockProduct.ProductId)
            .With(x => x.ProductBatteryTypeId, batteryType.Id)
            .With(x => x.ProductColorId, color.Id)
            .With(x => x.ProductId, product.Id)
            .With(x => x.LocationId, stock.LocationId)
            .With(x => x.Attributes, new List<StockProductItemAttributeDto>
            {
                new(attributeId, new AttributeValueDto(attributeValue.ValueId, attributeValue.Value))
            })
            .Without(x => x.NewStockProductItemId)
            .Create();

        var originalItem = new StockProductItem
        {
            Id = originalItemId,
            Status = StockProductItemStatus.UnderLnD,
            StockProductId = stockProduct.Id,
            SerialNumber = "SN124"
        };

        DbContext.Set<Product>().Add(product);
        DbContext.Set<ProductCategory>().Add(category);
        DbContext.Set<Stock>().Add(stock);
        DbContext.Set<StockProduct>().Add(stockProduct);
        DbContext.Set<StockProductItem>().Add(originalItem);
        DbContext.Set<Color>().Add(color);
        DbContext.Set<BatteryType>().Add(batteryType);
        DbContext.Set<ProductColor>().Add(productColor);
        DbContext.Set<ProductBatteryType>().Add(productBatteryType);
        DbContext.Set<Attribute>().Add(attribute);
        DbContext.Set<ProductAttribute>().Add(productAttribute);
        await DbContext.SaveChangesAsync();

        _serialNumbersValidatorMock.Setup(x =>
            x.CheckDuplicatedSerialNumbers(It.IsAny<Guid>(), It.IsAny<IEnumerable<string>>(), CancellationToken.None))
            .Returns(Task.CompletedTask);

        // Act
        var result = await _sut.Handle(command, CancellationToken.None);

        // Assert
        result.StockProductItemId.Should().NotBeEmpty();

        var newItem = await DbContext.StockProductItems.FirstOrDefaultAsync(x => x.SerialNumber == command.NewSerialNumber);
        newItem.Should().NotBeNull();
        newItem!.Status.Should().Be(StockProductItemStatus.Available);
        newItem.StockProductId.Should().Be(stockProductId);

        newItem!.ColorId.Should().Be(command.ProductColorId);
        newItem!.Color!.Name.Should().Be(color.Name);
        newItem!.BatteryTypeId.Should().Be(command.ProductBatteryTypeId);
        newItem!.BatteryType!.Name.Should().Be(batteryType.Name);

        _serialNumbersValidatorMock.Verify(x =>
            x.CheckDuplicatedSerialNumbers(
                It.Is<Guid>(id => id == product.ManufacturerId!.Value),
                It.Is<IEnumerable<string>>(serials => serials.Contains(command.NewSerialNumber)),
                CancellationToken.None),
            Times.Once);

        _eventPublisherMock.Verify(x =>
            x.SerializedStockAdjusted(
                It.Is<StockProduct>(sp => sp.Id == stockProductId),
                1,
                It.Is<IEnumerable<StockProductItem>>(items => items.Any(item => item.SerialNumber == command.NewSerialNumber)),
                CancellationToken.None),
            Times.Once);
    }
}
