using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Entities.Products;
using Auditdata.InventoryService.Core.Features.LnDOrders.Actions.Complete;
using Auditdata.InventoryService.UnitTests.Common;
using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Auditdata.InventoryService.UnitTests.LnDOrders;

public class CompleteStockProductItemUnderLnDHandlerTests : IClassFixture<BaseDbContextTest>
{
    private readonly CompleteStockProductItemUnderLnDHandler _sut;
    private readonly IDbContext _dbContext;

    public CompleteStockProductItemUnderLnDHandlerTests(BaseDbContextTest baseDbContextTest)
    {
        var logger = new Mock<ILogger<CompleteStockProductItemUnderLnDHandler>>();
        _dbContext = baseDbContextTest.DbContext;
        _sut = new CompleteStockProductItemUnderLnDHandler(
            logger.Object,
            _dbContext);
    }

    [Fact]
    public async Task Handle_Should_LogReturnedAndDeliveredByLnDOrder_When_SoldStatus()
    {
        // Arrange
        var locationId = Guid.NewGuid();
        var stock = new Stock
        {
            Id = Guid.NewGuid(),
            LocationId = locationId,
            Name = "Test Stock"
        };

        var category = new ProductCategory
        {
            Id = Guid.NewGuid(),
            Code = ProductCategoryCode.HearingAids,
            Name = "Category Name",
            IsDeleted = false
        };

        var manufacturer = Manufacturer.Create("TestManufacturer");

        var product = new Product
        {
            Id = Guid.NewGuid(),
            Name = "Test Product",
            Manufacturer = manufacturer,
            Category = category,
            ControlledByStock = true,
        };

        var stockProduct = new StockProduct
        {
            Id = Guid.NewGuid(),
            Product = product,
            Stock = stock,
        };
        stockProduct.SetQuantity(10);

        var stockProductItem = new StockProductItem
        {
            Id = Guid.NewGuid(),
            Status = StockProductItemStatus.Sold,
            StockProduct = stockProduct,
            LnDOrderId = Guid.NewGuid(),
            LnDInLocationId = locationId
        };

        var request = new CompleteStockProductItemUnderLnDCommand
        {
            ProductId = product.Id,
            SerialNumber = stockProductItem.SerialNumber!,
            Status = StockProductItemStatus.Sold,
            Number = "LnD123",
            PatientName = "PatientName"
        };

        _dbContext.Products.Add(product);
        _dbContext.Stocks.Add(stock);
        _dbContext.StockProducts.Add(stockProduct);
        _dbContext.StockProductItems.Add(stockProductItem);
        await _dbContext.SaveChangesAsync();

        // Act
        await _sut.Handle(request, default);

        // Assert
        var updatedStockProductItem = await _dbContext.StockProductItems
            .FirstOrDefaultAsync(x => x.Id == stockProductItem.Id);

        updatedStockProductItem.Should().NotBeNull();
        updatedStockProductItem!.Status.Should().Be(StockProductItemStatus.Sold);
        updatedStockProductItem.LnDOrderId.Should().BeNull();
        updatedStockProductItem.LnDInLocationId.Should().BeNull();

        var logEntry = await _dbContext.StockProductItemLogs
            .FirstOrDefaultAsync(x => x.StockProductItemId == stockProductItem.Id);
        logEntry.Should().NotBeNull();
    }

    [Fact]
    public async Task Handle_Should_LogReturnedAndDeliveredByLnDOrder_When_AvailableStatus()
    {
        // Arrange
        var locationId = Guid.NewGuid();
        var stock = new Stock
        {
            Id = Guid.NewGuid(),
            LocationId = locationId,
            Name = "Test Stock"
        };

        var category = new ProductCategory
        {
            Id = Guid.NewGuid(),
            Code = ProductCategoryCode.HearingAids,
            Name = "Category Name",
            IsDeleted = false
        };

        var manufacturer = Manufacturer.Create("TestManufacturer");

        var product = new Product
        {
            Id = Guid.NewGuid(),
            Name = "Test Product",
            Manufacturer = manufacturer,
            Category = category,
            ControlledByStock = true,
        };

        var stockProduct = new StockProduct
        {
            Id = Guid.NewGuid(),
            Product = product,
            Stock = stock,
        };
        stockProduct.SetQuantity(10);

        var stockProductItem = new StockProductItem
        {
            Id = Guid.NewGuid(),
            Status = StockProductItemStatus.Available,
            StockProduct = stockProduct,
            LnDOrderId = Guid.NewGuid(),
            LnDInLocationId = locationId
        };

        var request = new CompleteStockProductItemUnderLnDCommand
        {
            ProductId = product.Id,
            SerialNumber = stockProductItem.SerialNumber!,
            Status = StockProductItemStatus.Available,
            Number = "LnD123",
            PatientName = "PatientName"
        };

        _dbContext.Products.Add(product);
        _dbContext.Stocks.Add(stock);
        _dbContext.StockProducts.Add(stockProduct);
        _dbContext.StockProductItems.Add(stockProductItem);
        await _dbContext.SaveChangesAsync();

        // Act
        await _sut.Handle(request, default);

        // Assert
        var updatedStockProductItem = await _dbContext.StockProductItems
            .FirstOrDefaultAsync(x => x.Id == stockProductItem.Id);

        updatedStockProductItem.Should().NotBeNull();
        updatedStockProductItem!.Status.Should().Be(StockProductItemStatus.Available);
        updatedStockProductItem.LnDOrderId.Should().BeNull();
        updatedStockProductItem.LnDInLocationId.Should().BeNull();

        var logEntry = await _dbContext.StockProductItemLogs
            .FirstOrDefaultAsync(x => x.StockProductItemId == stockProductItem.Id);
        logEntry.Should().NotBeNull();
    }
}
