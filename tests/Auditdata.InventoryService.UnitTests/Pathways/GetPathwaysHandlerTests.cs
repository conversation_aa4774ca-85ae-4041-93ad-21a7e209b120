using Auditdata.InventoryService.Core.Features.Pathways.Handlers;
using Auditdata.InventoryService.Core.Features.Pathways.Queries;
using Auditdata.InventoryService.UnitTests.Common;
using FluentAssertions;

namespace Auditdata.InventoryService.UnitTests.Pathways;

public class GetPathwaysHandlerTests : BaseDbContextTest
{
    private readonly Fixture _fixture = new Fixture();
    private readonly GetPathwaysHandler _handler;

    public GetPathwaysHandlerTests()
    {
        _handler = new GetPathwaysHandler(DbContext);
    }

    [Fact]
    public async Task Consume_ShouldRespond_With_GetPathwaysResult()
    {
        var query = _fixture.Build<GetPathwaysQuery>().Create();

        var pathways = _fixture.Build<Pathway>().With(x => x.IsDeleted, false).CreateMany().ToList();
        DbContext.Pathways.AddRange(pathways);
        await DbContext.SaveChangesAsync();

        var result = await _handler.<PERSON>le(query, CancellationToken.None);

        result.Pathways.Intersect(pathways).Should().BeEquivalentTo(pathways);
    }
}