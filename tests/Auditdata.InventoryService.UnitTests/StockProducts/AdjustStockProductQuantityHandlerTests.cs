using Auditdata.InventoryService.Core.Constants;
using Auditdata.InventoryService.Core.Entities.Products;
using Auditdata.InventoryService.Core.Features.StockProducts.Commands;
using Auditdata.InventoryService.Core.Features.StockProducts.Events;
using Auditdata.InventoryService.Core.Features.StockProducts.Handlers;
using Auditdata.InventoryService.UnitTests.Common;
using Auditdata.Transport.Contracts.Exceptions;
using FluentAssertions;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Auditdata.InventoryService.UnitTests.StockProducts;

public class AdjustStockProductQuantityHandlerTests : BaseSqlLiteDbContextTest
{
    private readonly IFixture _fixture;
    private readonly AdjustStockProductQuantityHandler _handler;
    private readonly Mock<IMediator> _mediator = new();

    public AdjustStockProductQuantityHandlerTests()
    {
        _fixture = new RecursiveFixture();
        var stockProductsRepository = new Mock<IStockProductsRepository>();

        _handler = new AdjustStockProductQuantityHandler(
            stockProductsRepository.Object,
            DbContext,
            _mediator.Object);
    }


    //TODO: Fix
    // [Fact]
    // public async Task Handle_ShouldRespond_StockProductQuantity()
    // {
    //     var command = _fixture.Build<AdjustStockProductQuantityCommand>().Create();
    //     await _handler.Handle(command, CancellationToken.None);
    // }

    [Fact]
    public async Task Handle_ShouldIncrease_StockProductQuantity()
    {
        // Arrange
        var category = new ProductCategory()
        {
            Code = ProductCategoryCode.Accessories,
            Name = "test",
        };
        var manufacturer = Manufacturer.Create("Test");
        var stock = _fixture.Build<Stock>()
            .With(x => x.Id, Guid.NewGuid())
            .Create();
        var product = new Product
        {
            Id = Guid.NewGuid(),
            Name = "product",
            Manufacturer = manufacturer,
            Category = category,
            IsDeleted = false,
            ControlledByStock = true,
        };
        var stockProduct = _fixture.Build<StockProduct>()
            .With(x => x.Product, product)
            .With(x => x.Stock, stock)
            .With(x => x.IsDeleted, false)
            .Without(x => x.StockProductItems)
            .Create();

        DbContext.StockProducts.Add(stockProduct);

        await DbContext.SaveChangesAsync();

        var quantity = 5;
        var expectedQuantity = stockProduct.Quantity + quantity;
        
        var command = _fixture.Build<AdjustStockProductQuantityCommand>()
            .With(x => x.Quantity, quantity)
            .With(x => x.ProductId, product.Id)
            .With(x => x.StockId, stock.Id)
            .Create();

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        var updatedStockProduct =
            await DbContext.StockProducts.FirstAsync(x => x.StockId == stock.Id && x.ProductId == product.Id);

        updatedStockProduct.Quantity.Should().Be(expectedQuantity);
        _mediator.Verify(x => x.Publish(It.IsAny<StockProductAdjustedEvent>(), CancellationToken.None));
    }

    [Fact]
    public async Task Handle_ShouldIncrease_StockProductQuantity_WithPreOrderedItems()
    {
        // Arrange
        var stock = _fixture.Build<Stock>()
            .With(x => x.Id, Guid.NewGuid())
            .Create();
        var category = new ProductCategory()
        {
            Code = ProductCategoryCode.Accessories,
            Name = "test",
        };
        var manufacturer = Manufacturer.Create("Test");
        var product = new Product
        {
            Id = Guid.NewGuid(),
            Name = "test",
            Manufacturer = manufacturer,
            Category = category,
            IsSerialized = false,
            IsDeleted = false,
            ControlledByStock = true,
        };
        var stockProduct = _fixture.Build<StockProduct>()
            .With(x => x.Product, product)
            .With(x => x.Stock, stock)
            .With(x => x.IsDeleted, false)
            .Create();
        stockProduct.SetQuantity(10);
        var preOrderedItemsCount = 2;
        var preOrderedItems = _fixture.Build<StockProductItem>()
            .With(x => x.StockProduct, stockProduct)
            .With(x => x.IsDeleted, false)
            .CreateMany(preOrderedItemsCount);
        DbContext.StockProducts.Add(stockProduct);
        DbContext.StockProductItems.AddRange(preOrderedItems);

        await DbContext.SaveChangesAsync();

        var command = _fixture.Build<AdjustStockProductQuantityCommand>()
            .With(x => x.Quantity, 5)
            .With(x => x.ProductId, product.Id)
            .With(x => x.StockId, stock.Id)
            .Create();

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        stockProduct.Quantity.Should().Be(15);
        _mediator.Verify(x=> x.Publish(It.IsAny<StockProductAdjustedEvent>(),CancellationToken.None));
    }

    [Fact]
    public async Task Handle_ShouldReduce_StockProductQuantity_IfProductNotSerialized()
    {
        var negativeAdjustmentReason = _fixture.Build<StockAdjustmentReason>()
            .With(x => x.IsDeleted, false)
            .Create();
        DbContext.StockAdjustmentReasons.Add(negativeAdjustmentReason);
        var stock = _fixture.Build<Stock>()
            .With(x => x.Id, Guid.NewGuid())
            .Create();
        var product = _fixture.Build<Product>()
            .With(x => x.IsSerialized, false)
            .With(x => x.IsDeleted, false)
            .With(x => x.ControlledByStock, true)
            .With(x => x.Id, Guid.NewGuid())
            .Without(x => x.StockProducts)
            .Create();
        var stockProduct = _fixture.Build<StockProduct>()
            .With(x => x.Product, product)
            .With(x => x.Stock, stock)
            .With(x => x.IsDeleted, false)
            .Without(x => x.StockProductItems)
            .Create();
        stockProduct.SetQuantity(75);
        DbContext.StockProducts.Add(stockProduct);

        await DbContext.SaveChangesAsync();

        var quantity = -5;
        var command = _fixture.Build<AdjustStockProductQuantityCommand>()
            .With(x => x.Quantity, quantity)
            .With(x => x.ProductId, product.Id)
            .With(x => x.StockId, stock.Id)
            .With(x => x.ReasonId, negativeAdjustmentReason.Id)
            .Create();

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        stockProduct.Quantity.Should().Be(70);

        _mediator.Verify(x=> x.Publish(It.IsAny<StockProductAdjustedEvent>(),CancellationToken.None));
    }

    [Fact]
    public async Task Handle_ShouldRespond_NoQuantityAvailable()
    {
        // Arrange
        var stock = _fixture.Build<Stock>()
            .With(x => x.Id, Guid.NewGuid())
            .Create();
        var category = new ProductCategory()
        {
            Code = ProductCategoryCode.Accessories,
            Name = "test",
        };
        var manufacturer = Manufacturer.Create("Test");
        var product = new Product
        {
            Id = Guid.NewGuid(),
            Name = "test",
            Manufacturer = manufacturer,
            Category = category,
            IsSerialized = false,
            IsDeleted = false,
            ControlledByStock = true,
        };
        var stockProduct = _fixture.Build<StockProduct>()
            .With(x => x.Product, product)
            .With(x => x.Stock, stock)
            .With(x => x.IsDeleted, false)
            .Create();
        stockProduct.SetQuantity(1);
        DbContext.StockProducts.Add(stockProduct);

        var stockProductItems = _fixture.Build<StockProductItem>()
            .With(x => x.StockProduct, stockProduct)
            .With(x => x.IsDeleted, false)
            .CreateMany(1);
        DbContext.StockProductItems.AddRange(stockProductItems);

        await DbContext.SaveChangesAsync();

        var quantity = -5;
        var command = _fixture.Build<AdjustStockProductQuantityCommand>()
            .With(x => x.Quantity, quantity)
            .With(x => x.ProductId, product.Id)
            .With(x => x.StockId, stock.Id)
            .Create();

        // Act
        var act = async () => await _handler.Handle(command, CancellationToken.None);

        // Assert
        var exception = await act.Should().ThrowExactlyAsync<BusinessException>();
        exception.Which.ErrorCode.Should().Be(ErrorCodes.StockProductNegativeAdjustment);
    }

    [Fact]
    public async Task Handle_ShouldThrow_IfProductSerialized()
    {
        var stock = _fixture.Build<Stock>()
            .With(x => x.Id, Guid.NewGuid())
            .Create();
        var category = new ProductCategory()
        {
            Code = ProductCategoryCode.Accessories,
            Name = "test",
        };
        var manufacturer = Manufacturer.Create("Test");
        var product = new Product
        {
            Id = Guid.NewGuid(),
            Name = "test",
            Manufacturer = manufacturer,
            Category = category,
            IsSerialized = true,
            IsDeleted = false,
            ControlledByStock = true,
        };
        var stockProduct = _fixture.Build<StockProduct>()
            .With(x => x.Product, product)
            .With(x => x.Stock, stock)
            .With(x => x.IsDeleted, false)
            .Create();
        stockProduct.SetQuantity(10);
        DbContext.StockProducts.Add(stockProduct);

        await DbContext.SaveChangesAsync();

        var quantity = -5;
        var command = _fixture.Build<AdjustStockProductQuantityCommand>()
            .With(x => x.Quantity, quantity)
            .With(x => x.ProductId, product.Id)
            .With(x => x.StockId, stock.Id)
            .Create();

        await Assert.ThrowsAsync<BusinessException>(async () => await _handler.Handle(command, CancellationToken.None));
    }
}