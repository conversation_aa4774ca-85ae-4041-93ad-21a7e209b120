using Auditdata.InventoryService.Core.Entities.Products;
using Auditdata.InventoryService.Core.Features.StockProducts.Handlers;
using Auditdata.InventoryService.Core.Features.StockProducts.Queries;
using Auditdata.InventoryService.UnitTests.Common;
using FluentAssertions;

namespace Auditdata.InventoryService.UnitTests.StockProducts;

public class GetInvoiceStockProductsHandlerTests : BaseDbContextTest
{
    private readonly IFixture _fixture;
    private readonly GetInvoiceNhsStockProductsHandler _handler;

    public GetInvoiceStockProductsHandlerTests()
    {
        _fixture = new RecursiveFixture();
        _handler = new GetInvoiceNhsStockProductsHandler(DbContext);
    }

    [Fact]
    public async Task Consume_ShouldRespond_With_GetInvoiceStockProductsQuery()
    {
        var ukProduct = _fixture.Create<UKProduct>();
        var stockProduct = _fixture.Build<StockProduct>().With(x => x.Product, ukProduct).Create();
        stockProduct.IsDeleted = false;
        stockProduct.Product.IsDeleted = false;
        stockProduct.Product.IsActive = true;
        stockProduct.Product.IsSellable = true;

        DbContext.StockProducts.AddRange(stockProduct);
        await DbContext.SaveChangesAsync(CancellationToken.None);

        var result = await _handler.Handle(new GetInvoiceNhsStockProductsQuery()
        {
            LocationId = stockProduct.Stock.LocationId
        }, CancellationToken.None);

        result.StockProducts.Pagination!.Total.Should().Be(1);
    }
}