using Auditdata.InventoryService.Core.Entities.Products;
using Auditdata.InventoryService.Core.Features.StockProducts.Handlers;
using Auditdata.InventoryService.Core.Features.StockProducts.Queries;
using Auditdata.InventoryService.UnitTests.Common;
using FluentAssertions;

namespace Auditdata.InventoryService.UnitTests.StockProducts;

public class GetInStockAmountHandlerTests : BaseSqlLiteDbContextTest
{
    private readonly GetInStockAmountHandler _sut;
    private readonly IFixture _fixture = new RecursiveFixture();

    public GetInStockAmountHandlerTests()
    {
        _sut = new GetInStockAmountHandler(DbContext);
    }

    [Fact]
    public async Task Handle_Should_GetInStockAnount_When_QueryProvided()
    {
        // Arrange
        var stock = _fixture.Build<Stock>().Create();
        var product = _fixture.Build<Product>().Create();
        var stockProduct = _fixture.Build<StockProduct>()
            .With(x => x.IsDeleted, false)
            .With(x => x.Stock, stock).With(x => x.StockId, stock.Id)
            .With(x => x.Product, product).With(x => x.ProductId, product.Id)
            .Without(x => x.StockProductItems)
            .Create();
        var stockProductItems = _fixture.Build<StockProductItem>()
            .With(x => x.IsDeleted, false)
            .With(x => x.StockProduct, stockProduct).With(x => x.StockProductId, stockProduct.Id)
            .With(x => x.Status, StockProductItemStatus.Reserved)
            .CreateMany().ToList();
        stockProductItems[1].Status = StockProductItemStatus.Sold;
        stockProduct.StockProductItems = stockProductItems;
        DbContext.Stocks.Add(stock);
        DbContext.Products.Add(product);
        DbContext.StockProducts.Add(stockProduct);
        DbContext.StockProductItems.AddRange(stockProductItems);
        await DbContext.SaveChangesAsync();

        var query = new GetInStockAmountQuery(stockProduct.ProductId, new[] { stock.LocationId });

        // Act
        var result = await _sut.Handle(query, CancellationToken.None);

        // Assert
        result.InStock.Should().Be(stockProduct.Quantity + stockProductItems.Count - 1);
    }
}
