using Auditdata.InventoryService.Core.Constants;
using Auditdata.InventoryService.Core.Entities.Products;
using Auditdata.InventoryService.Core.Features.Products.Commands;
using Auditdata.InventoryService.Core.Features.Products.Handlers;
using Auditdata.InventoryService.Core.Features.Products.Models;
using Auditdata.InventoryService.UnitTests.Common;
using Auditdata.Transport.Contracts.Exceptions;
using FluentAssertions;
using MediatR;

namespace Auditdata.InventoryService.UnitTests.StockProducts;

public class ReserveProductHandlerTests : BaseDbContextTest
{
    private readonly IFixture _fixture = new RecursiveFixture();
    private readonly ReserveProductHandler _sut;

    public ReserveProductHandlerTests()
    {
        _sut = new ReserveProductHandler(DbContext, new Mock<IMediator>().Object);
    }

    [Fact]
    public async Task Handle_ShouldThrow_BusinessException_IfAtLeast_1_StockProductItem_IsNull()
    {
        //Arrange
        var command = new ReserveProductCommand(Guid.NewGuid(), new List<ReserveProductItem>
        {
            new(Guid.Empty, 1, Guid.NewGuid(), string.Empty),
            new(Guid.Empty, 1, null, string.Empty)
        }, Guid.NewGuid(), string.Empty);
        
        //Act
        var action = () => _sut.Handle(command, CancellationToken.None);
        
        //Assert
        (await action.Should().ThrowAsync<BusinessException>()).Which.ErrorCode.Should()
            .Be(ErrorCodes.StockProductItemReservationEmptyId);
    }

    [Fact]
    public async Task Handle_ShouldThrow_BusinessException_IfItems_Duplicate()
    {
        //Arrange
        var stockProductItemId = Guid.NewGuid();
        var command = new ReserveProductCommand(Guid.NewGuid(), new List<ReserveProductItem>
        {
            new(Guid.Empty, 1, stockProductItemId, string.Empty),
            new(Guid.Empty, 1, stockProductItemId, string.Empty)
        }, Guid.NewGuid(), string.Empty);
        
        //Act
        var action = () => _sut.Handle(command, CancellationToken.None);
        
        //Assert
        (await action.Should().ThrowAsync<BusinessException>()).Which.ErrorCode.Should()
            .Be(ErrorCodes.StockProductItemReservationDuplicateItem);
    }
    
    [Fact]
    public async Task Handle_ShouldReserve_StockProductItems()
    {
        //Arrange
        var product = _fixture.Build<Product>()
            .With(x => x.IsDeleted, false)
            .With(x => x.IsActive, true)
            .With(x => x.IsSellable, true)
            .With(x => x.IsSerialized, true)
            .With(x => x.ControlledByStock, true)
            .Without(x => x.StockProducts)
            .Create();
        var stockProduct = _fixture.Build<StockProduct>()
            .With(x => x.IsDeleted, false)
            .With(x => x.Product, product)
            .With(x => x.StockProductItems, new List<StockProductItem>
            {
                new()
                {
                    Id = Guid.NewGuid(),
                    SerialNumber = "test1",
                    Status = StockProductItemStatus.Available,
                    IsDeleted = false
                },
                new()
                {
                    Id = Guid.NewGuid(),
                    SerialNumber = "test2",
                    Status = StockProductItemStatus.Available,
                    IsDeleted = false
                }
            })
            .Create();
        stockProduct.SetQuantity(stockProduct.StockProductItems.Count);
        DbContext.StockProducts.Add(stockProduct);
        await DbContext.SaveChangesAsync();

        var command = new ReserveProductCommand(
            Guid.NewGuid(),
            stockProduct.StockProductItems.Select(x => new ReserveProductItem(Guid.NewGuid(), 1, x.Id, string.Empty)),
            Guid.NewGuid(),
            string.Empty);
        
        //Act
        var result = await _sut.Handle(command, CancellationToken.None);
        
        //Assert
        result.Should().HaveCount(2);
        result.Should().SatisfyRespectively(
            x1 =>
            {
                x1.SerialNumber.Should().Be("test1");
                x1.StockProductId.Should().Be(stockProduct.Id);
                x1.StockProductItemId.Should().Be(stockProduct.StockProductItems[0].Id);
            },
            x2 =>
            {
                x2.SerialNumber.Should().Be("test2");
                x2.StockProductId.Should().Be(stockProduct.Id);
                x2.StockProductItemId.Should().Be(stockProduct.StockProductItems[1].Id);
            });
    }
}