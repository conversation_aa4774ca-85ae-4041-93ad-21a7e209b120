using Auditdata.InventoryService.Core.Features.StockProducts.Handlers;
using Auditdata.InventoryService.Core.Features.StockProducts.Queries;
using Auditdata.InventoryService.UnitTests.Common;
using Auditdata.Transport.Contracts.PageResults;
using Auditdata.Transport.Contracts.Table;
using FluentAssertions;

namespace Auditdata.InventoryService.UnitTests.StockProducts;

public class SearchStockProductsHandlerTests
{
    private readonly IFixture _fixture;
    private readonly Mock<IProductsRepository> _productsRepository;
    private readonly SearchStockProductsHandler _handler;

    public SearchStockProductsHandlerTests()
    {
        _fixture = new RecursiveFixture();
        _productsRepository = new Mock<IProductsRepository>();
        _handler = new SearchStockProductsHandler(_productsRepository.Object);
    }

    [Fact]
    public async Task Consume_ShouldRespond_With_StockProductsSearchResult()
    {
        var query = _fixture.Build<SearchStockProductsQuery>().Create();
        var productInStock = _fixture.Build<ProductInStock>().Create();
        var pagedResult = new TablePageResult<ProductInStock>
        {
            Pagination = new PaginationBase(1, 1, string.Empty, 10),
            Rows = new []
            {
                new TableRow<ProductInStock>(productInStock)
            }
        };

        _productsRepository.Setup(x => x.SearchStockProductsAsync(
                query, query.SearchText, query.CategoryId, query.ManufacturerId))
            .ReturnsAsync(pagedResult);

        var result = await _handler.Handle(query, CancellationToken.None);

        result.Rows!.Length.Should().Be(pagedResult.Rows.Length);
    }
}