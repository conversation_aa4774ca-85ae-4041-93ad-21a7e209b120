using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Abstractions.Azure;
using Auditdata.InventoryService.Core.Entities.Products;
using Auditdata.InventoryService.Core.Features.Products.Mappings;
using Auditdata.InventoryService.Core.Features.StockProducts.Commands;
using Auditdata.InventoryService.Core.Features.StockProducts.Handlers;
using Auditdata.InventoryService.UnitTests.Common;
using Auditdata.Microservice.Messages.Events.Inventory;
using FluentAssertions;
using Microsoft.Extensions.Logging;

namespace Auditdata.InventoryService.UnitTests.StockProducts;

public class CreateStockProductsForAllStocksHandlerTest : IClassFixture<BaseDbContextTest>
{
    private readonly IFixture _fixture;
    private readonly Mock<IAzureServiceBusPublisher> _azureServiceBusPublisherMock;
    private readonly Mock<ILogger<CreateStockProductsForAllStocksHandler>> _loggerMock;
    private readonly CreateStockProductsForAllStocksHandler _handler;
    private readonly IDbContext _dbContext;

    public CreateStockProductsForAllStocksHandlerTest(BaseDbContextTest baseDbContextTest)
    {
        _fixture = new RecursiveFixture();
        _dbContext = baseDbContextTest.DbContext;

        _azureServiceBusPublisherMock = new Mock<IAzureServiceBusPublisher>();
        _loggerMock = new Mock<ILogger<CreateStockProductsForAllStocksHandler>>();
        var mapper = new MapperConfiguration(x => x.AddProfile<ProductMappings>()).CreateMapper();
        _handler = new CreateStockProductsForAllStocksHandler(
            mapper,
            _azureServiceBusPublisherMock.Object,
            _dbContext,
            _loggerMock.Object);
    }

    [Fact]
    public async Task Consume_ShouldCreate_StockProducts()
    {
        var productId = Guid.NewGuid();
        var product = _fixture.Build<Product>().With(x => x.Id, productId)
            .With(x => x.IsDeleted, false)
            .Without(x=> x.StockProducts).Create();
        var stocks = _fixture.CreateMany<Stock>().ToList();

        _dbContext.Products.Add(product);
        _dbContext.Stocks.AddRange(stocks);
        await _dbContext.SaveChangesAsync();
        
        await _handler.Handle(new CreateStockProductsForAllStocksCommand(productId), CancellationToken.None);

        _azureServiceBusPublisherMock.Verify(x =>
            x.PublishStockProductCreated(It.IsAny<IEnumerable<StockProductCreated>>()),Times.AtLeastOnce);

        _dbContext.StockProducts.ToList().Count.Should().Be(stocks.Count);
    }
}