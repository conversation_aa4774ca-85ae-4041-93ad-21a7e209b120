using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Features.StockProductItems.Commands;
using Auditdata.InventoryService.Core.Features.StockProductItems.Events;
using Auditdata.InventoryService.Core.Features.StockProductItems.Handlers;
using Auditdata.InventoryService.UnitTests.Common;
using FluentAssertions;
using IMediator = MediatR.IMediator;

namespace Auditdata.InventoryService.UnitTests.StockProducts.SwapReservation;

public class SwapReservationCommandHandlerTests : IClassFixture<BaseDbContextTest>
{
    private readonly SwapReservationCommandHandler _sut;
    private readonly IFixture _fixture;
    private readonly Mock<IMediator> _mediatorMock;
    private readonly IDbContext _dbContext;

    public SwapReservationCommandHandlerTests(BaseDbContextTest baseDbContextTest)
    {
        _mediatorMock = new Mock<IMediator>();
        _dbContext = baseDbContextTest.DbContext;
        _sut = new SwapReservationCommandHandler(_dbContext, _mediatorMock.Object);
        _fixture = new RecursiveFixture();
    }

    [RecursiveInlineAutoData]
    [Theory]
    public async Task Handle_Should_Swap_Reservation(StockProduct stockProduct, string newSerialNumber, string oldSerialNumber)
    {
        var toReserve = _fixture.Build<StockProductItem>().With(x => x.SerialNumber, newSerialNumber)
            .With(x => x.Status, StockProductItemStatus.Available).Create();

        var toUnreserve = _fixture.Build<StockProductItem>().With(x => x.SerialNumber, oldSerialNumber)
            .With(x => x.Status, StockProductItemStatus.Reserved).Create();

        stockProduct.StockProductItems.Add(toUnreserve);
        stockProduct.StockProductItems.Add(toReserve);
        
        _dbContext.StockProducts.Add(stockProduct);
        await _dbContext.SaveChangesAsync(CancellationToken.None);
        
        var command = new SwapReservationCommand(stockProduct.Id, oldSerialNumber, newSerialNumber, string.Empty);
        await _sut.Handle(command, CancellationToken.None);

        toUnreserve.Status.Should().Be(StockProductItemStatus.Available);
        toReserve.Status.Should().Be(StockProductItemStatus.Reserved);

        _mediatorMock.Verify(x => x.Publish(
            It.IsAny<ReservationSwappedEvent>(), It.IsAny<CancellationToken>()));
    }
}