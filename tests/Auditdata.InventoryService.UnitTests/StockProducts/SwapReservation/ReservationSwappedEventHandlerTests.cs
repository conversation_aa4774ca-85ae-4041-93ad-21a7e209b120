using Auditdata.InventoryService.Contracts.Commands;
using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Abstractions.Azure;
using Auditdata.InventoryService.Core.Features.StockProductItems.Events;
using Auditdata.InventoryService.Core.Features.StockProductItems.Handlers;
using Auditdata.InventoryService.UnitTests.Common;

namespace Auditdata.InventoryService.UnitTests.StockProducts.SwapReservation;

public class ReservationSwappedEventHandlerTests : BaseDbContextTest
{
    private readonly ReservationSwappedEventHandler _sut;
    private readonly Mock<IAzureServiceBusPublisher> _azureServiceBusPublisherMock = new();

    public ReservationSwappedEventHandlerTests()
    {
        _sut = new ReservationSwappedEventHandler(_azureServiceBusPublisherMock.Object, DbContext, new Mock<IEventPublisher>().Object);
    }

    [RecursiveInlineAutoData]
    [Theory]
    public async Task Handle_Should_Publish_Messages(ReservationSwappedEvent @event)
    {
        //Act
        await _sut.Handle(@event, CancellationToken.None);
     
        //Assert
        _azureServiceBusPublisherMock.Verify(x => 
            x.PublishAddStockTransactionCommand(It.IsAny<AddStockTransactionCommand>()), Times.Exactly(2));
    }
}