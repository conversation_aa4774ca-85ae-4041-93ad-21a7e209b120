using Auditdata.InventoryService.Core.Abstractions.Azure;
using Auditdata.InventoryService.Core.Constants;
using Auditdata.InventoryService.Core.Entities.Products;
using Auditdata.InventoryService.Core.Features.Products.Mappings;
using Auditdata.InventoryService.Core.Features.StockProducts.Commands;
using Auditdata.InventoryService.Core.Features.StockProducts.Handlers;
using Auditdata.InventoryService.UnitTests.Common;
using Auditdata.Microservice.Messages.Events.Inventory;
using Auditdata.Transport.Contracts.Exceptions;
using FluentAssertions;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Auditdata.InventoryService.UnitTests.StockProducts;

public class CreateStockProductsHandlerTests : BaseSqlLiteDbContextTest
{
    private readonly IFixture _fixture;
    private readonly CreateStockProductsHandler _handler;
    private readonly Mock<IAzureServiceBusPublisher> _publisher;

    public CreateStockProductsHandlerTests()
    {
        _fixture = new RecursiveFixture();
        _publisher = new Mock<IAzureServiceBusPublisher>();
        var logger = new Mock<ILogger<CreateStockProductsHandler>>();
        var mapper = new MapperConfiguration(x => x.AddProfile<ProductMappings>()).CreateMapper();
        _handler = new CreateStockProductsHandler(
            DbContext,
            new Mock<IMediator>().Object,
            mapper,
            _publisher.Object,
            logger.Object);
    }

    [Fact]
    public async Task Handle_ShouldThrow_IfAdding_InactiveProduct_ToStock()
    {
        var productId = Guid.NewGuid();
        var stockId = Guid.NewGuid();
        var stock = _fixture.Build<Stock>()
            .With(x => x.Id, stockId)
            .Create();
        DbContext.Stocks.Add(stock);
        var product = _fixture.Build<Product>()
            .With(x => x.Id, productId)
            .With(x => x.IsActive, false)
            .With(x => x.IsDeleted, false)
            .Create();
        DbContext.Products.Add(product);

        await DbContext.SaveChangesAsync();

        await Assert.ThrowsAsync<BusinessException>(async () => await _handler.Handle(
            new CreateStockProductCommand(stockId, productId, 5), CancellationToken.None));
    }
    
    [Fact]
    public async Task Handle_ShouldRespond_With_StockProductCreated()
    {
        // Arrange
        var productId = Guid.NewGuid();
        var stock = _fixture.Create<Stock>();
        DbContext.Stocks.Add(stock);
        var manufacturer = Manufacturer.Create("TestManufacturer");
        var product = new Product
        {
            Id = productId,
            Name = "test",
            Manufacturer = manufacturer,
            Category = new ProductCategory
            {
                Code = ProductCategoryCode.Accessories,
                Name = "test",
            },
            IsDeleted = false,
            IsActive = true,
            IsSerialized = true,
            ControlledByStock = true,
        };
        DbContext.Products.Add(product);
        
        var createStockProductCommand = _fixture.Build<CreateStockProductCommand>()
            .With(x => x.StockId, stock.Id)
            .With(x => x.ProductId, productId)
            .With(x => x.Quantity, 5)
            .Create();

        await DbContext.SaveChangesAsync();
        
        // Act
        var created = await _handler.Handle(createStockProductCommand, CancellationToken.None);

        // Assert
        created.Id.Should().NotBeEmpty();
        _publisher.Verify(x => x.PublishStockProductCreated(It.IsAny<StockProductCreated>()));

        var createdStockProduct = await DbContext.StockProducts
            .Include(x => x.StockProductItems)
            .FirstAsync(x => x.Id == created.Id);
        createdStockProduct.ProductId.Should().Be(createStockProductCommand.ProductId);
        createdStockProduct.StockId.Should().Be(stock.Id);
        createdStockProduct.Quantity.Should().Be(createStockProductCommand.Quantity);

        createdStockProduct.StockProductItems.Should().HaveCount(createStockProductCommand.Quantity);
        createdStockProduct.StockProductItems.All(x => x.Status == StockProductItemStatus.Available).Should().BeTrue();
    }

    [Fact]
    public async Task Handle_ShouldRespond_With_StockProductExists()
    {
        // Arrange
        var stock = _fixture.Build<Stock>()
            .With(x => x.Id, Guid.NewGuid())
            .Create();
        DbContext.Stocks.Add(stock);
        var category = new ProductCategory()
        {
            Code = ProductCategoryCode.Accessories,
            Name = "test",
        };
        var manufacturer = Manufacturer.Create("TestManufacturer");
        var product = new Product
        {
            Id = Guid.NewGuid(),
            Name = "test",
            Manufacturer = manufacturer,
            Category = category,
            IsDeleted = false,
            IsActive = true
        };
        DbContext.Products.Add(product);

        var createStockProductCommand = _fixture.Build<CreateStockProductCommand>()
            .With(x => x.StockId, stock.Id)
            .With(x => x.ProductId, product.Id)
            .With(x => x.Quantity, 5)
            .Create();

        var stockProduct = _fixture.Build<StockProduct>()
            .With(x => x.IsDeleted, false)
            .Without(x => x.Stock)
            .Without(x => x.Product)
            .With(x => x.StockId, stock.Id)
            .With(x => x.ProductId, product.Id)
            .Create();
        DbContext.StockProducts.Add(stockProduct);

        await DbContext.SaveChangesAsync();
        
        // Act
        var act = async () => await _handler.Handle(createStockProductCommand, CancellationToken.None);

        // Assert
        var ex = await act.Should().ThrowExactlyAsync<BusinessException>();
        ex.Which.ErrorCode.Should().Be(ErrorCodes.StockProductAlreadyExists);
    }
}