using Auditdata.Infrastructure.Contracts.CountryLayers;
using Auditdata.InventoryService.Core.Entities.Products;
using Auditdata.InventoryService.Core.Features.StockProducts.Handlers;
using Auditdata.InventoryService.Core.Features.StockProducts.Queries;
using Auditdata.InventoryService.Core.OperationContext;
using Auditdata.InventoryService.UnitTests.Common;
using FluentAssertions;

namespace Auditdata.InventoryService.UnitTests.StockProducts;

public class GetInvoiceUSSuggestedStockProductsHandlerTests : BaseDbContextTest
{
    private readonly IFixture _fixture;
    private readonly GetInvoiceUSSuggestedStockProductsHandler _sut;
    private readonly Mock<IInventoryServiceOperationContext> _operationContextMock = new();

    public GetInvoiceUSSuggestedStockProductsHandlerTests()
    {
        _fixture = new RecursiveFixture();
        _operationContextMock.Setup(x => x.CountryId).Returns(CountryEnum.UnitedStates);
        _sut = new GetInvoiceUSSuggestedStockProductsHandler(DbContext, _operationContextMock.Object);
    }

    [Fact]
    public async Task Handle_Should_ReturnSuggestedStockProducts_When_QueryProvided()
    {
        // Arrange
        var product = _fixture.Build<USProduct>().With(x => x.IsDeleted, false).Create();
        var usProduct = _fixture.Build<USProduct>().With(x => x.IsDeleted, false).Create();
        product.SuggestedProducts = new List<ProductSuggestedProduct>
        {
            ProductSuggestedProduct.Create(product.Id, usProduct.Id)
        };
        var stockProduct = _fixture.Build<StockProduct>()
            .With(x => x.Product, usProduct)
            .Create();
        stockProduct.StockProductItems.ForEach(t => t.IsDeleted = false);
        stockProduct.IsDeleted = false;
        stockProduct.Product.IsDeleted = false;
        stockProduct.Product.IsActive = true;
        stockProduct.Product.IsSellable = true;
        DbContext.StockProducts.AddRange(stockProduct);
        DbContext.Products.Add(product);
        await DbContext.SaveChangesAsync(CancellationToken.None);

        ProductCategory? productCategory = DbContext.ProductCategories.FirstOrDefault();
        usProduct.Category = productCategory is not null ? productCategory : new ProductCategory() { Name = "catName", Code = ProductCategoryCode.HearingAids };
        await DbContext.SaveChangesAsync(CancellationToken.None);

        _operationContextMock.Setup(t => t.CountryId).Returns(CountryEnum.UnitedStates);

        // Act
        var result = await _sut.Handle(new GetInvoiceUSSuggestedStockProductsQuery()
        {
            LocationId = stockProduct.Stock.LocationId,
            ProductId = product.Id,
        }, CancellationToken.None);

        // Assert
        result.StockProducts.Pagination!.Total.Should().Be(1);
        result.StockProducts.Rows?.FirstOrDefault()?.Model?.CPTCodes.Count().Should().Be(usProduct.CPTCodes.Count);
    }

    [Fact]
    public async Task Handle_ShouldReturnCorrectPagination_When_PaginationRequested()
    {
        // Arrange
        const int page = 2;
        const int perPage = 10;

        var product = _fixture.Build<USProduct>().With(x => x.IsDeleted, false).Create();
        var usProducts = _fixture.Build<USProduct>()
            .CreateMany(perPage * page).ToList();
        product.SuggestedProducts =
            usProducts.Select(x => ProductSuggestedProduct.Create(product.Id, x.Id)).ToList();

        var locationId = Guid.NewGuid();
        var stock = new Stock
        {
            Id = Guid.NewGuid(),
            LocationId = locationId,
            Name = "name"
        };
        usProducts.ForEach(t => { t.IsDeleted = false; t.IsActive = true; t.IsSellable = true; });
        var stockProducts = usProducts
            .Select(p => _fixture.Build<StockProduct>()
                .With(x => x.Product, p)
                .Create()).ToList();
        stockProducts.ForEach(t =>
        {
            t.StockProductItems.ForEach(t => t.IsDeleted = false); t.IsDeleted = false;
            t.Stock = stock;
        });
        DbContext.Products.Add(product);
        DbContext.StockProducts.AddRange(stockProducts);
        await DbContext.SaveChangesAsync(CancellationToken.None);

        ProductCategory? productCategory = DbContext.ProductCategories.FirstOrDefault();
        usProducts.ForEach(t => t.Category = productCategory is not null ? productCategory : new ProductCategory() { Name = "catName", Code = ProductCategoryCode.HearingAids });
        await DbContext.SaveChangesAsync(CancellationToken.None);

        _operationContextMock.Setup(t => t.CountryId).Returns(CountryEnum.UnitedStates);

        // Act
        var result = await _sut.Handle(new GetInvoiceUSSuggestedStockProductsQuery
        {
            Page = page,
            PerPage = perPage,
            LocationId = locationId,
            ProductId = product.Id,
        }, CancellationToken.None);

        // Assert
        result.StockProducts.Pagination!.Total.Should().Be(usProducts.Count);
        result.StockProducts.Pagination!.PerPage.Should().Be(perPage);
    }

    [Fact]
    public async Task Handle_ShouldReturnFilteredResult_When_SearchTextProvided()
    {
        // Arrange
        const string searchText = "search term";

        var product = _fixture.Build<USProduct>().With(x => x.IsDeleted, false).Create();
        var usProduct = _fixture.Build<USProduct>()
            .With(x => x.IsDeleted, false)
            .With(x => x.Name, searchText)
            .Create();
        product.SuggestedProducts = new List<ProductSuggestedProduct>
        {
            ProductSuggestedProduct.Create(product.Id, usProduct.Id)
        };

        var stockProduct = _fixture.Build<StockProduct>()
            .With(x => x.Product, usProduct)
            .Create();
        stockProduct.StockProductItems.ForEach(t => t.IsDeleted = false);
        stockProduct.IsDeleted = false;
        stockProduct.Product.IsDeleted = false;
        stockProduct.Product.IsActive = true;
        stockProduct.Product.IsSellable = true;

        DbContext.Products.Add(product);
        DbContext.StockProducts.AddRange(stockProduct);
        await DbContext.SaveChangesAsync(CancellationToken.None);

        ProductCategory? productCategory = DbContext.ProductCategories.FirstOrDefault();
        usProduct.Category = productCategory is not null ? productCategory : new ProductCategory() { Name = "catName", Code = ProductCategoryCode.HearingAids };
        await DbContext.SaveChangesAsync(CancellationToken.None);

        _operationContextMock.Setup(t => t.CountryId).Returns(CountryEnum.UnitedStates);

        // Act
        var result = await _sut.Handle(
            new GetInvoiceUSSuggestedStockProductsQuery()
            {
                SearchText = searchText,
                LocationId = stockProduct.Stock.LocationId,
                ProductId = product.Id,
            },
            CancellationToken.None);

        // Assert
        result.StockProducts.Pagination!.Total.Should().Be(1);
    }

    [Fact]
    public async Task Handle_ShouldReturnEmptyResult_When_NoMatchingProducts()
    {
        // Arrange
        _operationContextMock.Setup(t => t.CountryId).Returns(CountryEnum.UnitedStates);

        // Act
        var result = await _sut.Handle(new GetInvoiceUSSuggestedStockProductsQuery(), CancellationToken.None);

        // Assert
        result.StockProducts.Pagination!.Total.Should().Be(0);
    }
}
