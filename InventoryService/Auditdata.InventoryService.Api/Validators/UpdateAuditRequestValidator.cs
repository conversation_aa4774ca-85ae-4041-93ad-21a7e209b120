using Auditdata.InventoryService.Contracts.Requests.AuditRequests;
using Auditdata.InventoryService.Core.Constants;

namespace Auditdata.InventoryService.Api.Validators;

public class UpdateAuditRequestValidator : AbstractValidator<UpdateAuditRequestModel>
{
    public UpdateAuditRequestValidator()
    {
        RuleFor(x => x.Locations)
            .NotEmpty();

        RuleFor(x => x.DateTimeDue)
            .NotEmpty()
            .Must(dateTimeDue => !IsPastDateTime(dateTimeDue))
            .WithMessage("The due date or time cannot be in the past.")
            .WithErrorCode(ErrorCodes.CannotProvideThePastDateOrTime);

        RuleFor(x => x.Notes)
            .MaximumLength(255);

        When(x => x.Products != null && x.Products.Count > 0, () =>
        {
            RuleForEach(x => x.Products)
                .Must(y => y?.Counted == null || y.Counted >= 0)
                .WithName(nameof(AuditProductRequest.Counted))
                .WithMessage("Counted can't be negative.");
            RuleForEach(x => x.Products)
                .Must(y => y?.Counted == null || y.Counted <= PatchAuditRequestValidator.MaxAuditProductCountedValue)
                .WithName(nameof(PatchAuditProductRequest.Counted))
                .WithMessage($"Maximum value is {PatchAuditRequestValidator.MaxAuditProductCountedValue}")
                .WithErrorCode(ErrorCodes.CauntedMaxValueExceeded);
            RuleForEach(x => x.Products
                .Where(y => y != null && y.SerialNumbers != null)
                .SelectMany(y => y!.SerialNumbers))
                .Must(z => !string.IsNullOrWhiteSpace(z.SerialNumber) && z.SerialNumber.Length <= 255)
                .WithName(nameof(AuditProductSNRequest.SerialNumber))
                .WithMessage("Serial number can't be empty or bigger than 255 symbols.")
                .Must(z => z.Comments == null || z.Comments.Length <= 255)
                .WithName(nameof(AuditProductSNRequest.Comments))
                .WithMessage("Comments can't be bigger than 255 symbols.");

        });
    }

    private static bool IsPastDateTime(DateTimeOffset dateTimeDue)
    {
        return dateTimeDue < DateTimeOffset.UtcNow;
    }
}
