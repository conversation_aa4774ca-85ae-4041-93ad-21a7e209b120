using Auditdata.InventoryService.Contracts.Requests.ProductCategoryAccountCodes;

namespace Auditdata.InventoryService.Api.Validators;

public class CreateProductCategoryAccountCodeRequestValidator : AbstractValidator<CreateProductCategoryAccountCodeRequest>
{
    public CreateProductCategoryAccountCodeRequestValidator()
    {
        RuleFor(x => x.AccountCode)
            .MaximumLength(255)
            .When(x => !string.IsNullOrEmpty(x.AccountCode));

        RuleFor(x => x.ProductCategoryId).NotNull();
    }
}
