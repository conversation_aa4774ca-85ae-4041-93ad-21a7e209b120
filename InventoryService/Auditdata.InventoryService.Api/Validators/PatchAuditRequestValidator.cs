using Auditdata.InventoryService.Contracts.Requests.AuditRequests;
using Auditdata.InventoryService.Core.Constants;
using Morcatko.AspNetCore.JsonMergePatch.Internal;

namespace Auditdata.InventoryService.Api.Validators;

public class PatchAuditRequestValidator : AbstractValidator<InternalJsonMergePatchDocument<PatchAuditRequestModel>>
{
    public const int MaxAuditProductCountedValue = 9999;

    public PatchAuditRequestValidator()
    {
        When(x => x.Operations.Exists(y => string.Equals(y.path, "/dateTimeDue", StringComparison.OrdinalIgnoreCase)), () =>
        {
            RuleFor(x => x.Model.DateTimeDue)
                .Must(dateTimeDue => !IsPastDateTime(dateTimeDue))
                .WithMessage("The due date or time cannot be in the past.")
                .WithErrorCode(ErrorCodes.CannotProvideThePastDateOrTime);
        });
        When(x => x.Operations.Exists(y => string.Equals(y.path, "/notes", StringComparison.OrdinalIgnoreCase)), () =>
        {
            RuleFor(x => x.Model.Notes)
                .MaximumLength(255);
        });
        When(x => x.Operations.Exists(y => string.Equals(y.path, "/locations", StringComparison.OrdinalIgnoreCase)), () =>
        {
            RuleFor(x => x.Model.Locations)
                .NotEmpty();
        });
        When(x => x.Operations.Exists(y => string.Equals(y.path, "/products", StringComparison.OrdinalIgnoreCase)), () =>
        {
            RuleForEach(x => x.Model.Products.Values)
                .Must(y => y?.Counted == null || !int.TryParse(y.Counted, out var countedInt) || countedInt >= 0)
                .WithName(nameof(PatchAuditProductRequest.Counted))
                .WithMessage("Counted can't be negative.");
            RuleForEach(x => x.Model.Products.Values)
                .Must(y => y?.Counted == null
                    || (int.TryParse(y.Counted, out var countedInt) && countedInt <= MaxAuditProductCountedValue))
                .WithName(nameof(PatchAuditProductRequest.Counted))
                .WithMessage($"Maximum value is {MaxAuditProductCountedValue}")
                .WithErrorCode(ErrorCodes.CauntedMaxValueExceeded);
            RuleForEach(x => x.Model.Products.Values
                .Where(y => y != null && y.SerialNumbers != null)
                .SelectMany(y => y!.SerialNumbers))
                .Must(z => !string.IsNullOrWhiteSpace(z.SerialNumber) && z.SerialNumber.Length <= 255)
                .WithName(nameof(AuditProductSNRequest.SerialNumber))
                .WithMessage("Serial number can't be empty or bigger than 255 symbols.")
                .Must(z => z.Comments == null || z.Comments.Length <= 255)
                .WithName(nameof(AuditProductSNRequest.Comments))
                .WithMessage("Comments can't be bigger than 255 symbols.");
        });
    }

    private static bool IsPastDateTime(DateTimeOffset dateTimeDue)
    {
        return dateTimeDue < DateTimeOffset.UtcNow;
    }
}
