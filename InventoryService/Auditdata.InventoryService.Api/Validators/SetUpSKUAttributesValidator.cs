using Auditdata.InventoryService.Contracts.Requests.Skus;
using Auditdata.InventoryService.Core.Constants;

namespace Auditdata.InventoryService.Api.Validators;

public class SetUpSkuAttributesValidator : AbstractValidator<SetUpSkuConfigsRequest>
{
    public SetUpSkuAttributesValidator()
    {
        RuleFor(x => x.SkuConfigs)
            .Must(x => x.Where(x => x.SkuAttributeType == SkuAttributeTypeRequest.Attribute).All(y => y.AttributeId != null))
            .WithMessage("The attribute id should not be null.")
            .WithErrorCode(ErrorCodes.SkuAttributeIdIsNull);

        RuleFor(x => x.SkuConfigs)
            .Must(x => x.Where(x => x.SkuAttributeType != SkuAttributeTypeRequest.Attribute).All(y => y.AttributeId == null))
            .WithMessage("The attribute id should be null.")
            .WithErrorCode(ErrorCodes.SkuAttributeIdIsNotNull);
    }
}
