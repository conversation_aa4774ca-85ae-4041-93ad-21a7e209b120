using Auditdata.InventoryService.Contracts.Requests.CPTCodes;
using Auditdata.InventoryService.Core.Constants;

namespace Auditdata.InventoryService.Api.Validators;

public class ImportCPTCodesValidator : AbstractValidator<ImportCPTCodesRequest>
{
    public ImportCPTCodesValidator()
    {
        RuleFor(x => Path.GetExtension(x.ExcelFile.FileName))
            .Must(x => string
            .Equals(x, ".xlsx", StringComparison.OrdinalIgnoreCase))
            .WithMessage("The uploaded document extension is not supported.")
            .WithErrorCode(ErrorCodes.DocumentXlsxFormatOnly)
            .When(x => x.ExcelFile != null);

        RuleFor(x => x.ExcelFile.Length)
            .LessThanOrEqualTo(ValidationConstants.MaxExcelFileSize)
            .WithMessage("The document size exceeds 10 MB.")
            .WithErrorCode(ErrorCodes.DocumentSizeExceedsMaxSize)
            .When(x => x.ExcelFile != null);
    }
}
