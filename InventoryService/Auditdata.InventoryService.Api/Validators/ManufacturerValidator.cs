using Auditdata.InventoryService.Contracts.Requests.Manufacturers;

namespace Auditdata.InventoryService.Api.Validators;

public class ManufacturerValidator : AbstractValidator<ManufacturerRequest>
{
    public ManufacturerValidator()
    {
        RuleFor(x => x.Name).NotEmpty();
        RuleFor(x => x.PhoneNumber).NotEmpty();
        RuleFor(x => x.Address1).NotEmpty();
        RuleFor(x => x.CountryId).NotEmpty();
        RuleFor(x => x.City).NotEmpty();
        When(x => x.StateId is null, () =>
        {
            RuleFor(x => x.State).NotEmpty();
        });
    }
}
