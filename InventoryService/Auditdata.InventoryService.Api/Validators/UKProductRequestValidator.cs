using Auditdata.InventoryService.Contracts.Requests.Products;

namespace Auditdata.InventoryService.Api.Validators;

public class UKProductRequestValidator : ProductRequestValidator<UKProductRequest>
{
    public UKProductRequestValidator()
    {
        RuleForNhs();
        RuleForVat();
    }

    private void RuleForNhs()
    {
        When(x => x.IsNHS, () =>
        {
            RuleFor(x => x.NHSVAT).Must(x => x is null || x > 0)
                .WithMessage($"'{nameof(UKProductRequest.NHSVAT)}' must be empty or greater than '0' when '{nameof(UKProductRequest.IsNHS)}' is 'true'");

            RuleFor(x => x.PathwayIds)
                .NotNull()
                .NotEmpty()
                .WithMessage($"'{nameof(UKProductRequest.PathwayIds)}' must not be empty when '{nameof(UKProductRequest.IsNHS)}' is 'true'");
        });
    }

    private void RuleForVat()
    {
        RuleFor(x => x.FirstVAT).InclusiveBetween(0, 1);
        RuleFor(x => x.SecondVAT).InclusiveBetween(0, 1);
        RuleFor(x => x.FirstVAT + x.SecondVAT).Equal(1)
            .WithMessage($"'{nameof(UKProductRequest.FirstVAT)} + {nameof(UKProductRequest.SecondVAT)}' must be equal to '1'");
    }
}
