FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src
COPY . "InventoryService"
COPY .editorconfig "InventoryService"
RUN dotnet restore "InventoryService/Auditdata.InventoryService.Api/Auditdata.InventoryService.Api.csproj"
WORKDIR "/src/InventoryService/Auditdata.InventoryService.Api"
RUN ls
ARG BUILD_VERSION_ARG=1.0.0
RUN dotnet build "Auditdata.InventoryService.Api.csproj" -c Release -o /app/build --no-restore -p:Version=${BUILD_VERSION_ARG}

FROM build AS publish
ARG BUILD_VERSION_ARG=1.0.0
RUN dotnet publish "Auditdata.InventoryService.Api.csproj" -c Release -o /app/publish --no-restore -p:Version=${BUILD_VERSION_ARG}

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "Auditdata.InventoryService.Api.dll"]