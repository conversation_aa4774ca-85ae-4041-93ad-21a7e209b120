{"AllowedHosts": "*", "EnvironmentName": "dev", "ServiceName": "InventoryService", "ApplicationInsights": {"ConnectionString": "InstrumentationKey=7e49ff72-5113-4fc9-9d6d-d64c728c830f;IngestionEndpoint=https://northeurope-4.in.applicationinsights.azure.com/;LiveEndpoint=https://northeurope.livediagnostics.monitor.azure.com/;ApplicationId=d1160b80-f934-45a7-8ffd-e5298bd581d7"}, "IdentityServiceEndpoint": "https://aks-dev-authorizationservice.auditdata.app", "ConnectionStrings": {"AzureSql": "Data source=srv-oneoms-dev.database.windows.net;Initial Catalog=inventory-dev-ga;User ID=adminoneoms;Password=************************************"}, "AzureBlobStorageEndpoint": "https://auditdataoneneust.blob.core.windows.net/", "AzureServiceBus": {"Endpoint": "sb://manage-dev-ga-sbns.servicebus.windows.net/", "TopologyPrefix": "aks-local-dev-"}, "AzureLogAnalytics": {"WorkspaceId": "0870484f-e484-450e-af1f-b586763f533b", "SharedKey": "gF+TiRP7DkhO/VlHBCqP/Z+0vwg44q6L2vhBJdMWaxh+y5tvLHBLpQzFxIvrOLr9Z5eu8kG8j0hZ8lR4cvGKIg=="}}