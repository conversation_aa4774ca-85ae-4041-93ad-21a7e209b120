using Auditdata.InventoryService.Core.Exceptions;
using Auditdata.InventoryService.Core.Features.StockProductItems.Commands;
using Auditdata.Microservice.Messages.OriginEntities.Inventory;
using Auditdata.Microservice.Messages.RequestReply.Inventory.DeliverSoldProducts;
using Auditdata.Transport.Contracts.Exceptions;

namespace Auditdata.InventoryService.Api.Consumers;

public class DeliverSoldProductsRequestConsumer : IConsumer<DeliverSoldProductsRequest>
{
    private readonly MediatR.IMediator _mediator;
    private readonly IMapper _mapper;

    public DeliverSoldProductsRequestConsumer(
        MediatR.IMediator mediator,
        IMapper mapper)
    {
        _mediator = mediator;
        _mapper = mapper;
    }

    public async Task Consume(ConsumeContext<DeliverSoldProductsRequest> context)
    {
        try
        {
            await _mediator.Send(_mapper.Map<MarkProductSoldCommand>(context.Message));
            await context.RespondAsync(new DeliverSoldProductsReply());
        }
        catch (ProductItemUnavailableForSaleException ex)
        {
            await context.RespondAsync(new ProductItemUnavailableForSaleReply
            {
                StockProductId = ex.StockProductId
            });
        }
        catch (ProductItemInsufficientQuantityException ex)
        {
            await context.RespondAsync(new InsufficientProductQuantityResult
            {
                StockProductId = ex.StockProductId,
                Quantity = ex.Quantity
            });
        }
        catch (BusinessException)
        {
            await context.RespondAsync(new ProductItemUnavailableForSaleReply());
        }
    }
}
