using Auditdata.InventoryService.Core.Entities;
using Auditdata.InventoryService.Core.Exceptions;
using Auditdata.InventoryService.Core.Features.StockProductItems.Commands;
using Auditdata.Microservice.Messages.RequestReply.Inventory.SwapReservation;
using Auditdata.Transport.Contracts.Exceptions;

namespace Auditdata.InventoryService.Api.Consumers;

public class SwapReservationRequestConsumer : IConsumer<SwapReservationRequest>
{
    private readonly MediatR.IMediator _mediator;
    private readonly IMapper _mapper;
    private readonly ILogger<SwapReservationRequestConsumer> _logger;

    public SwapReservationRequestConsumer(
        MediatR.IMediator mediator,
        IMapper mapper,
        ILogger<SwapReservationRequestConsumer> logger)
    {
        _mediator = mediator;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task Consume(ConsumeContext<SwapReservationRequest> context)
    {
        _logger.LogInformation("Received product reservation swap request {@Request}", context.Message);

        var command = _mapper.Map<SwapReservationCommand>(context.Message);

        try
        {
            await _mediator.Send(command);
            await context.RespondAsync(new SwapReservationReply());
        }
        catch (EntityNotFoundException<StockProduct> stockProductNotFound)
        {
            _logger.LogError(stockProductNotFound, "StockProduct {Id} not found", stockProductNotFound.EntityId);
            await context.RespondAsync(new SwapReservationFailedReply(stockProductNotFound.ErrorCode));
        }
        catch (EntityNotFoundException<StockProductItem> stockProductItemNotFound)
        {
            _logger.LogError(stockProductItemNotFound, "StockProductItem {Id} not found", stockProductItemNotFound.EntityId);
            await context.RespondAsync(new SwapReservationFailedReply(stockProductItemNotFound.ErrorCode));
        }
        catch (BusinessException businessException)
        {
            _logger.LogError(businessException, "Validation errors occured while trying to reserve product items");
            await context.RespondAsync(new SwapReservationFailedReply(businessException.ErrorCode));
        }
    }
}
