using Auditdata.InventoryService.Core.Features.Products.Queries;
using Auditdata.Microservice.Messages.RequestReply.Inventory;
using MediatR;

namespace Auditdata.InventoryService.Api.Consumers;

public class GetProductForPrintingConsumer : IConsumer<GetProductForPrintingRequest>
{
    private readonly IMediator _mediator;
    private readonly IMapper _mapper;

    public GetProductForPrintingConsumer(IMediator mediator, IMapper mapper)
    {
        _mediator = mediator;
        _mapper = mapper;
    }

    public async Task Consume(ConsumeContext<GetProductForPrintingRequest> context)
    {
        var productForPrint = await _mediator.Send(new GetProductForPrintQuery(context.Message.ProductId));
        var reply = _mapper.Map<GetProductForPrintingReply>(productForPrint);
        await context.RespondAsync(reply);
    }
}
