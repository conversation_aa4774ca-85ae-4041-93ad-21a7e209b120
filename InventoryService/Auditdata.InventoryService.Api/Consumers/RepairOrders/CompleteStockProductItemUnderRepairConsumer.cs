using Auditdata.InventoryService.Core.Features.Repairs.Commands;
using Auditdata.Microservice.Messages.RequestReply.RepairOrder.StockProductItem.Replies;
using Auditdata.Microservice.Messages.RequestReply.RepairOrder.StockProductItem.Requests;

namespace Auditdata.InventoryService.Api.Consumers.RepairOrders;

public class CompleteStockProductItemUnderRepairConsumer : IConsumer<CompleteStockProductItemUnderRepairRequest>
{
    private readonly MediatR.IMediator _mediator;
    private readonly IMapper _mapper;

    public CompleteStockProductItemUnderRepairConsumer(
        MediatR.IMediator mediator,
        IMapper mapper)
    {
        _mediator = mediator;
        _mapper = mapper;
    }

    public async Task Consume(ConsumeContext<CompleteStockProductItemUnderRepairRequest> context)
    {
        var command = _mapper.Map<CompleteStockProductItemUnderRepairCommand>(context.Message);
        await _mediator.Send(command);

        await context.RespondAsync(new StockProductItemUnderRepairCompletedReply { });
    }
}
