using Auditdata.InventoryService.Core.Features.LnDOrders.Actions.MarkUnderLnD;
using Auditdata.Microservice.Messages.RequestReply.Inventory.ReserveProduct;
using Auditdata.Microservice.Messages.RequestReply.LnDOrder.StockProductItem.Replies;
using Auditdata.Microservice.Messages.RequestReply.LnDOrder.StockProductItem.Requests;

namespace Auditdata.InventoryService.Api.Consumers.LnDOrders;

public class MarkStockProductItemUnderLnDRequestConsumer : IConsumer<MarkStockProductItemUnderLnDRequest>
{
    private readonly MediatR.IMediator _mediator;
    private readonly IMapper _mapper;

    public MarkStockProductItemUnderLnDRequestConsumer(
        MediatR.IMediator mediator,
        IMapper mapper)
    {
        _mediator = mediator;
        _mapper = mapper;
    }

    public async Task Consume(ConsumeContext<MarkStockProductItemUnderLnDRequest> context)
    {
        try
        {
            var command = _mapper.Map<MarkStockProductItemUnderLnDCommand>(context.Message);
            await _mediator.Send(command);

            await context.RespondAsync(new EmptyReply());
        }
        catch (DuplicatedSerialNumbersException ex)
        {
            await context.RespondAsync(new DuplicatedSerialNumberReply { SerialNumbers = ex.DuplicatedSerialNumber });
        }
    }
}
