using Auditdata.InventoryService.Core.Features.LnDOrders.Actions.CreateStockProductItem;
using Auditdata.Microservice.Messages.RequestReply.Inventory.ReserveProduct;
using Auditdata.Microservice.Messages.RequestReply.LnDOrder.StockProductItem.Replies;
using Auditdata.Microservice.Messages.RequestReply.LnDOrder.StockProductItem.Requests;

namespace Auditdata.InventoryService.Api.Consumers.LnDOrders;

public class AddStockProductItemConsumer : IConsumer<AddStockProductItemRequest>
{
    private readonly MediatR.IMediator _mediator;
    private readonly IMapper _mapper;

    public AddStockProductItemConsumer(
        MediatR.IMediator mediator,
        IMapper mapper)
    {
        _mediator = mediator;
        _mapper = mapper;
    }

    public async Task Consume(ConsumeContext<AddStockProductItemRequest> context)
    {
        try
        {
            var command = _mapper.Map<CreateStockProductItemCommand>(context.Message);
            var result = await _mediator.Send(command);

            await context.RespondAsync(new AddStockProductItemReply { StockProductItemId = result.StockProductItemId });
        }
        catch (DuplicatedSerialNumbersException ex)
        {
            await context.RespondAsync(new DuplicatedSerialNumberReply { SerialNumbers = ex.DuplicatedSerialNumber });
        }
    }
}
