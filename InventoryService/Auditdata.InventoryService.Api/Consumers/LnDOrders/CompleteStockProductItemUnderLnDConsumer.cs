using Auditdata.InventoryService.Core.Features.LnDOrders.Actions.Complete;
using Auditdata.Microservice.Messages.RequestReply.LnDOrder.StockProductItem.Replies;
using Auditdata.Microservice.Messages.RequestReply.LnDOrder.StockProductItem.Requests;

namespace Auditdata.InventoryService.Api.Consumers.LnDOrders;

public class CompleteStockProductItemUnderLnDConsumer : IConsumer<CompleteStockProductItemUnderLnDRequest>
{
    private readonly MediatR.IMediator _mediator;
    private readonly IMapper _mapper;

    public CompleteStockProductItemUnderLnDConsumer(
        MediatR.IMediator mediator,
        IMapper mapper)
    {
        _mediator = mediator;
        _mapper = mapper;
    }

    public async Task Consume(ConsumeContext<CompleteStockProductItemUnderLnDRequest> context)
    {
        var command = _mapper.Map<CompleteStockProductItemUnderLnDCommand>(context.Message);
        await _mediator.Send(command);

        await context.RespondAsync(new EmptyReply { });
    }
}
