using Auditdata.InventoryService.Core.Features.LnDOrders.Actions.Cancel;
using Auditdata.Microservice.Messages.RequestReply.LnDOrder.StockProductItem.Replies;
using Auditdata.Microservice.Messages.RequestReply.LnDOrder.StockProductItem.Requests;

namespace Auditdata.InventoryService.Api.Consumers.LnDOrders;

public class CancelStockProductItemUnderLnDConsumer : IConsumer<CancelStockProductItemUnderLnDRequest>
{
    private readonly MediatR.IMediator _mediator;
    private readonly IMapper _mapper;

    public CancelStockProductItemUnderLnDConsumer(
        MediatR.IMediator mediator,
        IMapper mapper)
    {
        _mediator = mediator;
        _mapper = mapper;
    }

    public async Task Consume(ConsumeContext<CancelStockProductItemUnderLnDRequest> context)
    {
        var command = _mapper.Map<CancelStockProductItemUnderLnDCommand>(context.Message);
        await _mediator.Send(command);

        await context.RespondAsync(new EmptyReply());
    }
}
