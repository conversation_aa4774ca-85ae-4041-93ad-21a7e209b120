using Auditdata.InventoryService.Core.Features.StockProductItems.Commands;
using Auditdata.Microservice.Messages.Commands.Inventory;

namespace Auditdata.InventoryService.Api.Consumers;

public class UnReserveProductConsumer : IConsumer<UnReserveProductCommand>
{
    private readonly MediatR.IMediator _mediator;
    private readonly IMapper _mapper;

    public UnReserveProductConsumer(
        MediatR.IMediator mediator,
        IMapper mapper)
    {
        _mediator = mediator;
        _mapper = mapper;
    }

    public async Task Consume(ConsumeContext<UnReserveProductCommand> context)
    {
        var command = _mapper.Map<MarkProductAvailableAfterReservationCommand>(context.Message);
        await _mediator.Send(command);
    }
}
