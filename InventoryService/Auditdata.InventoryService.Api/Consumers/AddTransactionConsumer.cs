using Auditdata.InventoryService.Contracts.Commands;
using Auditdata.InventoryService.Core.Features.StockTransactions.Commands;

namespace Auditdata.InventoryService.Api.Consumers;

public class AddTransactionConsumer : IConsumer<AddStockTransactionCommand>
{
    private readonly MediatR.IMediator _mediator;
    private readonly IMapper _mapper;

    public AddTransactionConsumer(
        MediatR.IMediator mediator,
        IMapper mapper)
    {
        _mediator = mediator;
        _mapper = mapper;
    }

    public async Task Consume(ConsumeContext<AddStockTransactionCommand> context)
    {
        await _mediator.Send(_mapper.Map<CreateStockTransactionCommand>(context.Message));
    }
}
