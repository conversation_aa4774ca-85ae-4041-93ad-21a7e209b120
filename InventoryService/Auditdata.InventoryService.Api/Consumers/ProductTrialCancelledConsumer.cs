using Auditdata.InventoryService.Core.Features.StockProductItems.Commands;
using Auditdata.Microservice.Messages.Events.Invoicing;

namespace Auditdata.InventoryService.Api.Consumers;

public class ProductTrialCancelledConsumer : IConsumer<ProductTrialCancelled>
{
    private readonly MediatR.IMediator _mediator;
    private readonly IMapper _mapper;
    private readonly ILogger<ProductTrialCancelledConsumer> _logger;

    public ProductTrialCancelledConsumer(
        MediatR.IMediator mediator,
        IMapper mapper,
        ILogger<ProductTrialCancelledConsumer> logger)
    {
        _mediator = mediator;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task Consume(ConsumeContext<ProductTrialCancelled> context)
    {
        _logger.LogInformation("Received product trial cancelled event {@Event}", context.Message);

        var command = _mapper.Map<CancelStockProductItemsTrialCommand>(context.Message);
        await _mediator.Send(command);
    }
}
