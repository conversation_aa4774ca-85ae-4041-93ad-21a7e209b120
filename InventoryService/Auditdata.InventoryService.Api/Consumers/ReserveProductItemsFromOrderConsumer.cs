using Auditdata.InventoryService.Core.Entities;
using Auditdata.InventoryService.Core.Exceptions;
using Auditdata.InventoryService.Core.Features.Orders.Commands;
using Auditdata.Microservice.Messages.RequestReply.Inventory.Order;
using Auditdata.Transport.Contracts.Exceptions;

namespace Auditdata.InventoryService.Api.Consumers;

public class ReserveProductItemsFromOrderConsumer : IConsumer<ReserveProductItemsFromOrderRequest>
{
    private readonly MediatR.IMediator _mediator;
    private readonly IMapper _mapper;

    public ReserveProductItemsFromOrderConsumer(
        MediatR.IMediator mediator,
        IMapper mapper)
    {
        _mediator = mediator;
        _mapper = mapper;
    }

    public async Task Consume(ConsumeContext<ReserveProductItemsFromOrderRequest> context)
    {
        try
        {
            var command = _mapper.Map<ReserveProductItemsFromOrderCommand>(context.Message);

            var result = await _mediator.Send(command);

            var reply = _mapper.Map<ReserveProductItemsFromOrderReply>(result);

            await context.RespondAsync(reply);
        }
        catch (ReserveProductItemsFromOrderException reserveException)
        {
            switch (reserveException.BusinessException)
            {
                case EntityNotFoundException<StockProduct> notFoundException:
                    await context.RespondAsync(new ReserveProductItemsFromOrderFailedReply
                    {
                        OrderProductId = reserveException.OrderProductId,
                        ErrorCode = notFoundException.ErrorCode,
                        FaultReason = "Stock product not found"
                    });
                    break;
                case InvalidNumberOfSerialNumbersException invalidRangeException:
                    await context.RespondAsync(new ReserveProductItemsFromOrderFailedReply
                    {
                        OrderProductId = reserveException.OrderProductId,
                        ErrorCode = invalidRangeException.ErrorCode,
                        FaultReason = $"Number of available S/Ns: {invalidRangeException.Available}. Entered: {invalidRangeException.Entered}."
                    });
                    break;
                case DuplicatedSerialNumbersException duplicatedException:
                    await context.RespondAsync(new ReserveProductItemsFromOrderFailedReply
                    {
                        OrderProductId = reserveException.OrderProductId,
                        ErrorCode = duplicatedException.ErrorCode,
                        SerialNumbers = duplicatedException.DuplicatedSerialNumber.ToList(),
                        FaultReason = duplicatedException.Message
                    });
                    break;
                default:
                    await context.RespondAsync(new ReserveProductItemsFromOrderFailedReply
                    {
                        OrderProductId = reserveException.OrderProductId,
                        ErrorCode = reserveException.BusinessException.ErrorCode,
                        FaultReason = reserveException.BusinessException.Message,
                    });
                    break;
            }
        }
        catch (DuplicatedSerialNumbersException duplicatedException)
        {
            await context.RespondAsync(new ReserveProductItemsFromOrderFailedReply
            {
                ErrorCode = duplicatedException.ErrorCode,
                SerialNumbers = duplicatedException.DuplicatedSerialNumber.ToList(),
                FaultReason = duplicatedException.Message
            });
        }
        catch (BusinessException businessException)
        {
            await context.RespondAsync(new ReserveProductItemsFromOrderFailedReply
            {
                ErrorCode = businessException.ErrorCode,
                FaultReason = businessException.Message
            });
        }
    }
}
