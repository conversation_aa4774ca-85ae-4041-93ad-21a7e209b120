using Auditdata.InventoryService.Core.Exceptions;
using Auditdata.InventoryService.Core.Features.StockProductItems.Commands;
using Auditdata.Microservice.Messages.OriginEntities.Inventory;
using Auditdata.Microservice.Messages.RequestReply.Inventory;

namespace Auditdata.InventoryService.Api.Consumers;

public class ExchangeProductsConsumer : IConsumer<ExchangeProductsRequest>
{
    private readonly MediatR.IMediator _mediator;
    private readonly IMapper _mapper;

    public ExchangeProductsConsumer(
        MediatR.IMediator mediator,
        IMapper mapper)
    {
        _mediator = mediator;
        _mapper = mapper;
    }

    public async Task Consume(ConsumeContext<ExchangeProductsRequest> context)
    {
        try
        {
            var command = _mapper.Map<ExchangeProductsCommand>(context.Message);
            await _mediator.Send(command);
            await context.RespondAsync(new ProductsExchangedReply());
        }
        catch (ProductItemInsufficientQuantityException ex)
        {
            await context.RespondAsync(new InsufficientProductQuantityResult
            {
                StockProductId = ex.StockProductId,
                Quantity = ex.Quantity
            });
        }
    }
}
