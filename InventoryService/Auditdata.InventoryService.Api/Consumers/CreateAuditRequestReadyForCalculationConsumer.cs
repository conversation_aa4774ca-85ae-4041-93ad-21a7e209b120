using Auditdata.InventoryService.Contracts.Commands;
using Auditdata.InventoryService.Core.Features.AuditRequests.GoToReadyForCalculation;
using MediatR;

namespace Auditdata.InventoryService.Api.Consumers;

public class CreateAuditRequestReadyForCalculationConsumer : IConsumer<CreateAuditRequestReadyForCalculationCommand>
{
    private readonly IMediator _mediator;

    public CreateAuditRequestReadyForCalculationConsumer(IMediator mediator)
    {
        _mediator = mediator;
    }

    public async Task Consume(ConsumeContext<CreateAuditRequestReadyForCalculationCommand> context)
    {
        var command = context.Message;

        await _mediator.Send(new CreateReadyForCalculationCommand(command.AuditRequestId, command.LocationId));
    }
}
