using Auditdata.InventoryService.Core.Features.States.Commands;
using Auditdata.Microservice.Messages.Events.State;

namespace Auditdata.InventoryService.Api.Consumers;

public class StateDeletedConsumer : IConsumer<StateDeleted>
{
    private readonly MediatR.IMediator _mediator;
    private readonly IMapper _mapper;

    public StateDeletedConsumer(
        MediatR.IMediator mediator,
        IMapper mapper)
    {
        _mediator = mediator;
        _mapper = mapper;
    }

    public async Task Consume(ConsumeContext<StateDeleted> context)
    {
        var command = _mapper.Map<DeleteStateCommand>(context.Message);
        await _mediator.Send(command);
    }
}
