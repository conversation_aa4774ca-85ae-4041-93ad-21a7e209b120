using Auditdata.InventoryService.Core.Features.Stocks.Commands;
using Auditdata.Microservice.Messages.Events.Location;

namespace Auditdata.InventoryService.Api.Consumers;

public class LocationCreatedConsumer : IConsumer<LocationCreated>
{
    private readonly MediatR.IMediator _mediator;
    private readonly IMapper _mapper;

    public LocationCreatedConsumer(
        MediatR.IMediator mediator,
        IMapper mapper)
    {
        _mediator = mediator;
        _mapper = mapper;
    }

    public async Task Consume(ConsumeContext<LocationCreated> context)
    {
        var command = _mapper.Map<CreateStockCommand>(context.Message);
        await _mediator.Send(command);
    }
}
