using Auditdata.InventoryService.Core.Features.States.Commands;
using Auditdata.Microservice.Messages.Events.State;

namespace Auditdata.InventoryService.Api.Consumers;

public class StateCreatedConsumer : IConsumer<StateCreated>
{
    private readonly MediatR.IMediator _mediator;
    private readonly IMapper _mapper;

    public StateCreatedConsumer(
        MediatR.IMediator mediator,
        IMapper mapper)
    {
        _mediator = mediator;
        _mapper = mapper;
    }

    public async Task Consume(ConsumeContext<StateCreated> context)
    {
        var command = _mapper.Map<CreateOrUpdateStateCommand>(context.Message);
        await _mediator.Send(command);
    }
}
