using Auditdata.InventoryService.Core.Features.Stocks.Commands;
using Auditdata.Microservice.Messages.Events.Location;

namespace Auditdata.InventoryService.Api.Consumers;

public class LocationUpdatedConsumer : IConsumer<LocationUpdated>
{
    private readonly MediatR.IMediator _mediator;
    private readonly IMapper _mapper;

    public LocationUpdatedConsumer(
        MediatR.IMediator mediator,
        IMapper mapper)
    {
        _mediator = mediator;
        _mapper = mapper;
    }

    public async Task Consume(ConsumeContext<LocationUpdated> context)
    {
        var command = _mapper.Map<UpdateStockCommand>(context.Message);
        await _mediator.Send(command);
    }
}
