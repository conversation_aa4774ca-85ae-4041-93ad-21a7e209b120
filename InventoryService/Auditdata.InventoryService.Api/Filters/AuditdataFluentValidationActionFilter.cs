using Auditdata.Transport.Contracts.Infrastructure;
using Microsoft.AspNetCore.Mvc.Filters;

namespace Auditdata.InventoryService.Api.Filters;

public class AuditdataFluentValidationActionFilter : IAsyncActionFilter
{
    private const string ErrorMessage = "Something went wrong";
    private const string ErrorType = "ValidationException";

    public async Task OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
    {
        var services = context.HttpContext.RequestServices;
        foreach (var argument in context.ActionArguments)
        {
            if (argument.Value is not null && (!argument.Value.GetType().IsClass || argument.Value is string))
            {
                continue;
            }

            var validatorType = typeof(IValidator<>).MakeGenericType(argument.Value.GetType());

            var validator = (IValidator)services.GetService(validatorType)!;
            if (validator is not null)
            {
                var validationContextType = typeof(ValidationContext<>).MakeGenericType(argument.Value?.GetType());
                var validationContext = (IValidationContext)Activator.CreateInstance(validationContextType, argument.Value);

                var result = await validator.ValidateAsync(validationContext);
                if (!result.IsValid)
                {
                    var response = new BadRequestObjectResult(new FormattedResponse<object>(success: false)
                    {
                        Error = new ErrorResponse(
                            ErrorNotificationType.Error,
                            result.Errors.Count == 1 ? result.Errors[0].ErrorMessage : ErrorMessage,
                            ErrorType,
                            context.HttpContext.TraceIdentifier,
                            result.Errors
                                .Select(x =>
                                    new ErrorDetails
                                    {
                                        ErrorCode = x.ErrorCode, PropertyName = x.PropertyName, ErrorMessage = x.ErrorMessage
                                    })
                                .ToArray())
                    });

                    context.Result = response;
                    return;
                }
            }
        }

        await next();
    }
}
