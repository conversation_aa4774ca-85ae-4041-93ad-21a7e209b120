using Auditdata.Transport.Contracts.Infrastructure;
using System.Reflection;

namespace Auditdata.InventoryService.Api.Controllers;

[ApiController]
[ApiVersion("1.0")]
[AllowAnonymous]
[Route("version")]
public class VersionController : AuditdataController
{
    [HttpGet]
    [ProducesResponseType(typeof(FormattedResponse<string>), 200)]
    public IActionResult GetApplicationVersion()
    {
        return OkResponse(new { Version = Assembly.GetExecutingAssembly().GetName().Version!.ToString() });
    }
}
