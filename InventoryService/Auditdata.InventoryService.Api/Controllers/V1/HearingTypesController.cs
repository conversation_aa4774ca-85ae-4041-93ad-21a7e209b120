using Auditdata.InventoryService.Contracts.Responses.HearingAidTypes;
using Auditdata.InventoryService.Core.Features.HearingAidTypes.Queries;

namespace Auditdata.InventoryService.Api.Controllers.V1;

[ApiController]
[ApiVersion("1.0")]
[Authorize]
public class HearingTypesController : AuditdataController
{
    private readonly MediatR.IMediator _mediator;
    private readonly IMapper _mapper;

    public HearingTypesController(
        MediatR.IMediator mediator,
        IMapper mapper)
    {
        _mediator = mediator;
        _mapper = mapper;
    }

    [Obsolete]
    [HttpGet("hearing-types")]
    public async Task<IActionResult> GetAsync()
    {
        var result = await _mediator.Send(new GetHearingAidTypesQuery());
        return OkResponse(_mapper.Map<IEnumerable<HearingAidTypeResponse>>(result.HearingAidTypes));
    }

    [HttpGet("tenants/{tenantId:guid}/hearing-types")]
    public async Task<IActionResult> GetByTenantAsync()
    {
        var result = await _mediator.Send(new GetHearingAidTypesQuery());
        return OkResponse(_mapper.Map<IEnumerable<HearingAidTypeResponse>>(result.HearingAidTypes));
    }
}
