using Auditdata.InventoryService.Api.Constants;
using Auditdata.InventoryService.Api.Extensions;
using Auditdata.InventoryService.Contracts.Requests.Skus;
using Auditdata.InventoryService.Contracts.Responses.Skus;
using Auditdata.InventoryService.Core.Features.Skus.CreateSku;
using Auditdata.InventoryService.Core.Features.Skus.DeleteSku;
using Auditdata.InventoryService.Core.Features.Skus.DeleteSkus;
using Auditdata.InventoryService.Core.Features.Skus.GetById;
using Auditdata.InventoryService.Core.Features.Skus.GetSKUAttributes;
using Auditdata.InventoryService.Core.Features.Skus.SetUpSkuConfig;
using Auditdata.InventoryService.Core.Features.Skus.UpdateSku;
using Auditdata.InventoryService.Core.Features.SKUs.AssignSkus;
using Auditdata.InventoryService.Core.Features.SKUs.GetSkuConfigs;
using Auditdata.InventoryService.Core.Features.SKUs.SearchSkus;
using Auditdata.Transport.Contracts.Infrastructure;
using Auditdata.Transport.Contracts.PageResults;

namespace Auditdata.InventoryService.Api.Controllers.V1;

[ApiController]
[ApiVersion("1.0")]
[Authorize]
[Route("tenants/{tenantId:guid}/skus")]
public class SkusController : AuditdataController
{
    private readonly MediatR.IMediator _mediator;
    private readonly IMapper _mapper;

    public SkusController(
        MediatR.IMediator mediator,
        IMapper mapper)
    {
        _mediator = mediator;
        _mapper = mapper;
    }

    [HttpGet("products/{productId:guid}/attributes")]
    [Authorize(Policy = PolicyConstants.ViewProductCatalog)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(FormattedResponse<GetSkuAttributesResponse>))]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> GetSkuAttributesAsync(Guid productId)
    {
        var result = await _mediator.Send(new GetSkuAttributesQuery(productId));

        var response = _mapper.Map<GetSkuAttributesResponse>(result);

        return OkResponse(response);
    }

    [HttpGet("products/{productId:guid}/config")]
    [Authorize(Policy = PolicyConstants.ViewProductCatalog)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(FormattedResponse<List<SkuConfigResponse>>))]
    public async Task<IActionResult> GetSkuConfigsAsync(Guid productId)
    {
        var result = await _mediator.Send(new GetSkuConfigsQuery(productId));

        var response = _mapper.Map<List<SkuConfigResponse>>(result);

        return OkResponse(response);
    }

    [HttpPut("setup")]
    [Authorize(Policy = PolicyConstants.CreateSKU)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(FormattedResponse<object>))]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> SetUpAttributesAsync([FromBody] SetUpSkuConfigsRequest request)
    {
        var command = _mapper.Map<SetUpSkuConfigCommand>(request);
        await _mediator.Send(command);

        return OkResponse();
    }

    [HttpGet("{skuId:guid}")]
    [Authorize(Policy = PolicyConstants.ViewProductCatalog)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(FormattedResponse<SkuResponse>))]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> GetSkuAsync(Guid skuId)
    {
        var result = await _mediator.Send(new GetSkuQuery(skuId));
        var response = _mapper.Map<SkuResponse>(result);

        return OkResponse(response);
    }

    [HttpGet("search")]
    [Authorize(Policy = PolicyConstants.ViewProductCatalog)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(FormattedResponse<TablePageResult<SkuResponse>>))]
    public async Task<IActionResult> SearchSkusAsync([FromQuery] SearchSkusRequest request)
    {
        if (string.IsNullOrEmpty(request.OrderBy))
        {
            request.OrderBy = "SkuValue";
        }

        var query = _mapper.Map<SearchSkusQuery>(request);

        var result = await _mediator.Send(query);

        var pageResult = TablePageResultExtension.TablePageResult(
            _mapper.Map<IEnumerable<SkuResponse>>(result.Skus), query, result.Total);

        return OkResponse(pageResult);
    }

    [HttpPost]
    [Authorize(Policy = PolicyConstants.CreateSKU)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(FormattedResponse<Guid>))]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> CreateSkuAsync([FromBody] CreateSkuRequest request)
    {
        var command = _mapper.Map<CreateSkuCommand>(request);

        var skuId = await _mediator.Send(command);

        return OkResponse(skuId);
    }

    [HttpPost("products/{productId:guid}")]
    [Authorize(Policy = PolicyConstants.CreateSKU)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(FormattedResponse<object>))]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> AssignSkusAsync(Guid productId, [FromBody] AssignSkusRequest request)
    {
        var command = _mapper.Map<AssignSkusCommand>(request);
        command.ProductId = productId;

        await _mediator.Send(command);

        return OkResponse();
    }

    [HttpPut("{skuId:guid}")]
    [Authorize(Policy = PolicyConstants.CreateSKU)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(FormattedResponse<object>))]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> UpdateSkuAsync(Guid skuId, [FromBody] UpdateSkuRequest request)
    {
        var command = _mapper.Map<UpdateSkuCommand>(request);
        command.SkuId = skuId;
        await _mediator.Send(command);

        return OkResponse();
    }

    [HttpDelete("products/{productId:guid}")]
    [Authorize(Policy = PolicyConstants.CreateSKU)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(FormattedResponse<object>))]
    public async Task<IActionResult> DeleteSkusAsync(Guid productId)
    {
        await _mediator.Send(new DeleteSkusCommand(productId));

        return OkResponse();
    }

    [HttpDelete("{skuId:guid}")]
    [Authorize(Policy = PolicyConstants.CreateSKU)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(FormattedResponse<object>))]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> DeleteSkuAsync(Guid skuId)
    {
        await _mediator.Send(new DeleteSkuCommand(skuId));

        return OkResponse();
    }
}
