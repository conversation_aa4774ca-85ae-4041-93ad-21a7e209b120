using Auditdata.InventoryService.Contracts.Responses.Products;
using Auditdata.InventoryService.Contracts.Requests.Products;
using Auditdata.InventoryService.Core.Features.Products.Commands;
using Auditdata.InventoryService.Core.Features.Products.Queries;
using Auditdata.InventoryService.Core.Features.StockProducts.Commands;
using Auditdata.Transport.Contracts.Infrastructure;
using Auditdata.InventoryService.Api.Constants;
using Auditdata.InventoryService.Api.Extensions;
using Auditdata.Transport.Contracts.PageResults;
using Auditdata.InventoryService.Core.Features.ProductsExcelExport.Queries;
using Auditdata.Transport.Contracts.Table;

namespace Auditdata.InventoryService.Api.Controllers.V1;

[ApiController]
[ApiVersion("1.0")]
[Authorize]
[Route("tenants/{tenantId:guid}/products")]
public class ProductsController : AuditdataController
{
    private readonly MediatR.IMediator _mediator;
    private readonly IMapper _mapper;

    public ProductsController(
        MediatR.IMediator mediator,
        IMapper mapper)
    {
        _mediator = mediator;
        _mapper = mapper;
    }

    [HttpGet]
    [Authorize(Policy = PolicyConstants.ViewProductCatalog)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(FormattedResponse<TablePageResult<GetProductListResponse>>))]
    public async Task<IActionResult> GetAsync([FromQuery] TableQueryBase request)
    {
        var result = await _mediator.Send(new GetProductListQuery(
            request.PerPage, request.Page, request.OrderBy));

        var response = _mapper.Map<TablePageResult<GetProductListResponse>>(result);
        return OkResponse(response);
    }

    [HttpGet("{id:guid}")]
    [Authorize(Policy = PolicyConstants.ViewProduct)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(FormattedResponse<GetProductResponse>))]
    [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(FormattedResponse<ErrorResponse>))]
    public async Task<IActionResult> GetProductAsync(Guid id)
    {
        var query = new GetProductQuery(id);

        var result = await _mediator.Send(query);
        var response = _mapper.Map<GetProductResponse>(result);
        return OkResponse(response);
    }

    [HttpGet("types/{categoryId:guid}/manufacturers/{manufacturerId:guid}")]
    [Authorize(Policy = PolicyConstants.ViewProductCatalog)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(IEnumerable<GetProductResponse>))]
    public async Task<IActionResult> GetByCategoryAndManufacturerAsync(Guid categoryId, Guid manufacturerId)
    {
        var result = await _mediator.Send(new GetProductByManufacturerAndCategoryQuery(categoryId, manufacturerId));

        return OkResponse(_mapper.Map<IEnumerable<GetProductResponse>>(result.Products));
    }

    [HttpPost]
    [Authorize(Policy = PolicyConstants.ViewEditProductCatalog)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(FormattedResponse<CreateProductResponse>))]
    [ProducesResponseType(StatusCodes.Status409Conflict, Type = typeof(FormattedResponse<object>))]
    public async Task<IActionResult> CreateAsync([FromBody] ProductRequest request)
    {
        var command = _mapper.Map<CreateProductCommand>(request);

        var result = await _mediator.Send(command);
        return OkResponse(new CreateProductResponse { Id = result.Id, Name = result.Name });
    }

    [HttpDelete("{id:guid}")]
    [Authorize(Policy = PolicyConstants.ViewEditProductCatalog)]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> DeleteAsync([FromRoute] Guid id)
    {
        var command = new DeleteProductCommand(id);
        await _mediator.Send(command);
        return NoContent();
    }

    [HttpGet("search")]
    [Authorize(Policy = PolicyConstants.ViewProductCatalog)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(FormattedResponse<TablePageResult<ProductSearchResponse>>))]
    public async Task<IActionResult> SearchProductsAsync([FromQuery] SearchProductsRequest searchProducts)
    {
        var query = _mapper.Map<SearchProductsQuery>(searchProducts);

        var result = await _mediator.Send(query);

        var pageResult = TablePageResultExtension.TablePageResult(
            _mapper.Map<IEnumerable<ProductSearchResponse>>(result.Products), query, result.TotalCount);

        foreach (var row in pageResult.Rows!)
        {
            row.IsActive = row.Model!.IsActive;
        }

        return OkResponse(new { PageResult = pageResult });
    }

    [HttpGet("{id:guid}/search-suggested")]
    [Authorize(Policy = PolicyConstants.ViewProductCatalog)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(FormattedResponse<TablePageResult<ProductSearchResponse>>))]
    public async Task<IActionResult> SearchSuggestedProductsAsync(
        [FromRoute] Guid id,
        [FromQuery] SearchProductsRequest searchProducts)
    {
        var query = _mapper.Map<SearchSuggestedProductsQuery>(searchProducts);
        query.ProductId = id;

        var result = await _mediator.Send(query);

        var pageResult = TablePageResultExtension.TablePageResult(
            _mapper.Map<IEnumerable<ProductSearchResponse>>(result.Products), query, result.TotalCount);

        foreach (var row in pageResult.Rows!)
        {
            row.IsActive = row.Model!.IsActive;
        }

        return OkResponse(new { PageResult = pageResult });
    }

    [HttpPut("{id:guid}")]
    [Authorize(Policy = PolicyConstants.ViewEditProductCatalog)]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status409Conflict, Type = typeof(FormattedResponse<object>))]
    public async Task<IActionResult> UpdateAsync([FromRoute] Guid id, [FromBody] ProductRequest updateProductRequest)
    {
        var command = _mapper.Map<UpdateProductCommand>(updateProductRequest);
        command.Id = id;

        await _mediator.Send(command);
        return OkResponse();
    }

    [HttpPut("{id:guid}/is-active")]
    [Authorize(Policy = PolicyConstants.ViewEditProductCatalog)]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> UpdateIsActiveProductStatusAsync(
        [FromRoute] Guid id,
        [FromBody] UpdateIsActiveProductStatusRequest request)
    {
        var command = new UpdateIsActiveProductStatusCommand(id, request.IsActive);

        await _mediator.Send(command);
        return OkResponse();
    }

    [HttpPut("{id:guid}/is-sellable")]
    [Authorize(Policy = PolicyConstants.ViewEditProductCatalog)]
    [ProducesResponseType(StatusCodes.Status200OK)]
    public async Task<IActionResult> UpdateIsSellableProductStatusAsync(
        [FromRoute] Guid id,
        [FromBody] UpdateIsSellableProductStatusRequest request)
    {
        var command = new UpdateIsSellableProductStatusCommand(id, request.IsSellable);
        await _mediator.Send(command);
        return OkResponse();
    }

    [HttpPost("{id:guid}/summary")]
    [Authorize(Policy = PolicyConstants.ViewStock)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(FormattedResponse<ProductStockSummaryResponse>))]
    public async Task<IActionResult> GetStockSummaryAsync(Guid id, [FromBody] GetStockSummaryBodyRequest request)
    {
        var result = await _mediator.Send(new GetProductStockSummaryQuery(id, request.LocationIds));

        return OkResponse(_mapper.Map<ProductStockSummaryResponse>(result));
    }

    [HttpDelete("{id:guid}/stocks")]
    [Authorize(Policy = PolicyConstants.ViewEditStock)]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status409Conflict, Type = typeof(FormattedResponse<object>))]
    public async Task<IActionResult> DeleteProductFromStockAsync(Guid id)
    {
        var command = _mapper.Map<DeleteStockProductCommand>(id);
        await _mediator.Send(command);
        return NoContent();
    }

    [HttpGet("{id:guid}/cost")]
    [Authorize(Policy = PolicyConstants.ViewProductCost)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(FormattedResponse<decimal>))]
    public async Task<IActionResult> GetProductCostAsync([FromRoute] Guid id)
    {
        var result = await _mediator.Send(new GetProductCostQuery(id));

        return OkResponse(result.Cost);
    }

    [HttpPut("{id:guid}/cost")]
    [Authorize(Policy = PolicyConstants.EditProductCost)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(FormattedResponse<object>))]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> UpdateProductCostAsync([FromRoute] Guid id, UpdateProductCostRequest request)
    {
        await _mediator.Send(new UpdateProductCostCommand(id, request.Cost));
        return OkResponse();
    }

    [HttpGet("stock-products/{stockProductId:guid}")]
    [Authorize(Policy = PolicyConstants.ViewProduct)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(FormattedResponse<GetProductResponse>))]
    public async Task<IActionResult> GetProductByStockProductIdAsync(Guid stockProductId)
    {
        var result = await _mediator.Send(new GetProductByStockProductIdQuery(stockProductId));

        var response = _mapper.Map<GetProductResponse>(result);
        return OkResponse(response);
    }

    [HttpPost("stock-products")]
    [Authorize(Policy = PolicyConstants.ViewProduct)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(FormattedResponse<IEnumerable<GetProductResponse>>))]
    public async Task<IActionResult> GetProductByStockProductIdsAsync(GetProductByStockProductIdsRequest request)
    {
        var result = await _mediator.Send(new GetProductByStockProductIdsCommand(request.StockProductIds));

        var response = _mapper.Map<IEnumerable<GetProductResponse>>(result);
        return OkResponse(response);
    }

    [HttpGet("export")]
    [Authorize(Policy = PolicyConstants.ViewProduct)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(FormattedResponse<FileResult>))]
    public async Task<IActionResult> ExportProductsToExcelAsync([FromQuery] ProductsExportRequest request)
    {
        var query = _mapper.Map<ProductsExportQuery>(request);
        var result = await _mediator.Send(query);
        return File(result.ExcelFileStream, ContentTypeConstants.ExcelContent);
    }

    [HttpGet("{id:guid}/can-unserialize")]
    [Authorize(Policy = PolicyConstants.ViewEditProductCatalog)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(FormattedResponse<bool>))]
    public async Task<IActionResult> CanUnserializeAsync([FromRoute] Guid id, CancellationToken cancellationToken)
    {
        var result = await _mediator.Send(new CanUnserializeProductQuery(id), cancellationToken);
        return OkResponse(result);
    }
}
