using Auditdata.InventoryService.Api.Constants;
using Auditdata.InventoryService.Contracts.Requests.StockProducts;
using Auditdata.InventoryService.Contracts.Responses;
using Auditdata.InventoryService.Contracts.Responses.StockProduct;
using Auditdata.InventoryService.Contracts.Responses.StockProducts;
using Auditdata.InventoryService.Core.Features.StockProductItems.Dtos;
using Auditdata.InventoryService.Core.Features.StockProducts.Commands;
using Auditdata.InventoryService.Core.Features.StockProducts.Queries;
using Auditdata.Transport.Contracts.Infrastructure;
using Auditdata.Transport.Contracts.PageResults;

namespace Auditdata.InventoryService.Api.Controllers.V1;

[ApiController]
[ApiVersion("1.0")]
[Authorize]
[Route("tenants/{tenantId:guid}/stocks/products")]
public class StockProductsController : AuditdataController
{
    private readonly MediatR.IMediator _mediator;
    private readonly IMapper _mapper;

    public StockProductsController(
        MediatR.IMediator mediator,
        IMapper mapper)
    {
        _mediator = mediator;
        _mapper = mapper;
    }

    [HttpGet("~/api/v{version:apiVersion}/tenants/{tenantId:guid}/stock-products/{id:guid}")]
    [Authorize(Policy = PolicyConstants.ViewStock)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(FormattedResponse<StockProductResponse>))]
    public async Task<IActionResult> GetByIdAsync([FromRoute] Guid id)
    {
        var query = new GetStockProductByIdQuery(id);
        var result = await _mediator.Send(query);

        return OkResponse(_mapper.Map<StockProductResponse>(result));
    }

    [HttpPost]
    [Authorize(Policy = PolicyConstants.ViewEditStock)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(FormattedResponse<CreateStockProductResponse>))]
    public async Task<IActionResult> CreateAsync(CreateStockProductRequest request)
    {
        var command = new CreateStockProductCommand(
            request.StockId!.Value,
            request.ProductId!.Value,
            request.Quantity!.Value);

        var result = await _mediator.Send(command);

        return OkResponse(new CreateStockProductResponse { Id = result.Id });
    }

    [HttpGet("{productId:guid}")]
    [Authorize(Policy = PolicyConstants.ViewStock)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(FormattedResponse<IEnumerable<StockProductResponse>>))]
    public async Task<IActionResult> GetByProductAsync(Guid productId)
    {
        var query = new GetStockProductByProductIdQuery(productId);
        var result = await _mediator.Send(query);

        return OkResponse(_mapper.Map<IEnumerable<StockProductResponse>>(result.StockProducts));
    }

    [HttpPost("{productId:guid}/details")]
    [Authorize(Policy = PolicyConstants.ViewStock)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(FormattedResponse<IEnumerable<StockProductsDetailsResponse>>))]
    public async Task<IActionResult> GetDetailsByProductAsync(Guid productId, [FromBody] StockProductDetailsRequest request)
    {
        var result = await _mediator.Send(new GetStockProductDetailsQuery(productId, request.LocationIds));
        return OkResponse(_mapper.Map<StockProductsDetailsResponse>(result));
    }

    [HttpGet("search")]
    [Authorize(Policy = PolicyConstants.ViewStock)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(FormattedResponse<TablePageResult<StockProductsSearchResponse>>))]
    public async Task<IActionResult> SearchStockProductsByLocationsAsync(
        [FromQuery] SearchStockProductsRequest searchProducts)
    {
        var query = _mapper.Map<SearchStockProductsQuery>(searchProducts);

        var result = await _mediator.Send(query);

        var rows = _mapper.Map<TablePageResult<StockProductsSearchResponse>>(result);
        foreach (var row in rows.Rows!)
        {
            row.IsActive = row.Model!.IsActive;
        }

        return OkResponse(new { PageResult = rows });
    }

    [HttpPost("{productId:guid}/in-stock")]
    [Authorize(Policy = PolicyConstants.ViewStock)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(FormattedResponse<InStockAmountResponse>))]
    public async Task<IActionResult> GetInStockAmountAsync(
        [FromRoute] Guid productId, [FromBody] GetInStockAmountRequest request, CancellationToken cancellationToken)
    {
        var query = new GetInStockAmountQuery(productId, request.LocationIds);
        var result = await _mediator.Send(query, cancellationToken);

        return OkResponse(new InStockAmountResponse(result.InStock));
    }

    [HttpPost("adjust")]
    [Authorize(Policy = PolicyConstants.ViewEditStock)]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status422UnprocessableEntity)]
    public async Task<IActionResult> AdjustStockProductQuantityAsync(AdjustStockProductQuantityRequest request)
    {
        var command = _mapper.Map<AdjustStockProductQuantityCommand>(request);
        await _mediator.Send(command);
        return OkResponse();
    }

    [HttpPost("{stockProductId:guid}/adjust/serialized")]
    [Authorize]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(FormattedResponse<IEnumerable<AdjustSerializedProductResponse>>))]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> AdjustSerializedProductAsync(
        [FromRoute] Guid stockProductId,
        [FromBody] AdjustSerializedProductRequest request,
        CancellationToken cancellationToken)
    {
        var command = new AdjustSerializedStockProductCommand(stockProductId)
        {
            ColorId = request.ColorId,
            BatteryTypeId = request.BatteryTypeId,
            Attributes = request.Attributes.Select(a => new StockProductItemAttributeDto(
                a.AttributeId, new AttributeValueDto(a.Value.Id, a.Value.Value))
            {
                AttributeName = a.AttributeName
            }),
            SerialNumbers = request.SerialNumbers
        };

        var result = await _mediator.Send(command, cancellationToken);
        return OkResponse(result);
    }

    [HttpDelete("{stockProductId:guid}")]
    [Authorize(Policy = PolicyConstants.ViewEditStock)]
    [ProducesResponseType(StatusCodes.Status204NoContent, Type = typeof(FormattedResponse<object>))]
    [ProducesResponseType(StatusCodes.Status409Conflict, Type = typeof(FormattedResponse<object>))]
    public async Task<IActionResult> DeleteStockProductAsync(Guid stockProductId)
    {
        var command = _mapper.Map<DeleteStockProductCommand>(stockProductId);
        await _mediator.Send(command);
        return NoContent();
    }
}
