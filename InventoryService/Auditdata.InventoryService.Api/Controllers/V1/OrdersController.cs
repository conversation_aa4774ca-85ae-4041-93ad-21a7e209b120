using Auditdata.InventoryService.Contracts.Requests.Orders;
using Auditdata.InventoryService.Contracts.Requests.Suppliers;
using Auditdata.InventoryService.Contracts.Responses.Orders;
using Auditdata.InventoryService.Contracts.Responses.Suppliers;
using Auditdata.InventoryService.Core.Features.Orders.Queries;
using Auditdata.InventoryService.Core.Features.Suppliers.Queries;
using Auditdata.Transport.Contracts.Infrastructure;
using Auditdata.Transport.Contracts.PageResults;

namespace Auditdata.InventoryService.Api.Controllers.V1;

[ApiController]
[ApiVersion("1.0")]
[Authorize]
[Route("tenants/{tenantId:guid}/orders")]
public class OrdersController : AuditdataController
{
    private readonly MediatR.IMediator _mediator;
    private readonly IMapper _mapper;

    public OrdersController(
        MediatR.IMediator mediator,
        IMapper mapper)
    {
        _mediator = mediator;
        _mapper = mapper;
    }

    [HttpGet("products")]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(FormattedResponse<TablePageResult<OrderProductResponse>>))]
    public async Task<IActionResult> GetOrderProductsAsync([FromQuery] GetOrderProductsRequest request)
    {
        var query = _mapper.Map<GetOrderProductsQuery>(request);
        var result = await _mediator.Send(query);
        var response = _mapper.Map<TablePageResult<OrderProductResponse>>(result.StockProducts);
        return OkResponse(response);
    }

    [HttpPost("suppliers")]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(FormattedResponse<IEnumerable<SupplierResponse>>))]
    public async Task<IActionResult> GetSuppliersByIdsAsync([FromBody] GetSuppliersByIdsRequest request)
    {
        var result = await _mediator.Send(new GetSuppliersByIdsQuery(request.SupplierIds));

        var response = _mapper.Map<IEnumerable<SupplierResponse>>(result.Suppliers);

        return OkResponse(response);
    }

    [HttpPost("print/products")]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(FormattedResponse<IEnumerable<ProductsForPrintResponse>>))]
    public async Task<IActionResult> GetProductsForPrintAsync([FromBody] GetProductsForPrintRequest request)
    {
        var query = new GetProductsForPrintQuery(request.ProductIds);
        var result = await _mediator.Send(query);
        var response = _mapper.Map<IEnumerable<ProductsForPrintResponse>>(result.Products);
        return OkResponse(response);
    }
}
