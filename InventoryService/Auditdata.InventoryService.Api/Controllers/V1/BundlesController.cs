using Auditdata.InventoryService.Api.Constants;
using Auditdata.InventoryService.Api.Extensions;
using Auditdata.InventoryService.Contracts.Requests.Bundles;
using Auditdata.InventoryService.Contracts.Responses.Bundles;
using Auditdata.InventoryService.Core.Features.Bundles.CreateBundle;
using Auditdata.InventoryService.Core.Features.Bundles.DeleteBundle;
using Auditdata.InventoryService.Core.Features.Bundles.GetBundleById;
using Auditdata.InventoryService.Core.Features.Bundles.PatchBundle;
using Auditdata.InventoryService.Core.Features.Bundles.SearchBundles;
using Auditdata.InventoryService.Core.Features.Bundles.UpdateBundle;
using Auditdata.Transport.Contracts.Infrastructure;
using Auditdata.Transport.Contracts.PageResults;
using MediatR;

namespace Auditdata.InventoryService.Api.Controllers.V1;

[ApiController]
[ApiVersion("1.0")]
[Authorize]
[Route("tenants/{tenantId:guid}/bundles")]
public class BundlesController : AuditdataController
{
    private readonly IMediator _mediator;
    private readonly IMapper _mapper;

    public BundlesController(
        IMediator mediator,
        IMapper mapper)
    {
        _mediator = mediator;
        _mapper = mapper;
    }

    [HttpGet("{id:guid}")]
    [Authorize(Policy = PolicyConstants.ViewBundleCatalog)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(FormattedResponse<BundleResponse>))]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> GetByIdAsync([FromRoute] Guid id, CancellationToken cancellationToken)
    {
        var query = new GetBundleByIdQuery(id);
        var result = await _mediator.Send(query, cancellationToken);

        var response = _mapper.Map<BundleResponse>(result);
        return OkResponse(response);
    }

    [HttpGet("search")]
    [Authorize(Policy = PolicyConstants.ViewBundleCatalog)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(FormattedResponse<TablePageResult<SearchBundlesResponse>>))]
    public async Task<IActionResult> SearchAsync([FromQuery] SearchBundlesRequest request)
    {
        var query = _mapper.Map<SearchBundlesQuery>(request);

        var result = await _mediator.Send(query);

        var pageResult = TablePageResultExtension.TablePageResult(
            _mapper.Map<IEnumerable<SearchBundlesResponse>>(result.Bundles), query, result.TotalCount);

        foreach (var row in pageResult.Rows!)
        {
            row.IsActive = row.Model!.IsActive;
        }

        return OkResponse(pageResult);
    }

    [HttpPost]
    [Authorize(Policy = PolicyConstants.EditBundleCatalog)]
    [ProducesResponseType(StatusCodes.Status201Created, Type = typeof(FormattedResponse<object>))]
    [ProducesResponseType(StatusCodes.Status422UnprocessableEntity, Type = typeof(FormattedResponse<object>))]
    public async Task<IActionResult> CreateAsync([FromBody] CreateBundleRequest request)
    {
        var command = _mapper.Map<CreateBundleCommand>(request);
        var result = await _mediator.Send(command);

        return OkResponse(new
        {
            id = result
        });
    }

    [HttpPut("{id:guid}")]
    [Authorize(Policy = PolicyConstants.EditBundleCatalog)]
    [ProducesResponseType(StatusCodes.Status200OK)]
    public async Task<IActionResult> UpdateAsync([FromRoute] Guid id, [FromBody] UpdateBundleRequest request)
    {
        var command = _mapper.Map<UpdateBundleCommand>(request);
        command.Id = id;
        await _mediator.Send(command);

        return OkResponse();
    }

    [HttpPatch("{id:guid}")]
    [Authorize(Policy = PolicyConstants.EditBundleCatalog)]
    [ProducesResponseType(StatusCodes.Status200OK)]
    public async Task<IActionResult> PatchAsync([FromRoute] Guid id, [FromBody] PatchBundleRequest request)
    {
        var command = _mapper.Map<PatchBundleCommand>(request);
        command.Id = id;
        await _mediator.Send(command);

        return OkResponse();
    }

    [HttpDelete("{id:guid}")]
    [Authorize(Policy = PolicyConstants.EditBundleCatalog)]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    public async Task<IActionResult> DeleteAsync([FromRoute] Guid id)
    {
        var command = new DeleteBundleCommand(id);
        await _mediator.Send(command);

        return NoContent();
    }
}
