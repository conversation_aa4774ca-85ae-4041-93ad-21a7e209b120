using Auditdata.InventoryService.Contracts.Requests.Stocks;
using Auditdata.InventoryService.Contracts.Responses.Stocks;
using Auditdata.InventoryService.Core.Features.Stocks.Queries;
using Auditdata.Transport.Contracts.Infrastructure;

namespace Auditdata.InventoryService.Api.Controllers.V1;

[ApiController]
[ApiVersion("1.0")]
[Authorize]
[Route("tenants/{tenantId:guid}/stocks")]
public class StocksController : AuditdataController
{
    private readonly MediatR.IMediator _mediator;
    private readonly IMapper _mapper;

    public StocksController(
        MediatR.IMediator mediator,
        IMapper mapper)
    {
        _mediator = mediator;
        _mapper = mapper;
    }

    [HttpGet]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(FormattedResponse<IEnumerable<StocksResponse>>))]
    public async Task<IActionResult> GetStocksAsync([FromQuery]Guid? regionId)
    {
        var result = await _mediator.Send(new GetStocksQuery(regionId));
        return OkResponse(_mapper.Map<IEnumerable<StocksResponse>>(result.Stocks));
    }

    [HttpPost]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(FormattedResponse<IEnumerable<StocksResponse>>))]
    public async Task<IActionResult> GetStocksByLocationsAsync([FromBody] GetStocksByLocationsRequest request)
    {
        var result = await _mediator.Send(new GetStocksByLocationsQuery(request.LocationIds, request.RegionId));
        return OkResponse(_mapper.Map<IEnumerable<StocksResponse>>(result.Stocks));
    }
}
