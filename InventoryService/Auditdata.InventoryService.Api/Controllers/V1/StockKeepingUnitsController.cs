using Auditdata.InventoryService.Api.Constants;
using Auditdata.InventoryService.Contracts.Requests.StockKeepingUnits;
using Auditdata.InventoryService.Contracts.Responses.StockKeepingUnits;
using Auditdata.InventoryService.Core.Entities;
using Auditdata.InventoryService.Core.Features.StockKeepingUnits.Commands;
using Auditdata.InventoryService.Core.Features.StockKeepingUnits.Queries;
using Auditdata.Transport.Contracts.Infrastructure;
using MediatR;

namespace Auditdata.InventoryService.Api.Controllers.V1;

[NonController]
[ApiVersion("1.0")]
[Authorize]
[Route("tenants/{tenantId:guid}/stockkeepingunits")]
public class StockKeepingUnitsController : AuditdataController
{
    private readonly IMediator _mediator;
    private readonly IMapper _mapper;

    public StockKeepingUnitsController(IMediator mediator, IMapper mapper)
    {
        _mediator = mediator;
        _mapper = mapper;
    }

    [HttpPost("import")]
    [Authorize(Policy = PolicyConstants.CreateSKU)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(FormattedResponse<object>))]
    [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(FormattedResponse<ErrorResponse>))]
    [ProducesResponseType(StatusCodes.Status409Conflict, Type = typeof(FormattedResponse<ErrorResponse>))]
    public async Task<IActionResult> ImportSkusFromExcelAsync(
        [FromForm] ImportStockKeepingUnitRequest request,
        CancellationToken cancellationToken)
    {
        var command = new ImportStockKeepingUnitsCommand(request.ExcelFile, (ProductCategoryCode)request.CategoryCode);
        _ = await _mediator.Send(command, cancellationToken);
        return OkResponse();
    }

    [HttpGet("export")]
    [Authorize(Policy = PolicyConstants.CreateSKU)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(FormattedResponse<FileStreamResult>))]
    [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(FormattedResponse<ErrorResponse>))]
    [ProducesResponseType(StatusCodes.Status409Conflict, Type = typeof(FormattedResponse<ErrorResponse>))]
    public async Task<IActionResult> ExportSkusToExcelAsync(
        [FromQuery] ExportStockKeepingUnitRequest request,
        CancellationToken cancellationToken)
    {
        var command = new ExportStockKeepingUnitsCommand((ProductCategoryCode)request.CategoryCode, request.SkipProducts);

        var result = await _mediator.Send(command, cancellationToken);

        Response.Headers.Add(
            "Access-Control-Expose-Headers",
            new string[] { nameof(result.ProcessedProducts), nameof(result.AllSkusExported) });
        Response.Headers.Add(nameof(result.ProcessedProducts), result.ProcessedProducts.ToString());
        Response.Headers.Add(nameof(result.AllSkusExported), result.AllSkusExported.ToString());

        return File(result.ExcelFileStream, ContentTypeConstants.ExcelContent);
    }

    [HttpPost]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(FormattedResponse<IEnumerable<GetStockKeepingUnitResponse>>))]
    public async Task<IActionResult> GetSkusAsync([FromBody] GetStockKeepingUnitsRequest request)
    {
        var queries = _mapper.Map<List<GetStockKeepingUnitQuery>>(request.StockKeepingUnitRequests);

        var query = new GetStockKeepingUnitsQuery(queries);

        var result = await _mediator.Send(query);

        var response = _mapper.Map<IEnumerable<GetStockKeepingUnitResponse>>(result.StockKeepingUnitResults);

        return OkResponse(response);
    }
}
