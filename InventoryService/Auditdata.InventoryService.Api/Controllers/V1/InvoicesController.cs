using Auditdata.InventoryService.Contracts.Requests.StockProductItems;
using Auditdata.InventoryService.Contracts.Requests.StockProducts;
using Auditdata.InventoryService.Contracts.Requests.StockProducts.Hsp;
using Auditdata.InventoryService.Contracts.Requests.StockProducts.Nhs;
using Auditdata.InventoryService.Contracts.Requests.StockProducts.US;
using Auditdata.InventoryService.Contracts.Responses.ProductItems;
using Auditdata.InventoryService.Contracts.Responses.StockProducts.Hsp;
using Auditdata.InventoryService.Contracts.Responses.StockProducts.Nhs;
using Auditdata.InventoryService.Contracts.Responses.StockProducts.US;
using Auditdata.InventoryService.Core.Features.StockProductItems.Commands;
using Auditdata.InventoryService.Core.Features.StockProductItems.Queries;
using Auditdata.InventoryService.Core.Features.StockProducts.Queries;
using Auditdata.Transport.Contracts.Infrastructure;
using Auditdata.Transport.Contracts.PageResults;

namespace Auditdata.InventoryService.Api.Controllers.V1;

[ApiController]
[ApiVersion("1.0")]
[Authorize]
[Route("tenants/{tenantId:guid}/invoices")]
public class InvoicesController : AuditdataController
{
    private readonly MediatR.IMediator _mediator;
    private readonly IMapper _mapper;

    public InvoicesController(
        MediatR.IMediator mediator,
        IMapper mapper)
    {
        _mediator = mediator;
        _mapper = mapper;
    }

    [HttpGet("products")]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(FormattedResponse<TablePageResult<InvoiceNhsProductResponse>>))]
    public async Task<IActionResult> GetProductsAsync(
        [FromQuery] InvoiceNhsProductRequest request, CancellationToken cancellationToken)
    {
        var query = _mapper.Map<GetInvoiceNhsStockProductsQuery>(request);
        return OkResponse(await _mediator.Send(query, cancellationToken));
    }

    [HttpGet("us/products")]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(FormattedResponse<TablePageResult<InvoiceUSProductResponse>>))]
    public async Task<IActionResult> GetUSProductsAsync(
        [FromQuery] InvoiceUSProductRequest request, CancellationToken cancellationToken)
    {
        var query = _mapper.Map<GetInvoiceUSStockProductsQuery>(request);
        return OkResponse(await _mediator.Send(query, cancellationToken));
    }

    [HttpGet("hsp/products")]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(FormattedResponse<TablePageResult<InvoiceHspProductResponse>>))]
    public async Task<IActionResult> GetHspProductsAsync(
        [FromQuery] InvoiceHspProductRequest request, CancellationToken cancellationToken)
    {
        var query = _mapper.Map<GetInvoiceHspStockProductsQuery>(request);
        return OkResponse(await _mediator.Send(query, cancellationToken));
    }

    [HttpGet("suggested-products")]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(FormattedResponse<TablePageResult<InvoiceNhsProductResponse>>))]
    public async Task<IActionResult> GetSuggestedProductsAsync(
        [FromQuery] InvoiceSuggestedProductRequest request, CancellationToken cancellationToken)
    {
        var query = _mapper.Map<GetInvoiceNhsSuggestedStockProductsQuery>(request);
        return OkResponse(await _mediator.Send(query, cancellationToken));
    }

    [HttpGet("us/suggested-products")]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(FormattedResponse<TablePageResult<InvoiceUSProductResponse>>))]
    public async Task<IActionResult> GetUSSuggestedProductsAsync(
        [FromQuery] InvoiceSuggestedProductRequest request, CancellationToken cancellationToken)
    {
        var query = _mapper.Map<GetInvoiceUSSuggestedStockProductsQuery>(request);
        return OkResponse(await _mediator.Send(query, cancellationToken));
    }

    [HttpGet("hsp/suggested-products")]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(FormattedResponse<TablePageResult<InvoiceHspProductResponse>>))]
    public async Task<IActionResult> GetHspSuggestedProductsAsync(
        [FromQuery] InvoiceSuggestedProductRequest request, CancellationToken cancellationToken)
    {
        var query = _mapper.Map<GetInvoiceHspSuggestedStockProductsQuery>(request);
        return OkResponse(await _mediator.Send(query, cancellationToken));
    }

    [Obsolete($"Use {nameof(GetAvailableItemsByProductAsync)} instead")]
    [HttpGet("product-items/{stockProductId:guid}/available")]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(FormattedResponse<IEnumerable<StockProductItemResponse>>))]
    public async Task<IActionResult> GetAvailableByStockProductAsync([FromRoute] Guid stockProductId)
    {
        var result = await _mediator.Send(new GetInvoiceAvailableStockProductItemsQuery(stockProductId));

        return OkResponse(_mapper.Map<IEnumerable<StockProductItemResponse>>(result.StockProductItems));
    }

    [HttpPost("product-items")]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(FormattedResponse<CreatedProductItemResponse>))]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> CreateStockProductItemAsync([FromBody] CreateStockProductItemsRequest request)
    {
        var command = _mapper.Map<CreateStockProductItemsCommand>(request);
        var result = await _mediator.Send(command);
        var response = _mapper.Map<IEnumerable<CreatedProductItemResponse>>(result);
        return OkResponse(response);
    }

    [HttpPost("product-items/statuses")]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(FormattedResponse<IEnumerable<GetProductItemStatusResponse>>))]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> GetProductItemsStatusesAsync([FromBody] GetProductItemsStatusesRequest request)
    {
        var result = await _mediator.Send(new GetProductItemsStatusesQuery(request.StockProductItemIds));
        var response = _mapper.Map<IEnumerable<GetProductItemStatusResponse>>(result.Items);
        return OkResponse(response);
    }

    [Obsolete($"Use {nameof(GetAvailableItemsByProductAsync)} instead")]
    [HttpPost("product-items/reserved-by-order")]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(FormattedResponse<IEnumerable<GetProductItemsReservedByOrderResponse>>))]
    public async Task<IActionResult> GetProductItemsReservedByOrderAsync([FromBody] GetProductItemsReservedByOrderRequest request)
    {
        var result = await _mediator.Send(new GetProductItemsReservedByOrderQuery(request.LocationId, request.OrderIds));
        var response = _mapper.Map<IEnumerable<GetProductItemsReservedByOrderResponse>>(result.StockProductItems);
        return OkResponse(response);
    }

    [HttpGet("product-items/product/{productId:guid}/location/{locationId:guid}")]
    [ProducesResponseType(
        StatusCodes.Status200OK,
        Type = typeof(FormattedResponse<TablePageResult<GetInvoiceAvailableItemResponse>>))]
    public async Task<IActionResult> GetAvailableItemsByProductAsync(
        [FromRoute] Guid productId, [FromRoute] Guid locationId, [FromQuery] GetInvoiceAvailableItemsRequest request)
    {
        var result = await _mediator.Send(
            new GetInvoiceAvailableItemsByProductQuery
            {
                ProductId = productId,
                LocationId = locationId,
                ReservedBy = request.ReservedBy,
                SearchText = request.SearchText,
                Page = request.Page,
                PerPage = request.PerPage,
                OrderBy = request.OrderBy
            });

        var response = _mapper.Map<TablePageResult<GetInvoiceAvailableItemResponse>>(result);

        return OkResponse(response);
    }
}
