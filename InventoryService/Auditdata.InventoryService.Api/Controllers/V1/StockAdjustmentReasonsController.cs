using Auditdata.InventoryService.Contracts.Requests.StockAdjustmentReasons;
using Auditdata.InventoryService.Contracts.Responses.StockAdjustmentReasons;
using Auditdata.InventoryService.Core.Features.StockAdjustmentReasons.Commands;
using Auditdata.InventoryService.Core.Features.StockAdjustmentReasons.Queries;
using Auditdata.Transport.Contracts.Infrastructure;

namespace Auditdata.InventoryService.Api.Controllers.V1;

[ApiController]
[ApiVersion("1.0")]
[Authorize]
[Route("tenants/{tenantId:guid}/stock-adjustment-reasons")]
public class StockAdjustmentReasonsController : AuditdataController
{
    private readonly MediatR.IMediator _mediator;
    private readonly IMapper _mapper;

    public StockAdjustmentReasonsController(
        MediatR.IMediator mediator,
        IMapper mapper)
    {
        _mediator = mediator;
        _mapper = mapper;
    }

    [HttpGet]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(FormattedResponse<IEnumerable<StockAdjustmentReasonResponse>>))]
    public async Task<IActionResult> GetAsync(bool? isActive)
    {
        var result = await _mediator.Send(new GetStockAdjustmentReasonQuery
        {
            IsActive = isActive
        });

        return OkResponse(_mapper.Map<IEnumerable<StockAdjustmentReasonResponse>>(result.Reasons));
    }

    [HttpPost]
    [ProducesResponseType(StatusCodes.Status200OK)]
    public async Task<IActionResult> CreateAsync([FromBody] StockAdjustmentReasonRequest request)
    {
        var command = _mapper.Map<CreateStockAdjustmentReasonCommand>(request);

        var result = await _mediator.Send(command);

        return OkResponse(new
        {
            id = result.Id
        });
    }

    [HttpPut("{id:guid}")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    public async Task<IActionResult> UpdateAsync([FromRoute] Guid id, [FromBody] StockAdjustmentReasonRequest request)
    {
        var command = _mapper.Map<UpdateStockAdjustmentReasonCommand>(request) with { Id = id };
        await _mediator.Send(command);

        return OkResponse();
    }

    [HttpDelete("{id:guid}")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    public async Task<IActionResult> DeleteAsync([FromRoute] Guid id)
    {
        await _mediator.Send(new DeleteStockAdjustmentReasonCommand(id));
        return NoContent();
    }
}
