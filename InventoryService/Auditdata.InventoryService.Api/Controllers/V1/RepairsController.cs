using Auditdata.InventoryService.Api.Constants;
using Auditdata.InventoryService.Contracts.Requests.StockProductItems;
using Auditdata.InventoryService.Contracts.Responses.Repairs;
using Auditdata.InventoryService.Core.Features.Repairs.Queries;
using Auditdata.Transport.Contracts.Infrastructure;
using Auditdata.Transport.Contracts.PageResults;

namespace Auditdata.InventoryService.Api.Controllers.V1;

[ApiController]
[ApiVersion("1.0")]
[Authorize]
[Route("tenants/{tenantId:guid}/repairs")]
public class RepairsController : AuditdataController
{
    private readonly MediatR.IMediator _mediator;
    private readonly IMapper _mapper;

    public RepairsController(
        MediatR.IMediator mediator,
        IMapper mapper)
    {
        _mediator = mediator;
        _mapper = mapper;
    }

    [HttpGet("stock-product-items/{stockProductItemId:guid}")]
    [Authorize(Policy = PolicyConstants.ViewProductCatalog)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(FormattedResponse<RepairStockProductItemResponse>))]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> GetRepairProductItemAsync(Guid stockProductItemId)
    {
        var result = await _mediator.Send(new GetRepairStockProductItemQuery(stockProductItemId));

        var response = _mapper.Map<RepairStockProductItemResponse>(result);
        return OkResponse(response);
    }

    [HttpGet("stock-product-items")]
    [Authorize(Policy = PolicyConstants.ViewProductCatalog)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(FormattedResponse<RepairStockProductItemResponse>))]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> GetRepairProductItemByLocationProductSerialNumberAsync(
        [FromQuery] Guid productId,
        [FromQuery] string serialNumber)
    {
        var result = await _mediator.Send(new GetRepairStockProductItemByProductSerialNumberQuery(productId, serialNumber));

        var response = _mapper.Map<RepairStockProductItemResponse>(result);
        return OkResponse(response);
    }

    [HttpGet("stock-products/{stockProductId:guid}")]
    [Authorize(Policy = PolicyConstants.ViewProductCatalog)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(FormattedResponse<RepairStockProductResponse>))]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> GetRepairProductAsync(Guid stockProductId)
    {
        var result = await _mediator.Send(new GetRepairStockProductQuery(stockProductId));

        var response = _mapper.Map<RepairStockProductResponse>(result);
        return OkResponse(response);
    }

    [HttpGet("products/{productId:guid}/locations/{locationId:guid}/available")]
    [ProducesResponseType(
        StatusCodes.Status200OK, Type = typeof(FormattedResponse<TablePageResult<RepairsAvailableStockProductItemsResponse>>))]
    public async Task<IActionResult> GetAvailableItemsAsync(
        [FromRoute] Guid productId, [FromRoute] Guid locationId, [FromQuery] GetRepairAvailableStockProductItemsRequest request)
    {
        var result = await _mediator.Send(new GetRepairAvailableStockProductItemsQuery
        {
            RepairOrderStockProductItemId = request.RepairOrderStockProductItemId,
            ProductId = productId,
            LocationId = locationId,
            SearchText = request.SearchText,
            Page = request.Page,
            PerPage = request.PerPage,
            OrderBy = request.OrderBy,
        });

        var response = _mapper.Map<TablePageResult<RepairsAvailableStockProductItemsResponse>>(result);
        return OkResponse(response);
    }
}
