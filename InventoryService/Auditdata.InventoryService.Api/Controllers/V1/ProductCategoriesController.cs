using Auditdata.InventoryService.Api.Constants;
using Auditdata.InventoryService.Contracts.Responses.Common;
using Auditdata.InventoryService.Core.Features.ProductCategories.Queries;

namespace Auditdata.InventoryService.Api.Controllers.V1;

[ApiController]
[ApiVersion("1.0")]
[Authorize]
[Route("tenants/{tenantId:guid}/product-categories")]
public class ProductCategoriesController : AuditdataController
{
    private readonly MediatR.IMediator _mediator;
    private readonly IMapper _mapper;

    public ProductCategoriesController(
        MediatR.IMediator mediator,
        IMapper mapper)
    {
        _mediator = mediator;
        _mapper = mapper;
    }

    [HttpGet]
    [Authorize(Policy = PolicyConstants.ViewProductCategory)]
    public async Task<IActionResult> GetAsync()
    {
        var productCategories = await _mediator.Send(new GetProductCategoriesQuery());

        return OkResponse(_mapper.Map<IEnumerable<ProductCategoryResponse>>(productCategories.ProductCategories));
    }
}
