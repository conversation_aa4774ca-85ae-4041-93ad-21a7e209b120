using Auditdata.InventoryService.Api.Constants;
using Auditdata.InventoryService.Contracts.Requests.Suppliers;
using Auditdata.InventoryService.Contracts.Responses.Suppliers;
using Auditdata.InventoryService.Core.Features.Suppliers.Commands;
using Auditdata.InventoryService.Core.Features.Suppliers.Queries;
using Auditdata.Transport.Contracts.Infrastructure;
using Auditdata.Transport.Contracts.PageResults;

namespace Auditdata.InventoryService.Api.Controllers.V1;

[ApiController]
[ApiVersion("1.0")]
[Authorize]
[Route("tenants/{tenantId:guid}/suppliers")]
public class SuppliersController : AuditdataController
{
    private readonly MediatR.IMediator _mediator;
    private readonly IMapper _mapper;

    public SuppliersController(
        MediatR.IMediator mediator,
        IMapper mapper)
    {
        _mediator = mediator;
        _mapper = mapper;
    }

    [HttpGet("{id:guid}")]
    [Authorize(Policy = PolicyConstants.ViewProductViewInventorySettings)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(FormattedResponse<SupplierResponse>))]
    public async Task<IActionResult> GetByIdAsync([FromRoute] Guid id)
    {
        var result = await _mediator.Send(new GetSupplierByIdQuery(id));

        var response = _mapper.Map<SupplierResponse>(result.Supplier);

        return OkResponse(response);
    }

    [HttpGet]
    [Authorize(Policy = PolicyConstants.ViewProductViewInventorySettings)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(FormattedResponse<TablePageResult<SupplierResponse>>))]
    public async Task<IActionResult> GetAsync([FromQuery] SearchSuppliersRequest request)
    {
        var query = _mapper.Map<GetSuppliersQuery>(request);
        var result = await _mediator.Send(query);
        var response = _mapper.Map<TablePageResult<SupplierResponse>>(result.Suppliers);

        foreach (var row in response.Rows!)
        {
            row.IsActive = row.Model!.IsActive;
        }

        return OkResponse(response);
    }

    [HttpGet("active")]
    [Authorize(Policy = PolicyConstants.ViewProductViewInventorySettings)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(FormattedResponse<IEnumerable<SupplierResponse>>))]
    public async Task<IActionResult> GetActiveAsync(string? search)
    {
        var result = await _mediator.Send(new GetActiveSuppliersQuery(search));
        return OkResponse(_mapper.Map<IEnumerable<SupplierResponse>>(result.Suppliers));
    }

    [HttpPost]
    [Authorize(Policy = PolicyConstants.ViewEditInventorySettings)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(FormattedResponse<object>))]
    public async Task<IActionResult> CreateAsync(SupplierRequest request)
    {
        var command = _mapper.Map<CreateSupplierCommand>(request);
        var result = await _mediator.Send(command);

        return OkResponse(new CreatedSupplierResponse(result.Id));
    }

    [HttpPut("{id:guid}")]
    [Authorize(Policy = PolicyConstants.ViewEditInventorySettings)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(FormattedResponse<object>))]
    public async Task<IActionResult> UpdateAsync([FromRoute] Guid id, [FromBody] SupplierRequest request)
    {
        var command = _mapper.Map<UpdateSupplierCommand>(request);
        command.Id = id;

        await _mediator.Send(command);

        return OkResponse();
    }

    [HttpDelete("{id:guid}")]
    [Authorize(Policy = PolicyConstants.ViewEditInventorySettings)]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    public async Task<IActionResult> DeleteAsync([FromRoute] Guid id)
    {
        await _mediator.Send(new DeleteSupplierCommand(id));
        return NoContent();
    }
}
