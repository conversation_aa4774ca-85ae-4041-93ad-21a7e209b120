using Auditdata.InventoryService.Api.Constants;
using Auditdata.InventoryService.Contracts.Requests.ProductCategoryAccountCodes;
using Auditdata.InventoryService.Contracts.Responses;
using Auditdata.InventoryService.Core.Features.ProductCategoryAccountCodes.Commands;
using Auditdata.InventoryService.Core.Features.ProductCategoryAccountCodes.Queries;
using Auditdata.Transport.Contracts.Infrastructure;
using Auditdata.Transport.Contracts.PageResults;
using Auditdata.Transport.Contracts.Table;
using MediatR;

namespace Auditdata.InventoryService.Api.Controllers.V1;

[ApiController]
[ApiVersion("1.0")]
[Authorize]
[Route("tenants/{tenantId:guid}/product-category-account-codes")]
public class ProductCategoryAccountCodesController : AuditdataController
{
    private readonly IMediator _mediator;
    private readonly IMapper _mapper;

    public ProductCategoryAccountCodesController(IMediator mediator, IMapper mapper)
    {
        _mediator = mediator;
        _mapper = mapper;
    }

    [HttpGet]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(FormattedResponse<List<GetProductCategoryAccountCodesResponse>>))]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    [Authorize(Policy = PolicyConstants.ViewInventorySettings)]
    public async Task<IActionResult> GetAsync(CancellationToken cancellationToken)
    {
        var result = await _mediator.Send(new GetProductCategoryAccountCodesQuery(), cancellationToken);
        return OkResponse(_mapper.Map<List<GetProductCategoryAccountCodesResponse>>(result));
    }

    [HttpGet("search")]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(FormattedResponse<TablePageResult<GetProductCategoryAccountCodesResponse>>))]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    [Authorize(Policy = PolicyConstants.ViewInventorySettings)]
    public async Task<IActionResult> SearchAsync([FromQuery] TableQueryBase tableQuery, [FromQuery] string? name)
    {
        if (string.IsNullOrEmpty(tableQuery.OrderBy))
        {
            tableQuery.OrderBy = "Name";
        }

        var result = await _mediator.Send(new SearchProductCategoryAccountCodesQuery(tableQuery, name));

        var response = _mapper.Map<TablePageResult<GetProductCategoryAccountCodesResponse>>(result.ProductCategyAccountCodes);

        return OkResponse(response);
    }

    [HttpPut]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [Authorize(Policy = PolicyConstants.ViewEditInventorySettings)]
    public async Task<IActionResult> PutAsync(
        CreateProductCategoryAccountCodeRequest request,
        CancellationToken cancellationToken)
    {
        var command = _mapper.Map<UpdateProductCategoryAccountCodeCommand>(request);
        await _mediator.Send(command, cancellationToken);
        return OkResponse();
    }
}
