using System.Text;
using Auditdata.Infrastructure.Contracts.OperationContext;
using Auditdata.Infrastructure.Core.OperationContext;
using Auditdata.InventoryService.Core.OperationContext;
using IdentityService.Contracts.Roles;

namespace Auditdata.InventoryService.Api.Extensions;

public class CustomOperationContextExtension<TOperationContext> : HttpOperationContextBuilder<TOperationContext>
    where TOperationContext : InventoryServiceOperationContext, IOperationContext, new()
{
    private const string PermissionClaim = "permission_bit";

    protected override Task SetProperties(HttpContext context)
    {
        if (context.User.Identity!.IsAuthenticated)
        {
            var hasViewProductCostPermission = true;
            var hasEditProductCostPermission = true;
            var hasAuditRequestSpecialistPermission = true;
            var hasAuditRequestManagerPermission = true;
            var permissionBase64 = context.User.Claims.FirstOrDefault(c => c.Type == PermissionClaim)?.Value;

            if (permissionBase64 is not null)
            {
                var encodedBytes = Convert.FromBase64String(permissionBase64);
                var permissionBitString = Encoding.UTF8.GetString(encodedBytes);

                if (permissionBitString[(int)PermissionEnum.ViewProductCost] == '0')
                {
                    hasViewProductCostPermission = false;
                }

                if (permissionBitString[(int)PermissionEnum.EditProductCost] == '0')
                {
                    hasEditProductCostPermission = false;
                }

                if (permissionBitString[(int)PermissionEnum.UpdateAuditRequest] == '0')
                {
                    hasAuditRequestSpecialistPermission = false;
                }

                if (permissionBitString[(int)PermissionEnum.EditAuditRequest] == '0')
                {
                    hasAuditRequestManagerPermission = false;
                }
            }
            else
            {
                hasViewProductCostPermission = false;
                hasEditProductCostPermission = false;
            }

            operationContext.UserCanViewProductCost = hasViewProductCostPermission || operationContext.IsGlobalAdmin;
            operationContext.UserCanEditProductCost = hasEditProductCostPermission || operationContext.IsGlobalAdmin;
            operationContext.UserAuditRequestSpecialist = hasAuditRequestSpecialistPermission || operationContext.IsGlobalAdmin;
            operationContext.UserAuditRequestManager = hasAuditRequestManagerPermission || operationContext.IsGlobalAdmin;
            return Task.CompletedTask;
        }

        operationContext.UserCanViewProductCost = false;
        operationContext.UserCanEditProductCost = false;
        operationContext.UserAuditRequestSpecialist = false;
        operationContext.UserAuditRequestManager = false;
        return Task.CompletedTask;
    }
}
