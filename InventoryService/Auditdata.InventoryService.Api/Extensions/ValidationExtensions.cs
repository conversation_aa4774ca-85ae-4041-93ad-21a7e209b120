using Microsoft.AspNetCore.Mvc.ModelBinding;

namespace Auditdata.InventoryService.Api.Extensions;

public static class ValidationExtensions
{
    public static IReadOnlyCollection<string> GetErrors(this ModelStateDictionary.ValueEnumerable valueEnumerable)
    {
        var result = new List<string>();
        foreach (var error in valueEnumerable.Select(x => x.Errors))
        {
            result.AddRange(error.Select(x => x.ErrorMessage));
        }

        return result;
    }
}
