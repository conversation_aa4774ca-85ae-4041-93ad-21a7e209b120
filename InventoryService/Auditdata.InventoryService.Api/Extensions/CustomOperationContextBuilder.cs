using Auditdata.InventoryService.Core.OperationContext;

namespace Auditdata.InventoryService.Api.Extensions;

public static class CustomOperationContextBuilder
{
    public static IOperationContextConfigurator<InventoryServiceOperationContext> AddCustomHttpOperationContextBuilder(
        this IOperationContextConfigurator<InventoryServiceOperationContext> builder)
    {
        builder.AddHttpOperationContextBuilder<CustomOperationContextExtension<InventoryServiceOperationContext>, InventoryServiceOperationContext>();
        return builder;
    }
}
