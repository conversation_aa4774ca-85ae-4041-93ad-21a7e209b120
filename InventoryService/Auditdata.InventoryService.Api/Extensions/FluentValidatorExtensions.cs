namespace Auditdata.InventoryService.Api.Extensions;

public static class FluentValidatorExtensions
{
    public static void MustBeValidEmailAddress<T>(this IRuleBuilder<T, string> ruleBuilder)
    {
        ruleBuilder
            .Must(emailAddress => emailAddress.Contains('@')).WithMessage("The email address is missing the @ symbol")
            .Must(emailAddress => emailAddress.Contains('.')).WithMessage("Missing dot (.) in the email address")
            .EmailAddress().WithMessage("Invalid Email Address");
    }
}
