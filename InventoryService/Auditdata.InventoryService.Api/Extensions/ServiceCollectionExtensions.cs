using Auditdata.AspNet.Core.AuthorizationHandling;
using Auditdata.AspNet.Core.Extensions;
using Auditdata.AspNet.Core.Extensions.Swagger;
using Auditdata.AspNet.Core.HealthCheck;
using Auditdata.AuditTrail.Sdk.Configuration;
using Auditdata.AuditTrail.Sdk.Contracts;
using Auditdata.AuditTrail.Sdk.Tags;
using Auditdata.Infrastructure.Contracts.CountryLayers;
using Auditdata.Infrastructure.Core.JsonConverter;
using Auditdata.Infrastructure.Core.OperationContext;
using Auditdata.Infrastructure.DataPopulation;
using Auditdata.Infrastructure.DataPopulation.Consumers;
using Auditdata.Infrastructure.DataPopulation.Extensions;
using Auditdata.Infrastructure.EntityFrameworkCore.Interceptors;
using Auditdata.Infrastructure.Excel.Abstractions;
using Auditdata.Infrastructure.Excel.Services;
using Auditdata.InventoryService.Api.Audits;
using Auditdata.InventoryService.Api.Authorization;
using Auditdata.InventoryService.Api.Constants;
using Auditdata.InventoryService.Api.Consumers;
using Auditdata.InventoryService.Api.Consumers.RepairOrders;
using Auditdata.InventoryService.Api.Filters;
using Auditdata.InventoryService.Contracts.Commands;
using Auditdata.InventoryService.Contracts.Requests.BatteryTypes;
using Auditdata.InventoryService.Contracts.Requests.Colors;
using Auditdata.InventoryService.Contracts.Requests.CPTCodes;
using Auditdata.InventoryService.Contracts.Requests.Products;
using Auditdata.InventoryService.Core;
using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Abstractions.Azure;
using Auditdata.InventoryService.Core.Abstractions.Clients;
using Auditdata.InventoryService.Core.Abstractions.Repositories;
using Auditdata.InventoryService.Core.Abstractions.Services;
using Auditdata.InventoryService.Core.Abstractions.Validators;
using Auditdata.InventoryService.Core.Clients;
using Auditdata.InventoryService.Core.Configuration;
using Auditdata.InventoryService.Core.Entities;
using Auditdata.InventoryService.Core.Entities.Products;
using Auditdata.InventoryService.Core.Features.Attributes.Validators;
using Auditdata.InventoryService.Core.Features.AuditRequests.Services;
using Auditdata.InventoryService.Core.Features.Bundles.Validation;
using Auditdata.InventoryService.Core.Features.CPTCodes.Validators;
using Auditdata.InventoryService.Core.Features.CPTCodes.Validators.Interfaces;
using Auditdata.InventoryService.Core.Features.Manufacturers.Validators;
using Auditdata.InventoryService.Core.Features.Products.Services;
using Auditdata.InventoryService.Core.Features.Products.Validation;
using Auditdata.InventoryService.Core.Features.ProductsExcelImport.Services;
using Auditdata.InventoryService.Core.Features.ProductsExcelImport.Services.Interfaces;
using Auditdata.InventoryService.Core.Features.ProductsExcelImport.Validation;
using Auditdata.InventoryService.Core.Features.ProductsExcelImport.Validation.Interfaces;
using Auditdata.InventoryService.Core.Features.ProductsImport.ImportStrategies;
using Auditdata.InventoryService.Core.Features.Skus.Validation;
using Auditdata.InventoryService.Core.Features.StockKeepingUnits.Validation;
using Auditdata.InventoryService.Core.Features.StockKeepingUnits.Validation.Interfaces;
using Auditdata.InventoryService.Core.Features.StockProductItems.Validators;
using Auditdata.InventoryService.Core.Features.StockTransactions;
using Auditdata.InventoryService.Core.Features.Suppliers.Validators;
using Auditdata.InventoryService.Core.OperationContext;
using Auditdata.InventoryService.Core.Services;
using Auditdata.InventoryService.Dictionaries;
using Auditdata.InventoryService.Dictionaries.BatteryTypes;
using Auditdata.InventoryService.Dictionaries.Colors;
using Auditdata.InventoryService.Dictionaries.CPTCodes;
using Auditdata.InventoryService.Infrastructure.Azure;
using Auditdata.InventoryService.Infrastructure.Events;
using Auditdata.InventoryService.Infrastructure.Persistence;
using Auditdata.InventoryService.Infrastructure.Repositories;
using Auditdata.InventoryService.Infrastructure.Sagas.Transfers;
using Auditdata.Microservice.Messages.Commands.Inventory;
using Auditdata.Microservice.Messages.Events.DataPopulation;
using Auditdata.Microservice.Messages.Events.Inventory.AutomaticOrders;
using Auditdata.Microservice.Messages.Events.Invoicing;
using Auditdata.Microservice.Messages.Events.Location;
using Auditdata.Microservice.Messages.Events.State;
using Auditdata.Microservice.Messages.RequestReply.Inventory;
using Auditdata.Microservice.Messages.RequestReply.Inventory.DeliverSoldProducts;
using Auditdata.Microservice.Messages.RequestReply.Inventory.Order;
using Auditdata.Microservice.Messages.RequestReply.Inventory.ReserveProduct;
using Auditdata.Microservice.Messages.RequestReply.Inventory.SwapReservation;
using Auditdata.Microservice.Messages.RequestReply.RepairOrder.StockProductItem.Requests;
using Azure.Identity;
using Azure.Monitor.OpenTelemetry.Exporter;
using Azure.Storage.Blobs;
using IdentityService.Contracts.Roles;
using MassTransit.Logging;
using MassTransit.Monitoring;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Morcatko.AspNetCore.JsonMergePatch;
using OpenTelemetry.Resources;
using Attribute = Auditdata.InventoryService.Core.Entities.Attribute;

namespace Auditdata.InventoryService.Api.Extensions;

public static class ServiceCollectionExtensions
{
    private static string _identityServiceEndpoint = null!;

    public static void ConfigureServices(this IServiceCollection services, IConfiguration configuration)
    {
        _identityServiceEndpoint = configuration.GetValue<string>("IdentityServiceEndpoint") !;
        services.AddDictionariesPopulation();
        services.AddApplicationControllers();
        services.AddApplicationSwaggerGen();
        services.AddApplicationAuthentication();
        services.AddApplicationAuthorization();
        services.AddDefaultOperationContext();
        services.AddAppInsights(configuration);
        services.AddApplicationAuditTrail();
        services.AddAuditdataHealthChecks(configuration);
        services.AddCors(p => p.AddDefaultPolicy(builder =>
        {
            var allowedOrigins = configuration["CORS:AllowedOrigins"]?.Split(";");
            if (allowedOrigins is null)
            {
                builder.AllowAnyOrigin().AllowAnyMethod().AllowAnyHeader();
                return;
            }

            builder.WithOrigins(allowedOrigins).AllowAnyMethod().AllowAnyHeader();
        }));

        services.AddAutoMapper(typeof(IApiAssemblyMarker), typeof(ICoreAssemblyMarker), typeof(DictionaryMappingProfile));
        services.AddMediatR(cfg => cfg.RegisterServicesFromAssemblyContaining<ICoreAssemblyMarker>());
        services.AddValidatorsFromAssemblyContaining<IApiAssemblyMarker>();
        services.AddValidatorsFromAssemblyContaining<ICoreAssemblyMarker>();
        services.AddScoped<IProductUniqueNameValidator, ProductUniqueNameValidator>();
        services.AddScoped<IProductSkusValidator, ProductSkusValidator>();

        services.AddScoped<TenancyDataModelInterceptor>();
        services.AddScoped<SoftDeleteDataModelInterceptor>();
        services.AddScoped<AuditableDataModelInterceptor>();
        services.AddScoped<AuditablePersonalizedDataModelInterceptor>();
        services.AddDbContext<IDbContext, InventoryDbContext>((serviceProvider, options) =>
        {
            var tenantIdInterceptor = serviceProvider.GetRequiredService<TenancyDataModelInterceptor>();
            var softDeleteInterceptor = serviceProvider.GetRequiredService<SoftDeleteDataModelInterceptor>();
            var auditableInterceptor = serviceProvider.GetRequiredService<AuditableDataModelInterceptor>();
            var auditablePersonalizedInterceptor = serviceProvider.GetRequiredService<AuditablePersonalizedDataModelInterceptor>();
            options.UseSqlServer(
                    configuration.GetConnectionString("AzureSql"),
                    sql => sql.MigrationsAssembly("Auditdata.InventoryService.Infrastructure"))
                .AddInterceptors(
                    tenantIdInterceptor,
                    softDeleteInterceptor,
                    auditableInterceptor,
                    auditablePersonalizedInterceptor)
                .UseAuditInterceptor(serviceProvider);
        });

        services.AddMassTransit(bus =>
        {
            bus.AddEntityFrameworkOutbox<InventoryDbContext>(
                o =>
                {
                    o.UseSqlServer();
                    o.QueryDelay = TimeSpan.FromSeconds(1);
                    o.DuplicateDetectionWindow = TimeSpan.FromSeconds(30);
                    o.UseBusOutbox();
                });

            bus.SetKebabCaseEndpointNameFormatter();
            bus.AddConsumersFromNamespaceContaining<IApiAssemblyMarker>();
            bus.AddConsumersFromNamespaceContaining<IDataPopulationAssemblyMarker>();

            var topologyPrefix = configuration["AzureServiceBus:TopologyPrefix"];

            bus.AddSagaStateMachine<TransfersStateMachine, TransfersState>()
                .EntityFrameworkRepository(r =>
                {
                    r.ConcurrencyMode = ConcurrencyMode.Optimistic;
                    r.ExistingDbContext<InventoryDbContext>();
                })
                .Endpoint(x =>
                {
                    if (!string.IsNullOrEmpty(topologyPrefix))
                    {
                        x.Name = topologyPrefix + "inventory-transfers-state";
                    }
                });

            bus.UsingAzureServiceBus((context, cfg) =>
            {
                cfg.Host(new Uri(configuration["AzureServiceBus:Endpoint"]), h =>
                {
                    h.TokenCredential = new DefaultAzureCredential();
                });
                cfg.UseMassTransitOperationContext(context);

                if (!string.IsNullOrEmpty(topologyPrefix))
                {
                    cfg.MessageTopology.SetEntityNameFormatter(
                        new PrefixEntityNameFormatter(cfg.MessageTopology.EntityNameFormatter, topologyPrefix));
                }

                cfg.UseMessageRetry(retryCfg =>
                {
                    retryCfg.Handle<DbUpdateConcurrencyException>();
                    retryCfg.Interval(2, TimeSpan.FromMilliseconds(500));
                });

                cfg.SubscriptionEndpoint<ProductsSold>("inventory-product-sold", x => x.ConfigureConsumer<ProductSoldConsumer>(context));
                cfg.SubscriptionEndpoint<ProductsReturned>("inventory-product-returned", x => x.ConfigureConsumer<ProductReturnedConsumer>(context));
                cfg.SubscriptionEndpoint<LocationCreated>(
                    "inventory-service", x => x.ConfigureConsumer<LocationCreatedConsumer>(
                        context,
                        cfg => cfg.UseMessageRetry(cfg2 => cfg2.Intervals(5000, 10000, 15000))));
                cfg.SubscriptionEndpoint<LocationUpdated>(
                    "inventory-service",
                    x => x.ConfigureConsumer<LocationUpdatedConsumer>(
                        context,
                        cfg => cfg.UseMessageRetry(cfg2 => cfg2.Intervals(5000, 10000, 15000))));
                cfg.SubscriptionEndpoint<ReserveProductRequest>("inventory-service", x => x.ConfigureConsumer<ReserveProductRequestConsumer>(context));
                cfg.SubscriptionEndpoint<SwapReservationRequest>("inventory-service", x => x.ConfigureConsumer<SwapReservationRequestConsumer>(context));
                cfg.SubscriptionEndpoint<AddStockTransactionCommand>("inventory-service", x => x.ConfigureConsumer<AddTransactionConsumer>(context));
                cfg.SubscriptionEndpoint<AddProductToAllStocksCommand>("inventory-service", x => x.ConfigureConsumer<AddProductToAllStocksConsumer>(context));
                cfg.SubscriptionEndpoint<AssignSaleProductSerial>("inventory-service", x => x.ConfigureConsumer<AssignSaleProductSerialConsumer>(context));
                cfg.SubscriptionEndpoint<ReserveProductItemsFromOrderRequest>("inventory-service", x => x.ConfigureConsumer<ReserveProductItemsFromOrderConsumer>(context));
                cfg.SubscriptionEndpoint<DeliverSoldProductsRequest>("inventory-service", x => x.ConfigureConsumer<DeliverSoldProductsRequestConsumer>(context));
                cfg.SubscriptionEndpoint<ReplaceSaleProductCommand>("inventory-service", x =>
                {
                    x.UseEntityFrameworkOutbox<InventoryDbContext>(context);
                    x.ConfigureConsumer<ReplaceSaleProductCommandConsumer>(context);
                });

                cfg.SubscriptionEndpoint<MarkStockProductItemUnderRepairRequest>("inventory-service", x =>
                {
                    x.UseEntityFrameworkOutbox<InventoryDbContext>(context);
                    x.ConfigureConsumer<MarkStockProductItemUnderRepairConsumer>(context);
                });
                cfg.SubscriptionEndpoint<CancelStockProductItemUnderRepairRequest>("inventory-service", x => x.ConfigureConsumer<CancelStockProductItemUnderRepairConsumer>(context));
                cfg.SubscriptionEndpoint<ReplaceStockProductItemOnRepairRequest>("inventory-service", x =>
                {
                    x.UseEntityFrameworkOutbox<InventoryDbContext>(context);
                    x.ConfigureConsumer<ReplaceStockProductItemOnRepairConsumer>(context);
                });
                cfg.SubscriptionEndpoint<CompleteStockProductItemUnderRepairRequest>("inventory-service", x =>
                {
                    x.UseEntityFrameworkOutbox<InventoryDbContext>(context);
                    x.ConfigureConsumer<CompleteStockProductItemUnderRepairConsumer>(context);
                });
                cfg.SubscriptionEndpoint<UnReserveProductCommand>("inventory-service", x => x.ConfigureConsumer<UnReserveProductConsumer>(context));
                cfg.SubscriptionEndpoint<ExchangeProductsRequest>("inventory-service", x => x.ConfigureConsumer<ExchangeProductsConsumer>(context));
                cfg.SubscriptionEndpoint<GetProductInfoForPrintingRequest>("inventory-service", x => x.ConfigureConsumer<GetProductInfoForPrintingConsumer>(context));
                cfg.SubscriptionEndpoint<GetProductForPrintingRequest>("inventory-service", x => x.ConfigureConsumer<GetProductForPrintingConsumer>(context));
                cfg.SubscriptionEndpoint<DataPopulationRequested>("inventory-service", x => x.ConfigureConsumer<DataPopulationRequestedConsumer>(context));
                cfg.SubscriptionEndpoint<ProductTrialStarted>("inventory-service", x =>
                {
                    x.UseEntityFrameworkOutbox<InventoryDbContext>(context);
                    x.ConfigureConsumer<ProductTrialStartedConsumer>(context);
                });
                cfg.SubscriptionEndpoint<ProductTrialCancelled>("inventory-service", x =>
                {
                    x.UseEntityFrameworkOutbox<InventoryDbContext>(context);
                    x.ConfigureConsumer<ProductTrialCancelledConsumer>(context);
                });
                cfg.SubscriptionEndpoint<ProductTrialCompleted>("inventory-service", x =>
                {
                    x.UseEntityFrameworkOutbox<InventoryDbContext>(context);
                    x.ConfigureConsumer<ProductTrialCompletedConsumer>(context);
                });
                cfg.SubscriptionEndpoint<StateCreated>("inventory-service", x => x.ConfigureConsumer<StateCreatedConsumer>(context));
                cfg.SubscriptionEndpoint<StateUpdated>("inventory-service", x => x.ConfigureConsumer<StateUpdatedConsumer>(context));
                cfg.SubscriptionEndpoint<StateDeleted>("inventory-service", x => x.ConfigureConsumer<StateDeletedConsumer>(context));

                cfg.SubscriptionEndpoint<ValidateAutomaticOrderProductsRequested>("inventory-service", x => x.ConfigureConsumer<ValidateAutomaticOrderProductsRequestedConsumer>(context));

                cfg.SubscriptionEndpoint<CreateAuditRequestReadyForCalculationCommand>("inventory-service", x => x.ConfigureConsumer<CreateAuditRequestReadyForCalculationConsumer>(context));

                cfg.ConfigureEndpoints(context);
            });
        });

        services.AddMassTransitTracing(configuration);

        services.AddAzure(configuration);
        services.AddServiceBusClients();
        services.AddScoped<ITransactionNumberBuilder, TransactionNumberBuilder>();
        services.AddRepositories();
        services.AddValidators();
        services.AddServices();

        services.AddSingleton<IDateTimeService, DateTimeService>();
        services.AddSingleton<IHashingService, HashingService>();
        services.AddSingleton<ISerialNumbersService, SerialNumbersService>();

        services.AddScoped<IEventPublisher, EventPublisher>();

        services.AddScoped<IExcelExporter, ExcelExporter>();
        services.AddScoped<IExcelImporter, ExcelImporter>();
        services.AddScoped<IProductEventPublisher, ProductEventPublisher>();
        services.AddScoped<IProductEntitiesProvider, ProductEntitiesProvider>();
        services.AddProductImport(configuration);
        services.AddDictionaries();
    }

    private static void AddApplicationControllers(this IServiceCollection services)
    {
        var productsSubtypeConverter = JsonSubTypeConverterBuilder<ProductRequest>
            .Create(new OperationContextAccessor())
            .WithDerived(CountryEnum.UnitedKingdom, typeof(UKProductRequest))
            .WithDerived(CountryEnum.Ireland, typeof(ROIProductRequest))
            .WithDerived(CountryEnum.Australia, typeof(AUProductRequest))
            .WithDerived(CountryEnum.NewZealand, typeof(NZProductRequest))
            .WithDerived(CountryEnum.UnitedStates, typeof(USAProductRequest))
            .Build();

        services.AddApiVersioning(options =>
        {
            options.ReportApiVersions = true;
            options.AssumeDefaultVersionWhenUnspecified = true;
            options.DefaultApiVersion = new ApiVersion(1, 0);
        });

        services.AddVersionedApiExplorer(options =>
        {
            options.GroupNameFormat = "'v'VVV";
            options.SubstituteApiVersionInUrl = true;
        });

        services.Configure<RouteOptions>(options =>
        {
            options.LowercaseUrls = true;
        });

        services.AddControllers(o =>
            {
                o.UseGeneralRoutePrefix("api/v{version:apiVersion}");
                o.Filters.Add<AuditdataFluentValidationActionFilter>();
            })
            .AddDictionaryControllers()
            .ConfigureApiBehaviorOptions(options =>
            {
                options.SuppressModelStateInvalidFilter = true;
            })
            .AddJsonOptions(o => o.JsonSerializerOptions.Converters.Add(productsSubtypeConverter))
            .AddNewtonsoftJsonMergePatch(cfg =>
            {
                cfg.EnableDelete = true;
            });
    }

    private static void AddDictionariesPopulation(this IServiceCollection serviceCollection)
    {
        serviceCollection.AddScoped<CountryPropertySetter>();
        serviceCollection.AddScoped<BatteryTypesAfterPopulationHandler>();
        serviceCollection.AddScoped<ManufacturersAfterPopulationHandler>();
        serviceCollection.AddScoped<SuppliersAfterPopulationHandler>();

        serviceCollection.AddDataPopulation<InventoryDbContext>(
            "InventoryService",
            cfg =>
            {
                cfg.AddDictionary(nameof(InventoryDbContext.ProductCategories));
                cfg.AddDictionary(nameof(InventoryDbContext.BatteryTypes), dictCfg =>
                {
                    dictCfg.AddAfterPopulationHandler<BatteryTypesAfterPopulationHandler>();
                });
                cfg.AddDictionary(nameof(InventoryDbContext.HearingAidTypes));
                cfg.AddDictionary(nameof(InventoryDbContext.Countries));
                cfg.AddDictionary(nameof(InventoryDbContext.Manufacturers), dictCfg =>
                {
                    dictCfg.AddCustomPropertySetter<CountryPropertySetter>(nameof(Manufacturer.Country));
                    dictCfg.AddAfterPopulationHandler<ManufacturersAfterPopulationHandler>();
                });
                cfg.AddDictionary(nameof(InventoryDbContext.Suppliers), dictCfg =>
                {
                    dictCfg.AddCustomPropertySetter<CountryPropertySetter>(nameof(Supplier.Country));
                    dictCfg.AddAfterPopulationHandler<SuppliersAfterPopulationHandler>();
                });
                cfg.AddDictionary(nameof(InventoryDbContext.CPTCodes));
            });
    }

    private static void AddRepositories(this IServiceCollection services)
    {
        services.AddScoped<IProductsRepository, ProductsRepository>();
        services.AddScoped<IProductCategoriesRepository, ProductCategoriesRepository>();
        services.AddScoped<IStockProductsRepository, StockProductsRepository>();
        services.AddScoped<IStockTransactionsRepository, StockTransactionsRepository>();
        services.AddScoped<IStockProductItemsRepository, StockProductItemsRepository>();
        services.AddScoped<IHearingAidTypesRepository, HearingAidTypesRepository>();
        services.AddScoped<IStockAdjustmentReasonsRepository, StockAdjustmentReasonsRepository>();
        services.AddScoped<IProductCategoryAccountCodesRepository, ProductCategoryAccountCodesRepository>();
    }

    private static void AddValidators(this IServiceCollection services)
    {
        services.AddScoped<ISerialNumbersValidator, SerialNumbersValidator>();
        services.AddScoped<IUniqueNameValidator, UniqueNameValidator>();
        services.AddScoped<IImportCPTCodesCoreValidator, ImportCPTCodesCoreValidator>();
        services.AddScoped<IImportProductsCoreValidator, ImportProductsCoreValidator>();
        services.AddScoped<IImportSkusCoreValidator, ImportSkusCoreValidator>();
        services.AddScoped<IManufacturerValidator, ManufacturerValidator>();
        services.AddScoped<ISupplierValidator, SupplierValidator>();
        services.AddScoped<IAttributesValidator, AttributesValidator>();
        services.AddScoped<ISkuValidator, SkuValidator>();
    }

    private static void AddServices(this IServiceCollection services)
    {
        services.AddScoped<IAuditRequestService, AuditRequestService>();
    }

    private static void AddApplicationSwaggerGen(this IServiceCollection serviceCollection)
    {
        serviceCollection.AddSwaggerGen(options =>
        {
            options.ConfigureAuthentication(_identityServiceEndpoint, "api");
            options.UseOneOfForPolymorphism();
            options.SelectSubTypesUsing(GetTypes);

            options.DictionaryFilter();
            options.OperationFilter<JsonMergePatchDocumentOperationFilter>();
        });
    }

    private static void AddApplicationAuthentication(this IServiceCollection serviceCollection)
    {
        serviceCollection.AddAuthentication(
            options =>
            {
                options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
                options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
            })
            .AddJwtBearer(
            options =>
            {
                options.Audience = "api";
                options.RequireHttpsMetadata = false;
                options.Authority = _identityServiceEndpoint;
            });
    }

    private static void AddApplicationAuthorization(this IServiceCollection serviceCollection)
    {
        serviceCollection.AddSingleton<IAuthorizationMiddlewareResultHandler, ForbiddenResultHandler>();
        serviceCollection.AddAuthorization(options =>
        {
            options.AddPolicy("ApiScope", policy =>
            {
                policy.RequireAuthenticatedUser();
            });

            options.AddPolicyByPermission(PolicyConstants.ViewInventorySettings, PermissionEnum.ViewInventorySettings);
            options.AddPolicyByPermission(PolicyConstants.ViewEditInventorySettings, PermissionEnum.ViewInventorySettings, PermissionEnum.EditInventorySettings);
            options.AddPolicyByPermission(PolicyConstants.ViewProductCatalog, PermissionEnum.ViewProductCatalog);
            options.AddPolicyByPermission(PolicyConstants.ViewEditProductCatalog, PermissionEnum.ViewProductCatalog, PermissionEnum.EditProductCatalog);
            options.AddPolicyByPermission(PolicyConstants.ViewStock, PermissionEnum.ViewStock);
            options.AddPolicyByPermission(PolicyConstants.ViewEditStock, PermissionEnum.ViewStock, PermissionEnum.EditStock);
            options.AddPolicyByPermission(PolicyConstants.ViewEditNhsConfiguration, PermissionEnum.ViewGPConfiguration, PermissionEnum.EditGPConfiguration);
            options.AddPolicyByPermission(PolicyConstants.EditProductCost, PermissionEnum.ViewProductCost, PermissionEnum.EditProductCost);
            options.AddPolicyByEitherPermission(PolicyConstants.ViewProductsViewStock, new[] { PermissionEnum.ViewProductCatalog, PermissionEnum.ViewStock });
            options.AddPolicyByEitherPermission(PolicyConstants.ViewProduct, new[] { PermissionEnum.ViewStock, PermissionEnum.ViewProductCatalog });
            options.AddPolicyByEitherPermission(PolicyConstants.ViewProductViewInventorySettings, new[] { PermissionEnum.ViewProductCatalog, PermissionEnum.ViewInventorySettings });
            options.AddPolicyByEitherPermission(PolicyConstants.ViewProductCategory, new[] { PermissionEnum.ViewProductCatalog, PermissionEnum.ViewStock, PermissionEnum.ViewFastTrack, PermissionEnum.ViewPatientSale });
            options.AddPolicyByEitherPermission(PolicyConstants.ViewProductCost, new[] { PermissionEnum.ViewProductCost });
            options.AddPolicyByPermission(PolicyConstants.ViewBundleCatalog, PermissionEnum.ViewBundleCatalog);
            options.AddPolicyByPermission(PolicyConstants.EditBundleCatalog, PermissionEnum.EditBundleCatalog);
            options.AddPolicyByPermission(PolicyConstants.ImportExportProductCatalog, PermissionEnum.ImportExportProductCatalog);
            options.AddPolicyByPermission(PolicyConstants.ViewCPTcodes, PermissionEnum.ViewCPTcodes);
            options.AddPolicyByPermission(PolicyConstants.EditCPTcodes, PermissionEnum.ViewCPTcodes, PermissionEnum.EditCPTcodes);
            options.AddPolicyByPermission(PolicyConstants.EditStockItem, PermissionEnum.EditStockItem);
            options.AddPolicyByPermission(PolicyConstants.CreateSKU, PermissionEnum.CreateSKU);

            options.AddPolicyByPermission(PolicyConstants.ViewAuditRequest, PermissionEnum.ViewAuditRequest);
            options.AddPolicyByPermission(PolicyConstants.UpdateAuditRequest, PermissionEnum.UpdateAuditRequest);
            options.AddPolicyByPermission(PolicyConstants.EditAuditRequest, PermissionEnum.EditAuditRequest);
            options.AddPolicyByEitherPermission(PolicyConstants.EditUpdateAuditRequest, new[] { PermissionEnum.UpdateAuditRequest, PermissionEnum.EditAuditRequest });

            options.AddViewDictionaryPolicy(opt =>
            {
                opt.RequireDictionaryAssertion<ColorsService>(x =>
                    x.RequireEitherPermission(new[] { PermissionEnum.ViewInventorySettings, PermissionEnum.ViewProductCatalog }));
                opt.RequireDictionaryAssertion<BatteryTypesService>(x =>
                    x.RequireEitherPermission(new[] { PermissionEnum.ViewInventorySettings, PermissionEnum.ViewProductCatalog }));
                opt.RequireDictionaryAssertion<CPTCodesService>(x =>
                    x.RequirePermissions(PermissionEnum.ViewCPTcodes));
            });
            options.AddEditDictionaryPolicy(opt =>
            {
                opt.RequireDictionaryAssertion<ColorsService>(x =>
                    x.RequirePermissions(PermissionEnum.EditInventorySettings));
                opt.RequireDictionaryAssertion<BatteryTypesService>(x =>
                    x.RequirePermissions(PermissionEnum.EditInventorySettings));
                opt.RequireDictionaryAssertion<CPTCodesService>(x =>
                    x.RequirePermissions(PermissionEnum.EditCPTcodes));
            });
        });

        serviceCollection.AddTransient<IAuthorizationMiddlewareResultHandler, ExtendedAuthorizationMiddlewareResultHandler>();
    }

    private static void AddApplicationAuditTrail(this IServiceCollection services)
    {
        services.AddAuditTrail(cfg =>
        {
            cfg.SetEntryType(AuditLogEntryType.InventoryService);
            cfg.RegisterTypeConfigurationWithIdTag<AUProduct, InventoryTagProvider>(x => x.Id, nameof(Product));
            cfg.RegisterTypeConfigurationWithIdTag<UKProduct, InventoryTagProvider>(x => x.Id, nameof(Product));
            cfg.RegisterTypeConfigurationWithIdTag<NZProduct, InventoryTagProvider>(x => x.Id, nameof(Product));
            cfg.RegisterTypeConfigurationWithIdTag<ROIProduct, InventoryTagProvider>(x => x.Id, nameof(Product));
            cfg.RegisterTypeConfigurationWithIdTag<StockProduct, InventoryTagProvider>(x => x.Id);
            cfg.RegisterTypeConfigurationWithIdTag<Supplier, InventoryTagProvider>(x => x.Id);
            cfg.RegisterTypeConfigurationWithIdTag<Manufacturer, InventoryTagProvider>(x => x.Id);
            cfg.RegisterTypeConfigurationWithIdTag<StockProductItem, InventoryTagProvider>(x => x.Id);
            cfg.RegisterTypeConfigurationWithIdTag<ProductCategoryAccountCode, InventoryTagProvider>(x => x.Id);
            cfg.RegisterTypeConfiguration<Color, CrudTagProvider>();
            cfg.RegisterTypeConfiguration<CPTCode, CrudTagProvider>();
            cfg.RegisterTypeConfiguration<BatteryType, CrudTagProvider>();
            cfg.RegisterTypeConfiguration<Bundle, CrudTagProvider>();
            cfg.RegisterTypeConfiguration<BundleProduct, CrudTagProvider>();
            cfg.RegisterTypeConfiguration<Attribute, CrudTagProvider>();
            cfg.RegisterTypeConfiguration<StockSetting, CrudTagProvider>();
        });
    }

    private static void AddAuditdataHealthChecks(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddHealthChecks()
            .AddCheck<AppConfigurationHealthCheck>("AppConfiguration", tags: new[] { HealthCheckTags.Startup })
            .AddSqlServer(configuration.GetConnectionString("AzureSql")!, failureStatus: HealthStatus.Unhealthy, tags: new[] { HealthCheckTags.Ready });
    }

    private static void AddDefaultOperationContext(this IServiceCollection services)
    {
        services.AddOperationContext<IInventoryServiceOperationContext, InventoryServiceOperationContext>()
            .AddCustomHttpOperationContextBuilder()
            .AddMassTransitOperationContextBuilder<InventoryServiceMassTransitOperationContext, InventoryServiceOperationContext>();
    }

    private static void AddAzure(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddScoped<IAzureServiceBusPublisher, AzureServiceBusPublisher>();
        services.AddSingleton(new BlobServiceClient(
            new Uri(configuration["AzureBlobStorageEndpoint"] !), new DefaultAzureCredential()));
        services.AddScoped<IAzureBlobStorageService, AzureBlobStorageService>();
    }

    private static void AddServiceBusClients(this IServiceCollection services)
    {
        services.AddScoped<IThirdPartyPayerClient, ThirdPartyPayerClient>();
    }

    private static IEnumerable<Type> GetTypes(Type type)
    {
        if (type == typeof(ProductRequest))
        {
            return new[]
            {
                typeof(UKProductRequest),
                typeof(AUProductRequest),
                typeof(ROIProductRequest),
                typeof(NZProductRequest),
                typeof(USAProductRequest)
            };
        }

        return Enumerable.Empty<Type>();
    }

    private static void AddProductImport(this IServiceCollection services, IConfiguration configuration)
    {
        services.Configure<ProductsImportSettings>(
            configuration.GetSection(ProductsImportSettings.ConfigurationName));
        services.AddScoped<IExternalProductImportStrategyFactory, ExternalProductImportStrategyFactory>();
        services.AddScoped<HimsaProductImportStrategy>();
    }

    private static void AddDictionaries(this IServiceCollection services)
    {
        services.AddDictionaryProvider()
            .AddAuditdataDictionary<ColorsService, ColorRequest, ColorsGetPagedQuery>()
            .AddAuditdataDictionary<BatteryTypesService, BatteryTypeRequest, BatteryTypesGetPagedQuery>()
            .AddAuditdataDictionary<CPTCodesService, CPTCodesRequest, CPTCodePagedQuery>();
    }
}

public static class OpenTelemetryExtensions
{
    private const string EnvironmentNameProperty = "EnvironmentName";

    ///<Summary>
    /// Register open telemetry and enable MassTransit tracing and metrics with support of app insights trace exporter.
    /// Require MassTransit:EnableTracing and ApplicationInsights:ConnectionString app settings.
    ///</Summary>
    /// <param name="applicationName">By default EnvironmentName environment variable will be used to populate ServiceName property. If environment variable is not preset, you can specify your own name by passing this variable.</param>
    public static void AddMassTransitTracing(this IServiceCollection services, IConfiguration configuration,
        string? applicationName = null)
    {
        var enableTracing = configuration.GetValue<bool>("MassTransit:EnableTracing");
        // if (!enableTracing) return;

        var connectionString = configuration["ApplicationInsights:ConnectionString"];
        ArgumentNullException.ThrowIfNull(
            connectionString,
            "ApplicationInsights:ConnectionString");

        services.AddOpenTelemetry()
            .ConfigureResource(
                x =>
                {
                    var envName = Environment.GetEnvironmentVariable(EnvironmentNameProperty);
                    var appName = string.IsNullOrEmpty(envName) ? applicationName : envName;

                    x.AddService(appName ?? Environment.MachineName, serviceInstanceId: Environment.MachineName);
                })
            .WithTracing(
                b => b
                    .AddSource(DiagnosticHeaders.DefaultListenerName) // MassTransit ActivitySource
                    .AddAzureMonitorTraceExporter(
                        o => { o.ConnectionString = connectionString; }))
            .WithMetrics(b => b
                .AddMeter(InstrumentationOptions.MeterName) // MassTransit Meter
                .AddAzureMonitorMetricExporter(o => { o.ConnectionString = connectionString; }));
    }
}
