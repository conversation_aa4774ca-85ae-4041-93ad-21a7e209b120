using Auditdata.InventoryService.Contracts.Requests.CPTCodes;
using Auditdata.InventoryService.Core.Features.CPTCodes.Commands;
using Auditdata.InventoryService.Core.Features.CPTCodes.Queries;

namespace Auditdata.InventoryService.Api.Mappings;

public class CPTCodesMapping : Profile
{
    public CPTCodesMapping()
    {
        CreateMap<ExportCPTCodesRequest, ExportCPTCodesQuery>();
        CreateMap<ImportCPTCodesRequest, ImportCPTCodesCommand>();
    }
}
