using Auditdata.InventoryService.Contracts.Requests.Orders;
using Auditdata.InventoryService.Contracts.Responses.Orders;
using Auditdata.InventoryService.Core.Entities;
using Auditdata.InventoryService.Core.Entities.Products;
using Auditdata.InventoryService.Core.Features.Orders.Queries;

namespace Auditdata.InventoryService.Api.Mappings;

public class OrdersMappings : Profile
{
    public OrdersMappings()
    {
        CreateMap<StockProduct, OrderProductResponse>()
            .ForMember(x => x.ProductName, opt => opt.MapFrom(y => y.Product.Name))
            .ForMember(x => x.UnitCost, opt => opt.MapFrom(y => y.Product.Cost))
            .ForMember(x => x.Category, opt => opt.MapFrom(y => y.Product.Category.Name))
            .ForMember(x => x.Manufacturer, opt => opt.MapFrom(y => y.Product.Manufacturer!.Name))
            .ForMember(x => x.Description, opt => opt.MapFrom(y => y.Product.DescriptionValue))
            .ForMember(x => x.BatteryTypes, opt => opt.MapFrom(y => y.Product.BatteryTypes))
            .ForMember(x => x.Colors, opt => opt.MapFrom(y => y.Product.Colors))
            .ForMember(x => x.Attributes, opt => opt.MapFrom(y => y.Product.Attributes));
        CreateMap<GetOrderProductsRequest, GetOrderProductsQuery>();
        CreateMap<Product, ProductsForPrintResponse>();
    }
}
