using Auditdata.InventoryService.Contracts.Responses;
using Auditdata.InventoryService.Contracts.Responses.ProductItems;
using Auditdata.InventoryService.Contracts.Responses.Stocks;
using Auditdata.InventoryService.Core.Entities;
using Auditdata.InventoryService.Core.Features.Stocks.Commands;
using Auditdata.Microservice.Messages.Events.Location;

namespace Auditdata.InventoryService.Api.Mappings;

public class StocksMapping : Profile
{
    public StocksMapping()
    {
        CreateMap<Stock, StockResponse>();
        CreateMap<StockProduct, StockProductResponse>();
        CreateMap<StockProductItem, StockProductItemResponse>()
            .ForMember(x => x.LocationId, opt => opt.MapFrom(y => y.StockProduct.Stock.LocationId))
            .ForMember(x => x.ProductId, opt => opt.MapFrom(y => y.StockProduct.ProductId))
            .ForMember(x => x.Stock, opt => opt.MapFrom(y => y.StockProduct.Stock.Name))
            .ForMember(x => x.CreatedOn, opt => opt.MapFrom(y => y.CreationDate))
            .ForMember(x => x.Color, opt => opt.MapFrom(y => y.Color!.Name))
            .ForMember(x => x.BatteryType, opt => opt.MapFrom(y => y.BatteryType!.Name));

        CreateMap<Stock, StocksResponse>();

        CreateMap<LocationCreated, CreateStockCommand>()
            .ForMember(x => x.LocationId, opt => opt.MapFrom(y => y.LocationID))
            .ForMember(x => x.RegionId, opt => opt.MapFrom(y => y.RegionID))
            .ForMember(x => x.ParentLocationId, opt => opt.MapFrom(y => y.ParentLocationID));
        CreateMap<LocationUpdated, UpdateStockCommand>()
            .ForMember(x => x.LocationId, opt => opt.MapFrom(y => y.LocationID))
            .ForMember(x => x.RegionId, opt => opt.MapFrom(y => y.RegionID))
            .ForMember(x => x.ParentLocationId, opt => opt.MapFrom(y => y.ParentLocationID));
    }
}
