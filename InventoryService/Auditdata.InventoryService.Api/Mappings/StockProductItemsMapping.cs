using Auditdata.InventoryService.Contracts.Requests.StockProductItems;
using Auditdata.InventoryService.Contracts.Responses.ProductItems;
using Auditdata.InventoryService.Core.Entities;
using Auditdata.InventoryService.Core.Features.StockProductItems.Commands;
using Auditdata.InventoryService.Core.Features.StockProductItems.Dtos;
using Auditdata.InventoryService.Core.Features.StockProductItems.Models;
using Auditdata.InventoryService.Core.Features.StockProductItems.SearchStockProductItems;
using Auditdata.Microservice.Messages.Commands.Inventory;
using Auditdata.Microservice.Messages.Events.Invoicing;
using Auditdata.Microservice.Messages.OriginEntities.Invoicing;
using Auditdata.Microservice.Messages.OriginEntities.Printing;

namespace Auditdata.InventoryService.Api.Mappings;

public class StockProductItemsMapping : Profile
{
    public StockProductItemsMapping()
    {
        CreateMap<ReplaceSaleProductCommand, ReplaceStockProductItemOnSaleCommand>()
            .ForMember(x => x.Attributes, opt => opt.MapFrom(y => y.Attributes
            .Select(z => new StockProductItemAttributeDto(z.Id, new AttributeValueDto(z.Value.Id, z.Value.Value)))));

        CreateMap<ReplaceSerialNumberRequest, ReplaceSerialNumberCommand>();
        CreateMap<GetGeneratedSerialNumbers, GeneratedSerialNumbersResponse>();
        CreateMap<CreateStockProductItemRequest, CreateStockProductItem>();
        CreateMap<CreateStockProductItemsRequest, CreateStockProductItemsCommand>();
        CreateMap<StockProductItem, GetProductItemStatusResponse>()
            .ForMember(x => x.StockProductItemId, opt => opt.MapFrom(y => y.Id));
        CreateMap<StockProductItemCreated, CreatedProductItemResponse>();
        CreateMap<StockProductItem, GetProductItemsReservedByOrderResponse>();
        CreateMap<StockProductItem, PrintStockProductItem>()
            .ForMember(pspi => pspi.StockProductItemStatus, cfg => cfg.MapFrom(spi => spi.Status.ToString()))
            .ForMember(pspi => pspi.NegativeAdjustmentReasonName, cfg => cfg.MapFrom(spi => spi.NegativeAdjustmentReason.Name));
        CreateMap<SearchStockProductItemsRequest, SearchStockProductItemsQuery>()
            .ForMember(x => x.StockIds, o => o.MapFrom(s => new List<Guid> { s.StockId }));
        CreateMap<SearchStockProductItemsPostRequest, SearchStockProductItemsQuery>();

        CreateMap<InvoiceAvailableItem, GetInvoiceAvailableItemResponse>();

        CreateMap<ProductTrialStarted, StartStockProductItemsTrialCommand>();
        CreateMap<ProductTrialCancelled, CancelStockProductItemsTrialCommand>();
        CreateMap<ProductTrialCompleted, ProductTrialCompletedCommand>();
        CreateMap<TrialProductItem, TrialStockProductItem>()
            .ForCtorParam(nameof(TrialStockProductItem.ProductId), opt => opt.MapFrom(y => y.InventoryProductId));
    }
}
