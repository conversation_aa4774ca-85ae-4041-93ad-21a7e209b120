using Auditdata.InventoryService.Contracts.Requests.ProductCategoryAccountCodes;
using Auditdata.InventoryService.Contracts.Responses;
using Auditdata.InventoryService.Core.Features.ProductCategoryAccountCodes.Commands;
using Auditdata.InventoryService.Core.Features.ProductCategoryAccountCodes.Models;

namespace Auditdata.InventoryService.Api.Mappings;

public class ProductCategoryAccountCodesMapping : Profile
{
    public ProductCategoryAccountCodesMapping()
    {
        CreateMap<CreateProductCategoryAccountCodeRequest, UpdateProductCategoryAccountCodeCommand>();
        CreateMap<GetProductCategoryAccountCodesResult, GetProductCategoryAccountCodesResponse>();
    }
}
