using Auditdata.InventoryService.Contracts.Commands;
using Auditdata.InventoryService.Contracts.Responses.StockTransactions;
using Auditdata.InventoryService.Core.Entities;
using Auditdata.InventoryService.Core.Features.StockTransactions.Commands;

namespace Auditdata.InventoryService.Api.Mappings;

public class StockTransactionsMappings : Profile
{
    public StockTransactionsMappings()
    {
        CreateMap<StockTransaction, StockTransactionResponse>()
            .ForMember(x => x.Type, opt => opt.MapFrom(x => x.TypeId.ToString()))
            .ForMember(x => x.Stock, opt => opt.MapFrom(x => x.Stock.Name))
            .ForMember(x => x.CreatedOn, opt => opt.MapFrom(x => x.CreationDate));

        CreateMap<AddStockTransactionCommand, CreateStockTransactionCommand>();
    }
}
