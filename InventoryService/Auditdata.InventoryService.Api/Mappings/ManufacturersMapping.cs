using Auditdata.InventoryService.Contracts.Requests.Manufacturers;
using Auditdata.InventoryService.Contracts.Responses.Manufacturers;
using Auditdata.InventoryService.Core.Entities;
using Auditdata.InventoryService.Core.Features.Manufacturers.Commands;

namespace Auditdata.InventoryService.Api.Mappings;

public class ManufacturersMapping : Profile
{
    public ManufacturersMapping()
    {
        CreateMap<ManufacturerContact, ManufacturerContactResponse>();
        CreateMap<ManufacturerContactRequest, ManufacturerContact>();
        CreateMap<ManufacturerRequest, UpdateManufacturerCommand>();

        CreateMap<Manufacturer, ManufacturerResponse>()
            .ForMember(x => x.Country, y => y.MapFrom(z => z.Country!.Name));
        CreateMap<Manufacturer, GetManufacturerListResponse>()
            .ForMember(x => x.Country, y => y.MapFrom(z => z.Country!.Name));
        CreateMap<ManufacturerRequest, CreateManufacturerCommand>()
            .ForMember(x => x.AccountReceivableContact, y => y.MapFrom(z => z.AccountReceivableContact))
            .ForMember(x => x.SalesContact, y => y.MapFrom(z => z.SalesContact));
    }
}
