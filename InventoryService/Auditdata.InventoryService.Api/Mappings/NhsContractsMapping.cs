using Auditdata.InventoryService.Contracts.Requests.NhsContracts;
using Auditdata.InventoryService.Contracts.Responses.NhsContracts;
using Auditdata.InventoryService.Core.Entities.Products;
using Auditdata.InventoryService.Core.Features.NhsContracts.Queries;

namespace Auditdata.InventoryService.Api.Mappings;

public class NhsContractsMapping : Profile
{
    public NhsContractsMapping()
    {
        CreateMap<GetNhsContractProductsRequest, GetNhsContractProductsQuery>();
        CreateMap<Product, NhsContractProductsResponse>()
            .ForMember(x => x.Type, opt => opt.MapFrom(y => y.Category.Name))
            .ForMember(x => x.Manufacturer, opt => opt.MapFrom(y => y.Manufacturer!.Name))
            .ForMember(x => x.ContractId, opt => opt.MapFrom(y => y.NhsContractProducts!.FirstOrDefault()!.ContractId))
            .ForMember(x => x.InContract, opt => opt.MapFrom(y => y.NhsContractProducts!.Count != 0));
    }
}
