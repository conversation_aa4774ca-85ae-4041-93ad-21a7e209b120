using Auditdata.InventoryService.Contracts.Responses.States;
using Auditdata.InventoryService.Core.Features.States.Commands;
using Auditdata.Microservice.Messages.Events.State;
using State = Auditdata.InventoryService.Core.Entities.State;

namespace Auditdata.InventoryService.Api.Mappings;

public class StateMappings : Profile
{
    public StateMappings()
    {
        CreateMap<StateCreated, CreateOrUpdateStateCommand>();
        CreateMap<StateUpdated, CreateOrUpdateStateCommand>();
        CreateMap<StateDeleted, DeleteStateCommand>();

        CreateMap<State, StateResponse>();
    }
}
