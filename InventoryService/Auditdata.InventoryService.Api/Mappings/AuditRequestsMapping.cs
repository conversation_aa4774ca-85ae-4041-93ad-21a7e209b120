using Auditdata.InventoryService.Contracts.Requests.AuditRequests;
using Auditdata.InventoryService.Contracts.Responses.AuditRequests;
using Auditdata.InventoryService.Contracts.Responses.Common;
using Auditdata.InventoryService.Core.Entities.AuditRequests;
using Auditdata.InventoryService.Core.Features.AuditRequests.Create;
using Auditdata.InventoryService.Core.Features.AuditRequests.Models;
using Auditdata.InventoryService.Core.Features.AuditRequests.Models.Search;
using Auditdata.InventoryService.Core.Features.AuditRequests.Patch;
using Auditdata.InventoryService.Core.Features.AuditRequests.Search;
using Auditdata.InventoryService.Core.Features.AuditRequests.SearchLogs;
using Auditdata.InventoryService.Core.Features.AuditRequests.SearchProducts;
using Auditdata.InventoryService.Core.Features.AuditRequests.SearchProductsFromStock;
using Auditdata.InventoryService.Core.Features.AuditRequests.Update;

namespace Auditdata.InventoryService.Api.Mappings;

public class AuditRequestsMapping : Profile
{
    public AuditRequestsMapping()
    {
        CreateMap<CreateAuditRequestModel, CreateAuditRequestCommand>();
        CreateMap<AuditLocationRequest, AuditLocationDto>();

        CreateMap<AuditRequestResult, AuditRequestResponse>();
        CreateMap<AuditLocationResult, DictionaryResponse>();
        CreateMap<AuditProductResult, AuditRequestProductResponse>();
        CreateMap<AuditProductSerialNumberResult, AuditProductSerialNumberResponse>();

        CreateMap<SearchAuditRequestsRequest, SearchAuditRequestsQuery>();
        CreateMap<SearchAuditRequestDto, SearchAuditRequestResponse>();

        CreateMap<SearchAuditProductsRequest, SearchAuditProductsQuery>();

        CreateMap<SearchAuditRequestLogsRequest, SearchAuditRequestLogsQuery>();

        CreateMap<SearchAuditRequestProductsFromStockRequest, SearchAuditRequestProductsFromStockQuery>();
        CreateMap<SearchAuditRequestProductFromStockDto, SearchAuditRequestProductFromStockResponse>();

        CreateMap<AuditProductRequest, AuditProductDto>();
        CreateMap<AuditProductSNRequest, AuditProductSerialNumberDto>();

        CreateMap<UpdateAuditRequestModel, UpdateAuditRequestCommand>();

        CreateMap<AuditRequestLog, AuditRequestLogResponse>();
        CreateMap<AuditRequestChanges, AuditRequestChangesResponse>();
        CreateMap<AuditProductChanges, AuditProductChangesResponse>();
        CreateMap<AuditSerialNumberChanges, AuditSerialNumberChangesResponse>();
        CreateMap<PropertyChangeModel, PropertyChangeModelResponse>();

        CreateMap<PatchAuditRequestModel, PatchAuditRequestDto>();
        CreateMap<PatchAuditProductRequest, PatchAuditProductDto>()
            .ForMember(
            x => x.Counted,
            opt => opt.MapFrom(src => string.IsNullOrWhiteSpace(src.Counted) ? (int?)null : int.Parse(src.Counted)));
    }
}
