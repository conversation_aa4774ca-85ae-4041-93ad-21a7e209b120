using System.Linq.Expressions;
using Auditdata.Infrastructure.Dictionary.Services;
using Auditdata.InventoryService.Contracts.Requests.Colors;
using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Entities;
using Auditdata.InventoryService.Dictionaries.Constants;
using Auditdata.InventoryService.Dictionaries.Extensions;
using Auditdata.InventoryService.Infrastructure.Persistence;
using FluentValidation;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Auditdata.InventoryService.Dictionaries.Colors;

public class ColorsService : DictionaryService<ColorRequest, Color, ColorsGetPagedQuery>
{
    private readonly InventoryDbContext _inventoryDbContext;
    private readonly IEventPublisher _eventPublisher;
    public override string DictionaryTypeName => "product-colors";
    protected override string DefaultOrderByQueryString => "name:asc";
    
    public ColorsService(
        InventoryDbContext dbContext,
        IEventPublisher eventPublisher,
        ILogger<ColorsService> logger) : base(dbContext, logger)
    {
        _inventoryDbContext = dbContext;
        _eventPublisher = eventPublisher;
    }

    protected override void ConfigureCreateAndUpdateValidator(DictionaryCreateAndUpdateValidator validator)
    {
        validator
            .RuleFor(x => x.Name)
            .NotEmpty()
            .MaximumLength(100);
        
        validator
            .RuleFor(x => x)
            .MustAsync(async (productColor, cancellationToken) =>
            {
                var existingProductColor = await dbTable
                    .AsNoTracking()
                    .FirstOrDefaultAsync(x => x.Name == productColor.Name, cancellationToken);

                if (existingProductColor is null)
                    return true;

                return existingProductColor.Id == productColor.Id;
            })
            .WithName("Name")
            .WithMessage("Product color with specified name already exists.")
            .WithErrorCode(ValidatorErrorCodes.Colors.AlreadyExists);

            validator.RuleFor(x => x)
            .MustAsync(async (colorRequest, cancellationToken) =>
            {
                return !(await IsColorInUse(colorRequest, cancellationToken));
            })
            .WithMessage("The selected product color is in use on and cannot be updated.")
            .WithErrorCode(ValidatorErrorCodes.Colors.CantUpdate);
    }

    private async Task<bool> IsColorInUse(ColorRequest color, CancellationToken cancellationToken)
    {
        var alreadyExists = await
            _inventoryDbContext.ProductColors.AnyAsync(x =>
                x.ColorId == color.Id && !x.Product.IsDeleted, cancellationToken);
        return alreadyExists;
    }

    protected override void ConfigureDeleteValidator(DictionaryDeleteValidator validator)
    {
        base.ConfigureDeleteValidator(validator);
        validator.RuleFor(x => x)
            .MustAsync(async (colorRequest, cancellationToken) => !(await IsColorInUse(colorRequest, cancellationToken)))

            .WithMessage("The selected product color is in use on and cannot be deleted.")
            .WithErrorCode(ValidatorErrorCodes.Colors.CantDelete);
    }

    protected override Expression<Func<Color, bool>> GetFilterExpression(ColorsGetPagedQuery tableQuery)
    {
        Expression<Func<Color, bool>> filterExpression = r => true;

        var filter = tableQuery.SearchText;
        if (!string.IsNullOrEmpty(filter))
        {
            filterExpression = r => r.Name.Contains(filter);
        }

        if (tableQuery.IsActive.HasValue)
        {
            filterExpression = filterExpression.And(r => r.IsActive == tableQuery.IsActive);
        }

        return filterExpression;
    }

    protected override string GetLocalizationPrefix() => nameof(ColorRequest);

    protected override bool TableRowIsActive(Color dataModel) => dataModel.IsActive;

    protected override async Task AfterCreate(Color dataModel)
    {
        await _eventPublisher.ColorCreated(dataModel);
    }

    protected override async Task AfterUpdate(Color dataModel, Color originalDataModel)
    {
        await _eventPublisher.ColorUpdated(dataModel);
    }

    protected override async Task AfterDelete(Color dataModel)
    {
        await _eventPublisher.ColorDeleted(dataModel);
    }
}
