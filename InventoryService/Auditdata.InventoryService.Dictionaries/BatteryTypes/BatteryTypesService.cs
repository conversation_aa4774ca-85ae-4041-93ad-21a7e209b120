using System.Linq.Expressions;
using Auditdata.Infrastructure.Dictionary.Services;
using Auditdata.InventoryService.Contracts.Requests.BatteryTypes;
using Auditdata.InventoryService.Core.Abstractions.Azure;
using Auditdata.InventoryService.Core.Entities;
using Auditdata.InventoryService.Dictionaries.Constants;
using Auditdata.InventoryService.Dictionaries.Extensions;
using Auditdata.InventoryService.Infrastructure.Persistence;
using Auditdata.Microservice.Messages.Events.Inventory;
using AutoMapper;
using FluentValidation;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Auditdata.InventoryService.Dictionaries.BatteryTypes;

public class BatteryTypesService : DictionaryService<BatteryTypeRequest, BatteryType, BatteryTypesGetPagedQuery>
{
    private readonly InventoryDbContext _inventoryDbContext;
    private readonly IAzureServiceBusPublisher _azureServiceBusPublisher;
    private readonly IMapper _mapper;
    public override string DictionaryTypeName => "battery-types";
    protected override string DefaultOrderByQueryString => "name:asc";

    public BatteryTypesService(
        InventoryDbContext dbContext,
        ILogger<BatteryTypesService> logger,
        IAzureServiceBusPublisher azureServiceBusPublisher,
        IMapper mapper) : base(dbContext, logger)
    {
        _inventoryDbContext = dbContext;
        _azureServiceBusPublisher = azureServiceBusPublisher;
        _mapper = mapper;
    }

    protected override void ConfigureCreateAndUpdateValidator(DictionaryCreateAndUpdateValidator validator)
    {
        validator
            .RuleFor(x => x.Name)
            .NotEmpty()
            .MaximumLength(100);

        validator
            .RuleFor(x => x)
            .MustAsync(async (batteryType, cancellationToken) =>
            {
                var existingBatteryType = await dbTable
                    .AsNoTracking()
                    .FirstOrDefaultAsync(x => x.Name == batteryType.Name, cancellationToken);

                if (existingBatteryType is null)
                    return true;

                return existingBatteryType.Id == batteryType.Id;
            })
            .WithName("Name")
            .WithMessage("Battery type with specified name already exists.")
            .WithErrorCode(ValidatorErrorCodes.BatteryTypes.AlreadyExists);

        validator
            .RuleFor(x => x)
            .MustAsync(async (batteryType, cancellationToken) =>
            {
                return !(await IsBatteryTypeInUse(batteryType, cancellationToken));
            })
            .WithMessage("Battery type with specified name is in use and can't be updated.")
            .WithErrorCode(ValidatorErrorCodes.BatteryTypes.CantUpdate);
    }

    private async Task<bool> IsBatteryTypeInUse(BatteryTypeRequest batteryType, CancellationToken cancellationToken)
    {
        var alreadyExists = await
            _inventoryDbContext.ProductBatteryTypes.AnyAsync(x =>
                x.BatteryTypeId == batteryType.Id && !x.Product.IsDeleted, cancellationToken);

        return alreadyExists;
    }

    protected override void ConfigureDeleteValidator(DictionaryDeleteValidator validator)
    {
        base.ConfigureDeleteValidator(validator);
       
        validator.RuleFor(x => x)
            .MustAsync(async (batteryTypeRequest, cancellationToken) => !(await IsBatteryTypeInUse(batteryTypeRequest, cancellationToken)))
            .WithMessage("Battery type with specified name is in use and can't be deleted.")
            .WithErrorCode(ValidatorErrorCodes.BatteryTypes.CantDelete);
    }

    protected override Expression<Func<BatteryType, bool>> GetFilterExpression(BatteryTypesGetPagedQuery tableQuery)
    {
        Expression<Func<BatteryType, bool>> filterExpression = r => true;

        var filter = tableQuery.SearchText;
        if (!string.IsNullOrEmpty(filter))
        {
            filterExpression = r => r.Name.Contains(filter);
        }

        if (tableQuery.IsActive.HasValue)
        {
            filterExpression = filterExpression.And(r => r.IsActive == tableQuery.IsActive);
        }
        return filterExpression;
    }

    protected override async Task AfterCreate(BatteryType dataModel)
    {
        await _azureServiceBusPublisher.PublishBatteryTypeCreated(_mapper.Map<BatteryTypeCreated>(dataModel));
    }

    protected override async Task AfterUpdate(BatteryType dataModel, BatteryType originalDataModel)
    {
        await _azureServiceBusPublisher.PublishBatteryTypeUpdated(_mapper.Map<BatteryTypeUpdated>(dataModel));
    }

    protected override async Task AfterDelete(BatteryType dataModel)
    {
        await _azureServiceBusPublisher.PublishBatteryTypeDeleted(_mapper.Map<BatteryTypeDeleted>(dataModel));
    }

    protected override string GetLocalizationPrefix() => nameof(BatteryTypeRequest);

    protected override bool TableRowIsActive(BatteryType dataModel) => dataModel.IsActive;
}
