using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Abstractions.Repositories;
using Auditdata.InventoryService.Core.Entities;
using Medallion.Threading.SqlServer;
using Microsoft.EntityFrameworkCore;

namespace Auditdata.InventoryService.Infrastructure.Repositories;

public class StockProductsRepository : BaseEntityRepository<StockProduct>, IStockProductsRepository
{
    public StockProductsRepository(IDbContext dbContext) : base(dbContext)
    {
    }

    public async Task<IEnumerable<StockProduct>> GetByProductIdAsync(Guid productId)
    {
        return await DbSet.Where(x => x.ProductId == productId).ToListAsync();
    }

    public async Task<StockProduct?> GetWithProductAsync(Guid stockProductId)
    {
        return await DbSet.Include(x => x.Product).FirstOrDefaultAsync(x => x.Id == stockProductId);
    }

    public async Task<StockProduct?> GetWithStockAsync(Guid stockProductId)
    {
        return await DbSet.Include(x => x.Stock).FirstOrDefaultAsync(x => x.Id == stockProductId);
    }

    public async Task<IAsyncDisposable> AcquireLockAsync(Guid stockId, Guid productId)
    {
        var connectionString = DbContext.Database.GetConnectionString()!;
        var myDistributedLock = new SqlDistributedLock($"stock-{stockId}-product-{productId}", connectionString);
        return await myDistributedLock.AcquireAsync(TimeSpan.FromSeconds(30));
    }

    public async Task<StockProduct?> GetRepairProductAsync(Guid stockProductId)
    {
        return await DbSet
            .Include(x => x.Product)
            .Include(x => x.Product.Manufacturer)
            .Include(x => x.Product.Supplier)
            .FirstOrDefaultAsync(x => x.Id == stockProductId);
    }

    public async Task<(int Available, int Reserved)> GetStockProductsSummaryAsync(Guid productId, ICollection<Guid> locationIds)
    {
        var stockProducts = await DbSet
            .Include(x => x.StockProductItems)
            .Where(x =>
                x.ProductId == productId &&
                locationIds.Contains(x.Stock.LocationId))
            .Select(x => new
            {
                x.Quantity,
                ReservedQuantity = x.StockProductItems
                    .Count(y => !y.IsDeleted && (y.Status == StockProductItemStatus.Reserved 
                        || y.Status == StockProductItemStatus.ReservedByOrder))
            })
            .ToListAsync();

        var available = stockProducts.Sum(x => x.Quantity);
        var reserved = stockProducts.Sum(x => x.ReservedQuantity);

        return (available, reserved);
    }
}
