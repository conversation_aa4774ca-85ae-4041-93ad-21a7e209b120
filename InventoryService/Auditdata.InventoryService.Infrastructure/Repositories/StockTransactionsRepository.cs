using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Abstractions.Repositories;
using Auditdata.InventoryService.Core.Entities;
using Medallion.Threading.SqlServer;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;

namespace Auditdata.InventoryService.Infrastructure.Repositories;

public class StockTransactionsRepository : BaseEntityRepository<StockTransaction>, IStockTransactionsRepository
{
    private readonly IConfiguration _configuration;

    public StockTransactionsRepository(
        IDbContext dbContext,
        IConfiguration configuration) : base(dbContext)
    {
        _configuration = configuration;
    }

    public async Task<IEnumerable<StockTransaction>> GetByProductIdAndLocationIdsAsync(Guid productId, ICollection<Guid> locationIds)
    {
        return await DbSet
            .Include(x => x.Stock)
            .OrderByDescending(x => x.CreationDate)
            .Where(x => 
                x.ProductId == productId && 
                locationIds.Contains(x.Stock.LocationId))
            .ToListAsync();
    }

    public async Task<IAsyncDisposable> AcquireLockAsync(Guid stockId)
    {
        var myDistributedLock = new SqlDistributedLock($"stock-{stockId}", _configuration.GetConnectionString("AzureSql")!);
        return await myDistributedLock.AcquireAsync(TimeSpan.FromSeconds(30));
    }
}
