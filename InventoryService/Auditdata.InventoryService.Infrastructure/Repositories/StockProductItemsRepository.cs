using Auditdata.InventoryService.Core;
using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Abstractions.Repositories;
using Auditdata.InventoryService.Core.Entities;
using Microsoft.EntityFrameworkCore;

namespace Auditdata.InventoryService.Infrastructure.Repositories;

public class StockProductItemsRepository : BaseEntityRepository<StockProductItem>, IStockProductItemsRepository
{
    public StockProductItemsRepository(IDbContext dbContext) : base(dbContext)
    {
    }

    public async Task DeleteSerialNumberAsync(Guid stockProductItemId)
    {
        var stockProductItem = await DbSet.FindAsync(stockProductItemId);
        if (stockProductItem is null)
            return;

        stockProductItem.SerialNumber = null;
        await DbContext.SaveChangesAsync();
    }

    public async Task<IEnumerable<StockProductItem>> GetByProductIdWithStockAsync(Guid productId, string? searchText, Guid? stockId, StockProductItemStatus? status)
    {
        var query = DbSet.Include(x => x.StockProduct.Stock)
            .Include(x=> x.BatteryType)
            .Include(x=> x.Color)
            .Where(x => x.StockProduct.ProductId == productId && !x.IsDeleted);

        if (stockId.HasValue)
        {
            query = query.Where(x => x.StockProduct.StockId == stockId);
        }

        if (!string.IsNullOrEmpty(searchText))
        {
            query = query.Where(x => x.SerialNumber!.Contains(searchText));
        }

        if (status.HasValue)
        {
            query = query.Where(x => x.Status == status);
        }

        return await query
            .OrderBy(x => x.SerialNumber == null)
            .ThenBy(x => x.CreationDate)
            .ToListAsync();
    }

    public async Task<IEnumerable<StockProductItem>> GetStockProductItemsPendingTransferToStockAsync(Guid productId, Guid? stockId, string? searchText, StockProductItemStatus? status)
    {
        var query = DbContext.Set<TransfersState>()
            .Include(x => x.StockProductItem)
                .ThenInclude(x => x!.StockProduct)
                    .ThenInclude(x => x.Stock)
            .Where(x => x.ToStockId == stockId
                    && x.CurrentState == DbConst.Transfer.Requested
                    && x.StockProductItem!.StockProduct.ProductId == productId)
            .Select(q => q.StockProductItem!);

        if (!string.IsNullOrEmpty(searchText))
        {
            query = query.Where(x => x.SerialNumber!.Contains(searchText));
        }

        if (status.HasValue)
        {
            query = query.Where(x => x.Status == status);
        }

        return await query
            .OrderBy(x => x.SerialNumber == null)
            .ThenBy(x => x.CreationDate)
            .ToListAsync();
    }

    public async Task<IEnumerable<StockProductItem>> GetAvailableAsync(Guid productId, Guid? stockId)
    {
        var query = DbSet.Include(x => x.StockProduct.Stock)
            .Where(x => x.StockProduct.ProductId == productId && x.Status == StockProductItemStatus.Available && !x.IsDeleted);
        if (stockId.HasValue)
        {
            query = query.Where(x => x.StockProduct.StockId == stockId);
        }

        return await query.ToListAsync();
    }

    public async Task<IEnumerable<StockProductItem>> GetAvailableAsync(Guid stockProductId)
    {
        return await DbSet.Where(x =>
            x.StockProductId == stockProductId &&
            x.Status == StockProductItemStatus.Available &&
            !x.IsDeleted).ToListAsync();
    }

    public async Task<StockProductItem?> GetAvailableBySerialNumberAsync(Guid stockProductId, string serialNumber)
    {
        return await DbSet
            .Include(x => x.StockProduct)
            .FirstOrDefaultAsync(x =>
                x.StockProductId == stockProductId &&
                x.SerialNumber == serialNumber &&
                x.Status == StockProductItemStatus.Available);
    }

    public async Task<StockProductItem?> GetAvailableProductItemAssignedToSale(Guid saleId, Guid stockProductId, string serialNumber)
    {
        return await DbSet
            .Include(x => x.StockProduct)
            .FirstOrDefaultAsync(x =>
                x.StockProductId == stockProductId &&
                x.SerialNumber == serialNumber &&
                x.Status == StockProductItemStatus.Available &&
                x.SaleId == saleId);
    }

    public async Task<StockProductItem?> GetRepairProductItemAsync(Guid stockProductItemId)
    {
        return await DbSet
            .Include(x => x.StockProduct)
            .Include(x => x.StockProduct.Product)
            .Include(x => x.StockProduct.Product.Manufacturer)
            .Include(x => x.StockProduct.Product.Supplier)
            .FirstOrDefaultAsync(x => x.Id == stockProductItemId);
    }

    public async Task<StockProductItem?> GetRepairProductItemAsync(Guid productId, string serialNumber)
    {
        return await DbSet
            .Include(x => x.StockProduct)
            .Include(x => x.StockProduct.Stock)
            .Include(x => x.StockProduct.Product)
            .Include(x => x.StockProduct.Product.Manufacturer)
            .Include(x => x.StockProduct.Product.Supplier)
            .FirstOrDefaultAsync(x => x.SerialNumber == serialNumber
                && x.StockProduct.ProductId == productId);
    }

    public async Task<StockProductItem?> GetItemLinkedToSale(Guid saleId, Guid stockProductId)
    {
        return await DbSet.Include(x => x.StockProduct).FirstOrDefaultAsync(
            x => x.SaleId == saleId &&
                 x.StockProductId == stockProductId &&
                 x.SerialNumber == null &&
                 !x.IsDeleted);
    }

    public async Task<StockProductItem?> GetByIdWithStockProductAsync(Guid stockProductItemId)
    {
        return await DbSet
            .Include(x => x.StockProduct).ThenInclude(x => x.Stock)
            .Include(x => x.StockProduct).ThenInclude(x => x.Product).ThenInclude(x => x.Supplier)
            .FirstOrDefaultAsync(x => x.Id == stockProductItemId);
    }

    public async Task<StockProductItem?> GetReservedAsync(Guid stockProductId, Guid saleId)
    {
        return await DbSet
            .Include(x => x.StockProduct)
            .Where(x => 
                x.StockProductId == stockProductId && 
                x.SaleId == saleId &&
                x.Status == StockProductItemStatus.Reserved)
            .FirstOrDefaultAsync();
    }

    public async Task<StockProductItem?> GetSoldAsync(Guid stockProductId, Guid saleId)
    {
        return await DbSet
            .Include(x => x.StockProduct).ThenInclude(x => x.Stock)
            .Include(x => x.StockProduct).ThenInclude(x => x.Product).ThenInclude(x => x.Supplier)
            .FirstOrDefaultAsync(x =>
                x.StockProductId == stockProductId &&
                x.Status == StockProductItemStatus.Sold &&
                x.SaleId == saleId &&
                !x.IsDeleted);
    }

    public async Task<IEnumerable<StockProductItem>> GetByIdsAsync(IEnumerable<Guid> ids)
    {
        return await DbSet
            .Include(x => x.StockProduct)
            .Include(x => x.StockProduct.Product).ThenInclude(x => x.Manufacturer)
            .Include(x => x.StockProduct.Product).ThenInclude(x => x.Category)
            .Include(x => x.StockProduct.Product).ThenInclude(x => x.Supplier)
            .Include(x => x.StockProduct.Product).ThenInclude(x => x.BatteryTypes)
            .Include(x => x.StockProduct.Product).ThenInclude(x => x.HearingAidType)
            .Where(x => ids.Contains(x.Id))
            .ToArrayAsync();
    }

    public async Task<IEnumerable<StockProductItem>> GetReservedByOrderAsync(Guid locationId, IEnumerable<Guid> orderIds, CancellationToken cancellationToken)
    {
        return await DbSet
            .Where(x => 
                x.StockProduct.Stock.LocationId == locationId &&
                x.Status == StockProductItemStatus.ReservedByOrder &&
                x.OrderId.HasValue &&
                orderIds.Contains(x.OrderId.Value))
            .ToArrayAsync(cancellationToken);
    }
}
