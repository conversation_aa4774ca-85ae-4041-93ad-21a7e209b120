using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Abstractions.Repositories;
using Auditdata.InventoryService.Core.Entities;
using Auditdata.InventoryService.Core.Entities.Products;
using Auditdata.InventoryService.Core.OperationContext;
using Auditdata.Transport.Contracts.PageResults;
using Auditdata.Transport.Contracts.Table;
using Dapper;
using Microsoft.EntityFrameworkCore;

namespace Auditdata.InventoryService.Infrastructure.Repositories;

public class ProductsRepository : BaseEntityRepository<Product>, IProductsRepository
{
    private readonly Guid _tenantId;
    public ProductsRepository(IDbContext dbContext, IInventoryServiceOperationContext operationContext) : base(dbContext)
    {
        _tenantId = operationContext.TenantId;
    }

    public async Task<IEnumerable<Product>> GetByManufacturerAndCategoryAsync(Guid manufacturerId, Guid categoryId)
    {
        return await DbSet
            .Include(x => x.Category)
            .Include(x => x.Manufacturer)
            .Where(x => x.ManufacturerId == manufacturerId && x.CategoryId == categoryId && x.IsActive).ToListAsync();
    }

    public async Task<TablePageResult<Product>> GetProductsAsync(TableQueryBase query)
    {
        var (result, total) = await DbSet.AsNoTracking()
            .Where(x => !x.IsDeleted)
            .Include(x => x.Manufacturer)
            .ApplyTableQueryAsync(query.Page, query.PerPage, query.OrderBy);

        return new TablePageResult<Product>(
            result.Select(x => new TableRow<Product>(x)).ToArray(), query, total);
    }

    public async Task<bool> IsAssociatedWithManufacturer(Guid manufacturerId)
    {
        return await DbSet.AnyAsync(x => x.ManufacturerId == manufacturerId);
    }

    public async Task<TablePageResult<ProductInStock>> SearchStockProductsAsync(TableQueryBase tableQuery, string? searchText,
        Guid? categoryId, Guid? manufacturerId)
    {
        var manufacturerCondition = manufacturerId == null ? "" : "AND P.ManufacturerId = @ManufacturerId";
        var categoryCondition = categoryId == null ? "" : "AND P.CategoryId = @CategoryId";
        var searchTextCondition = searchText == null ? "" : "AND (P.Name LIKE @SearchText OR P.DescriptionValue LIKE @SearchText)";

        var orderCondition = tableQuery.OrderBy switch
        {
            "isActive:asc" => "P.IsActive ASC",
            "isActive:desc" => "P.IsActive DESC",
            "name:asc" => "P.Name ASC",
            "name:desc" => "P.Name DESC",
            "manufacturer:asc" => "M.Name ASC",
            "manufacturer:desc" => "M.Name DESC",
            "isSellable:asc" => "P.IsSellable ASC",
            "isSellable:desc" => "P.IsSellable DESC",
            "type:asc" => "PC.Name ASC",
            "type:desc" => "PC.Name DESC",
            "description:asc" => "P.DescriptionValue ASC",
            "description:desc" => "P.DescriptionValue DESC",
            _ => "P.CreationDate DESC"
        };

        var query = @$"
            SELECT
                P.Id AS [Id],
                P.Name AS [Name],
                P.Description AS [Description],
                P.IsActive AS [IsActive],
                P.IsSellable AS [IsSellable],
                P.IsSerialized AS [IsSerialized],
                M.Id AS [ManufacturerId],
                M.Name AS [Manufacturer],
                PC.Id AS [CategoryId],
                PC.Name AS [Category]
            FROM Products P
                     LEFT JOIN Manufacturers M ON p.ManufacturerId = M.Id
                     LEFT JOIN ProductCategories PC ON p.CategoryId = PC.Id
            WHERE
                    P.ControlledByStock = 1
              AND
                    P.IsDeleted = 0
              AND (
                        P.IsActive = 1
                    OR
                        (P.IsActive = 0 AND (
                                                SELECT SUM(SP.Quantity)
                                                FROM StockProducts SP
                                                WHERE SP.ProductId = P.Id AND SP.IsDeleted = 0 AND SP.TenantId = @TenantId
                                            ) > 0)
                )
              AND
                    P.TenantId = @TenantId
              {manufacturerCondition}
              {categoryCondition}
              {searchTextCondition}
            ORDER BY {orderCondition} 
            OFFSET @Offset ROWS
                FETCH NEXT @PerPage ROWS ONLY

            SELECT COUNT(P.Id)
            FROM Products P
            WHERE
                    P.ControlledByStock = 1
              AND
                    P.IsDeleted = 0
              AND (
                        P.IsActive = 1
                    OR
                        (P.IsActive = 0 AND (
                                                SELECT SUM(SP.Quantity)
                                                FROM StockProducts SP
                                                WHERE SP.ProductId = P.Id AND SP.IsDeleted = 0 AND SP.TenantId = @TenantId
                                            ) > 0)
                )
              AND
                    P.TenantId = @TenantId
              {manufacturerCondition}
              {categoryCondition}
              {searchTextCondition}
        ";
        
        await using var connection = DbContext.Database.GetDbConnection();
        var multipleResult = await connection.QueryMultipleAsync(query,
            new
            {
                TenantId = _tenantId,
                Offset = tableQuery.PerPage * (tableQuery.Page - 1),
                tableQuery.PerPage,
                ManufacturerId = manufacturerId,
                CategoryId = categoryId,
                SearchText = string.IsNullOrEmpty(searchText) ? null : $"%{searchText}%",
                tableQuery.OrderBy
            });
        return new TablePageResult<ProductInStock>(
            multipleResult.Read<ProductInStock>().Select(x => new TableRow<ProductInStock>(x)).ToArray(), 
            tableQuery,
            multipleResult.Read<int>().Single());
    }

    public async Task<(TablePageResult<UKProduct>, int)> GetNhsContractProductsAsync(Guid contractId, string? searchText, TableQueryBase tableQuery)
    {
        var query = DbContext.Set<UKProduct>()
            .Include(x => x.Manufacturer)
            .Include(x => x.Category)
            .Include(x => x.NhsContractProducts.Where(x => x.ContractId == contractId))
            .Where(x => x.IsNHS && !x.IsDeleted);

        if (!string.IsNullOrEmpty(searchText))
        {
            query = query.Where(x => x.Name.Contains(searchText) || x.DescriptionValue!.Contains(searchText));
        }

        var assignedProductsCount =
            query.Count(x => x.NhsContractProducts.Select(y => y.ContractId).Contains(contractId));

        if (tableQuery.OrderBy is not null && tableQuery.OrderBy.Trim().ToLower().Contains("InContract".ToLower()))
        {
            if (tableQuery.OrderBy.ToLower().Contains("desc"))
            {
                query = query
                    .OrderBy(x => !x.NhsContractProducts.Select(y => y.ContractId).Contains(contractId))
                    .ThenByDescending(x => x.CreationDate);
            }
            else
            {
                query = query
                    .OrderBy(x => x.NhsContractProducts.Select(y => y.ContractId).Contains(contractId))
                    .ThenByDescending(x => x.CreationDate);
            }

            tableQuery.OrderBy = string.Empty;
        }
        else if (tableQuery.OrderBy is not null && tableQuery.OrderBy.Trim().ToLower().Contains("Manufacturer".ToLower()))
        {
            if (tableQuery.OrderBy.ToLower().Contains("desc"))
            {
                query = query.OrderByDescending(x => x.Manufacturer!.Name);
            }
            else
            {
                query = query.OrderBy(x => x.Manufacturer!.Name);
            }

            tableQuery.OrderBy = string.Empty;
        }
        else if (string.IsNullOrEmpty(tableQuery.OrderBy))
        {
            query = query
                .OrderBy(x => !x.NhsContractProducts.Select(y => y.ContractId).Contains(contractId))
                .ThenByDescending(x => x.CreationDate);
        }

        var (result, total) = await query.ApplyTableQueryAsync(tableQuery.Page, tableQuery.PerPage, tableQuery.OrderBy);

        return (new TablePageResult<UKProduct>(result.Select(x => new TableRow<UKProduct>(x)).ToArray(), tableQuery, total),
            assignedProductsCount);
    }
}
