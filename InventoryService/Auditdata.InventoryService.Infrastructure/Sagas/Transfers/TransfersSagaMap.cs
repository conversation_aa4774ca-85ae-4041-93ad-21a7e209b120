using Auditdata.InventoryService.Core.Entities;
using MassTransit;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Auditdata.InventoryService.Infrastructure.Sagas.Transfers;

public class TransfersSagaMap : SagaClassMap<TransfersState>
{
    protected override void Configure(EntityTypeBuilder<TransfersState> entity, ModelBuilder model)
    {
        entity.HasKey(x => x.TransferId);
        entity.Property(x => x.TransferId).ValueGeneratedNever();
        entity.Property(x => x.CurrentState).HasMaxLength(50);
        entity.Property(x => x.RequestedAt);
        entity.Property(x => x.FromStockId);
        entity.Property(x => x.ToStockId);
        entity.Property(x => x.StockProductItemId);
        entity.Property(x => x.RowVersion).IsRowVersion();
    }
}
