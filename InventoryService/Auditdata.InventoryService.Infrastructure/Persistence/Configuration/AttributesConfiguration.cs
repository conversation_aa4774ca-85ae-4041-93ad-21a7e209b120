using Auditdata.InventoryService.Core.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Attribute = Auditdata.InventoryService.Core.Entities.Attribute;

namespace Auditdata.InventoryService.Infrastructure.Persistence.Configuration;

internal class AttributesConfiguration : IEntityTypeConfiguration<Attribute>
{
    public void Configure(EntityTypeBuilder<Attribute> builder)
    {
        builder.Property(x => x.Name)
            .HasMaxLength(255)
            .IsRequired();
        builder.Property(x => x.ValueType)
            .HasMaxLength(255)
            .HasConversion(new EnumToStringConverter<AttributeValueType>())
            .IsRequired();
        builder.Property(x => x.Code).HasMaxLength(255);
        builder.OwnsMany(x => x.Values, p => p.<PERSON>());
        builder
            .HasMany(x => x.ProductCategories)
            .WithMany(x => x.Attributes)
            .UsingEntity<AttributeProductCategory>();
    }
}
