using Auditdata.InventoryService.Core;
using Auditdata.InventoryService.Core.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Auditdata.InventoryService.Infrastructure.Persistence.Configuration;

public class PathwaysConfiguration : IEntityTypeConfiguration<Pathway>
{
    public void Configure(EntityTypeBuilder<Pathway> builder)
    {
        builder.HasData(new List<Pathway>
        {
            new()
            {
                Id = new Guid("4E7301D0-88C5-4F33-962D-ED06B4E64E4D"),
                Name = "Wax Removal",
                Type = "wax_removal",
                CreatedBy = DbConst.DefaultAdminUser
            },
            new()
            {
                Id = new Guid("A8A63EFD-3763-41F7-A568-B887B4341E24"),
                Name = "HA Fitting",
                Type = "ha_fitting",
                CreatedBy = DbConst.DefaultAdminUser
            },
            new()
            {
                Id = new Guid("9D363EC1-D09A-4380-93AF-E0324E9D028D"),
                Name = "Recall",
                Type = "recall",
                CreatedBy = DbConst.DefaultAdminUser
            },
            new()
            {
                Id = new Guid("345201F6-4599-4871-BB28-2E44834F8A30"),
                Name = "Aftercare",
                Type = "aftercare",
                CreatedBy = DbConst.DefaultAdminUser
            },
            new()
            {
                Id = new Guid("98DC257C-EC38-49C8-B658-5BB4AFC71CE8"),
                Name = "Remote Aftercare",
                Type = "remote_aftercare",
                CreatedBy = DbConst.DefaultAdminUser
            },
            new()
            {
                Id = new Guid("6B7D87CC-4F5B-4B00-8D55-6DA6D5A8C9B3"),
                Name = "Hearing Check",
                Type = "hearing_check",
                CreatedBy = DbConst.DefaultAdminUser
            },
            new()
            {
                Id = new Guid("B3943D8B-80C9-4822-9998-A79A3A42F239"),
                Name = "Private Test",
                Type = "private_test",
                CreatedBy = DbConst.DefaultAdminUser
            },
            new()
            {
                Id = new Guid("19DE7AA8-E174-47F8-83A8-953BBC15594E"),
                Name = "NHS Assess & Fit",
                Type = "nhs_assess_and_fit",
                CreatedBy = DbConst.DefaultAdminUser
            },
            new()
            {
                Id = new Guid("5A674586-39FC-4BA3-9FD8-C3A2CA17C80E"),
                Name = "Private Test & HA Consult",
                Type = "private_test_ha_consult",
                CreatedBy = DbConst.DefaultAdminUser
            },
            new()
            {
                Id = new Guid("440E198B-1089-4C28-99E5-CDCF79332407"),
                Name = "Private HA Consultation",
                Type = "private_ha_consultation",
                CreatedBy = DbConst.DefaultAdminUser
            },
            new()
            {
                Id = new Guid("3E0A70B0-61E0-434D-A258-709583EA0F2B"),
                Name = "Wax Triage",
                Type = "wax_triage",
                CreatedBy = DbConst.DefaultAdminUser
            },
            new()
            {
                Id = new Guid("0B9909D1-B2BB-4EFC-8D24-FD6DCC994013"),
                Name = "Follow Up",
                Type = "follow_up",
                CreatedBy = DbConst.DefaultAdminUser
            },
            new()
            {
                Id = new Guid("88C28659-77DC-4BD5-A2D3-31AA671E6210"),
                Name = "Remote Follow Up",
                Type = "remote_follow_up",
                CreatedBy = DbConst.DefaultAdminUser
            },
            new()
            {
                Id = new Guid("AC95BDFD-F8A8-4D1F-89E8-7B4AA06E9165"),
                Name = "Re-Assessment",
                Type = "re_assessment",
                CreatedBy = DbConst.DefaultAdminUser
            },
            new()
            {
                Id = new Guid("EFFF08A8-C1E2-4199-9488-5405506FC9ED"),
                Name = "Hearing Protection",
                Type = "hearing_protection",
                CreatedBy = DbConst.DefaultAdminUser
            }
        });
        builder.Property(x => x.Name).HasMaxLength(255);
        builder.Property(x => x.Type).HasMaxLength(255);
    }
}
