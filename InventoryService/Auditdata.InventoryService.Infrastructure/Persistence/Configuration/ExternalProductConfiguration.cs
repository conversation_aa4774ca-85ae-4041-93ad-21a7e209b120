using Auditdata.InventoryService.Core.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

namespace Auditdata.InventoryService.Infrastructure.Persistence.Configuration;

internal class ExternalProductConfiguration : IEntityTypeConfiguration<ExternalProduct>
{
    public void Configure(EntityTypeBuilder<ExternalProduct> builder)
    {
        builder.Property(x => x.ExternalId).HasMaxLength(255);
        builder.Property(x => x.Name).HasMaxLength(255);
        builder.Property(x => x.Manufacturer).HasMaxLength(255);
        builder.Property(x => x.HearingAidType).HasMaxLength(255);
        builder.Property(x => x.Category)
            .HasMaxLength(255)
            .HasConversion(new EnumToStringConverter<ExternalProductCategory>());
    }
}
