using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using Auditdata.InventoryService.Core.Entities.AuditRequests;
using Auditdata.InventoryService.Infrastructure.Extensions;

namespace Auditdata.InventoryService.Infrastructure.Persistence.Configuration;

public class AuditProductConfiguration : IEntityTypeConfiguration<AuditProduct>
{
    public void Configure(EntityTypeBuilder<AuditProduct> builder)
    {
        builder.HasKey(ap => ap.Id);

        builder.Property(x => x.ProductName).HasMaxLength(255);

        builder.Property(x => x.SupplierName).HasMaxLength(255);

        builder.Property(x => x.ProductCategoryName).HasMaxLength(255);

        builder.Property(x => x.ManufacturerName).HasMaxLength(255);

        builder.HasOne(ap => ap.AuditRequest)
            .WithMany(ar => ar.AuditProducts)
            .HasForeignKey(ap => ap.AuditRequestId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(ap => ap.Product)
            .WithMany()
            .HasForeignKey(ap => ap.ProductId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasMany(ar => ar.SerialNumbers)
            .WithOne(ap => ap.AuditProduct)
            .HasForeignKey(ap => ap.AuditProductId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.Property(e => e.Skus)
            .HasMaxLength(2000)
            .HasConversion(
                v => v.ToJson(),  
                v => v.FromJson())
            .HasDefaultValue(new List<string>());

        builder.Property(al => al.Version)
            .IsRowVersion();

        builder.HasIndex(x => new { x.ProductId, x.AuditRequestId, x.TenantId })
            .HasFilter($"{nameof(AuditProduct.IsDeleted)} = 0")
            .IsUnique();
    }
}
