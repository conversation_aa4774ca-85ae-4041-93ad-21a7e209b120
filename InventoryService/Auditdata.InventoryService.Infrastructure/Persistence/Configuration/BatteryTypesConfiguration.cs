using Auditdata.InventoryService.Core.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Auditdata.InventoryService.Infrastructure.Persistence.Configuration;

public class BatteryTypesConfiguration : IEntityTypeConfiguration<BatteryType>
{
    public void Configure(EntityTypeBuilder<BatteryType> builder)
    {
        builder.Property(x => x.Name).HasMaxLength(255);
        builder.HasIndex(x => new { x.Name, x.TenantId, x.IsDeleted })
            .HasFilter($"{nameof(BatteryType.IsDeleted)} = 0")
            .IsUnique();
    }
}
