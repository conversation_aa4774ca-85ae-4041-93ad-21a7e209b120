using Auditdata.InventoryService.Core.Entities.Products;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Auditdata.InventoryService.Infrastructure.Persistence.Configuration;

public class AUProductConfiguration : IEntityTypeConfiguration<AUProduct>
{
    public void Configure(EntityTypeBuilder<AUProduct> builder)
    {
        builder.HasBaseType<Product>();
        builder.Property(x => x.HspClientPrice).HasPrecision(18, 2);
        builder.Property(x => x.HspCategory).HasMaxLength(255);
        builder.Property(x => x.HspCode).HasMaxLength(255);
        builder.Property(x => x.HspServiceNumber).HasMaxLength(255);
    }
}
