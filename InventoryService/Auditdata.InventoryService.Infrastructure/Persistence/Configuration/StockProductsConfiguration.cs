using Auditdata.InventoryService.Core.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Auditdata.InventoryService.Infrastructure.Persistence.Configuration;

public class StockProductsConfiguration : IEntityTypeConfiguration<StockProduct>
{
    public void Configure(EntityTypeBuilder<StockProduct> builder)
    {
        builder.HasIndex(p => new { p.ProductId, p.StockId, p.TenantId })
            .IsUnique()
            .HasFilter("[IsDeleted] = 0");

        builder.Property(x => x.Version)
            .IsRowVersion();
    }
}
