using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using Auditdata.InventoryService.Core.Entities;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

namespace Auditdata.InventoryService.Infrastructure.Persistence.Configuration;
internal class ImportMetadataConfiguration : IEntityTypeConfiguration<ImportMetadata>
{
    public void Configure(EntityTypeBuilder<ImportMetadata> builder)
    {
        builder.Property(x => x.Filename)
            .IsRequired()
            .HasMaxLength(255);

        builder.Property(x => x.EntityType)
            .IsRequired()
            .HasMaxLength(255)
            .HasConversion(new EnumToStringConverter<ImportEntityType>());
    }
}
