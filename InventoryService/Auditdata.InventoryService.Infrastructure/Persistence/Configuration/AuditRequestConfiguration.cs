using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using Auditdata.InventoryService.Core.Entities.AuditRequests;

namespace Auditdata.InventoryService.Infrastructure.Persistence.Configuration;

public class AuditRequestConfiguration : IEntityTypeConfiguration<AuditRequest>
{
    public void Configure(EntityTypeBuilder<AuditRequest> builder)
    {
        builder.HasKey(ar => ar.Id);
        
        builder.Property(ar => ar.RequestNumber)
            .HasMaxLength(255);

        builder.Property(ar => ar.Status)
            .IsRequired()
            .HasConversion<string>()
            .HasMaxLength(255);

        builder.Property(ar => ar.DateTimeDue)
            .IsRequired();

        builder.Property(ar => ar.Notes)
            .HasMaxLength(255); 

        builder.Property(ar => ar.IsDeleted)
            .HasDefaultValue(false);

        builder.HasMany(ar => ar.AuditProducts)
            .WithOne(ap => ap.AuditRequest)
            .HasForeignKey(ap => ap.AuditRequestId)
            .OnDelete(DeleteBehavior.Cascade); 

        builder.HasMany(ar => ar.AuditLocations)
            .WithOne(al => al.AuditRequest)
            .HasForeignKey(al => al.AuditRequestId)
            .OnDelete(DeleteBehavior.Cascade); 

        builder.Property(ar => ar.TenantId)
            .IsRequired();

        builder.Property(al => al.Version)
            .IsRowVersion();

        builder.HasIndex(x => new { x.Status, x.TenantId, x.IsDeleted });
        builder.HasIndex(x => new { x.DateTimeDue, x.TenantId, x.IsDeleted });

        builder.HasIndex(x => new { x.RequestNumber, x.TenantId, x.IsDeleted })
            .IsUnique()
            .HasFilter($"{nameof(AuditRequest.RequestNumber)} IS NOT NULL AND {nameof(AuditRequest.IsDeleted)} = 0");
    }
}
