using Auditdata.InventoryService.Core.Entities;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;

namespace Auditdata.InventoryService.Infrastructure.Persistence.Configuration;

public class StockAdjustmentReasonConfiguration : IEntityTypeConfiguration<StockAdjustmentReason>
{
    public void Configure(EntityTypeBuilder<StockAdjustmentReason> builder)
    {
        builder.Property(x => x.Name).HasMaxLength(255);
    }
}
