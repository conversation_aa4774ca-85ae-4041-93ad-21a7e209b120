using Auditdata.InventoryService.Core.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Auditdata.InventoryService.Infrastructure.Persistence.Configuration;

public class StocksConfiguration : IEntityTypeConfiguration<Stock>
{
    public void Configure(EntityTypeBuilder<Stock> builder)
    {
        builder.Property(x => x.LocationId).IsRequired();
        builder.HasIndex(x => x.LocationId).IsUnique();
        builder.Property(x => x.Name).HasMaxLength(255);
        builder.HasIndex(x => x.RegionId);
    }
}
