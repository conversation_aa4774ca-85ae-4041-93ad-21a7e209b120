using Auditdata.InventoryService.Core.Entities.AuditRequests;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;

namespace Auditdata.InventoryService.Infrastructure.Persistence.Configuration;

public class AuditProductSerialNumberConfiguration : IEntityTypeConfiguration<AuditProductSerialNumber>
{
    public void Configure(EntityTypeBuilder<AuditProductSerialNumber> builder)
    {
        builder.<PERSON>Key(sn => sn.Id);

        builder.Property(ap => ap.Comments)
            .HasMaxLength(255);

        builder.Property(sn => sn.SerialNumber)
            .IsRequired()
            .HasMaxLength(255);

        builder.Property(sn => sn.Status)
            .IsRequired()
            .HasConversion<string>()
            .HasMaxLength(255);

        builder.HasOne(sn => sn.AuditProduct)
            .WithMany(ap => ap.SerialNumbers)
            .HasForeignKey(sn => sn.AuditProductId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.Property(al => al.Version)
            .IsRowVersion();

        builder.HasIndex(x => new { x.Status, x.IsManual, x.TenantId });
        builder.HasIndex(x => new { x.SerialNumber, x.TenantId });
    }
}
