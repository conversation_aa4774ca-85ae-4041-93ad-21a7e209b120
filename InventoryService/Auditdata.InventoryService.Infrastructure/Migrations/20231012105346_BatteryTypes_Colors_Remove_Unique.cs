using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Auditdata.InventoryService.Infrastructure.Migrations;

/// <inheritdoc />
public partial class BatteryTypes_Colors_Remove_Unique : Migration
{
    /// <inheritdoc />
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.DropIndex(
            name: "IX_Colors_TenantId_Name_IsDeleted",
            table: "Colors");

        migrationBuilder.DropIndex(
            name: "IX_BatteryTypes_TenantId_Name_IsDeleted",
            table: "BatteryTypes");
    }

    /// <inheritdoc />
    protected override void Down(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.CreateIndex(
            name: "IX_Colors_TenantId_Name_IsDeleted",
            table: "Colors",
            columns: new[] { "TenantId", "Name", "IsDeleted" },
            unique: true);

        migrationBuilder.CreateIndex(
            name: "IX_BatteryTypes_TenantId_Name_IsDeleted",
            table: "BatteryTypes",
            columns: new[] { "TenantId", "Name", "IsDeleted" },
            unique: true);
    }
}
