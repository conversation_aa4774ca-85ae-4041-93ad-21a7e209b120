using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Auditdata.InventoryService.Infrastructure.Migrations;

public partial class AddStockProductItem : Migration
{
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.DropIndex(
            name: "IX_StockProducts_SerialNumber",
            table: "StockProducts");

        migrationBuilder.DropColumn(
            name: "SerialNumber",
            table: "StockProducts");

        migrationBuilder.RenameColumn(
            name: "Status",
            table: "StockProducts",
            newName: "Quantity");

        migrationBuilder.AddColumn<bool>(
            name: "IsActive",
            table: "StockProducts",
            type: "bit",
            nullable: false,
            defaultValue: false);

        migrationBuilder.AddColumn<bool>(
            name: "IsSallable",
            table: "StockProducts",
            type: "bit",
            nullable: false,
            defaultValue: false);

        migrationBuilder.CreateTable(
            name: "StockProductItems",
            columns: table => new
            {
                Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                StockProductId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                SerialNumber = table.Column<string>(type: "nvarchar(450)", nullable: true),
                Status = table.Column<int>(type: "int", nullable: false),
                CreatedOn = table.Column<DateTime>(type: "datetime2", nullable: false),
                ModifiedOn = table.Column<DateTime>(type: "datetime2", nullable: true),
                TenantId = table.Column<Guid>(type: "uniqueidentifier", nullable: false)
            },
            constraints: table =>
            {
                table.PrimaryKey("PK_StockProductItems", x => x.Id);
                table.ForeignKey(
                    name: "FK_StockProductItems_StockProducts_StockProductId",
                    column: x => x.StockProductId,
                    principalTable: "StockProducts",
                    principalColumn: "Id");
            });

        migrationBuilder.CreateIndex(
            name: "IX_StockProductItems_SerialNumber",
            table: "StockProductItems",
            column: "SerialNumber",
            unique: true,
            filter: "[SerialNumber] IS NOT NULL");

        migrationBuilder.CreateIndex(
            name: "IX_StockProductItems_StockProductId",
            table: "StockProductItems",
            column: "StockProductId");
    }

    protected override void Down(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.DropTable(
            name: "StockProductItems");

        migrationBuilder.DropColumn(
            name: "IsActive",
            table: "StockProducts");

        migrationBuilder.DropColumn(
            name: "IsSallable",
            table: "StockProducts");

        migrationBuilder.RenameColumn(
            name: "Quantity",
            table: "StockProducts",
            newName: "Status");

        migrationBuilder.AddColumn<string>(
            name: "SerialNumber",
            table: "StockProducts",
            type: "nvarchar(450)",
            nullable: true);

        migrationBuilder.CreateIndex(
            name: "IX_StockProducts_SerialNumber",
            table: "StockProducts",
            column: "SerialNumber",
            unique: true,
            filter: "[SerialNumber] IS NOT NULL");
    }
}
