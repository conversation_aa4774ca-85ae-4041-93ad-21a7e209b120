using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Auditdata.InventoryService.Infrastructure.Migrations;

public partial class AddProductBatteryHearingAidsTypesSeeding : Migration
{
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.InsertData(
            table: "BatteryTypes",
            columns: new[] { "Id", "IsDeleted", "Name" },
            values: new object[,]
            {
                { new Guid("6b84bed9-740c-43e7-9855-fd8275231a43"), false, "675" },
                { new Guid("975340bb-d5fd-41e2-9bab-f6f06109edc9"), false, "10" },
                { new Guid("ad270856-4d29-4e3e-b0bc-e9089d196fab"), false, "312" },
                { new Guid("c33221bc-e71a-4b85-b92d-8632358cd37d"), false, "13" }
            });

        migrationBuilder.InsertData(
            table: "HearingAidTypes",
            columns: new[] { "Id", "IsDeleted", "Name" },
            values: new object[,]
            {
                { new Guid("14b2517f-33a2-4a46-bed3-a33cea6dbe92"), false, "ITC" },
                { new Guid("49dbb0e9-bb6b-4ef8-be24-88fdefafb920"), false, "IIC" },
                { new Guid("8267c588-abf9-436a-8160-a989b34c3a19"), false, "ITE" },
                { new Guid("8bb7392c-fce0-4522-97c8-1d246b136d01"), false, "RIC" },
                { new Guid("b350b034-3c83-45d9-af86-a1a0554684b9"), false, "Power" },
                { new Guid("bdf916f7-cfb6-4016-bd05-0d1c2e2c5b10"), false, "BTE" },
                { new Guid("c9ec550f-4673-483f-b085-eda334887e0c"), false, "Open" },
                { new Guid("eadf48c0-7f9e-4158-90e5-aa976904cae0"), false, "CIC" }
            });

        migrationBuilder.UpdateData(
            table: "ProductTypes",
            keyColumn: "Id",
            keyValue: new Guid("08300ca2-6a6a-4a25-ad16-3818e9550847"),
            column: "Name",
            value: "Battery");

        migrationBuilder.UpdateData(
            table: "ProductTypes",
            keyColumn: "Id",
            keyValue: new Guid("30931946-6b02-4d5b-b645-77dcd545ff8c"),
            column: "Name",
            value: "Service");

        migrationBuilder.UpdateData(
            table: "ProductTypes",
            keyColumn: "Id",
            keyValue: new Guid("47acc31b-22af-48dd-aca7-09e3b4f1127c"),
            column: "Name",
            value: "Earmold");

        migrationBuilder.UpdateData(
            table: "ProductTypes",
            keyColumn: "Id",
            keyValue: new Guid("9c26d46a-3b38-4d34-b723-387e1deaa952"),
            column: "Name",
            value: "Accessories");

        migrationBuilder.UpdateData(
            table: "ProductTypes",
            keyColumn: "Id",
            keyValue: new Guid("a3e47357-481e-421a-ba04-d6f1946d9e69"),
            column: "Name",
            value: "Repair type");

        migrationBuilder.UpdateData(
            table: "ProductTypes",
            keyColumn: "Id",
            keyValue: new Guid("b045b8b4-0b41-418f-b2d6-0724ea8177b7"),
            column: "Name",
            value: "Hearing Aids");

        migrationBuilder.InsertData(
            table: "ProductTypes",
            columns: new[] { "Id", "IsDeleted", "Name" },
            values: new object[,]
            {
                { new Guid("a9dc025b-75fd-4b30-b1d3-ca58fb7fb270"), false, "Receiver" },
                { new Guid("f2ae794b-734c-4c41-ad84-a84f54000167"), false, "Remote" }
            });
    }

    protected override void Down(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.DeleteData(
            table: "BatteryTypes",
            keyColumn: "Id",
            keyValue: new Guid("6b84bed9-740c-43e7-9855-fd8275231a43"));

        migrationBuilder.DeleteData(
            table: "BatteryTypes",
            keyColumn: "Id",
            keyValue: new Guid("975340bb-d5fd-41e2-9bab-f6f06109edc9"));

        migrationBuilder.DeleteData(
            table: "BatteryTypes",
            keyColumn: "Id",
            keyValue: new Guid("ad270856-4d29-4e3e-b0bc-e9089d196fab"));

        migrationBuilder.DeleteData(
            table: "BatteryTypes",
            keyColumn: "Id",
            keyValue: new Guid("c33221bc-e71a-4b85-b92d-8632358cd37d"));

        migrationBuilder.DeleteData(
            table: "HearingAidTypes",
            keyColumn: "Id",
            keyValue: new Guid("14b2517f-33a2-4a46-bed3-a33cea6dbe92"));

        migrationBuilder.DeleteData(
            table: "HearingAidTypes",
            keyColumn: "Id",
            keyValue: new Guid("49dbb0e9-bb6b-4ef8-be24-88fdefafb920"));

        migrationBuilder.DeleteData(
            table: "HearingAidTypes",
            keyColumn: "Id",
            keyValue: new Guid("8267c588-abf9-436a-8160-a989b34c3a19"));

        migrationBuilder.DeleteData(
            table: "HearingAidTypes",
            keyColumn: "Id",
            keyValue: new Guid("8bb7392c-fce0-4522-97c8-1d246b136d01"));

        migrationBuilder.DeleteData(
            table: "HearingAidTypes",
            keyColumn: "Id",
            keyValue: new Guid("b350b034-3c83-45d9-af86-a1a0554684b9"));

        migrationBuilder.DeleteData(
            table: "HearingAidTypes",
            keyColumn: "Id",
            keyValue: new Guid("bdf916f7-cfb6-4016-bd05-0d1c2e2c5b10"));

        migrationBuilder.DeleteData(
            table: "HearingAidTypes",
            keyColumn: "Id",
            keyValue: new Guid("c9ec550f-4673-483f-b085-eda334887e0c"));

        migrationBuilder.DeleteData(
            table: "HearingAidTypes",
            keyColumn: "Id",
            keyValue: new Guid("eadf48c0-7f9e-4158-90e5-aa976904cae0"));

        migrationBuilder.DeleteData(
            table: "ProductTypes",
            keyColumn: "Id",
            keyValue: new Guid("a9dc025b-75fd-4b30-b1d3-ca58fb7fb270"));

        migrationBuilder.DeleteData(
            table: "ProductTypes",
            keyColumn: "Id",
            keyValue: new Guid("f2ae794b-734c-4c41-ad84-a84f54000167"));

        migrationBuilder.UpdateData(
            table: "ProductTypes",
            keyColumn: "Id",
            keyValue: new Guid("08300ca2-6a6a-4a25-ad16-3818e9550847"),
            column: "Name",
            value: "Hearing aids");

        migrationBuilder.UpdateData(
            table: "ProductTypes",
            keyColumn: "Id",
            keyValue: new Guid("30931946-6b02-4d5b-b645-77dcd545ff8c"),
            column: "Name",
            value: "Hearing aids accessories");

        migrationBuilder.UpdateData(
            table: "ProductTypes",
            keyColumn: "Id",
            keyValue: new Guid("47acc31b-22af-48dd-aca7-09e3b4f1127c"),
            column: "Name",
            value: "Consumables");

        migrationBuilder.UpdateData(
            table: "ProductTypes",
            keyColumn: "Id",
            keyValue: new Guid("9c26d46a-3b38-4d34-b723-387e1deaa952"),
            column: "Name",
            value: "Services");

        migrationBuilder.UpdateData(
            table: "ProductTypes",
            keyColumn: "Id",
            keyValue: new Guid("a3e47357-481e-421a-ba04-d6f1946d9e69"),
            column: "Name",
            value: "Hearing aid batteries");

        migrationBuilder.UpdateData(
            table: "ProductTypes",
            keyColumn: "Id",
            keyValue: new Guid("b045b8b4-0b41-418f-b2d6-0724ea8177b7"),
            column: "Name",
            value: "Ear moulds");
    }
}
