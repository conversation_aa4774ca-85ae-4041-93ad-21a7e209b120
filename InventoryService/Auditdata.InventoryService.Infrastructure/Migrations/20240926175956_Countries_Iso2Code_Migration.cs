using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Auditdata.InventoryService.Infrastructure.Migrations;

/// <inheritdoc />
public partial class Countries_Iso2Code_Migration : Migration
{
    /// <inheritdoc />
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.Sql(@"
            update [Countries] set [Iso2Code] = 'AF' where [Name] = 'Afghanistan';
            update [Countries] set [Iso2Code] = 'AL' where [Name] = 'Albania';
            update [Countries] set [Iso2Code] = 'DZ' where [Name] = 'Algeria';
            update [Countries] set [Iso2Code] = 'AR' where [Name] = 'Argentina';
            update [Countries] set [Iso2Code] = 'AM' where [Name] = 'Armenia';
            update [Countries] set [Iso2Code] = 'AU' where [Name] = 'Australia';
            update [Countries] set [Iso2Code] = 'AT' where [Name] = 'Austria';
            update [Countries] set [Iso2Code] = 'AZ' where [Name] = 'Azerbaijan';
            update [Countries] set [Iso2Code] = 'BH' where [Name] = 'Bahrain';
            update [Countries] set [Iso2Code] = 'BD' where [Name] = 'Bangladesh';
            update [Countries] set [Iso2Code] = 'BY' where [Name] = 'Belarus';
            update [Countries] set [Iso2Code] = 'BE' where [Name] = 'Belgium';
            update [Countries] set [Iso2Code] = 'BZ' where [Name] = 'Belize';
            update [Countries] set [Iso2Code] = 'BT' where [Name] = 'Bhutan';
            update [Countries] set [Iso2Code] = 'BO' where [Name] = 'Bolivia';
            update [Countries] set [Iso2Code] = 'BA' where [Name] = 'Bosnia & Herzegovina';
            update [Countries] set [Iso2Code] = 'BW' where [Name] = 'Botswana';
            update [Countries] set [Iso2Code] = 'BR' where [Name] = 'Brazil';
            update [Countries] set [Iso2Code] = 'BN' where [Name] = 'Brunei';
            update [Countries] set [Iso2Code] = 'BG' where [Name] = 'Bulgaria';
            update [Countries] set [Iso2Code] = 'KH' where [Name] = 'Cambodia';
            update [Countries] set [Iso2Code] = 'CM' where [Name] = 'Cameroon';
            update [Countries] set [Iso2Code] = 'CA' where [Name] = 'Canada';
            update [Countries] set [Iso2Code] = 'CL' where [Name] = 'Chile';
            update [Countries] set [Iso2Code] = 'CN' where [Name] = 'China';
            update [Countries] set [Iso2Code] = 'CO' where [Name] = 'Colombia';
            update [Countries] set [Iso2Code] = 'CD' where [Name] = 'Congo (DRC)';
            update [Countries] set [Iso2Code] = 'CR' where [Name] = 'Costa Rica';
            update [Countries] set [Iso2Code] = 'CI' where [Name] = 'Côte d’Ivoire';
            update [Countries] set [Iso2Code] = 'HR' where [Name] = 'Croatia';
            update [Countries] set [Iso2Code] = 'CU' where [Name] = 'Cuba';
            update [Countries] set [Iso2Code] = 'CZ' where [Name] = 'Czechia';
            update [Countries] set [Iso2Code] = 'DK' where [Name] = 'Denmark';
            update [Countries] set [Iso2Code] = 'DO' where [Name] = 'Dominican Republic';
            update [Countries] set [Iso2Code] = 'EC' where [Name] = 'Ecuador';
            update [Countries] set [Iso2Code] = 'EG' where [Name] = 'Egypt';
            update [Countries] set [Iso2Code] = 'SV' where [Name] = 'El Salvador';
            update [Countries] set [Iso2Code] = 'ER' where [Name] = 'Eritrea';
            update [Countries] set [Iso2Code] = 'EE' where [Name] = 'Estonia';
            update [Countries] set [Iso2Code] = 'ET' where [Name] = 'Ethiopia';
            update [Countries] set [Iso2Code] = 'FO' where [Name] = 'Faroe Islands';
            update [Countries] set [Iso2Code] = 'FI' where [Name] = 'Finland';
            update [Countries] set [Iso2Code] = 'FR' where [Name] = 'France';
            update [Countries] set [Iso2Code] = 'GE' where [Name] = 'Georgia';
            update [Countries] set [Iso2Code] = 'DE' where [Name] = 'Germany';
            update [Countries] set [Iso2Code] = 'GR' where [Name] = 'Greece';
            update [Countries] set [Iso2Code] = 'GL' where [Name] = 'Greenland';
            update [Countries] set [Iso2Code] = 'GT' where [Name] = 'Guatemala';
            update [Countries] set [Iso2Code] = 'HT' where [Name] = 'Haiti';
            update [Countries] set [Iso2Code] = 'HN' where [Name] = 'Honduras';
            update [Countries] set [Iso2Code] = 'HK' where [Name] = 'Hong Kong SAR';
            update [Countries] set [Iso2Code] = 'HU' where [Name] = 'Hungary';
            update [Countries] set [Iso2Code] = 'IS' where [Name] = 'Iceland';
            update [Countries] set [Iso2Code] = 'IN' where [Name] = 'India';
            update [Countries] set [Iso2Code] = 'ID' where [Name] = 'Indonesia';
            update [Countries] set [Iso2Code] = 'IR' where [Name] = 'Iran';
            update [Countries] set [Iso2Code] = 'IQ' where [Name] = 'Iraq';
            update [Countries] set [Iso2Code] = 'IE' where [Name] = 'Ireland';
            update [Countries] set [Iso2Code] = 'IL' where [Name] = 'Israel';
            update [Countries] set [Iso2Code] = 'IT' where [Name] = 'Italy';
            update [Countries] set [Iso2Code] = 'JM' where [Name] = 'Jamaica';
            update [Countries] set [Iso2Code] = 'JP' where [Name] = 'Japan';
            update [Countries] set [Iso2Code] = 'JO' where [Name] = 'Jordan';
            update [Countries] set [Iso2Code] = 'KZ' where [Name] = 'Kazakhstan';
            update [Countries] set [Iso2Code] = 'KE' where [Name] = 'Kenya';
            update [Countries] set [Iso2Code] = 'KR' where [Name] = 'Korea';
            update [Countries] set [Iso2Code] = 'KW' where [Name] = 'Kuwait';
            update [Countries] set [Iso2Code] = 'KG' where [Name] = 'Kyrgyzstan';
            update [Countries] set [Iso2Code] = 'LA' where [Name] = 'Laos';
            update [Countries] set [Iso2Code] = 'LV' where [Name] = 'Latvia';
            update [Countries] set [Iso2Code] = 'LB' where [Name] = 'Lebanon';
            update [Countries] set [Iso2Code] = 'LY' where [Name] = 'Libya';
            update [Countries] set [Iso2Code] = 'LI' where [Name] = 'Liechtenstein';
            update [Countries] set [Iso2Code] = 'LT' where [Name] = 'Lithuania';
            update [Countries] set [Iso2Code] = 'LU' where [Name] = 'Luxembourg';
            update [Countries] set [Iso2Code] = 'MY' where [Name] = 'Malaysia';
            update [Countries] set [Iso2Code] = 'MV' where [Name] = 'Maldives';
            update [Countries] set [Iso2Code] = 'ML' where [Name] = 'Mali';
            update [Countries] set [Iso2Code] = 'MT' where [Name] = 'Malta';
            update [Countries] set [Iso2Code] = 'MX' where [Name] = 'Mexico';
            update [Countries] set [Iso2Code] = 'MD' where [Name] = 'Moldova';
            update [Countries] set [Iso2Code] = 'MC' where [Name] = 'Monaco';
            update [Countries] set [Iso2Code] = 'MN' where [Name] = 'Mongolia';
            update [Countries] set [Iso2Code] = 'ME' where [Name] = 'Montenegro';
            update [Countries] set [Iso2Code] = 'MA' where [Name] = 'Morocco';
            update [Countries] set [Iso2Code] = 'MM' where [Name] = 'Myanmar';
            update [Countries] set [Iso2Code] = 'NP' where [Name] = 'Nepal';
            update [Countries] set [Iso2Code] = 'NL' where [Name] = 'Netherlands';
            update [Countries] set [Iso2Code] = 'NZ' where [Name] = 'New Zealand';
            update [Countries] set [Iso2Code] = 'NI' where [Name] = 'Nicaragua';
            update [Countries] set [Iso2Code] = 'NG' where [Name] = 'Nigeria';
            update [Countries] set [Iso2Code] = 'MK' where [Name] = 'North Macedonia';
            update [Countries] set [Iso2Code] = 'NO' where [Name] = 'Norway';
            update [Countries] set [Iso2Code] = 'OM' where [Name] = 'Oman';
            update [Countries] set [Iso2Code] = 'PK' where [Name] = 'Pakistan';
            update [Countries] set [Iso2Code] = 'PA' where [Name] = 'Panama';
            update [Countries] set [Iso2Code] = 'PY' where [Name] = 'Paraguay';
            update [Countries] set [Iso2Code] = 'PE' where [Name] = 'Peru';
            update [Countries] set [Iso2Code] = 'PH' where [Name] = 'Philippines';
            update [Countries] set [Iso2Code] = 'PL' where [Name] = 'Poland';
            update [Countries] set [Iso2Code] = 'PT' where [Name] = 'Portugal';
            update [Countries] set [Iso2Code] = 'PR' where [Name] = 'Puerto Rico';
            update [Countries] set [Iso2Code] = 'QA' where [Name] = 'Qatar';
            update [Countries] set [Iso2Code] = 'RE' where [Name] = 'Réunion';
            update [Countries] set [Iso2Code] = 'RO' where [Name] = 'Romania';
            update [Countries] set [Iso2Code] = 'RU' where [Name] = 'Russia';
            update [Countries] set [Iso2Code] = 'RW' where [Name] = 'Rwanda';
            update [Countries] set [Iso2Code] = 'SA' where [Name] = 'Saudi Arabia';
            update [Countries] set [Iso2Code] = 'SN' where [Name] = 'Senegal';
            update [Countries] set [Iso2Code] = 'RS' where [Name] = 'Serbia';
            update [Countries] set [Iso2Code] = 'SG' where [Name] = 'Singapore';
            update [Countries] set [Iso2Code] = 'SK' where [Name] = 'Slovakia';
            update [Countries] set [Iso2Code] = 'SI' where [Name] = 'Slovenia';
            update [Countries] set [Iso2Code] = 'SO' where [Name] = 'Somalia';
            update [Countries] set [Iso2Code] = 'ZA' where [Name] = 'South Africa';
            update [Countries] set [Iso2Code] = 'ES' where [Name] = 'Spain';
            update [Countries] set [Iso2Code] = 'LK' where [Name] = 'Sri Lanka';
            update [Countries] set [Iso2Code] = 'SE' where [Name] = 'Sweden';
            update [Countries] set [Iso2Code] = 'CH' where [Name] = 'Switzerland';
            update [Countries] set [Iso2Code] = 'SY' where [Name] = 'Syria';
            update [Countries] set [Iso2Code] = 'TH' where [Name] = 'Thailand';
            update [Countries] set [Iso2Code] = 'TT' where [Name] = 'Trinidad & Tobago';
            update [Countries] set [Iso2Code] = 'TN' where [Name] = 'Tunisia';
            update [Countries] set [Iso2Code] = 'TR' where [Name] = 'Turkey';
            update [Countries] set [Iso2Code] = 'TM' where [Name] = 'Turkmenistan';
            update [Countries] set [Iso2Code] = 'UA' where [Name] = 'Ukraine';
            update [Countries] set [Iso2Code] = 'AE' where [Name] = 'United Arab Emirates';
            update [Countries] set [Iso2Code] = 'GB' where [Name] = 'United Kingdom';
            update [Countries] set [Iso2Code] = 'US' where [Name] = 'United States';
            update [Countries] set [Iso2Code] = 'UY' where [Name] = 'Uruguay';
            update [Countries] set [Iso2Code] = 'UZ' where [Name] = 'Uzbekistan';
            update [Countries] set [Iso2Code] = 'VE' where [Name] = 'Venezuela';
            update [Countries] set [Iso2Code] = 'VN' where [Name] = 'Vietnam';
            update [Countries] set [Iso2Code] = 'YE' where [Name] = 'Yemen';
            update [Countries] set [Iso2Code] = 'ZW' where [Name] = 'Zimbabwe';
            update [Countries] set [Iso2Code] = 'GB-NIR' where [Name] = 'Northern Ireland';
            update [Countries] set [Iso2Code] = 'GB-SCT' where [Name] = 'Scotland';
            update [Countries] set [Iso2Code] = 'GB-ENG' where [Name] = 'England';
            update [Countries] set [Iso2Code] = 'GB-WLS' where [Name] = 'Wales';
        ");
    }

    /// <inheritdoc />
    protected override void Down(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.Sql(@"
            update [Countries] set [Iso2Code] = '' where [Iso2Code] != '';
        ");
    }
}
