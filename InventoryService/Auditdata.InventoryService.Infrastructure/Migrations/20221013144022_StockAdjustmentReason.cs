using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Auditdata.InventoryService.Infrastructure.Migrations;

public partial class StockAdjustmentReason : Migration
{
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.AddColumn<Guid>(
            name: "NegativeAdjustmentReasonId",
            table: "StockProductItems",
            type: "uniqueidentifier",
            nullable: true);

        migrationBuilder.CreateTable(
            name: "StockAdjustmentReasons",
            columns: table => new
            {
                Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                Name = table.Column<string>(type: "nvarchar(max)", nullable: false),
                IsActive = table.Column<bool>(type: "bit", nullable: false),
                TenantId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                IsDeleted = table.Column<bool>(type: "bit", nullable: false)
            },
            constraints: table =>
            {
                table.PrimaryKey("PK_StockAdjustmentReasons", x => x.Id);
            });

        migrationBuilder.CreateIndex(
            name: "IX_StockProductItems_NegativeAdjustmentReasonId",
            table: "StockProductItems",
            column: "NegativeAdjustmentReasonId");

        migrationBuilder.AddForeignKey(
            name: "FK_StockProductItems_StockAdjustmentReasons_NegativeAdjustmentReasonId",
            table: "StockProductItems",
            column: "NegativeAdjustmentReasonId",
            principalTable: "StockAdjustmentReasons",
            principalColumn: "Id");
    }

    protected override void Down(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.DropForeignKey(
            name: "FK_StockProductItems_StockAdjustmentReasons_NegativeAdjustmentReasonId",
            table: "StockProductItems");

        migrationBuilder.DropTable(
            name: "StockAdjustmentReasons");

        migrationBuilder.DropIndex(
            name: "IX_StockProductItems_NegativeAdjustmentReasonId",
            table: "StockProductItems");

        migrationBuilder.DropColumn(
            name: "NegativeAdjustmentReasonId",
            table: "StockProductItems");
    }
}
