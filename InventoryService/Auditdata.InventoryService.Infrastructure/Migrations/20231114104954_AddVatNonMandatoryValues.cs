using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Auditdata.InventoryService.Infrastructure.Migrations;

/// <inheritdoc />
public partial class AddVatNonMandatoryValues : Migration
{
    /// <inheritdoc />
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.AlterColumn<decimal>(
            name: "SecondVAT",
            table: "Products",
            type: "decimal(18,6)",
            precision: 18,
            scale: 6,
            nullable: true,
            defaultValue: 0m,
            oldClrType: typeof(decimal),
            oldType: "decimal(18,6)",
            oldPrecision: 18,
            oldScale: 6);

        migrationBuilder.AlterColumn<decimal>(
            name: "FirstVAT",
            table: "Products",
            type: "decimal(18,6)",
            precision: 18,
            scale: 6,
            nullable: true,
            defaultValue: 0m,
            oldClrType: typeof(decimal),
            oldType: "decimal(18,6)",
            oldPrecision: 18,
            oldScale: 6);
    }

    /// <inheritdoc />
    protected override void Down(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.AlterColumn<decimal>(
            name: "SecondVAT",
            table: "Products",
            type: "decimal(18,6)",
            precision: 18,
            scale: 6,
            nullable: false,
            defaultValue: 0m,
            oldClrType: typeof(decimal),
            oldType: "decimal(18,6)",
            oldPrecision: 18,
            oldScale: 6,
            oldNullable: true,
            oldDefaultValue: 0m);

        migrationBuilder.AlterColumn<decimal>(
            name: "FirstVAT",
            table: "Products",
            type: "decimal(18,6)",
            precision: 18,
            scale: 6,
            nullable: false,
            defaultValue: 0m,
            oldClrType: typeof(decimal),
            oldType: "decimal(18,6)",
            oldPrecision: 18,
            oldScale: 6,
            oldNullable: true,
            oldDefaultValue: 0m);
    }
}
