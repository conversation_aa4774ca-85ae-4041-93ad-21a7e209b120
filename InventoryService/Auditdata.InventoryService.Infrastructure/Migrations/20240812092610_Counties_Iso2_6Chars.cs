using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Auditdata.InventoryService.Infrastructure.Migrations;

/// <inheritdoc />
public partial class Counties_Iso2_6Chars : Migration
{
    /// <inheritdoc />
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.AlterColumn<string>(
            name: "Iso2Code",
            table: "Countries",
            type: "nvarchar(6)",
            maxLength: 6,
            nullable: false,
            oldClrType: typeof(string),
            oldType: "nvarchar(2)",
            oldMaxLength: 2);
    }

    /// <inheritdoc />
    protected override void Down(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.AlterColumn<string>(
            name: "Iso2Code",
            table: "Countries",
            type: "nvarchar(2)",
            maxLength: 2,
            nullable: false,
            oldClrType: typeof(string),
            oldType: "nvarchar(6)",
            oldMaxLength: 6);
    }
}
