using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Auditdata.InventoryService.Infrastructure.Migrations;

/// <inheritdoc />
public partial class CPTCodes_Drop_Code_IsDeleted_Index : Migration
{
    /// <inheritdoc />
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.DropIndex(
            name: "IX_CPTCodes_Code_TenantId_IsDeleted",
            table: "CPTCodes");
    }

    /// <inheritdoc />
    protected override void Down(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.CreateIndex(
            name: "IX_CPTCodes_Code_TenantId_IsDeleted",
            table: "CPTCodes",
            columns: new[] { "Code", "TenantId", "IsDeleted" },
            unique: true);
    }
}
