using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Auditdata.InventoryService.Infrastructure.Migrations;

/// <inheritdoc />
public partial class AttributeValue_ValueNameToValue : Migration
{
    /// <inheritdoc />
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.Sql(@"
            UPDATE a
            SET a.[Values] = REPLACE([Values],'ValueName','Value')
            FROM Attributes a
            WHERE [Values] IS NOT NULL;
        ");
        migrationBuilder.Sql(@"
            UPDATE pa
            SET pa.[Value] = REPLACE([Value],'ValueName','Value')
            FROM ProductAttributes pa
            WHERE pa.Value IS NOT NULL;
        ");
    }

    /// <inheritdoc />
    protected override void Down(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.Sql(@"
            UPDATE a
            SET a.[Values] = REPLACE([Values], 'Value','ValueName')
            FROM Attributes a
            WHERE [Values] IS NOT NULL;
        ");
        migrationBuilder.Sql(@"
            UPDATE pa
            SET pa.[Value] = REPLACE([Value], 'Value','ValueName')
            FROM ProductAttributes pa
            WHERE pa.Value IS NOT NULL;
        ");
    }
}
