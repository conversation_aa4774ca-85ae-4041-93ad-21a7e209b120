using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Auditdata.InventoryService.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class DeleteForeignKeyForState : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Manufacturers_States_StateId",
                table: "Manufacturers");

            migrationBuilder.DropForeignKey(
                name: "FK_Suppliers_States_StateId",
                table: "Suppliers");

            migrationBuilder.DropIndex(
                name: "IX_Suppliers_StateId",
                table: "Suppliers");

            migrationBuilder.DropIndex(
                name: "IX_Manufacturers_StateId",
                table: "Manufacturers");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateIndex(
                name: "IX_Suppliers_StateId",
                table: "Suppliers",
                column: "StateId");

            migrationBuilder.CreateIndex(
                name: "IX_Manufacturers_StateId",
                table: "Manufacturers",
                column: "StateId");

            migrationBuilder.AddForeignKey(
                name: "FK_Manufacturers_States_StateId",
                table: "Manufacturers",
                column: "StateId",
                principalTable: "States",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Suppliers_States_StateId",
                table: "Suppliers",
                column: "StateId",
                principalTable: "States",
                principalColumn: "Id");
        }
    }
}
