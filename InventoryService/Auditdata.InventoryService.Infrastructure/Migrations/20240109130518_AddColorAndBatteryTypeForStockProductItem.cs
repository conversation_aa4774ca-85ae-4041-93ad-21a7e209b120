using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Auditdata.InventoryService.Infrastructure.Migrations;

/// <inheritdoc />
public partial class AddColorAndBatteryTypeForStockProductItem : Migration
{
    /// <inheritdoc />
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.DropTable(
            name: "StockProductItemBatteryTypes");

        migrationBuilder.DropTable(
            name: "StockProductItemColors");

        migrationBuilder.AddColumn<Guid>(
            name: "BatteryTypeId",
            table: "StockProductItems",
            type: "uniqueidentifier",
            nullable: true);

        migrationBuilder.AddColumn<Guid>(
            name: "ColorId",
            table: "StockProductItems",
            type: "uniqueidentifier",
            nullable: true);

        migrationBuilder.CreateIndex(
            name: "IX_StockProductItems_BatteryTypeId",
            table: "StockProductItems",
            column: "BatteryTypeId");

        migrationBuilder.CreateIndex(
            name: "IX_StockProductItems_ColorId",
            table: "StockProductItems",
            column: "ColorId");

        migrationBuilder.AddForeignKey(
            name: "FK_StockProductItems_BatteryTypes_BatteryTypeId",
            table: "StockProductItems",
            column: "BatteryTypeId",
            principalTable: "BatteryTypes",
            principalColumn: "Id");

        migrationBuilder.AddForeignKey(
            name: "FK_StockProductItems_Colors_ColorId",
            table: "StockProductItems",
            column: "ColorId",
            principalTable: "Colors",
            principalColumn: "Id");
    }

    /// <inheritdoc />
    protected override void Down(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.DropForeignKey(
            name: "FK_StockProductItems_BatteryTypes_BatteryTypeId",
            table: "StockProductItems");

        migrationBuilder.DropForeignKey(
            name: "FK_StockProductItems_Colors_ColorId",
            table: "StockProductItems");

        migrationBuilder.DropIndex(
            name: "IX_StockProductItems_BatteryTypeId",
            table: "StockProductItems");

        migrationBuilder.DropIndex(
            name: "IX_StockProductItems_ColorId",
            table: "StockProductItems");

        migrationBuilder.DropColumn(
            name: "BatteryTypeId",
            table: "StockProductItems");

        migrationBuilder.DropColumn(
            name: "ColorId",
            table: "StockProductItems");

        migrationBuilder.CreateTable(
            name: "StockProductItemBatteryTypes",
            columns: table => new
            {
                Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                BatteryTypeId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                StockProductItemId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                ChangeDate = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                ChangedBy = table.Column<string>(type: "nvarchar(max)", nullable: true),
                CreatedBy = table.Column<string>(type: "nvarchar(max)", nullable: false, defaultValue: "admin"),
                CreationDate = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                TenantId = table.Column<Guid>(type: "uniqueidentifier", nullable: false)
            },
            constraints: table =>
            {
                table.PrimaryKey("PK_StockProductItemBatteryTypes", x => x.Id);
                table.ForeignKey(
                    name: "FK_StockProductItemBatteryTypes_BatteryTypes_BatteryTypeId",
                    column: x => x.BatteryTypeId,
                    principalTable: "BatteryTypes",
                    principalColumn: "Id",
                    onDelete: ReferentialAction.Cascade);
                table.ForeignKey(
                    name: "FK_StockProductItemBatteryTypes_StockProductItems_StockProductItemId",
                    column: x => x.StockProductItemId,
                    principalTable: "StockProductItems",
                    principalColumn: "Id",
                    onDelete: ReferentialAction.Cascade);
            });

        migrationBuilder.CreateTable(
            name: "StockProductItemColors",
            columns: table => new
            {
                Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                ColorId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                StockProductItemId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                ChangeDate = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                ChangedBy = table.Column<string>(type: "nvarchar(max)", nullable: true),
                CreatedBy = table.Column<string>(type: "nvarchar(max)", nullable: false, defaultValue: "admin"),
                CreationDate = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                TenantId = table.Column<Guid>(type: "uniqueidentifier", nullable: false)
            },
            constraints: table =>
            {
                table.PrimaryKey("PK_StockProductItemColors", x => x.Id);
                table.ForeignKey(
                    name: "FK_StockProductItemColors_Colors_ColorId",
                    column: x => x.ColorId,
                    principalTable: "Colors",
                    principalColumn: "Id",
                    onDelete: ReferentialAction.Cascade);
                table.ForeignKey(
                    name: "FK_StockProductItemColors_StockProductItems_StockProductItemId",
                    column: x => x.StockProductItemId,
                    principalTable: "StockProductItems",
                    principalColumn: "Id",
                    onDelete: ReferentialAction.Cascade);
            });

        migrationBuilder.CreateIndex(
            name: "IX_StockProductItemBatteryTypes_BatteryTypeId",
            table: "StockProductItemBatteryTypes",
            column: "BatteryTypeId");

        migrationBuilder.CreateIndex(
            name: "IX_StockProductItemBatteryTypes_StockProductItemId",
            table: "StockProductItemBatteryTypes",
            column: "StockProductItemId");

        migrationBuilder.CreateIndex(
            name: "IX_StockProductItemColors_ColorId",
            table: "StockProductItemColors",
            column: "ColorId");

        migrationBuilder.CreateIndex(
            name: "IX_StockProductItemColors_StockProductItemId",
            table: "StockProductItemColors",
            column: "StockProductItemId");
    }
}
