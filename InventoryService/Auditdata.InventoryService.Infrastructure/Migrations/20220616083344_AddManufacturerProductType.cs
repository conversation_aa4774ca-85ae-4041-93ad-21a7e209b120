using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Auditdata.InventoryService.Infrastructure.Migrations;

public partial class AddManufacturerProductType : Migration
{
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.CreateTable(
            name: "ManufacturerProductTypes",
            columns: table => new
            {
                Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                ManufacturerId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                ProductTypeId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                IsDeleted = table.Column<bool>(type: "bit", nullable: false)
            },
            constraints: table =>
            {
                table.PrimaryKey("PK_ManufacturerProductTypes", x => x.Id);
                table.ForeignKey(
                    name: "FK_ManufacturerProductTypes_Manufacturers_ManufacturerId",
                    column: x => x.ManufacturerId,
                    principalTable: "Manufacturers",
                    principalColumn: "Id");
                table.ForeignKey(
                    name: "FK_ManufacturerProductTypes_ProductTypes_ProductTypeId",
                    column: x => x.ProductTypeId,
                    principalTable: "ProductTypes",
                    principalColumn: "Id");
            });

        migrationBuilder.CreateIndex(
            name: "IX_ManufacturerProductTypes_ManufacturerId",
            table: "ManufacturerProductTypes",
            column: "ManufacturerId");

        migrationBuilder.CreateIndex(
            name: "IX_ManufacturerProductTypes_ProductTypeId",
            table: "ManufacturerProductTypes",
            column: "ProductTypeId");
    }

    protected override void Down(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.DropTable(
            name: "ManufacturerProductTypes");
    }
}
