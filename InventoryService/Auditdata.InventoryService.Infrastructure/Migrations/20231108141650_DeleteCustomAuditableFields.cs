using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Auditdata.InventoryService.Infrastructure.Migrations;

/// <inheritdoc />
public partial class DeleteCustomAuditableFields : Migration
{
    /// <inheritdoc />
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        //Migrate data from old fields to new ones
        migrationBuilder.Sql(@"    
    DECLARE @CreatedOnColumnName varchar(20);
    DECLARE @ModifiedOnColumnName varchar(20);

    SET @CreatedOnColumnName = 'CreatedOn';
    SET @ModifiedOnColumnName = 'ModifiedOn';

	declare @sql nvarchar (1000);
	set @sql = N'update Products set CreationDate = ' + @CreatedOnColumnName + ', ChangeDate = ' + @ModifiedOnColumnName +'';
    exec sp_executesql @sql;

	set @sql = N'update Suppliers set CreationDate = ' + @CreatedOnColumnName + ', ChangeDate = ' + @ModifiedOnColumnName +'';
    exec sp_executesql @sql;

	set @sql = N'update Manufacturers set CreationDate = ' + @CreatedOnColumnName + ', ChangeDate = ' + @ModifiedOnColumnName +'';
    exec sp_executesql @sql;

	set @sql = N'update ProductCategoryAccountCodes set CreationDate = ' + @CreatedOnColumnName + ', ChangeDate = ' + @ModifiedOnColumnName +'';
    exec sp_executesql @sql;

	set @sql = N'update StockProducts set CreationDate = ' + @CreatedOnColumnName + ', ChangeDate = ' + @ModifiedOnColumnName +'';
    exec sp_executesql @sql;

	set @sql = N'update StockProductItemLogs set CreationDate = ' + @CreatedOnColumnName + '';
    exec sp_executesql @sql;

	set @sql = N'update StockTransactions set CreationDate = ' + @CreatedOnColumnName + ', ChangeDate = ' + @ModifiedOnColumnName +'';
    exec sp_executesql @sql;");
        
        
        migrationBuilder.DropColumn(
            name: "CreatedOn",
            table: "Suppliers");

        migrationBuilder.DropColumn(
            name: "ModifiedOn",
            table: "Suppliers");

        migrationBuilder.DropColumn(
            name: "CreatedOn",
            table: "StockTransactions");

        migrationBuilder.DropColumn(
            name: "ModifiedOn",
            table: "StockTransactions");

        migrationBuilder.DropColumn(
            name: "CreatedOn",
            table: "StockProducts");

        migrationBuilder.DropColumn(
            name: "ModifiedOn",
            table: "StockProducts");

        migrationBuilder.DropColumn(
            name: "CreatedOn",
            table: "StockProductItems");

        migrationBuilder.DropColumn(
            name: "ModifiedOn",
            table: "StockProductItems");

        migrationBuilder.DropColumn(
            name: "CreatedOn",
            table: "Products");

        migrationBuilder.DropColumn(
            name: "ModifiedOn",
            table: "Products");

        migrationBuilder.DropColumn(
            name: "CreatedOn",
            table: "ProductCategoryAccountCodes");

        migrationBuilder.DropColumn(
            name: "ModifiedOn",
            table: "ProductCategoryAccountCodes");

        migrationBuilder.DropColumn(
            name: "CreatedOn",
            table: "Manufacturers");

        migrationBuilder.DropColumn(
            name: "ModifiedOn",
            table: "Manufacturers");
    }

    /// <inheritdoc />
    protected override void Down(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.AddColumn<DateTime>(
            name: "CreatedOn",
            table: "Suppliers",
            type: "datetime2",
            nullable: false,
            defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

        migrationBuilder.AddColumn<DateTime>(
            name: "ModifiedOn",
            table: "Suppliers",
            type: "datetime2",
            nullable: true);

        migrationBuilder.AddColumn<DateTime>(
            name: "CreatedOn",
            table: "StockTransactions",
            type: "datetime2",
            nullable: false,
            defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

        migrationBuilder.AddColumn<DateTime>(
            name: "ModifiedOn",
            table: "StockTransactions",
            type: "datetime2",
            nullable: true);

        migrationBuilder.AddColumn<DateTime>(
            name: "CreatedOn",
            table: "StockProducts",
            type: "datetime2",
            nullable: false,
            defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

        migrationBuilder.AddColumn<DateTime>(
            name: "ModifiedOn",
            table: "StockProducts",
            type: "datetime2",
            nullable: true);

        migrationBuilder.AddColumn<DateTime>(
            name: "CreatedOn",
            table: "StockProductItems",
            type: "datetime2",
            nullable: false,
            defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

        migrationBuilder.AddColumn<DateTime>(
            name: "ModifiedOn",
            table: "StockProductItems",
            type: "datetime2",
            nullable: true);

        migrationBuilder.AddColumn<DateTime>(
            name: "CreatedOn",
            table: "Products",
            type: "datetime2",
            nullable: false,
            defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

        migrationBuilder.AddColumn<DateTime>(
            name: "ModifiedOn",
            table: "Products",
            type: "datetime2",
            nullable: true);

        migrationBuilder.AddColumn<DateTime>(
            name: "CreatedOn",
            table: "ProductCategoryAccountCodes",
            type: "datetime2",
            nullable: false,
            defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

        migrationBuilder.AddColumn<DateTime>(
            name: "ModifiedOn",
            table: "ProductCategoryAccountCodes",
            type: "datetime2",
            nullable: true);

        migrationBuilder.AddColumn<DateTime>(
            name: "CreatedOn",
            table: "Manufacturers",
            type: "datetime2",
            nullable: false,
            defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

        migrationBuilder.AddColumn<DateTime>(
            name: "ModifiedOn",
            table: "Manufacturers",
            type: "datetime2",
            nullable: true);
    }
}
