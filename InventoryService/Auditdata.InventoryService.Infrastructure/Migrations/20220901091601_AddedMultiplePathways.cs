using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Auditdata.InventoryService.Infrastructure.Migrations;

public partial class AddedMultiplePathways : Migration
{
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.DropForeignKey(
            name: "FK_Products_Pathways_PathwayId",
            table: "Products");

        migrationBuilder.DropIndex(
            name: "IX_Products_PathwayId",
            table: "Products");

        migrationBuilder.DropColumn(
            name: "PathwayId",
            table: "Products");

        migrationBuilder.CreateTable(
            name: "ProductPathways",
            columns: table => new
            {
                Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                ProductId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                PathwayId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                IsDeleted = table.Column<bool>(type: "bit", nullable: false)
            },
            constraints: table =>
            {
                table.PrimaryKey("PK_ProductPathways", x => x.Id);
                table.ForeignKey(
                    name: "FK_ProductPathways_Pathways_PathwayId",
                    column: x => x.PathwayId,
                    principalTable: "Pathways",
                    principalColumn: "Id");
                table.ForeignKey(
                    name: "FK_ProductPathways_Products_ProductId",
                    column: x => x.ProductId,
                    principalTable: "Products",
                    principalColumn: "Id");
            });

        migrationBuilder.CreateIndex(
            name: "IX_ProductPathways_PathwayId",
            table: "ProductPathways",
            column: "PathwayId");

        migrationBuilder.CreateIndex(
            name: "IX_ProductPathways_ProductId",
            table: "ProductPathways",
            column: "ProductId");
    }

    protected override void Down(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.DropTable(
            name: "ProductPathways");

        migrationBuilder.AddColumn<Guid>(
            name: "PathwayId",
            table: "Products",
            type: "uniqueidentifier",
            nullable: true);

        migrationBuilder.CreateIndex(
            name: "IX_Products_PathwayId",
            table: "Products",
            column: "PathwayId");

        migrationBuilder.AddForeignKey(
            name: "FK_Products_Pathways_PathwayId",
            table: "Products",
            column: "PathwayId",
            principalTable: "Pathways",
            principalColumn: "Id");
    }
}
