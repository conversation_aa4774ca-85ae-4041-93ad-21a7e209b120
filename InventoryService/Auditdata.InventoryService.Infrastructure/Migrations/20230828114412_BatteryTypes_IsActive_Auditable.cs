using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Auditdata.InventoryService.Infrastructure.Migrations;

/// <inheritdoc />
public partial class BatteryTypes_IsActive_Auditable : Migration
{
    /// <inheritdoc />
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.AddColumn<string>(
            name: "MessageType",
            table: "OutboxMessage",
            type: "nvarchar(max)",
            nullable: false,
            defaultValue: "");

        migrationBuilder.AddColumn<DateTimeOffset>(
            name: "ChangeDate",
            table: "BatteryTypes",
            type: "datetimeoffset",
            nullable: true);

        migrationBuilder.AddColumn<string>(
            name: "ChangedBy",
            table: "BatteryTypes",
            type: "nvarchar(max)",
            nullable: true);

        migrationBuilder.AddColumn<string>(
            name: "CreatedBy",
            table: "BatteryTypes",
            type: "nvarchar(max)",
            nullable: true);

        migrationBuilder.AddColumn<DateTimeOffset>(
            name: "CreationDate",
            table: "BatteryTypes",
            type: "datetimeoffset",
            nullable: false,
            defaultValue: new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)));

        migrationBuilder.AddColumn<bool>(
            name: "IsActive",
            table: "BatteryTypes",
            type: "bit",
            nullable: false,
            defaultValue: false);

        migrationBuilder.UpdateData(
            table: "BatteryTypes",
            keyColumn: "Id",
            keyValue: new Guid("1b4b298c-f1bf-469a-8286-18471ce7342f"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreatedBy", "CreationDate", "IsActive" },
            values: new object[] { null, null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)), false });

        migrationBuilder.UpdateData(
            table: "BatteryTypes",
            keyColumn: "Id",
            keyValue: new Guid("6b84bed9-740c-43e7-9855-fd8275231a43"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreatedBy", "CreationDate", "IsActive" },
            values: new object[] { null, null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)), false });

        migrationBuilder.UpdateData(
            table: "BatteryTypes",
            keyColumn: "Id",
            keyValue: new Guid("6e213806-80c7-40cf-9605-d58eaf9789ff"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreatedBy", "CreationDate", "IsActive" },
            values: new object[] { null, null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)), false });

        migrationBuilder.UpdateData(
            table: "BatteryTypes",
            keyColumn: "Id",
            keyValue: new Guid("975340bb-d5fd-41e2-9bab-f6f06109edc9"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreatedBy", "CreationDate", "IsActive" },
            values: new object[] { null, null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)), false });

        migrationBuilder.UpdateData(
            table: "BatteryTypes",
            keyColumn: "Id",
            keyValue: new Guid("ad270856-4d29-4e3e-b0bc-e9089d196fab"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreatedBy", "CreationDate", "IsActive" },
            values: new object[] { null, null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)), false });

        migrationBuilder.UpdateData(
            table: "BatteryTypes",
            keyColumn: "Id",
            keyValue: new Guid("c33221bc-e71a-4b85-b92d-8632358cd37d"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreatedBy", "CreationDate", "IsActive" },
            values: new object[] { null, null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)), false });

        migrationBuilder.Sql("UPDATE [BatteryTypes] SET [IsActive] = 1 WHERE [IsDeleted] = 0;");
    }

    /// <inheritdoc />
    protected override void Down(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.DropColumn(
            name: "MessageType",
            table: "OutboxMessage");

        migrationBuilder.DropColumn(
            name: "ChangeDate",
            table: "BatteryTypes");

        migrationBuilder.DropColumn(
            name: "ChangedBy",
            table: "BatteryTypes");

        migrationBuilder.DropColumn(
            name: "CreatedBy",
            table: "BatteryTypes");

        migrationBuilder.DropColumn(
            name: "CreationDate",
            table: "BatteryTypes");

        migrationBuilder.DropColumn(
            name: "IsActive",
            table: "BatteryTypes");
    }
}
