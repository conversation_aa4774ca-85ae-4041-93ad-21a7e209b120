using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Auditdata.InventoryService.Infrastructure.Migrations;

public partial class AddedNhsServiceTariffs : Migration
{
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.AddColumn<Guid>(
            name: "NhsServiceTariffId",
            table: "Products",
            type: "uniqueidentifier",
            nullable: true);

        migrationBuilder.CreateTable(
            name: "NhsServiceTariffs",
            columns: table => new
            {
                Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                Name = table.Column<string>(type: "nvarchar(max)", nullable: false)
            },
            constraints: table =>
            {
                table.PrimaryKey("PK_NhsServiceTariffs", x => x.Id);
            });

        migrationBuilder.InsertData(
            table: "NhsServiceTariffs",
            columns: new[] { "Id", "Name" },
            values: new object[,]
            {
                { new Guid("1d735f43-c93a-476c-9dec-b842006ebcfe"), "Aftercare Tariff" },
                { new Guid("2a45a286-7fd4-445e-a87c-2c8a4d77d5a2"), "Monaural Tariff" },
                { new Guid("59efc888-e8ba-40e4-af5c-21cdb1201c15"), "Binaural Tariff" },
                { new Guid("5a2c1480-729b-4309-af5a-ffd04337a727"), "Assessment Only Tariff" },
                { new Guid("7a955d81-5e05-4fd4-95f8-b31f010e6d9b"), "Wax Removal Tariff" },
                { new Guid("bae28bd8-338f-4679-9000-807e227b7676"), "Replacement hearing aids tariff" }
            });

        migrationBuilder.CreateIndex(
            name: "IX_Products_NhsServiceTariffId",
            table: "Products",
            column: "NhsServiceTariffId");

        migrationBuilder.AddForeignKey(
            name: "FK_Products_NhsServiceTariffs_NhsServiceTariffId",
            table: "Products",
            column: "NhsServiceTariffId",
            principalTable: "NhsServiceTariffs",
            principalColumn: "Id");
    }

    protected override void Down(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.DropForeignKey(
            name: "FK_Products_NhsServiceTariffs_NhsServiceTariffId",
            table: "Products");

        migrationBuilder.DropTable(
            name: "NhsServiceTariffs");

        migrationBuilder.DropIndex(
            name: "IX_Products_NhsServiceTariffId",
            table: "Products");

        migrationBuilder.DropColumn(
            name: "NhsServiceTariffId",
            table: "Products");
    }
}
