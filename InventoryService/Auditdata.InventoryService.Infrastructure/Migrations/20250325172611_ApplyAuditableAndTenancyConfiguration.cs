using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Auditdata.InventoryService.Infrastructure.Migrations;

/// <inheritdoc />
public partial class ApplyAuditableAndTenancyConfiguration : Migration
{
    /// <inheritdoc />
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "Suppliers",
            type: "nvarchar(200)",
            maxLength: 200,
            nullable: false,
            defaultValue: "admin",
            oldClrType: typeof(string),
            oldType: "nvarchar(100)",
            oldMaxLength: 100,
            oldDefaultValue: "admin");

        migrationBuilder.AlterColumn<string>(
            name: "ChangedBy",
            table: "Suppliers",
            type: "nvarchar(200)",
            maxLength: 200,
            nullable: true,
            oldClrType: typeof(string),
            oldType: "nvarchar(100)",
            oldMaxLength: 100,
            oldNullable: true);

        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "StockTransactions",
            type: "nvarchar(200)",
            maxLength: 200,
            nullable: false,
            defaultValue: "admin",
            oldClrType: typeof(string),
            oldType: "nvarchar(100)",
            oldMaxLength: 100,
            oldDefaultValue: "admin");

        migrationBuilder.AlterColumn<string>(
            name: "ChangedBy",
            table: "StockTransactions",
            type: "nvarchar(200)",
            maxLength: 200,
            nullable: true,
            oldClrType: typeof(string),
            oldType: "nvarchar(100)",
            oldMaxLength: 100,
            oldNullable: true);

        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "StockSettings",
            type: "nvarchar(200)",
            maxLength: 200,
            nullable: false,
            defaultValue: "admin",
            oldClrType: typeof(string),
            oldType: "nvarchar(100)",
            oldMaxLength: 100,
            oldDefaultValue: "admin");

        migrationBuilder.AlterColumn<string>(
            name: "ChangedBy",
            table: "StockSettings",
            type: "nvarchar(200)",
            maxLength: 200,
            nullable: true,
            oldClrType: typeof(string),
            oldType: "nvarchar(100)",
            oldMaxLength: 100,
            oldNullable: true);

        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "Stocks",
            type: "nvarchar(200)",
            maxLength: 200,
            nullable: false,
            defaultValue: "admin",
            oldClrType: typeof(string),
            oldType: "nvarchar(100)",
            oldMaxLength: 100,
            oldDefaultValue: "admin");

        migrationBuilder.AlterColumn<string>(
            name: "ChangedBy",
            table: "Stocks",
            type: "nvarchar(200)",
            maxLength: 200,
            nullable: true,
            oldClrType: typeof(string),
            oldType: "nvarchar(100)",
            oldMaxLength: 100,
            oldNullable: true);

        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "StockProducts",
            type: "nvarchar(200)",
            maxLength: 200,
            nullable: false,
            defaultValue: "admin",
            oldClrType: typeof(string),
            oldType: "nvarchar(100)",
            oldMaxLength: 100,
            oldDefaultValue: "admin");

        migrationBuilder.AlterColumn<string>(
            name: "ChangedBy",
            table: "StockProducts",
            type: "nvarchar(200)",
            maxLength: 200,
            nullable: true,
            oldClrType: typeof(string),
            oldType: "nvarchar(100)",
            oldMaxLength: 100,
            oldNullable: true);

        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "StockProductItems",
            type: "nvarchar(200)",
            maxLength: 200,
            nullable: false,
            defaultValue: "admin",
            oldClrType: typeof(string),
            oldType: "nvarchar(100)",
            oldMaxLength: 100,
            oldDefaultValue: "admin");

        migrationBuilder.AlterColumn<string>(
            name: "ChangedBy",
            table: "StockProductItems",
            type: "nvarchar(200)",
            maxLength: 200,
            nullable: true,
            oldClrType: typeof(string),
            oldType: "nvarchar(100)",
            oldMaxLength: 100,
            oldNullable: true);

        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "StockProductItemLogs",
            type: "nvarchar(200)",
            maxLength: 200,
            nullable: false,
            defaultValue: "admin",
            oldClrType: typeof(string),
            oldType: "nvarchar(100)",
            oldMaxLength: 100,
            oldDefaultValue: "admin");

        migrationBuilder.AlterColumn<string>(
            name: "ChangedBy",
            table: "StockProductItemLogs",
            type: "nvarchar(200)",
            maxLength: 200,
            nullable: true,
            oldClrType: typeof(string),
            oldType: "nvarchar(100)",
            oldMaxLength: 100,
            oldNullable: true);

        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "StockProductItemAttributes",
            type: "nvarchar(200)",
            maxLength: 200,
            nullable: false,
            defaultValue: "admin",
            oldClrType: typeof(string),
            oldType: "nvarchar(100)",
            oldMaxLength: 100,
            oldDefaultValue: "admin");

        migrationBuilder.AlterColumn<string>(
            name: "ChangedBy",
            table: "StockProductItemAttributes",
            type: "nvarchar(200)",
            maxLength: 200,
            nullable: true,
            oldClrType: typeof(string),
            oldType: "nvarchar(100)",
            oldMaxLength: 100,
            oldNullable: true);

        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "StockKeepingUnits",
            type: "nvarchar(200)",
            maxLength: 200,
            nullable: false,
            defaultValue: "admin",
            oldClrType: typeof(string),
            oldType: "nvarchar(100)",
            oldMaxLength: 100,
            oldDefaultValue: "admin");

        migrationBuilder.AlterColumn<string>(
            name: "ChangedBy",
            table: "StockKeepingUnits",
            type: "nvarchar(200)",
            maxLength: 200,
            nullable: true,
            oldClrType: typeof(string),
            oldType: "nvarchar(100)",
            oldMaxLength: 100,
            oldNullable: true);

        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "StockAdjustmentReasons",
            type: "nvarchar(200)",
            maxLength: 200,
            nullable: false,
            defaultValue: "admin",
            oldClrType: typeof(string),
            oldType: "nvarchar(100)",
            oldMaxLength: 100,
            oldDefaultValue: "admin");

        migrationBuilder.AlterColumn<string>(
            name: "ChangedBy",
            table: "StockAdjustmentReasons",
            type: "nvarchar(200)",
            maxLength: 200,
            nullable: true,
            oldClrType: typeof(string),
            oldType: "nvarchar(100)",
            oldMaxLength: 100,
            oldNullable: true);

        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "States",
            type: "nvarchar(200)",
            maxLength: 200,
            nullable: false,
            defaultValue: "admin",
            oldClrType: typeof(string),
            oldType: "nvarchar(100)",
            oldMaxLength: 100,
            oldDefaultValue: "admin");

        migrationBuilder.AlterColumn<string>(
            name: "ChangedBy",
            table: "States",
            type: "nvarchar(200)",
            maxLength: 200,
            nullable: true,
            oldClrType: typeof(string),
            oldType: "nvarchar(100)",
            oldMaxLength: 100,
            oldNullable: true);

        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "Skus",
            type: "nvarchar(200)",
            maxLength: 200,
            nullable: false,
            defaultValue: "admin",
            oldClrType: typeof(string),
            oldType: "nvarchar(100)",
            oldMaxLength: 100,
            oldDefaultValue: "admin");

        migrationBuilder.AlterColumn<string>(
            name: "ChangedBy",
            table: "Skus",
            type: "nvarchar(200)",
            maxLength: 200,
            nullable: true,
            oldClrType: typeof(string),
            oldType: "nvarchar(100)",
            oldMaxLength: 100,
            oldNullable: true);

        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "SkuConfigs",
            type: "nvarchar(200)",
            maxLength: 200,
            nullable: false,
            defaultValue: "admin",
            oldClrType: typeof(string),
            oldType: "nvarchar(100)",
            oldMaxLength: 100,
            oldDefaultValue: "admin");

        migrationBuilder.AlterColumn<string>(
            name: "ChangedBy",
            table: "SkuConfigs",
            type: "nvarchar(200)",
            maxLength: 200,
            nullable: true,
            oldClrType: typeof(string),
            oldType: "nvarchar(100)",
            oldMaxLength: 100,
            oldNullable: true);

        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "SkuAttributes",
            type: "nvarchar(200)",
            maxLength: 200,
            nullable: false,
            defaultValue: "admin",
            oldClrType: typeof(string),
            oldType: "nvarchar(100)",
            oldMaxLength: 100,
            oldDefaultValue: "admin");

        migrationBuilder.AlterColumn<string>(
            name: "ChangedBy",
            table: "SkuAttributes",
            type: "nvarchar(200)",
            maxLength: 200,
            nullable: true,
            oldClrType: typeof(string),
            oldType: "nvarchar(100)",
            oldMaxLength: 100,
            oldNullable: true);

        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "ProductSuggestedProducts",
            type: "nvarchar(200)",
            maxLength: 200,
            nullable: false,
            defaultValue: "admin",
            oldClrType: typeof(string),
            oldType: "nvarchar(100)",
            oldMaxLength: 100,
            oldDefaultValue: "admin");

        migrationBuilder.AlterColumn<string>(
            name: "ChangedBy",
            table: "ProductSuggestedProducts",
            type: "nvarchar(200)",
            maxLength: 200,
            nullable: true,
            oldClrType: typeof(string),
            oldType: "nvarchar(100)",
            oldMaxLength: 100,
            oldNullable: true);

        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "Products",
            type: "nvarchar(200)",
            maxLength: 200,
            nullable: false,
            defaultValue: "admin",
            oldClrType: typeof(string),
            oldType: "nvarchar(100)",
            oldMaxLength: 100,
            oldDefaultValue: "admin");

        migrationBuilder.AlterColumn<string>(
            name: "ChangedBy",
            table: "Products",
            type: "nvarchar(200)",
            maxLength: 200,
            nullable: true,
            oldClrType: typeof(string),
            oldType: "nvarchar(100)",
            oldMaxLength: 100,
            oldNullable: true);

        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "ProductPathways",
            type: "nvarchar(200)",
            maxLength: 200,
            nullable: false,
            defaultValue: "admin",
            oldClrType: typeof(string),
            oldType: "nvarchar(100)",
            oldMaxLength: 100,
            oldDefaultValue: "admin");

        migrationBuilder.AlterColumn<string>(
            name: "ChangedBy",
            table: "ProductPathways",
            type: "nvarchar(200)",
            maxLength: 200,
            nullable: true,
            oldClrType: typeof(string),
            oldType: "nvarchar(100)",
            oldMaxLength: 100,
            oldNullable: true);

        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "ProductCPTCodes",
            type: "nvarchar(200)",
            maxLength: 200,
            nullable: false,
            defaultValue: "admin",
            oldClrType: typeof(string),
            oldType: "nvarchar(100)",
            oldMaxLength: 100,
            oldDefaultValue: "admin");

        migrationBuilder.AlterColumn<string>(
            name: "ChangedBy",
            table: "ProductCPTCodes",
            type: "nvarchar(200)",
            maxLength: 200,
            nullable: true,
            oldClrType: typeof(string),
            oldType: "nvarchar(100)",
            oldMaxLength: 100,
            oldNullable: true);

        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "ProductColors",
            type: "nvarchar(200)",
            maxLength: 200,
            nullable: false,
            defaultValue: "admin",
            oldClrType: typeof(string),
            oldType: "nvarchar(100)",
            oldMaxLength: 100,
            oldDefaultValue: "admin");

        migrationBuilder.AlterColumn<string>(
            name: "ChangedBy",
            table: "ProductColors",
            type: "nvarchar(200)",
            maxLength: 200,
            nullable: true,
            oldClrType: typeof(string),
            oldType: "nvarchar(100)",
            oldMaxLength: 100,
            oldNullable: true);

        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "ProductCategoryAccountCodes",
            type: "nvarchar(200)",
            maxLength: 200,
            nullable: false,
            defaultValue: "admin",
            oldClrType: typeof(string),
            oldType: "nvarchar(100)",
            oldMaxLength: 100,
            oldDefaultValue: "admin");

        migrationBuilder.AlterColumn<string>(
            name: "ChangedBy",
            table: "ProductCategoryAccountCodes",
            type: "nvarchar(200)",
            maxLength: 200,
            nullable: true,
            oldClrType: typeof(string),
            oldType: "nvarchar(100)",
            oldMaxLength: 100,
            oldNullable: true);

        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "ProductCategories",
            type: "nvarchar(200)",
            maxLength: 200,
            nullable: false,
            defaultValue: "admin",
            oldClrType: typeof(string),
            oldType: "nvarchar(100)",
            oldMaxLength: 100,
            oldDefaultValue: "admin");

        migrationBuilder.AlterColumn<string>(
            name: "ChangedBy",
            table: "ProductCategories",
            type: "nvarchar(200)",
            maxLength: 200,
            nullable: true,
            oldClrType: typeof(string),
            oldType: "nvarchar(100)",
            oldMaxLength: 100,
            oldNullable: true);

        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "ProductBatteryTypes",
            type: "nvarchar(200)",
            maxLength: 200,
            nullable: false,
            defaultValue: "admin",
            oldClrType: typeof(string),
            oldType: "nvarchar(100)",
            oldMaxLength: 100,
            oldDefaultValue: "admin");

        migrationBuilder.AlterColumn<string>(
            name: "ChangedBy",
            table: "ProductBatteryTypes",
            type: "nvarchar(200)",
            maxLength: 200,
            nullable: true,
            oldClrType: typeof(string),
            oldType: "nvarchar(100)",
            oldMaxLength: 100,
            oldNullable: true);

        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "ProductAttributes",
            type: "nvarchar(200)",
            maxLength: 200,
            nullable: false,
            defaultValue: "admin",
            oldClrType: typeof(string),
            oldType: "nvarchar(100)",
            oldMaxLength: 100,
            oldDefaultValue: "admin");

        migrationBuilder.AlterColumn<string>(
            name: "ChangedBy",
            table: "ProductAttributes",
            type: "nvarchar(200)",
            maxLength: 200,
            nullable: true,
            oldClrType: typeof(string),
            oldType: "nvarchar(100)",
            oldMaxLength: 100,
            oldNullable: true);

        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "Pathways",
            type: "nvarchar(200)",
            maxLength: 200,
            nullable: false,
            defaultValue: "admin",
            oldClrType: typeof(string),
            oldType: "nvarchar(100)",
            oldMaxLength: 100,
            oldDefaultValue: "admin");

        migrationBuilder.AlterColumn<string>(
            name: "ChangedBy",
            table: "Pathways",
            type: "nvarchar(200)",
            maxLength: 200,
            nullable: true,
            oldClrType: typeof(string),
            oldType: "nvarchar(100)",
            oldMaxLength: 100,
            oldNullable: true);

        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "NhsContractProducts",
            type: "nvarchar(200)",
            maxLength: 200,
            nullable: false,
            defaultValue: "admin",
            oldClrType: typeof(string),
            oldType: "nvarchar(100)",
            oldMaxLength: 100,
            oldDefaultValue: "admin");

        migrationBuilder.AlterColumn<string>(
            name: "ChangedBy",
            table: "NhsContractProducts",
            type: "nvarchar(200)",
            maxLength: 200,
            nullable: true,
            oldClrType: typeof(string),
            oldType: "nvarchar(100)",
            oldMaxLength: 100,
            oldNullable: true);

        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "Manufacturers",
            type: "nvarchar(200)",
            maxLength: 200,
            nullable: false,
            defaultValue: "admin",
            oldClrType: typeof(string),
            oldType: "nvarchar(100)",
            oldMaxLength: 100,
            oldDefaultValue: "admin");

        migrationBuilder.AlterColumn<string>(
            name: "ChangedBy",
            table: "Manufacturers",
            type: "nvarchar(200)",
            maxLength: 200,
            nullable: true,
            oldClrType: typeof(string),
            oldType: "nvarchar(100)",
            oldMaxLength: 100,
            oldNullable: true);

        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "ImportMetadatas",
            type: "nvarchar(200)",
            maxLength: 200,
            nullable: false,
            defaultValue: "admin",
            oldClrType: typeof(string),
            oldType: "nvarchar(100)",
            oldMaxLength: 100,
            oldDefaultValue: "admin");

        migrationBuilder.AlterColumn<string>(
            name: "ChangedBy",
            table: "ImportMetadatas",
            type: "nvarchar(200)",
            maxLength: 200,
            nullable: true,
            oldClrType: typeof(string),
            oldType: "nvarchar(100)",
            oldMaxLength: 100,
            oldNullable: true);

        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "HearingAidTypes",
            type: "nvarchar(200)",
            maxLength: 200,
            nullable: false,
            defaultValue: "admin",
            oldClrType: typeof(string),
            oldType: "nvarchar(100)",
            oldMaxLength: 100,
            oldDefaultValue: "admin");

        migrationBuilder.AlterColumn<string>(
            name: "ChangedBy",
            table: "HearingAidTypes",
            type: "nvarchar(200)",
            maxLength: 200,
            nullable: true,
            oldClrType: typeof(string),
            oldType: "nvarchar(100)",
            oldMaxLength: 100,
            oldNullable: true);

        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "ExternalProducts",
            type: "nvarchar(200)",
            maxLength: 200,
            nullable: false,
            defaultValue: "admin",
            oldClrType: typeof(string),
            oldType: "nvarchar(100)",
            oldMaxLength: 100,
            oldDefaultValue: "admin");

        migrationBuilder.AlterColumn<string>(
            name: "ChangedBy",
            table: "ExternalProducts",
            type: "nvarchar(200)",
            maxLength: 200,
            nullable: true,
            oldClrType: typeof(string),
            oldType: "nvarchar(100)",
            oldMaxLength: 100,
            oldNullable: true);

        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "ExternalProductOptions",
            type: "nvarchar(200)",
            maxLength: 200,
            nullable: false,
            defaultValue: "admin",
            oldClrType: typeof(string),
            oldType: "nvarchar(100)",
            oldMaxLength: 100,
            oldDefaultValue: "admin");

        migrationBuilder.AlterColumn<string>(
            name: "ChangedBy",
            table: "ExternalProductOptions",
            type: "nvarchar(200)",
            maxLength: 200,
            nullable: true,
            oldClrType: typeof(string),
            oldType: "nvarchar(100)",
            oldMaxLength: 100,
            oldNullable: true);

        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "ExternalProductImports",
            type: "nvarchar(200)",
            maxLength: 200,
            nullable: false,
            defaultValue: "admin",
            oldClrType: typeof(string),
            oldType: "nvarchar(100)",
            oldMaxLength: 100,
            oldDefaultValue: "admin");

        migrationBuilder.AlterColumn<string>(
            name: "ChangedBy",
            table: "ExternalProductImports",
            type: "nvarchar(200)",
            maxLength: 200,
            nullable: true,
            oldClrType: typeof(string),
            oldType: "nvarchar(100)",
            oldMaxLength: 100,
            oldNullable: true);

        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "CPTCodes",
            type: "nvarchar(200)",
            maxLength: 200,
            nullable: false,
            defaultValue: "admin",
            oldClrType: typeof(string),
            oldType: "nvarchar(100)",
            oldMaxLength: 100,
            oldDefaultValue: "admin");

        migrationBuilder.AlterColumn<string>(
            name: "ChangedBy",
            table: "CPTCodes",
            type: "nvarchar(200)",
            maxLength: 200,
            nullable: true,
            oldClrType: typeof(string),
            oldType: "nvarchar(100)",
            oldMaxLength: 100,
            oldNullable: true);

        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "Countries",
            type: "nvarchar(200)",
            maxLength: 200,
            nullable: false,
            defaultValue: "admin",
            oldClrType: typeof(string),
            oldType: "nvarchar(100)",
            oldMaxLength: 100,
            oldDefaultValue: "admin");

        migrationBuilder.AlterColumn<string>(
            name: "ChangedBy",
            table: "Countries",
            type: "nvarchar(200)",
            maxLength: 200,
            nullable: true,
            oldClrType: typeof(string),
            oldType: "nvarchar(100)",
            oldMaxLength: 100,
            oldNullable: true);

        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "Colors",
            type: "nvarchar(200)",
            maxLength: 200,
            nullable: false,
            defaultValue: "admin",
            oldClrType: typeof(string),
            oldType: "nvarchar(100)",
            oldMaxLength: 100,
            oldDefaultValue: "admin");

        migrationBuilder.AlterColumn<string>(
            name: "ChangedBy",
            table: "Colors",
            type: "nvarchar(200)",
            maxLength: 200,
            nullable: true,
            oldClrType: typeof(string),
            oldType: "nvarchar(100)",
            oldMaxLength: 100,
            oldNullable: true);

        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "Bundles",
            type: "nvarchar(200)",
            maxLength: 200,
            nullable: false,
            defaultValue: "admin",
            oldClrType: typeof(string),
            oldType: "nvarchar(100)",
            oldMaxLength: 100,
            oldDefaultValue: "admin");

        migrationBuilder.AlterColumn<string>(
            name: "ChangedBy",
            table: "Bundles",
            type: "nvarchar(200)",
            maxLength: 200,
            nullable: true,
            oldClrType: typeof(string),
            oldType: "nvarchar(100)",
            oldMaxLength: 100,
            oldNullable: true);

        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "BundleProducts",
            type: "nvarchar(200)",
            maxLength: 200,
            nullable: false,
            defaultValue: "admin",
            oldClrType: typeof(string),
            oldType: "nvarchar(100)",
            oldMaxLength: 100,
            oldDefaultValue: "admin");

        migrationBuilder.AlterColumn<string>(
            name: "ChangedBy",
            table: "BundleProducts",
            type: "nvarchar(200)",
            maxLength: 200,
            nullable: true,
            oldClrType: typeof(string),
            oldType: "nvarchar(100)",
            oldMaxLength: 100,
            oldNullable: true);

        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "BatteryTypes",
            type: "nvarchar(200)",
            maxLength: 200,
            nullable: false,
            defaultValue: "admin",
            oldClrType: typeof(string),
            oldType: "nvarchar(100)",
            oldMaxLength: 100,
            oldDefaultValue: "admin");

        migrationBuilder.AlterColumn<string>(
            name: "ChangedBy",
            table: "BatteryTypes",
            type: "nvarchar(200)",
            maxLength: 200,
            nullable: true,
            oldClrType: typeof(string),
            oldType: "nvarchar(100)",
            oldMaxLength: 100,
            oldNullable: true);

        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "AuditRequests",
            type: "nvarchar(200)",
            maxLength: 200,
            nullable: false,
            defaultValue: "admin",
            oldClrType: typeof(string),
            oldType: "nvarchar(100)",
            oldMaxLength: 100,
            oldDefaultValue: "admin");

        migrationBuilder.AlterColumn<string>(
            name: "ChangedBy",
            table: "AuditRequests",
            type: "nvarchar(200)",
            maxLength: 200,
            nullable: true,
            oldClrType: typeof(string),
            oldType: "nvarchar(100)",
            oldMaxLength: 100,
            oldNullable: true);

        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "AuditRequestLogs",
            type: "nvarchar(200)",
            maxLength: 200,
            nullable: false,
            defaultValue: "admin",
            oldClrType: typeof(string),
            oldType: "nvarchar(100)",
            oldMaxLength: 100,
            oldDefaultValue: "admin");

        migrationBuilder.AlterColumn<string>(
            name: "ChangedBy",
            table: "AuditRequestLogs",
            type: "nvarchar(200)",
            maxLength: 200,
            nullable: true,
            oldClrType: typeof(string),
            oldType: "nvarchar(100)",
            oldMaxLength: 100,
            oldNullable: true);

        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "AuditProductSerialNumbers",
            type: "nvarchar(200)",
            maxLength: 200,
            nullable: false,
            defaultValue: "admin",
            oldClrType: typeof(string),
            oldType: "nvarchar(100)",
            oldMaxLength: 100,
            oldDefaultValue: "admin");

        migrationBuilder.AlterColumn<string>(
            name: "ChangedBy",
            table: "AuditProductSerialNumbers",
            type: "nvarchar(200)",
            maxLength: 200,
            nullable: true,
            oldClrType: typeof(string),
            oldType: "nvarchar(100)",
            oldMaxLength: 100,
            oldNullable: true);

        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "AuditProducts",
            type: "nvarchar(200)",
            maxLength: 200,
            nullable: false,
            defaultValue: "admin",
            oldClrType: typeof(string),
            oldType: "nvarchar(100)",
            oldMaxLength: 100,
            oldDefaultValue: "admin");

        migrationBuilder.AlterColumn<string>(
            name: "ChangedBy",
            table: "AuditProducts",
            type: "nvarchar(200)",
            maxLength: 200,
            nullable: true,
            oldClrType: typeof(string),
            oldType: "nvarchar(100)",
            oldMaxLength: 100,
            oldNullable: true);

        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "AuditLocations",
            type: "nvarchar(200)",
            maxLength: 200,
            nullable: false,
            defaultValue: "admin",
            oldClrType: typeof(string),
            oldType: "nvarchar(100)",
            oldMaxLength: 100,
            oldDefaultValue: "admin");

        migrationBuilder.AlterColumn<string>(
            name: "ChangedBy",
            table: "AuditLocations",
            type: "nvarchar(200)",
            maxLength: 200,
            nullable: true,
            oldClrType: typeof(string),
            oldType: "nvarchar(100)",
            oldMaxLength: 100,
            oldNullable: true);

        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "Attributes",
            type: "nvarchar(200)",
            maxLength: 200,
            nullable: false,
            defaultValue: "admin",
            oldClrType: typeof(string),
            oldType: "nvarchar(100)",
            oldMaxLength: 100,
            oldDefaultValue: "admin");

        migrationBuilder.AlterColumn<string>(
            name: "ChangedBy",
            table: "Attributes",
            type: "nvarchar(200)",
            maxLength: 200,
            nullable: true,
            oldClrType: typeof(string),
            oldType: "nvarchar(100)",
            oldMaxLength: 100,
            oldNullable: true);

        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "AttributeProductCategories",
            type: "nvarchar(200)",
            maxLength: 200,
            nullable: false,
            defaultValue: "admin",
            oldClrType: typeof(string),
            oldType: "nvarchar(100)",
            oldMaxLength: 100,
            oldDefaultValue: "admin");

        migrationBuilder.AlterColumn<string>(
            name: "ChangedBy",
            table: "AttributeProductCategories",
            type: "nvarchar(200)",
            maxLength: 200,
            nullable: true,
            oldClrType: typeof(string),
            oldType: "nvarchar(100)",
            oldMaxLength: 100,
            oldNullable: true);
    }

    /// <inheritdoc />
    protected override void Down(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "Suppliers",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: false,
            defaultValue: "admin",
            oldClrType: typeof(string),
            oldType: "nvarchar(200)",
            oldMaxLength: 200,
            oldDefaultValue: "admin");

        migrationBuilder.AlterColumn<string>(
            name: "ChangedBy",
            table: "Suppliers",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: true,
            oldClrType: typeof(string),
            oldType: "nvarchar(200)",
            oldMaxLength: 200,
            oldNullable: true);

        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "StockTransactions",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: false,
            defaultValue: "admin",
            oldClrType: typeof(string),
            oldType: "nvarchar(200)",
            oldMaxLength: 200,
            oldDefaultValue: "admin");

        migrationBuilder.AlterColumn<string>(
            name: "ChangedBy",
            table: "StockTransactions",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: true,
            oldClrType: typeof(string),
            oldType: "nvarchar(200)",
            oldMaxLength: 200,
            oldNullable: true);

        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "StockSettings",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: false,
            defaultValue: "admin",
            oldClrType: typeof(string),
            oldType: "nvarchar(200)",
            oldMaxLength: 200,
            oldDefaultValue: "admin");

        migrationBuilder.AlterColumn<string>(
            name: "ChangedBy",
            table: "StockSettings",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: true,
            oldClrType: typeof(string),
            oldType: "nvarchar(200)",
            oldMaxLength: 200,
            oldNullable: true);

        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "Stocks",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: false,
            defaultValue: "admin",
            oldClrType: typeof(string),
            oldType: "nvarchar(200)",
            oldMaxLength: 200,
            oldDefaultValue: "admin");

        migrationBuilder.AlterColumn<string>(
            name: "ChangedBy",
            table: "Stocks",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: true,
            oldClrType: typeof(string),
            oldType: "nvarchar(200)",
            oldMaxLength: 200,
            oldNullable: true);

        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "StockProducts",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: false,
            defaultValue: "admin",
            oldClrType: typeof(string),
            oldType: "nvarchar(200)",
            oldMaxLength: 200,
            oldDefaultValue: "admin");

        migrationBuilder.AlterColumn<string>(
            name: "ChangedBy",
            table: "StockProducts",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: true,
            oldClrType: typeof(string),
            oldType: "nvarchar(200)",
            oldMaxLength: 200,
            oldNullable: true);

        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "StockProductItems",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: false,
            defaultValue: "admin",
            oldClrType: typeof(string),
            oldType: "nvarchar(200)",
            oldMaxLength: 200,
            oldDefaultValue: "admin");

        migrationBuilder.AlterColumn<string>(
            name: "ChangedBy",
            table: "StockProductItems",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: true,
            oldClrType: typeof(string),
            oldType: "nvarchar(200)",
            oldMaxLength: 200,
            oldNullable: true);

        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "StockProductItemLogs",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: false,
            defaultValue: "admin",
            oldClrType: typeof(string),
            oldType: "nvarchar(200)",
            oldMaxLength: 200,
            oldDefaultValue: "admin");

        migrationBuilder.AlterColumn<string>(
            name: "ChangedBy",
            table: "StockProductItemLogs",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: true,
            oldClrType: typeof(string),
            oldType: "nvarchar(200)",
            oldMaxLength: 200,
            oldNullable: true);

        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "StockProductItemAttributes",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: false,
            defaultValue: "admin",
            oldClrType: typeof(string),
            oldType: "nvarchar(200)",
            oldMaxLength: 200,
            oldDefaultValue: "admin");

        migrationBuilder.AlterColumn<string>(
            name: "ChangedBy",
            table: "StockProductItemAttributes",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: true,
            oldClrType: typeof(string),
            oldType: "nvarchar(200)",
            oldMaxLength: 200,
            oldNullable: true);

        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "StockKeepingUnits",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: false,
            defaultValue: "admin",
            oldClrType: typeof(string),
            oldType: "nvarchar(200)",
            oldMaxLength: 200,
            oldDefaultValue: "admin");

        migrationBuilder.AlterColumn<string>(
            name: "ChangedBy",
            table: "StockKeepingUnits",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: true,
            oldClrType: typeof(string),
            oldType: "nvarchar(200)",
            oldMaxLength: 200,
            oldNullable: true);

        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "StockAdjustmentReasons",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: false,
            defaultValue: "admin",
            oldClrType: typeof(string),
            oldType: "nvarchar(200)",
            oldMaxLength: 200,
            oldDefaultValue: "admin");

        migrationBuilder.AlterColumn<string>(
            name: "ChangedBy",
            table: "StockAdjustmentReasons",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: true,
            oldClrType: typeof(string),
            oldType: "nvarchar(200)",
            oldMaxLength: 200,
            oldNullable: true);

        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "States",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: false,
            defaultValue: "admin",
            oldClrType: typeof(string),
            oldType: "nvarchar(200)",
            oldMaxLength: 200,
            oldDefaultValue: "admin");

        migrationBuilder.AlterColumn<string>(
            name: "ChangedBy",
            table: "States",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: true,
            oldClrType: typeof(string),
            oldType: "nvarchar(200)",
            oldMaxLength: 200,
            oldNullable: true);

        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "Skus",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: false,
            defaultValue: "admin",
            oldClrType: typeof(string),
            oldType: "nvarchar(200)",
            oldMaxLength: 200,
            oldDefaultValue: "admin");

        migrationBuilder.AlterColumn<string>(
            name: "ChangedBy",
            table: "Skus",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: true,
            oldClrType: typeof(string),
            oldType: "nvarchar(200)",
            oldMaxLength: 200,
            oldNullable: true);

        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "SkuConfigs",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: false,
            defaultValue: "admin",
            oldClrType: typeof(string),
            oldType: "nvarchar(200)",
            oldMaxLength: 200,
            oldDefaultValue: "admin");

        migrationBuilder.AlterColumn<string>(
            name: "ChangedBy",
            table: "SkuConfigs",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: true,
            oldClrType: typeof(string),
            oldType: "nvarchar(200)",
            oldMaxLength: 200,
            oldNullable: true);

        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "SkuAttributes",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: false,
            defaultValue: "admin",
            oldClrType: typeof(string),
            oldType: "nvarchar(200)",
            oldMaxLength: 200,
            oldDefaultValue: "admin");

        migrationBuilder.AlterColumn<string>(
            name: "ChangedBy",
            table: "SkuAttributes",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: true,
            oldClrType: typeof(string),
            oldType: "nvarchar(200)",
            oldMaxLength: 200,
            oldNullable: true);

        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "ProductSuggestedProducts",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: false,
            defaultValue: "admin",
            oldClrType: typeof(string),
            oldType: "nvarchar(200)",
            oldMaxLength: 200,
            oldDefaultValue: "admin");

        migrationBuilder.AlterColumn<string>(
            name: "ChangedBy",
            table: "ProductSuggestedProducts",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: true,
            oldClrType: typeof(string),
            oldType: "nvarchar(200)",
            oldMaxLength: 200,
            oldNullable: true);

        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "Products",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: false,
            defaultValue: "admin",
            oldClrType: typeof(string),
            oldType: "nvarchar(200)",
            oldMaxLength: 200,
            oldDefaultValue: "admin");

        migrationBuilder.AlterColumn<string>(
            name: "ChangedBy",
            table: "Products",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: true,
            oldClrType: typeof(string),
            oldType: "nvarchar(200)",
            oldMaxLength: 200,
            oldNullable: true);

        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "ProductPathways",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: false,
            defaultValue: "admin",
            oldClrType: typeof(string),
            oldType: "nvarchar(200)",
            oldMaxLength: 200,
            oldDefaultValue: "admin");

        migrationBuilder.AlterColumn<string>(
            name: "ChangedBy",
            table: "ProductPathways",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: true,
            oldClrType: typeof(string),
            oldType: "nvarchar(200)",
            oldMaxLength: 200,
            oldNullable: true);

        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "ProductCPTCodes",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: false,
            defaultValue: "admin",
            oldClrType: typeof(string),
            oldType: "nvarchar(200)",
            oldMaxLength: 200,
            oldDefaultValue: "admin");

        migrationBuilder.AlterColumn<string>(
            name: "ChangedBy",
            table: "ProductCPTCodes",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: true,
            oldClrType: typeof(string),
            oldType: "nvarchar(200)",
            oldMaxLength: 200,
            oldNullable: true);

        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "ProductColors",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: false,
            defaultValue: "admin",
            oldClrType: typeof(string),
            oldType: "nvarchar(200)",
            oldMaxLength: 200,
            oldDefaultValue: "admin");

        migrationBuilder.AlterColumn<string>(
            name: "ChangedBy",
            table: "ProductColors",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: true,
            oldClrType: typeof(string),
            oldType: "nvarchar(200)",
            oldMaxLength: 200,
            oldNullable: true);

        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "ProductCategoryAccountCodes",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: false,
            defaultValue: "admin",
            oldClrType: typeof(string),
            oldType: "nvarchar(200)",
            oldMaxLength: 200,
            oldDefaultValue: "admin");

        migrationBuilder.AlterColumn<string>(
            name: "ChangedBy",
            table: "ProductCategoryAccountCodes",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: true,
            oldClrType: typeof(string),
            oldType: "nvarchar(200)",
            oldMaxLength: 200,
            oldNullable: true);

        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "ProductCategories",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: false,
            defaultValue: "admin",
            oldClrType: typeof(string),
            oldType: "nvarchar(200)",
            oldMaxLength: 200,
            oldDefaultValue: "admin");

        migrationBuilder.AlterColumn<string>(
            name: "ChangedBy",
            table: "ProductCategories",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: true,
            oldClrType: typeof(string),
            oldType: "nvarchar(200)",
            oldMaxLength: 200,
            oldNullable: true);

        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "ProductBatteryTypes",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: false,
            defaultValue: "admin",
            oldClrType: typeof(string),
            oldType: "nvarchar(200)",
            oldMaxLength: 200,
            oldDefaultValue: "admin");

        migrationBuilder.AlterColumn<string>(
            name: "ChangedBy",
            table: "ProductBatteryTypes",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: true,
            oldClrType: typeof(string),
            oldType: "nvarchar(200)",
            oldMaxLength: 200,
            oldNullable: true);

        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "ProductAttributes",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: false,
            defaultValue: "admin",
            oldClrType: typeof(string),
            oldType: "nvarchar(200)",
            oldMaxLength: 200,
            oldDefaultValue: "admin");

        migrationBuilder.AlterColumn<string>(
            name: "ChangedBy",
            table: "ProductAttributes",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: true,
            oldClrType: typeof(string),
            oldType: "nvarchar(200)",
            oldMaxLength: 200,
            oldNullable: true);

        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "Pathways",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: false,
            defaultValue: "admin",
            oldClrType: typeof(string),
            oldType: "nvarchar(200)",
            oldMaxLength: 200,
            oldDefaultValue: "admin");

        migrationBuilder.AlterColumn<string>(
            name: "ChangedBy",
            table: "Pathways",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: true,
            oldClrType: typeof(string),
            oldType: "nvarchar(200)",
            oldMaxLength: 200,
            oldNullable: true);

        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "NhsContractProducts",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: false,
            defaultValue: "admin",
            oldClrType: typeof(string),
            oldType: "nvarchar(200)",
            oldMaxLength: 200,
            oldDefaultValue: "admin");

        migrationBuilder.AlterColumn<string>(
            name: "ChangedBy",
            table: "NhsContractProducts",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: true,
            oldClrType: typeof(string),
            oldType: "nvarchar(200)",
            oldMaxLength: 200,
            oldNullable: true);

        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "Manufacturers",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: false,
            defaultValue: "admin",
            oldClrType: typeof(string),
            oldType: "nvarchar(200)",
            oldMaxLength: 200,
            oldDefaultValue: "admin");

        migrationBuilder.AlterColumn<string>(
            name: "ChangedBy",
            table: "Manufacturers",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: true,
            oldClrType: typeof(string),
            oldType: "nvarchar(200)",
            oldMaxLength: 200,
            oldNullable: true);

        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "ImportMetadatas",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: false,
            defaultValue: "admin",
            oldClrType: typeof(string),
            oldType: "nvarchar(200)",
            oldMaxLength: 200,
            oldDefaultValue: "admin");

        migrationBuilder.AlterColumn<string>(
            name: "ChangedBy",
            table: "ImportMetadatas",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: true,
            oldClrType: typeof(string),
            oldType: "nvarchar(200)",
            oldMaxLength: 200,
            oldNullable: true);

        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "HearingAidTypes",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: false,
            defaultValue: "admin",
            oldClrType: typeof(string),
            oldType: "nvarchar(200)",
            oldMaxLength: 200,
            oldDefaultValue: "admin");

        migrationBuilder.AlterColumn<string>(
            name: "ChangedBy",
            table: "HearingAidTypes",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: true,
            oldClrType: typeof(string),
            oldType: "nvarchar(200)",
            oldMaxLength: 200,
            oldNullable: true);

        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "ExternalProducts",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: false,
            defaultValue: "admin",
            oldClrType: typeof(string),
            oldType: "nvarchar(200)",
            oldMaxLength: 200,
            oldDefaultValue: "admin");

        migrationBuilder.AlterColumn<string>(
            name: "ChangedBy",
            table: "ExternalProducts",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: true,
            oldClrType: typeof(string),
            oldType: "nvarchar(200)",
            oldMaxLength: 200,
            oldNullable: true);

        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "ExternalProductOptions",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: false,
            defaultValue: "admin",
            oldClrType: typeof(string),
            oldType: "nvarchar(200)",
            oldMaxLength: 200,
            oldDefaultValue: "admin");

        migrationBuilder.AlterColumn<string>(
            name: "ChangedBy",
            table: "ExternalProductOptions",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: true,
            oldClrType: typeof(string),
            oldType: "nvarchar(200)",
            oldMaxLength: 200,
            oldNullable: true);

        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "ExternalProductImports",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: false,
            defaultValue: "admin",
            oldClrType: typeof(string),
            oldType: "nvarchar(200)",
            oldMaxLength: 200,
            oldDefaultValue: "admin");

        migrationBuilder.AlterColumn<string>(
            name: "ChangedBy",
            table: "ExternalProductImports",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: true,
            oldClrType: typeof(string),
            oldType: "nvarchar(200)",
            oldMaxLength: 200,
            oldNullable: true);

        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "CPTCodes",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: false,
            defaultValue: "admin",
            oldClrType: typeof(string),
            oldType: "nvarchar(200)",
            oldMaxLength: 200,
            oldDefaultValue: "admin");

        migrationBuilder.AlterColumn<string>(
            name: "ChangedBy",
            table: "CPTCodes",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: true,
            oldClrType: typeof(string),
            oldType: "nvarchar(200)",
            oldMaxLength: 200,
            oldNullable: true);

        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "Countries",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: false,
            defaultValue: "admin",
            oldClrType: typeof(string),
            oldType: "nvarchar(200)",
            oldMaxLength: 200,
            oldDefaultValue: "admin");

        migrationBuilder.AlterColumn<string>(
            name: "ChangedBy",
            table: "Countries",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: true,
            oldClrType: typeof(string),
            oldType: "nvarchar(200)",
            oldMaxLength: 200,
            oldNullable: true);

        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "Colors",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: false,
            defaultValue: "admin",
            oldClrType: typeof(string),
            oldType: "nvarchar(200)",
            oldMaxLength: 200,
            oldDefaultValue: "admin");

        migrationBuilder.AlterColumn<string>(
            name: "ChangedBy",
            table: "Colors",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: true,
            oldClrType: typeof(string),
            oldType: "nvarchar(200)",
            oldMaxLength: 200,
            oldNullable: true);

        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "Bundles",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: false,
            defaultValue: "admin",
            oldClrType: typeof(string),
            oldType: "nvarchar(200)",
            oldMaxLength: 200,
            oldDefaultValue: "admin");

        migrationBuilder.AlterColumn<string>(
            name: "ChangedBy",
            table: "Bundles",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: true,
            oldClrType: typeof(string),
            oldType: "nvarchar(200)",
            oldMaxLength: 200,
            oldNullable: true);

        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "BundleProducts",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: false,
            defaultValue: "admin",
            oldClrType: typeof(string),
            oldType: "nvarchar(200)",
            oldMaxLength: 200,
            oldDefaultValue: "admin");

        migrationBuilder.AlterColumn<string>(
            name: "ChangedBy",
            table: "BundleProducts",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: true,
            oldClrType: typeof(string),
            oldType: "nvarchar(200)",
            oldMaxLength: 200,
            oldNullable: true);

        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "BatteryTypes",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: false,
            defaultValue: "admin",
            oldClrType: typeof(string),
            oldType: "nvarchar(200)",
            oldMaxLength: 200,
            oldDefaultValue: "admin");

        migrationBuilder.AlterColumn<string>(
            name: "ChangedBy",
            table: "BatteryTypes",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: true,
            oldClrType: typeof(string),
            oldType: "nvarchar(200)",
            oldMaxLength: 200,
            oldNullable: true);

        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "AuditRequests",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: false,
            defaultValue: "admin",
            oldClrType: typeof(string),
            oldType: "nvarchar(200)",
            oldMaxLength: 200,
            oldDefaultValue: "admin");

        migrationBuilder.AlterColumn<string>(
            name: "ChangedBy",
            table: "AuditRequests",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: true,
            oldClrType: typeof(string),
            oldType: "nvarchar(200)",
            oldMaxLength: 200,
            oldNullable: true);

        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "AuditRequestLogs",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: false,
            defaultValue: "admin",
            oldClrType: typeof(string),
            oldType: "nvarchar(200)",
            oldMaxLength: 200,
            oldDefaultValue: "admin");

        migrationBuilder.AlterColumn<string>(
            name: "ChangedBy",
            table: "AuditRequestLogs",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: true,
            oldClrType: typeof(string),
            oldType: "nvarchar(200)",
            oldMaxLength: 200,
            oldNullable: true);

        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "AuditProductSerialNumbers",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: false,
            defaultValue: "admin",
            oldClrType: typeof(string),
            oldType: "nvarchar(200)",
            oldMaxLength: 200,
            oldDefaultValue: "admin");

        migrationBuilder.AlterColumn<string>(
            name: "ChangedBy",
            table: "AuditProductSerialNumbers",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: true,
            oldClrType: typeof(string),
            oldType: "nvarchar(200)",
            oldMaxLength: 200,
            oldNullable: true);

        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "AuditProducts",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: false,
            defaultValue: "admin",
            oldClrType: typeof(string),
            oldType: "nvarchar(200)",
            oldMaxLength: 200,
            oldDefaultValue: "admin");

        migrationBuilder.AlterColumn<string>(
            name: "ChangedBy",
            table: "AuditProducts",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: true,
            oldClrType: typeof(string),
            oldType: "nvarchar(200)",
            oldMaxLength: 200,
            oldNullable: true);

        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "AuditLocations",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: false,
            defaultValue: "admin",
            oldClrType: typeof(string),
            oldType: "nvarchar(200)",
            oldMaxLength: 200,
            oldDefaultValue: "admin");

        migrationBuilder.AlterColumn<string>(
            name: "ChangedBy",
            table: "AuditLocations",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: true,
            oldClrType: typeof(string),
            oldType: "nvarchar(200)",
            oldMaxLength: 200,
            oldNullable: true);

        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "Attributes",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: false,
            defaultValue: "admin",
            oldClrType: typeof(string),
            oldType: "nvarchar(200)",
            oldMaxLength: 200,
            oldDefaultValue: "admin");

        migrationBuilder.AlterColumn<string>(
            name: "ChangedBy",
            table: "Attributes",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: true,
            oldClrType: typeof(string),
            oldType: "nvarchar(200)",
            oldMaxLength: 200,
            oldNullable: true);

        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "AttributeProductCategories",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: false,
            defaultValue: "admin",
            oldClrType: typeof(string),
            oldType: "nvarchar(200)",
            oldMaxLength: 200,
            oldDefaultValue: "admin");

        migrationBuilder.AlterColumn<string>(
            name: "ChangedBy",
            table: "AttributeProductCategories",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: true,
            oldClrType: typeof(string),
            oldType: "nvarchar(200)",
            oldMaxLength: 200,
            oldNullable: true);
    }
}
