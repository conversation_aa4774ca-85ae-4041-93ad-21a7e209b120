using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Auditdata.InventoryService.Infrastructure.Migrations;

/// <inheritdoc />
public partial class AddAuditbaleFields : Migration
{
    /// <inheritdoc />
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.AddColumn<DateTimeOffset>(
            name: "ChangeDate",
            table: "Suppliers",
            type: "datetimeoffset",
            nullable: true);

        migrationBuilder.AddColumn<string>(
            name: "ChangedBy",
            table: "Suppliers",
            type: "nvarchar(max)",
            nullable: true);

        migrationBuilder.AddColumn<string>(
            name: "CreatedBy",
            table: "Suppliers",
            type: "nvarchar(max)",
            nullable: false,
            defaultValue: "admin");

        migrationBuilder.AddColumn<DateTimeOffset>(
            name: "CreationDate",
            table: "Suppliers",
            type: "datetimeoffset",
            nullable: false,
            defaultValue: new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)));

        migrationBuilder.AddColumn<DateTimeOffset>(
            name: "ChangeDate",
            table: "StockTransactions",
            type: "datetimeoffset",
            nullable: true);

        migrationBuilder.AddColumn<string>(
            name: "ChangedBy",
            table: "StockTransactions",
            type: "nvarchar(max)",
            nullable: true);

        migrationBuilder.AddColumn<string>(
            name: "CreatedBy",
            table: "StockTransactions",
            type: "nvarchar(max)",
            nullable: false,
            defaultValue: "admin");

        migrationBuilder.AddColumn<DateTimeOffset>(
            name: "CreationDate",
            table: "StockTransactions",
            type: "datetimeoffset",
            nullable: false,
            defaultValue: new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)));

        migrationBuilder.AddColumn<DateTimeOffset>(
            name: "ChangeDate",
            table: "Stocks",
            type: "datetimeoffset",
            nullable: true);

        migrationBuilder.AddColumn<string>(
            name: "ChangedBy",
            table: "Stocks",
            type: "nvarchar(max)",
            nullable: true);

        migrationBuilder.AddColumn<string>(
            name: "CreatedBy",
            table: "Stocks",
            type: "nvarchar(max)",
            nullable: false,
            defaultValue: "admin");

        migrationBuilder.AddColumn<DateTimeOffset>(
            name: "CreationDate",
            table: "Stocks",
            type: "datetimeoffset",
            nullable: false,
            defaultValue: new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)));

        migrationBuilder.AddColumn<DateTimeOffset>(
            name: "ChangeDate",
            table: "StockProducts",
            type: "datetimeoffset",
            nullable: true);

        migrationBuilder.AddColumn<string>(
            name: "ChangedBy",
            table: "StockProducts",
            type: "nvarchar(max)",
            nullable: true);

        migrationBuilder.AddColumn<string>(
            name: "CreatedBy",
            table: "StockProducts",
            type: "nvarchar(max)",
            nullable: false,
            defaultValue: "admin");

        migrationBuilder.AddColumn<DateTimeOffset>(
            name: "CreationDate",
            table: "StockProducts",
            type: "datetimeoffset",
            nullable: false,
            defaultValue: new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)));

        migrationBuilder.AddColumn<DateTimeOffset>(
            name: "ChangeDate",
            table: "StockProductItems",
            type: "datetimeoffset",
            nullable: true);

        migrationBuilder.AddColumn<string>(
            name: "ChangedBy",
            table: "StockProductItems",
            type: "nvarchar(max)",
            nullable: true);

        migrationBuilder.AddColumn<string>(
            name: "CreatedBy",
            table: "StockProductItems",
            type: "nvarchar(max)",
            nullable: false,
            defaultValue: "admin");

        migrationBuilder.AddColumn<DateTimeOffset>(
            name: "CreationDate",
            table: "StockProductItems",
            type: "datetimeoffset",
            nullable: false,
            defaultValue: new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)));

        migrationBuilder.AddColumn<DateTimeOffset>(
            name: "ChangeDate",
            table: "StockProductItemLogs",
            type: "datetimeoffset",
            nullable: true);

        migrationBuilder.AddColumn<string>(
            name: "ChangedBy",
            table: "StockProductItemLogs",
            type: "nvarchar(max)",
            nullable: true);

        migrationBuilder.AddColumn<string>(
            name: "CreatedBy",
            table: "StockProductItemLogs",
            type: "nvarchar(max)",
            nullable: false,
            defaultValue: "admin");

        migrationBuilder.AddColumn<DateTimeOffset>(
            name: "CreationDate",
            table: "StockProductItemLogs",
            type: "datetimeoffset",
            nullable: false,
            defaultValue: new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)));

        migrationBuilder.AddColumn<DateTimeOffset>(
            name: "ChangeDate",
            table: "StockAdjustmentReasons",
            type: "datetimeoffset",
            nullable: true);

        migrationBuilder.AddColumn<string>(
            name: "ChangedBy",
            table: "StockAdjustmentReasons",
            type: "nvarchar(max)",
            nullable: true);

        migrationBuilder.AddColumn<string>(
            name: "CreatedBy",
            table: "StockAdjustmentReasons",
            type: "nvarchar(max)",
            nullable: false,
            defaultValue: "admin");

        migrationBuilder.AddColumn<DateTimeOffset>(
            name: "CreationDate",
            table: "StockAdjustmentReasons",
            type: "datetimeoffset",
            nullable: false,
            defaultValue: new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)));

        migrationBuilder.AddColumn<DateTimeOffset>(
            name: "ChangeDate",
            table: "Products",
            type: "datetimeoffset",
            nullable: true);

        migrationBuilder.AddColumn<string>(
            name: "ChangedBy",
            table: "Products",
            type: "nvarchar(max)",
            nullable: true);

        migrationBuilder.AddColumn<string>(
            name: "CreatedBy",
            table: "Products",
            type: "nvarchar(max)",
            nullable: false,
            defaultValue: "admin");

        migrationBuilder.AddColumn<DateTimeOffset>(
            name: "CreationDate",
            table: "Products",
            type: "datetimeoffset",
            nullable: false,
            defaultValue: new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)));

        migrationBuilder.AddColumn<DateTimeOffset>(
            name: "ChangeDate",
            table: "ProductPathways",
            type: "datetimeoffset",
            nullable: true);

        migrationBuilder.AddColumn<string>(
            name: "ChangedBy",
            table: "ProductPathways",
            type: "nvarchar(max)",
            nullable: true);

        migrationBuilder.AddColumn<string>(
            name: "CreatedBy",
            table: "ProductPathways",
            type: "nvarchar(max)",
            nullable: false,
            defaultValue: "admin");

        migrationBuilder.AddColumn<DateTimeOffset>(
            name: "CreationDate",
            table: "ProductPathways",
            type: "datetimeoffset",
            nullable: false,
            defaultValue: new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)));

        migrationBuilder.AddColumn<DateTimeOffset>(
            name: "ChangeDate",
            table: "ProductCPTCodes",
            type: "datetimeoffset",
            nullable: true);

        migrationBuilder.AddColumn<string>(
            name: "ChangedBy",
            table: "ProductCPTCodes",
            type: "nvarchar(max)",
            nullable: true);

        migrationBuilder.AddColumn<string>(
            name: "CreatedBy",
            table: "ProductCPTCodes",
            type: "nvarchar(max)",
            nullable: false,
            defaultValue: "admin");

        migrationBuilder.AddColumn<DateTimeOffset>(
            name: "CreationDate",
            table: "ProductCPTCodes",
            type: "datetimeoffset",
            nullable: false,
            defaultValue: new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)));

        migrationBuilder.AddColumn<DateTimeOffset>(
            name: "ChangeDate",
            table: "ProductColors",
            type: "datetimeoffset",
            nullable: true);

        migrationBuilder.AddColumn<string>(
            name: "ChangedBy",
            table: "ProductColors",
            type: "nvarchar(max)",
            nullable: true);

        migrationBuilder.AddColumn<string>(
            name: "CreatedBy",
            table: "ProductColors",
            type: "nvarchar(max)",
            nullable: false,
            defaultValue: "admin");

        migrationBuilder.AddColumn<DateTimeOffset>(
            name: "CreationDate",
            table: "ProductColors",
            type: "datetimeoffset",
            nullable: false,
            defaultValue: new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)));

        migrationBuilder.AddColumn<DateTimeOffset>(
            name: "ChangeDate",
            table: "ProductCategoryAccountCodes",
            type: "datetimeoffset",
            nullable: true);

        migrationBuilder.AddColumn<string>(
            name: "ChangedBy",
            table: "ProductCategoryAccountCodes",
            type: "nvarchar(max)",
            nullable: true);

        migrationBuilder.AddColumn<string>(
            name: "CreatedBy",
            table: "ProductCategoryAccountCodes",
            type: "nvarchar(max)",
            nullable: false,
            defaultValue: "admin");

        migrationBuilder.AddColumn<DateTimeOffset>(
            name: "CreationDate",
            table: "ProductCategoryAccountCodes",
            type: "datetimeoffset",
            nullable: false,
            defaultValue: new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)));

        migrationBuilder.AddColumn<DateTimeOffset>(
            name: "ChangeDate",
            table: "ProductCategories",
            type: "datetimeoffset",
            nullable: true);

        migrationBuilder.AddColumn<string>(
            name: "ChangedBy",
            table: "ProductCategories",
            type: "nvarchar(max)",
            nullable: true);

        migrationBuilder.AddColumn<string>(
            name: "CreatedBy",
            table: "ProductCategories",
            type: "nvarchar(max)",
            nullable: false,
            defaultValue: "admin");

        migrationBuilder.AddColumn<DateTimeOffset>(
            name: "CreationDate",
            table: "ProductCategories",
            type: "datetimeoffset",
            nullable: false,
            defaultValue: new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)));

        migrationBuilder.AddColumn<DateTimeOffset>(
            name: "ChangeDate",
            table: "ProductBatteryTypes",
            type: "datetimeoffset",
            nullable: true);

        migrationBuilder.AddColumn<string>(
            name: "ChangedBy",
            table: "ProductBatteryTypes",
            type: "nvarchar(max)",
            nullable: true);

        migrationBuilder.AddColumn<string>(
            name: "CreatedBy",
            table: "ProductBatteryTypes",
            type: "nvarchar(max)",
            nullable: false,
            defaultValue: "admin");

        migrationBuilder.AddColumn<DateTimeOffset>(
            name: "CreationDate",
            table: "ProductBatteryTypes",
            type: "datetimeoffset",
            nullable: false,
            defaultValue: new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)));

        migrationBuilder.AddColumn<DateTimeOffset>(
            name: "ChangeDate",
            table: "ProductAttributes",
            type: "datetimeoffset",
            nullable: true);

        migrationBuilder.AddColumn<string>(
            name: "ChangedBy",
            table: "ProductAttributes",
            type: "nvarchar(max)",
            nullable: true);

        migrationBuilder.AddColumn<string>(
            name: "CreatedBy",
            table: "ProductAttributes",
            type: "nvarchar(max)",
            nullable: false,
            defaultValue: "admin");

        migrationBuilder.AddColumn<DateTimeOffset>(
            name: "CreationDate",
            table: "ProductAttributes",
            type: "datetimeoffset",
            nullable: false,
            defaultValue: new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)));

        migrationBuilder.AddColumn<DateTimeOffset>(
            name: "ChangeDate",
            table: "Pathways",
            type: "datetimeoffset",
            nullable: true);

        migrationBuilder.AddColumn<string>(
            name: "ChangedBy",
            table: "Pathways",
            type: "nvarchar(max)",
            nullable: true);

        migrationBuilder.AddColumn<string>(
            name: "CreatedBy",
            table: "Pathways",
            type: "nvarchar(max)",
            nullable: false,
            defaultValue: "admin");

        migrationBuilder.AddColumn<DateTimeOffset>(
            name: "CreationDate",
            table: "Pathways",
            type: "datetimeoffset",
            nullable: false,
            defaultValue: new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)));

        migrationBuilder.AddColumn<DateTimeOffset>(
            name: "ChangeDate",
            table: "NhsContractProducts",
            type: "datetimeoffset",
            nullable: true);

        migrationBuilder.AddColumn<string>(
            name: "ChangedBy",
            table: "NhsContractProducts",
            type: "nvarchar(max)",
            nullable: true);

        migrationBuilder.AddColumn<string>(
            name: "CreatedBy",
            table: "NhsContractProducts",
            type: "nvarchar(max)",
            nullable: false,
            defaultValue: "admin");

        migrationBuilder.AddColumn<DateTimeOffset>(
            name: "CreationDate",
            table: "NhsContractProducts",
            type: "datetimeoffset",
            nullable: false,
            defaultValue: new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)));

        migrationBuilder.AddColumn<DateTimeOffset>(
            name: "ChangeDate",
            table: "Manufacturers",
            type: "datetimeoffset",
            nullable: true);

        migrationBuilder.AddColumn<string>(
            name: "ChangedBy",
            table: "Manufacturers",
            type: "nvarchar(max)",
            nullable: true);

        migrationBuilder.AddColumn<string>(
            name: "CreatedBy",
            table: "Manufacturers",
            type: "nvarchar(max)",
            nullable: false,
            defaultValue: "admin");

        migrationBuilder.AddColumn<DateTimeOffset>(
            name: "CreationDate",
            table: "Manufacturers",
            type: "datetimeoffset",
            nullable: false,
            defaultValue: new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)));

        migrationBuilder.AddColumn<DateTimeOffset>(
            name: "ChangeDate",
            table: "HearingAidTypes",
            type: "datetimeoffset",
            nullable: true);

        migrationBuilder.AddColumn<string>(
            name: "ChangedBy",
            table: "HearingAidTypes",
            type: "nvarchar(max)",
            nullable: true);

        migrationBuilder.AddColumn<string>(
            name: "CreatedBy",
            table: "HearingAidTypes",
            type: "nvarchar(max)",
            nullable: false,
            defaultValue: "admin");

        migrationBuilder.AddColumn<DateTimeOffset>(
            name: "CreationDate",
            table: "HearingAidTypes",
            type: "datetimeoffset",
            nullable: false,
            defaultValue: new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)));

        migrationBuilder.AddColumn<DateTimeOffset>(
            name: "ChangeDate",
            table: "ExternalProducts",
            type: "datetimeoffset",
            nullable: true);

        migrationBuilder.AddColumn<string>(
            name: "ChangedBy",
            table: "ExternalProducts",
            type: "nvarchar(max)",
            nullable: true);

        migrationBuilder.AddColumn<string>(
            name: "CreatedBy",
            table: "ExternalProducts",
            type: "nvarchar(max)",
            nullable: false,
            defaultValue: "admin");

        migrationBuilder.AddColumn<DateTimeOffset>(
            name: "CreationDate",
            table: "ExternalProducts",
            type: "datetimeoffset",
            nullable: false,
            defaultValue: new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)));

        migrationBuilder.AddColumn<DateTimeOffset>(
            name: "ChangeDate",
            table: "ExternalProductOptions",
            type: "datetimeoffset",
            nullable: true);

        migrationBuilder.AddColumn<string>(
            name: "ChangedBy",
            table: "ExternalProductOptions",
            type: "nvarchar(max)",
            nullable: true);

        migrationBuilder.AddColumn<string>(
            name: "CreatedBy",
            table: "ExternalProductOptions",
            type: "nvarchar(max)",
            nullable: false,
            defaultValue: "admin");

        migrationBuilder.AddColumn<DateTimeOffset>(
            name: "CreationDate",
            table: "ExternalProductOptions",
            type: "datetimeoffset",
            nullable: false,
            defaultValue: new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)));

        migrationBuilder.AddColumn<DateTimeOffset>(
            name: "ChangeDate",
            table: "ExternalProductImports",
            type: "datetimeoffset",
            nullable: true);

        migrationBuilder.AddColumn<string>(
            name: "ChangedBy",
            table: "ExternalProductImports",
            type: "nvarchar(max)",
            nullable: true);

        migrationBuilder.AddColumn<string>(
            name: "CreatedBy",
            table: "ExternalProductImports",
            type: "nvarchar(max)",
            nullable: false,
            defaultValue: "admin");

        migrationBuilder.AddColumn<DateTimeOffset>(
            name: "CreationDate",
            table: "ExternalProductImports",
            type: "datetimeoffset",
            nullable: false,
            defaultValue: new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)));

        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "CPTCodes",
            type: "nvarchar(max)",
            nullable: false,
            defaultValue: "admin",
            oldClrType: typeof(string),
            oldType: "nvarchar(max)");

        migrationBuilder.AddColumn<DateTimeOffset>(
            name: "ChangeDate",
            table: "Countries",
            type: "datetimeoffset",
            nullable: true);

        migrationBuilder.AddColumn<string>(
            name: "ChangedBy",
            table: "Countries",
            type: "nvarchar(max)",
            nullable: true);

        migrationBuilder.AddColumn<string>(
            name: "CreatedBy",
            table: "Countries",
            type: "nvarchar(max)",
            nullable: false,
            defaultValue: "admin");

        migrationBuilder.AddColumn<DateTimeOffset>(
            name: "CreationDate",
            table: "Countries",
            type: "datetimeoffset",
            nullable: false,
            defaultValue: new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)));

        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "Colors",
            type: "nvarchar(max)",
            nullable: false,
            defaultValue: "admin",
            oldClrType: typeof(string),
            oldType: "nvarchar(max)");

        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "Bundles",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: false,
            defaultValue: "admin",
            oldClrType: typeof(string),
            oldType: "nvarchar(100)",
            oldMaxLength: 100);

        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "BundleProducts",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: false,
            defaultValue: "admin",
            oldClrType: typeof(string),
            oldType: "nvarchar(100)",
            oldMaxLength: 100);

        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "BatteryTypes",
            type: "nvarchar(max)",
            nullable: false,
            defaultValue: "admin",
            oldClrType: typeof(string),
            oldType: "nvarchar(max)");

        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "Attributes",
            type: "nvarchar(max)",
            nullable: false,
            defaultValue: "admin",
            oldClrType: typeof(string),
            oldType: "nvarchar(max)");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("2033baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("2133baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("2233baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("2333baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("2433baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("2533baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("2633baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("2733baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("2833baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("2933baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("2a33baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("2b33baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("2c33baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("2d33baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("2e33baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("2f33baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("3033baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("3133baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("3233baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("3333baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("3401508a-33f4-4fbd-8899-5acda30dcbb9"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("3433baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("3533baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("3633baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("3733baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("3833baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("3933baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("3a33baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("3b33baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("3c33baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("3d33baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("3e33baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("3f33baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("4033baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("4133baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("4233baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("4333baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("4433baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("4533baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("4633baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("4733baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("4833baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("4933baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("4a33baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("4b33baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("4c33baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("4d33baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("4e33baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("4f33baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("5033baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("5133baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("5233baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("5333baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("5433baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("5533baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("5633baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("565a9699-066e-45cf-b960-411ac3d4ec20"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("5733baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("5833baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("5933baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("5a33baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("5b33baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("5c33baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("5d33baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("5e33baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("5f33baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("6033baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("6133baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("6233baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("6333baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("6433baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("6533baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("6633baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("6733baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("6833baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("6933baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("6a33baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("6b33baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("6c33baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("6d33baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("6e33baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("6f33baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("7033baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("7133baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("7233baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("7333baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("7433baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("7533baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("7633baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("7733baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("7833baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("7933baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("7a33baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("7b33baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("7c33baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("7d33baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("7e33baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("7f33baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("8033baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("8133baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("8233baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("8333baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("8433baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("8533baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("8633baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("8733baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("8833baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("8933baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("8a33baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("8b33baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("8c33baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("8d33baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("8e33baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("8f33baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("9033baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("9133baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("9233baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("9333baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("9433baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("9533baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("9633baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("9733baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("9833baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("9933baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("9a33baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("9b33baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("9c33baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("9d33baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("9e33baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("9e395310-8e3c-4284-9a4b-21824d9e34b3"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("9f33baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("a033baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("a133baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("a233baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("a333baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("a433baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("a533baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("a633baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("a733baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("a833baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("a933baa3-2b72-ed11-9f60-00224899f97e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("b27ac0b5-a6a3-4af2-8a78-0b3d11215aca"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "HearingAidTypes",
            keyColumn: "Id",
            keyValue: new Guid("8267c588-abf9-436a-8160-a989b34c3a19"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "HearingAidTypes",
            keyColumn: "Id",
            keyValue: new Guid("8bb7392c-fce0-4522-97c8-1d246b136d01"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "HearingAidTypes",
            keyColumn: "Id",
            keyValue: new Guid("9c3d9f64-88a7-44c1-9138-056bea29cf44"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "HearingAidTypes",
            keyColumn: "Id",
            keyValue: new Guid("bdf916f7-cfb6-4016-bd05-0d1c2e2c5b10"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "HearingAidTypes",
            keyColumn: "Id",
            keyValue: new Guid("eadf48c0-7f9e-4158-90e5-aa976904cae0"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Pathways",
            keyColumn: "Id",
            keyValue: new Guid("0b9909d1-b2bb-4efc-8d24-fd6dcc994013"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Pathways",
            keyColumn: "Id",
            keyValue: new Guid("19de7aa8-e174-47f8-83a8-953bbc15594e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Pathways",
            keyColumn: "Id",
            keyValue: new Guid("345201f6-4599-4871-bb28-2e44834f8a30"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Pathways",
            keyColumn: "Id",
            keyValue: new Guid("3e0a70b0-61e0-434d-a258-709583ea0f2b"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Pathways",
            keyColumn: "Id",
            keyValue: new Guid("440e198b-1089-4c28-99e5-cdcf79332407"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Pathways",
            keyColumn: "Id",
            keyValue: new Guid("4e7301d0-88c5-4f33-962d-ed06b4e64e4d"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Pathways",
            keyColumn: "Id",
            keyValue: new Guid("5a674586-39fc-4ba3-9fd8-c3a2ca17c80e"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Pathways",
            keyColumn: "Id",
            keyValue: new Guid("6b7d87cc-4f5b-4b00-8d55-6da6d5a8c9b3"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Pathways",
            keyColumn: "Id",
            keyValue: new Guid("88c28659-77dc-4bd5-a2d3-31aa671e6210"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Pathways",
            keyColumn: "Id",
            keyValue: new Guid("98dc257c-ec38-49c8-b658-5bb4afc71ce8"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Pathways",
            keyColumn: "Id",
            keyValue: new Guid("9d363ec1-d09a-4380-93af-e0324e9d028d"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Pathways",
            keyColumn: "Id",
            keyValue: new Guid("a8a63efd-3763-41f7-a568-b887b4341e24"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Pathways",
            keyColumn: "Id",
            keyValue: new Guid("ac95bdfd-f8a8-4d1f-89e8-7b4aa06e9165"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Pathways",
            keyColumn: "Id",
            keyValue: new Guid("b3943d8b-80c9-4822-9998-a79a3a42f239"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "Pathways",
            keyColumn: "Id",
            keyValue: new Guid("efff08a8-c1e2-4199-9488-5405506fc9ed"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "ProductCategories",
            keyColumn: "Id",
            keyValue: new Guid("08300ca2-6a6a-4a25-ad16-3818e9550847"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "ProductCategories",
            keyColumn: "Id",
            keyValue: new Guid("30931946-6b02-4d5b-b645-77dcd545ff8c"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "ProductCategories",
            keyColumn: "Id",
            keyValue: new Guid("47acc31b-22af-48dd-aca7-09e3b4f1127c"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "ProductCategories",
            keyColumn: "Id",
            keyValue: new Guid("9c26d46a-3b38-4d34-b723-387e1deaa952"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "ProductCategories",
            keyColumn: "Id",
            keyValue: new Guid("a3e47357-481e-421a-ba04-d6f1946d9e69"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "ProductCategories",
            keyColumn: "Id",
            keyValue: new Guid("a9dc025b-75fd-4b30-b1d3-ca58fb7fb270"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "ProductCategories",
            keyColumn: "Id",
            keyValue: new Guid("b045b8b4-0b41-418f-b2d6-0724ea8177b7"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "ProductCategories",
            keyColumn: "Id",
            keyValue: new Guid("b9ab271e-5c4c-4f2b-88f2-80bd07484813"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });

        migrationBuilder.UpdateData(
            table: "ProductCategories",
            keyColumn: "Id",
            keyValue: new Guid("f2ae794b-734c-4c41-ad84-a84f54000167"),
            columns: new[] { "ChangeDate", "ChangedBy", "CreationDate" },
            values: new object[] { null, null, new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)) });
    }

    /// <inheritdoc />
    protected override void Down(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.DropColumn(
            name: "ChangeDate",
            table: "Suppliers");

        migrationBuilder.DropColumn(
            name: "ChangedBy",
            table: "Suppliers");

        migrationBuilder.DropColumn(
            name: "CreatedBy",
            table: "Suppliers");

        migrationBuilder.DropColumn(
            name: "CreationDate",
            table: "Suppliers");

        migrationBuilder.DropColumn(
            name: "ChangeDate",
            table: "StockTransactions");

        migrationBuilder.DropColumn(
            name: "ChangedBy",
            table: "StockTransactions");

        migrationBuilder.DropColumn(
            name: "CreatedBy",
            table: "StockTransactions");

        migrationBuilder.DropColumn(
            name: "CreationDate",
            table: "StockTransactions");

        migrationBuilder.DropColumn(
            name: "ChangeDate",
            table: "Stocks");

        migrationBuilder.DropColumn(
            name: "ChangedBy",
            table: "Stocks");

        migrationBuilder.DropColumn(
            name: "CreatedBy",
            table: "Stocks");

        migrationBuilder.DropColumn(
            name: "CreationDate",
            table: "Stocks");

        migrationBuilder.DropColumn(
            name: "ChangeDate",
            table: "StockProducts");

        migrationBuilder.DropColumn(
            name: "ChangedBy",
            table: "StockProducts");

        migrationBuilder.DropColumn(
            name: "CreatedBy",
            table: "StockProducts");

        migrationBuilder.DropColumn(
            name: "CreationDate",
            table: "StockProducts");

        migrationBuilder.DropColumn(
            name: "ChangeDate",
            table: "StockProductItems");

        migrationBuilder.DropColumn(
            name: "ChangedBy",
            table: "StockProductItems");

        migrationBuilder.DropColumn(
            name: "CreatedBy",
            table: "StockProductItems");

        migrationBuilder.DropColumn(
            name: "CreationDate",
            table: "StockProductItems");

        migrationBuilder.DropColumn(
            name: "ChangeDate",
            table: "StockProductItemLogs");

        migrationBuilder.DropColumn(
            name: "ChangedBy",
            table: "StockProductItemLogs");

        migrationBuilder.DropColumn(
            name: "CreatedBy",
            table: "StockProductItemLogs");

        migrationBuilder.DropColumn(
            name: "CreationDate",
            table: "StockProductItemLogs");

        migrationBuilder.DropColumn(
            name: "ChangeDate",
            table: "StockAdjustmentReasons");

        migrationBuilder.DropColumn(
            name: "ChangedBy",
            table: "StockAdjustmentReasons");

        migrationBuilder.DropColumn(
            name: "CreatedBy",
            table: "StockAdjustmentReasons");

        migrationBuilder.DropColumn(
            name: "CreationDate",
            table: "StockAdjustmentReasons");

        migrationBuilder.DropColumn(
            name: "ChangeDate",
            table: "Products");

        migrationBuilder.DropColumn(
            name: "ChangedBy",
            table: "Products");

        migrationBuilder.DropColumn(
            name: "CreatedBy",
            table: "Products");

        migrationBuilder.DropColumn(
            name: "CreationDate",
            table: "Products");

        migrationBuilder.DropColumn(
            name: "ChangeDate",
            table: "ProductPathways");

        migrationBuilder.DropColumn(
            name: "ChangedBy",
            table: "ProductPathways");

        migrationBuilder.DropColumn(
            name: "CreatedBy",
            table: "ProductPathways");

        migrationBuilder.DropColumn(
            name: "CreationDate",
            table: "ProductPathways");

        migrationBuilder.DropColumn(
            name: "ChangeDate",
            table: "ProductCPTCodes");

        migrationBuilder.DropColumn(
            name: "ChangedBy",
            table: "ProductCPTCodes");

        migrationBuilder.DropColumn(
            name: "CreatedBy",
            table: "ProductCPTCodes");

        migrationBuilder.DropColumn(
            name: "CreationDate",
            table: "ProductCPTCodes");

        migrationBuilder.DropColumn(
            name: "ChangeDate",
            table: "ProductColors");

        migrationBuilder.DropColumn(
            name: "ChangedBy",
            table: "ProductColors");

        migrationBuilder.DropColumn(
            name: "CreatedBy",
            table: "ProductColors");

        migrationBuilder.DropColumn(
            name: "CreationDate",
            table: "ProductColors");

        migrationBuilder.DropColumn(
            name: "ChangeDate",
            table: "ProductCategoryAccountCodes");

        migrationBuilder.DropColumn(
            name: "ChangedBy",
            table: "ProductCategoryAccountCodes");

        migrationBuilder.DropColumn(
            name: "CreatedBy",
            table: "ProductCategoryAccountCodes");

        migrationBuilder.DropColumn(
            name: "CreationDate",
            table: "ProductCategoryAccountCodes");

        migrationBuilder.DropColumn(
            name: "ChangeDate",
            table: "ProductCategories");

        migrationBuilder.DropColumn(
            name: "ChangedBy",
            table: "ProductCategories");

        migrationBuilder.DropColumn(
            name: "CreatedBy",
            table: "ProductCategories");

        migrationBuilder.DropColumn(
            name: "CreationDate",
            table: "ProductCategories");

        migrationBuilder.DropColumn(
            name: "ChangeDate",
            table: "ProductBatteryTypes");

        migrationBuilder.DropColumn(
            name: "ChangedBy",
            table: "ProductBatteryTypes");

        migrationBuilder.DropColumn(
            name: "CreatedBy",
            table: "ProductBatteryTypes");

        migrationBuilder.DropColumn(
            name: "CreationDate",
            table: "ProductBatteryTypes");

        migrationBuilder.DropColumn(
            name: "ChangeDate",
            table: "ProductAttributes");

        migrationBuilder.DropColumn(
            name: "ChangedBy",
            table: "ProductAttributes");

        migrationBuilder.DropColumn(
            name: "CreatedBy",
            table: "ProductAttributes");

        migrationBuilder.DropColumn(
            name: "CreationDate",
            table: "ProductAttributes");

        migrationBuilder.DropColumn(
            name: "ChangeDate",
            table: "Pathways");

        migrationBuilder.DropColumn(
            name: "ChangedBy",
            table: "Pathways");

        migrationBuilder.DropColumn(
            name: "CreatedBy",
            table: "Pathways");

        migrationBuilder.DropColumn(
            name: "CreationDate",
            table: "Pathways");

        migrationBuilder.DropColumn(
            name: "ChangeDate",
            table: "NhsContractProducts");

        migrationBuilder.DropColumn(
            name: "ChangedBy",
            table: "NhsContractProducts");

        migrationBuilder.DropColumn(
            name: "CreatedBy",
            table: "NhsContractProducts");

        migrationBuilder.DropColumn(
            name: "CreationDate",
            table: "NhsContractProducts");

        migrationBuilder.DropColumn(
            name: "ChangeDate",
            table: "Manufacturers");

        migrationBuilder.DropColumn(
            name: "ChangedBy",
            table: "Manufacturers");

        migrationBuilder.DropColumn(
            name: "CreatedBy",
            table: "Manufacturers");

        migrationBuilder.DropColumn(
            name: "CreationDate",
            table: "Manufacturers");

        migrationBuilder.DropColumn(
            name: "ChangeDate",
            table: "HearingAidTypes");

        migrationBuilder.DropColumn(
            name: "ChangedBy",
            table: "HearingAidTypes");

        migrationBuilder.DropColumn(
            name: "CreatedBy",
            table: "HearingAidTypes");

        migrationBuilder.DropColumn(
            name: "CreationDate",
            table: "HearingAidTypes");

        migrationBuilder.DropColumn(
            name: "ChangeDate",
            table: "ExternalProducts");

        migrationBuilder.DropColumn(
            name: "ChangedBy",
            table: "ExternalProducts");

        migrationBuilder.DropColumn(
            name: "CreatedBy",
            table: "ExternalProducts");

        migrationBuilder.DropColumn(
            name: "CreationDate",
            table: "ExternalProducts");

        migrationBuilder.DropColumn(
            name: "ChangeDate",
            table: "ExternalProductOptions");

        migrationBuilder.DropColumn(
            name: "ChangedBy",
            table: "ExternalProductOptions");

        migrationBuilder.DropColumn(
            name: "CreatedBy",
            table: "ExternalProductOptions");

        migrationBuilder.DropColumn(
            name: "CreationDate",
            table: "ExternalProductOptions");

        migrationBuilder.DropColumn(
            name: "ChangeDate",
            table: "ExternalProductImports");

        migrationBuilder.DropColumn(
            name: "ChangedBy",
            table: "ExternalProductImports");

        migrationBuilder.DropColumn(
            name: "CreatedBy",
            table: "ExternalProductImports");

        migrationBuilder.DropColumn(
            name: "CreationDate",
            table: "ExternalProductImports");

        migrationBuilder.DropColumn(
            name: "ChangeDate",
            table: "Countries");

        migrationBuilder.DropColumn(
            name: "ChangedBy",
            table: "Countries");

        migrationBuilder.DropColumn(
            name: "CreatedBy",
            table: "Countries");

        migrationBuilder.DropColumn(
            name: "CreationDate",
            table: "Countries");

        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "CPTCodes",
            type: "nvarchar(max)",
            nullable: false,
            oldClrType: typeof(string),
            oldType: "nvarchar(max)",
            oldDefaultValue: "admin");

        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "Colors",
            type: "nvarchar(max)",
            nullable: false,
            oldClrType: typeof(string),
            oldType: "nvarchar(max)",
            oldDefaultValue: "admin");

        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "Bundles",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: false,
            oldClrType: typeof(string),
            oldType: "nvarchar(100)",
            oldMaxLength: 100,
            oldDefaultValue: "admin");

        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "BundleProducts",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: false,
            oldClrType: typeof(string),
            oldType: "nvarchar(100)",
            oldMaxLength: 100,
            oldDefaultValue: "admin");

        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "BatteryTypes",
            type: "nvarchar(max)",
            nullable: false,
            oldClrType: typeof(string),
            oldType: "nvarchar(max)",
            oldDefaultValue: "admin");

        migrationBuilder.AlterColumn<string>(
            name: "CreatedBy",
            table: "Attributes",
            type: "nvarchar(max)",
            nullable: false,
            oldClrType: typeof(string),
            oldType: "nvarchar(max)",
            oldDefaultValue: "admin");
    }
}
