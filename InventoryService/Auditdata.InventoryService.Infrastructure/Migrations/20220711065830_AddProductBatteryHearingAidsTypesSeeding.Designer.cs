// <auto-generated />
using System;
using Auditdata.InventoryService.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace Auditdata.InventoryService.Infrastructure.Migrations
{
    [DbContext(typeof(InventoryDbContext))]
    [Migration("20220711065830_AddProductBatteryHearingAidsTypesSeeding")]
    partial class AddProductBatteryHearingAidsTypesSeeding
    {
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "6.0.6")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder, 1L, 1);

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.BatteryType", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("BatteryTypes");

                    b.HasData(
                        new
                        {
                            Id = new Guid("975340bb-d5fd-41e2-9bab-f6f06109edc9"),
                            IsDeleted = false,
                            Name = "10"
                        },
                        new
                        {
                            Id = new Guid("ad270856-4d29-4e3e-b0bc-e9089d196fab"),
                            IsDeleted = false,
                            Name = "312"
                        },
                        new
                        {
                            Id = new Guid("c33221bc-e71a-4b85-b92d-8632358cd37d"),
                            IsDeleted = false,
                            Name = "13"
                        },
                        new
                        {
                            Id = new Guid("6b84bed9-740c-43e7-9855-fd8275231a43"),
                            IsDeleted = false,
                            Name = "675"
                        });
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.HearingAidType", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("HearingAidTypes");

                    b.HasData(
                        new
                        {
                            Id = new Guid("bdf916f7-cfb6-4016-bd05-0d1c2e2c5b10"),
                            IsDeleted = false,
                            Name = "BTE"
                        },
                        new
                        {
                            Id = new Guid("8bb7392c-fce0-4522-97c8-1d246b136d01"),
                            IsDeleted = false,
                            Name = "RIC"
                        },
                        new
                        {
                            Id = new Guid("eadf48c0-7f9e-4158-90e5-aa976904cae0"),
                            IsDeleted = false,
                            Name = "CIC"
                        },
                        new
                        {
                            Id = new Guid("14b2517f-33a2-4a46-bed3-a33cea6dbe92"),
                            IsDeleted = false,
                            Name = "ITC"
                        },
                        new
                        {
                            Id = new Guid("8267c588-abf9-436a-8160-a989b34c3a19"),
                            IsDeleted = false,
                            Name = "ITE"
                        },
                        new
                        {
                            Id = new Guid("49dbb0e9-bb6b-4ef8-be24-88fdefafb920"),
                            IsDeleted = false,
                            Name = "IIC"
                        },
                        new
                        {
                            Id = new Guid("c9ec550f-4673-483f-b085-eda334887e0c"),
                            IsDeleted = false,
                            Name = "Open"
                        },
                        new
                        {
                            Id = new Guid("b350b034-3c83-45d9-af86-a1a0554684b9"),
                            IsDeleted = false,
                            Name = "Power"
                        });
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.Manufacturer", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("Manufacturers");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.ManufacturerProductType", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<Guid?>("ManufacturerId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("ProductTypeId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("ManufacturerId");

                    b.HasIndex("ProductTypeId");

                    b.ToTable("ManufacturerProductTypes");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.Product", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("BatteryTypeId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Code")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("HearingAidTypeId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<bool>("IsNHS")
                        .HasColumnType("bit");

                    b.Property<bool>("IsSellable")
                        .HasColumnType("bit");

                    b.Property<bool>("IsSerialized")
                        .HasColumnType("bit");

                    b.Property<Guid?>("ManufacturerId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("ModifiedOn")
                        .HasColumnType("datetime2");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Quantity")
                        .HasColumnType("int");

                    b.Property<decimal>("RetailPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("TypeId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Warranty")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("BatteryTypeId");

                    b.HasIndex("HearingAidTypeId");

                    b.HasIndex("ManufacturerId");

                    b.HasIndex("TypeId");

                    b.ToTable("Products");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.ProductType", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("ProductTypes");

                    b.HasData(
                        new
                        {
                            Id = new Guid("08300ca2-6a6a-4a25-ad16-3818e9550847"),
                            IsDeleted = false,
                            Name = "Battery"
                        },
                        new
                        {
                            Id = new Guid("30931946-6b02-4d5b-b645-77dcd545ff8c"),
                            IsDeleted = false,
                            Name = "Service"
                        },
                        new
                        {
                            Id = new Guid("a3e47357-481e-421a-ba04-d6f1946d9e69"),
                            IsDeleted = false,
                            Name = "Repair type"
                        },
                        new
                        {
                            Id = new Guid("47acc31b-22af-48dd-aca7-09e3b4f1127c"),
                            IsDeleted = false,
                            Name = "Earmold"
                        },
                        new
                        {
                            Id = new Guid("b045b8b4-0b41-418f-b2d6-0724ea8177b7"),
                            IsDeleted = false,
                            Name = "Hearing Aids"
                        },
                        new
                        {
                            Id = new Guid("9c26d46a-3b38-4d34-b723-387e1deaa952"),
                            IsDeleted = false,
                            Name = "Accessories"
                        },
                        new
                        {
                            Id = new Guid("f2ae794b-734c-4c41-ad84-a84f54000167"),
                            IsDeleted = false,
                            Name = "Remote"
                        },
                        new
                        {
                            Id = new Guid("a9dc025b-75fd-4b30-b1d3-ca58fb7fb270"),
                            IsDeleted = false,
                            Name = "Receiver"
                        });
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.Stock", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.ToTable("Stocks");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.StockProduct", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<bool>("IsSellable")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("ModifiedOn")
                        .HasColumnType("datetime2");

                    b.Property<decimal>("Price")
                        .HasColumnType("decimal(18,2)");

                    b.Property<Guid?>("ProductId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("Quantity")
                        .HasColumnType("int");

                    b.Property<Guid?>("StockId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("ProductId");

                    b.HasIndex("StockId");

                    b.ToTable("StockProducts");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.StockProductItem", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("ModifiedOn")
                        .HasColumnType("datetime2");

                    b.Property<string>("SerialNumber")
                        .HasColumnType("nvarchar(450)");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<Guid?>("StockProductId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("SerialNumber")
                        .IsUnique()
                        .HasFilter("[SerialNumber] IS NOT NULL");

                    b.HasIndex("StockProductId");

                    b.ToTable("StockProductItems");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.StockTransaction", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<decimal>("Amount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("ModifiedOn")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("ProductId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("Quantity")
                        .HasColumnType("int");

                    b.Property<Guid?>("StockId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("TotalQuantity")
                        .HasColumnType("int");

                    b.Property<string>("TransactionId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("TypeId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("ProductId");

                    b.HasIndex("StockId");

                    b.ToTable("StockTransactions");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.ManufacturerProductType", b =>
                {
                    b.HasOne("Auditdata.InventoryService.Core.Entities.Manufacturer", "Manufacturer")
                        .WithMany("ManufacturerProductTypes")
                        .HasForeignKey("ManufacturerId");

                    b.HasOne("Auditdata.InventoryService.Core.Entities.ProductType", "ProductType")
                        .WithMany()
                        .HasForeignKey("ProductTypeId");

                    b.Navigation("Manufacturer");

                    b.Navigation("ProductType");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.Product", b =>
                {
                    b.HasOne("Auditdata.InventoryService.Core.Entities.BatteryType", "BatteryType")
                        .WithMany()
                        .HasForeignKey("BatteryTypeId");

                    b.HasOne("Auditdata.InventoryService.Core.Entities.HearingAidType", "HearingAidType")
                        .WithMany()
                        .HasForeignKey("HearingAidTypeId");

                    b.HasOne("Auditdata.InventoryService.Core.Entities.Manufacturer", "Manufacturer")
                        .WithMany()
                        .HasForeignKey("ManufacturerId");

                    b.HasOne("Auditdata.InventoryService.Core.Entities.ProductType", "Type")
                        .WithMany()
                        .HasForeignKey("TypeId");

                    b.Navigation("BatteryType");

                    b.Navigation("HearingAidType");

                    b.Navigation("Manufacturer");

                    b.Navigation("Type");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.StockProduct", b =>
                {
                    b.HasOne("Auditdata.InventoryService.Core.Entities.Product", "Product")
                        .WithMany()
                        .HasForeignKey("ProductId");

                    b.HasOne("Auditdata.InventoryService.Core.Entities.Stock", "Stock")
                        .WithMany()
                        .HasForeignKey("StockId");

                    b.Navigation("Product");

                    b.Navigation("Stock");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.StockProductItem", b =>
                {
                    b.HasOne("Auditdata.InventoryService.Core.Entities.StockProduct", "StockProduct")
                        .WithMany("StockProductItems")
                        .HasForeignKey("StockProductId");

                    b.Navigation("StockProduct");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.StockTransaction", b =>
                {
                    b.HasOne("Auditdata.InventoryService.Core.Entities.Product", "Product")
                        .WithMany()
                        .HasForeignKey("ProductId");

                    b.HasOne("Auditdata.InventoryService.Core.Entities.Stock", "Stock")
                        .WithMany()
                        .HasForeignKey("StockId");

                    b.Navigation("Product");

                    b.Navigation("Stock");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.Manufacturer", b =>
                {
                    b.Navigation("ManufacturerProductTypes");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.StockProduct", b =>
                {
                    b.Navigation("StockProductItems");
                });
#pragma warning restore 612, 618
        }
    }
}
