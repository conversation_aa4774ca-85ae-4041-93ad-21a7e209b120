using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Auditdata.InventoryService.Infrastructure.Migrations;

/// <inheritdoc />
public partial class Countries_IsoCodes : Migration
{
    /// <inheritdoc />
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.AddColumn<string>(
            name: "Iso2Code",
            table: "Countries",
            type: "nvarchar(2)",
            maxLength: 2,
            nullable: false,
            defaultValue: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("2033baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("2133baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("2233baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("2333baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("2433baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("2533baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("2633baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("2733baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("2833baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("2933baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("2a33baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("2b33baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("2c33baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("2d33baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("2e33baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("2f33baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("3033baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("3133baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("3233baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("3333baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("3401508a-33f4-4fbd-8899-5acda30dcbb9"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("3433baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("3533baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("3633baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("3733baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("3833baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("3933baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("3a33baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("3b33baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("3c33baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("3d33baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("3e33baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("3f33baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("4033baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("4133baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("4233baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("4333baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("4433baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("4533baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("4633baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("4733baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("4833baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("4933baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("4a33baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("4b33baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("4c33baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("4d33baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("4e33baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("4f33baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("5033baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("5133baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("5233baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("5333baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("5433baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("5533baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("5633baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("565a9699-066e-45cf-b960-411ac3d4ec20"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("5733baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("5833baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("5933baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("5a33baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("5b33baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("5c33baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("5d33baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("5e33baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("5f33baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("6033baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("6133baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("6233baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("6333baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("6433baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("6533baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("6633baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("6733baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("6833baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("6933baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("6a33baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("6b33baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("6c33baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("6d33baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("6e33baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("6f33baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("7033baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("7133baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("7233baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("7333baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("7433baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("7533baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("7633baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("7733baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("7833baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("7933baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("7a33baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("7b33baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("7c33baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("7d33baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("7e33baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("7f33baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("8033baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("8133baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("8233baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("8333baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("8433baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("8533baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("8633baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("8733baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("8833baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("8933baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("8a33baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("8b33baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("8c33baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("8d33baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("8e33baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("8f33baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("9033baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("9133baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("9233baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("9333baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("9433baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("9533baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("9633baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("9733baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("9833baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("9933baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("9a33baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("9b33baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("9c33baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("9d33baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("9e33baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("9e395310-8e3c-4284-9a4b-21824d9e34b3"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("9f33baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("a033baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("a133baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("a233baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("a333baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("a433baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("a533baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("a633baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("a733baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("a833baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("a933baa3-2b72-ed11-9f60-00224899f97e"),
            column: "Iso2Code",
            value: "");

        migrationBuilder.UpdateData(
            table: "Countries",
            keyColumn: "Id",
            keyValue: new Guid("b27ac0b5-a6a3-4af2-8a78-0b3d11215aca"),
            column: "Iso2Code",
            value: "");
    }

    /// <inheritdoc />
    protected override void Down(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.DropColumn(
            name: "Iso2Code",
            table: "Countries");
    }
}
