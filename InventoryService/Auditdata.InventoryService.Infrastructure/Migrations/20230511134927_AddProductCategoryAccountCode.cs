using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Auditdata.InventoryService.Infrastructure.Migrations;

/// <inheritdoc />
public partial class AddProductCategoryAccountCode : Migration
{
    /// <inheritdoc />
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.CreateTable(
            name: "ProductCategoryAccountCodes",
            columns: table => new
            {
                Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                ProductCategoryId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                AccountCode = table.Column<string>(type: "nvarchar(10)", maxLength: 10, nullable: false),
                TenantId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                CreatedOn = table.Column<DateTime>(type: "datetime2", nullable: false),
                ModifiedOn = table.Column<DateTime>(type: "datetime2", nullable: true)
            },
            constraints: table =>
            {
                table.PrimaryKey("PK_ProductCategoryAccountCodes", x => x.Id);
                table.ForeignKey(
                    name: "FK_ProductCategoryAccountCodes_ProductCategories_ProductCategoryId",
                    column: x => x.ProductCategoryId,
                    principalTable: "ProductCategories",
                    principalColumn: "Id",
                    onDelete: ReferentialAction.Cascade);
            });

        migrationBuilder.CreateIndex(
            name: "IX_ProductCategoryAccountCodes_TenantId_ProductCategoryId",
            table: "ProductCategoryAccountCodes",
            columns: new[] { "TenantId", "ProductCategoryId" },
            unique: true);
    }

    /// <inheritdoc />
    protected override void Down(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.DropTable(
            name: "ProductCategoryAccountCodes");
    }
}
