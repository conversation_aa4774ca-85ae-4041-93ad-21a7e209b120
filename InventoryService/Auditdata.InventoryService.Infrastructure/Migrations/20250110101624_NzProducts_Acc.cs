using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Auditdata.InventoryService.Infrastructure.Migrations;

/// <inheritdoc />
public partial class NzProducts_Acc : Migration
{
    /// <inheritdoc />
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.AddColumn<string>(
            name: "Acc_Code",
            table: "Products",
            type: "nvarchar(50)",
            maxLength: 50,
            nullable: true);

        migrationBuilder.AddColumn<string>(
            name: "Acc_Description",
            table: "Products",
            type: "nvarchar(4000)",
            maxLength: 4000,
            nullable: true);

        migrationBuilder.AddColumn<decimal>(
            name: "Acc_PriceExclGst",
            table: "Products",
            type: "decimal(18,2)",
            precision: 18,
            scale: 2,
            nullable: true);

        migrationBuilder.AddColumn<decimal>(
            name: "Acc_PriceInclGst",
            table: "Products",
            type: "decimal(18,2)",
            precision: 18,
            scale: 2,
            nullable: true);
    }

    /// <inheritdoc />
    protected override void Down(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.DropColumn(
            name: "Acc_Code",
            table: "Products");

        migrationBuilder.DropColumn(
            name: "Acc_Description",
            table: "Products");

        migrationBuilder.DropColumn(
            name: "Acc_PriceExclGst",
            table: "Products");

        migrationBuilder.DropColumn(
            name: "Acc_PriceInclGst",
            table: "Products");
    }
}
