// <auto-generated />
using System;
using Auditdata.InventoryService.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace Auditdata.InventoryService.Infrastructure.Migrations
{
    [DbContext(typeof(InventoryDbContext))]
    [Migration("20250313085251_AddMovedToReadyForCalculationDateTime")]
    partial class AddMovedToReadyForCalculationDateTime
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.10")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.Attribute", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("NEWSEQUENTIALID()");

                    b.Property<DateTimeOffset?>("ChangeDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("ChangedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Code")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasDefaultValue("admin");

                    b.Property<DateTimeOffset>("CreationDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ValueType")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.HasKey("Id");

                    b.ToTable("Attributes");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.AttributeProductCategory", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("NEWSEQUENTIALID()");

                    b.Property<Guid>("AttributeId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTimeOffset?>("ChangeDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("ChangedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasDefaultValue("admin");

                    b.Property<DateTimeOffset>("CreationDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<Guid>("ProductCategoryId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("AttributeId");

                    b.HasIndex("ProductCategoryId");

                    b.ToTable("AttributeProductCategories");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.AuditRequests.AuditLocation", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("NEWSEQUENTIALID()");

                    b.Property<Guid>("AuditRequestId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTimeOffset?>("ChangeDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("ChangedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasDefaultValue("admin");

                    b.Property<DateTimeOffset>("CreationDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<Guid>("LocationId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("LocationName")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<byte[]>("Version")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.HasKey("Id");

                    b.HasIndex("AuditRequestId");

                    b.HasIndex("LocationId");

                    b.ToTable("AuditLocations");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.AuditRequests.AuditProduct", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("NEWSEQUENTIALID()");

                    b.Property<Guid>("AuditRequestId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTimeOffset?>("ChangeDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("ChangedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int>("CountedQuantity")
                        .HasColumnType("int");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasDefaultValue("admin");

                    b.Property<DateTimeOffset>("CreationDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<int>("ExpectedQuantity")
                        .HasColumnType("int");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<string>("ProductCategoryName")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid>("ProductId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ProductName")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("Skus")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(2000)
                        .HasColumnType("nvarchar(2000)")
                        .HasDefaultValue("[]");

                    b.Property<string>("SupplierName")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<byte[]>("Version")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.HasKey("Id");

                    b.HasIndex("AuditRequestId");

                    b.HasIndex("ProductId");

                    b.ToTable("AuditProducts");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.AuditRequests.AuditProductSerialNumber", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("NEWSEQUENTIALID()");

                    b.Property<Guid>("AuditProductId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTimeOffset?>("ChangeDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("ChangedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Comments")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasDefaultValue("admin");

                    b.Property<DateTimeOffset>("CreationDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<bool>("IsManual")
                        .HasColumnType("bit");

                    b.Property<string>("SerialNumber")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<byte[]>("Version")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.HasKey("Id");

                    b.HasIndex("AuditProductId");

                    b.HasIndex("SerialNumber", "TenantId");

                    b.HasIndex("Status", "IsManual", "TenantId");

                    b.ToTable("AuditProductSerialNumbers");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.AuditRequests.AuditRequest", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("NEWSEQUENTIALID()");

                    b.Property<bool>("AddAllProductsFromCatalog")
                        .HasColumnType("bit");

                    b.Property<bool>("BlindStockCalculation")
                        .HasColumnType("bit");

                    b.Property<DateTimeOffset?>("ChangeDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("ChangedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasDefaultValue("admin");

                    b.Property<DateTimeOffset>("CreationDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<DateTimeOffset>("DateTimeDue")
                        .HasColumnType("datetimeoffset");

                    b.Property<bool>("IncludeOnTrialProducts")
                        .HasColumnType("bit");

                    b.Property<bool>("IndicateSerialNumber")
                        .HasColumnType("bit");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<DateTimeOffset?>("MovedToReadyForCalculationDateTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("Notes")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("RequestNumber")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<bool>("ReturnedToDraft")
                        .HasColumnType("bit");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<byte[]>("Version")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.HasKey("Id");

                    b.HasIndex("DateTimeDue", "TenantId", "IsDeleted");

                    b.HasIndex("RequestNumber", "TenantId", "IsDeleted")
                        .IsUnique()
                        .HasFilter("RequestNumber IS NOT NULL AND IsDeleted = 0");

                    b.HasIndex("Status", "TenantId", "IsDeleted");

                    b.ToTable("AuditRequests");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.AuditRequests.AuditRequestLog", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("NEWSEQUENTIALID()");

                    b.Property<string>("Action")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid?>("AuditRequestId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTimeOffset?>("ChangeDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("ChangedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasDefaultValue("admin");

                    b.Property<DateTimeOffset>("CreationDate")
                        .HasColumnType("datetimeoffset");

                    b.HasKey("Id");

                    b.HasIndex("AuditRequestId");

                    b.ToTable("AuditRequestLogs");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.BatteryType", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("NEWSEQUENTIALID()");

                    b.Property<DateTimeOffset?>("ChangeDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("ChangedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasDefaultValue("admin");

                    b.Property<DateTimeOffset>("CreationDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("Name", "TenantId", "IsDeleted")
                        .IsUnique()
                        .HasFilter("IsDeleted = 0");

                    b.ToTable("BatteryTypes");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.Bundle", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("NEWSEQUENTIALID()");

                    b.Property<DateTimeOffset?>("ChangeDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("ChangedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasDefaultValue("admin");

                    b.Property<DateTimeOffset>("CreationDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<bool>("IsAutomaticDelivery")
                        .HasColumnType("bit");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<bool>("IsPriceChangeAllowed")
                        .HasColumnType("bit");

                    b.Property<bool>("IsSellable")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("nvarchar(250)");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<decimal>("TotalCost")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.HasKey("Id");

                    b.ToTable("Bundles");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.BundleProduct", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("NEWSEQUENTIALID()");

                    b.Property<Guid>("BundleId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTimeOffset?>("ChangeDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("ChangedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasDefaultValue("admin");

                    b.Property<DateTimeOffset>("CreationDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<decimal>("Price")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<Guid>("ProductId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("Quantity")
                        .HasColumnType("int");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("BundleId");

                    b.HasIndex("ProductId");

                    b.ToTable("BundleProducts");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.CPTCode", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("NEWSEQUENTIALID()");

                    b.Property<DateTimeOffset?>("ChangeDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("ChangedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasDefaultValue("admin");

                    b.Property<DateTimeOffset>("CreationDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("Description")
                        .HasMaxLength(4000)
                        .HasColumnType("nvarchar(4000)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("Code", "TenantId", "IsDeleted")
                        .IsUnique()
                        .HasFilter("IsDeleted = 0");

                    b.ToTable("CPTCodes");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.Color", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("NEWSEQUENTIALID()");

                    b.Property<DateTimeOffset?>("ChangeDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("ChangedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasDefaultValue("admin");

                    b.Property<DateTimeOffset>("CreationDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("Name", "TenantId", "IsDeleted")
                        .IsUnique()
                        .HasFilter("IsDeleted = 0");

                    b.ToTable("Colors");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.Country", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("NEWSEQUENTIALID()");

                    b.Property<DateTimeOffset?>("ChangeDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("ChangedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasDefaultValue("admin");

                    b.Property<DateTimeOffset>("CreationDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<string>("Iso2Code")
                        .IsRequired()
                        .HasMaxLength(6)
                        .HasColumnType("nvarchar(6)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("TenantId", "Name", "IsDeleted")
                        .IsUnique()
                        .HasFilter("IsDeleted = 0");

                    b.ToTable("Countries");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.ExternalProduct", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("NEWSEQUENTIALID()");

                    b.Property<string>("Category")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<DateTimeOffset?>("ChangeDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("ChangedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasDefaultValue("admin");

                    b.Property<DateTimeOffset>("CreationDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("ExternalId")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("HearingAidType")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid>("ImportId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Manufacturer")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<DateTimeOffset>("ModifiedAt")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid?>("ProductId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("ImportId");

                    b.HasIndex("ProductId");

                    b.ToTable("ExternalProducts");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.ExternalProductImport", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("NEWSEQUENTIALID()");

                    b.Property<DateTimeOffset?>("ChangeDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("ChangedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasDefaultValue("admin");

                    b.Property<DateTimeOffset>("CreationDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("FileContent")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<string>("Source")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<DateTimeOffset?>("StartedAt")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("State")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTimeOffset?>("UploadedAt")
                        .HasColumnType("datetimeoffset");

                    b.HasKey("Id");

                    b.ToTable("ExternalProductImports");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.ExternalProductOption", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("NEWSEQUENTIALID()");

                    b.Property<DateTimeOffset?>("ChangeDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("ChangedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasDefaultValue("admin");

                    b.Property<DateTimeOffset>("CreationDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("ExternalId")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid>("ExternalProductId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTimeOffset>("ModifiedAt")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("ExternalProductId");

                    b.ToTable("ExternalProductOptions");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.HearingAidType", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("NEWSEQUENTIALID()");

                    b.Property<DateTimeOffset?>("ChangeDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("ChangedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasDefaultValue("admin");

                    b.Property<DateTimeOffset>("CreationDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("TenantId", "Name", "IsDeleted")
                        .IsUnique();

                    b.ToTable("HearingAidTypes");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.ImportMetadata", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("NEWSEQUENTIALID()");

                    b.Property<int>("AddedObjectsCount")
                        .HasColumnType("int");

                    b.Property<Guid?>("CategoryId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTimeOffset?>("ChangeDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("ChangedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasDefaultValue("admin");

                    b.Property<DateTimeOffset>("CreationDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("EntityType")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("Filename")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<DateTimeOffset>("ImportDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("UpdatedObjectsCount")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("CategoryId");

                    b.ToTable("ImportMetadatas");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.Manufacturer", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("NEWSEQUENTIALID()");

                    b.Property<string>("Address1")
                        .IsRequired()
                        .HasMaxLength(4000)
                        .HasColumnType("nvarchar(4000)");

                    b.Property<string>("Address2")
                        .HasMaxLength(4000)
                        .HasColumnType("nvarchar(4000)");

                    b.Property<DateTimeOffset?>("ChangeDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("ChangedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("City")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid?>("CountryId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasDefaultValue("admin");

                    b.Property<DateTimeOffset>("CreationDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("EmailAddress")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("FaxNumber")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("PhoneNumber")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("PostalCode")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("State")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid?>("StateId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Website")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.HasKey("Id");

                    b.HasIndex("CountryId");

                    b.HasIndex("Name", "TenantId", "IsDeleted")
                        .IsUnique()
                        .HasFilter("IsDeleted = 0");

                    b.ToTable("Manufacturers");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.NhsContractProduct", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("NEWSEQUENTIALID()");

                    b.Property<DateTimeOffset?>("ChangeDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("ChangedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<Guid>("ContractId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasDefaultValue("admin");

                    b.Property<DateTimeOffset>("CreationDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<Guid>("ProductId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("ProductId");

                    b.ToTable("NhsContractProducts");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.Pathway", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("NEWSEQUENTIALID()");

                    b.Property<DateTimeOffset?>("ChangeDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("ChangedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasDefaultValue("admin");

                    b.Property<DateTimeOffset>("CreationDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.HasKey("Id");

                    b.ToTable("Pathways");

                    b.HasData(
                        new
                        {
                            Id = new Guid("4e7301d0-88c5-4f33-962d-ed06b4e64e4d"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Wax Removal",
                            Type = "wax_removal"
                        },
                        new
                        {
                            Id = new Guid("a8a63efd-3763-41f7-a568-b887b4341e24"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "HA Fitting",
                            Type = "ha_fitting"
                        },
                        new
                        {
                            Id = new Guid("9d363ec1-d09a-4380-93af-e0324e9d028d"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Recall",
                            Type = "recall"
                        },
                        new
                        {
                            Id = new Guid("345201f6-4599-4871-bb28-2e44834f8a30"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Aftercare",
                            Type = "aftercare"
                        },
                        new
                        {
                            Id = new Guid("98dc257c-ec38-49c8-b658-5bb4afc71ce8"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Remote Aftercare",
                            Type = "remote_aftercare"
                        },
                        new
                        {
                            Id = new Guid("6b7d87cc-4f5b-4b00-8d55-6da6d5a8c9b3"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Hearing Check",
                            Type = "hearing_check"
                        },
                        new
                        {
                            Id = new Guid("b3943d8b-80c9-4822-9998-a79a3a42f239"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Private Test",
                            Type = "private_test"
                        },
                        new
                        {
                            Id = new Guid("19de7aa8-e174-47f8-83a8-953bbc15594e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "NHS Assess & Fit",
                            Type = "nhs_assess_and_fit"
                        },
                        new
                        {
                            Id = new Guid("5a674586-39fc-4ba3-9fd8-c3a2ca17c80e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Private Test & HA Consult",
                            Type = "private_test_ha_consult"
                        },
                        new
                        {
                            Id = new Guid("440e198b-1089-4c28-99e5-cdcf79332407"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Private HA Consultation",
                            Type = "private_ha_consultation"
                        },
                        new
                        {
                            Id = new Guid("3e0a70b0-61e0-434d-a258-709583ea0f2b"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Wax Triage",
                            Type = "wax_triage"
                        },
                        new
                        {
                            Id = new Guid("0b9909d1-b2bb-4efc-8d24-fd6dcc994013"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Follow Up",
                            Type = "follow_up"
                        },
                        new
                        {
                            Id = new Guid("88c28659-77dc-4bd5-a2d3-31aa671e6210"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Remote Follow Up",
                            Type = "remote_follow_up"
                        },
                        new
                        {
                            Id = new Guid("ac95bdfd-f8a8-4d1f-89e8-7b4aa06e9165"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Re-Assessment",
                            Type = "re_assessment"
                        },
                        new
                        {
                            Id = new Guid("efff08a8-c1e2-4199-9488-5405506fc9ed"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Hearing Protection",
                            Type = "hearing_protection"
                        });
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.ProductAttribute", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("NEWSEQUENTIALID()");

                    b.Property<Guid>("AttributeId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTimeOffset?>("ChangeDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("ChangedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasDefaultValue("admin");

                    b.Property<DateTimeOffset>("CreationDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<Guid>("ProductId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("AttributeId");

                    b.HasIndex("ProductId");

                    b.ToTable("ProductAttributes");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.ProductBatteryType", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("NEWSEQUENTIALID()");

                    b.Property<Guid>("BatteryTypeId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTimeOffset?>("ChangeDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("ChangedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasDefaultValue("admin");

                    b.Property<DateTimeOffset>("CreationDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<Guid>("ProductId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("BatteryTypeId");

                    b.HasIndex("ProductId");

                    b.ToTable("ProductBatteryTypes");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.ProductCPTCode", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("NEWSEQUENTIALID()");

                    b.Property<Guid>("CPTCodeId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTimeOffset?>("ChangeDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("ChangedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasDefaultValue("admin");

                    b.Property<DateTimeOffset>("CreationDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<Guid>("ProductId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("CPTCodeId");

                    b.HasIndex("ProductId", "CPTCodeId", "TenantId")
                        .IsUnique();

                    b.ToTable("ProductCPTCodes");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.ProductCategory", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("NEWSEQUENTIALID()");

                    b.Property<DateTimeOffset?>("ChangeDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("ChangedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasDefaultValue("admin");

                    b.Property<DateTimeOffset>("CreationDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("TenantId", "Name", "IsDeleted")
                        .IsUnique();

                    b.ToTable("ProductCategories");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.ProductCategoryAccountCode", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("NEWSEQUENTIALID()");

                    b.Property<string>("AccountCode")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<DateTimeOffset?>("ChangeDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("ChangedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasDefaultValue("admin");

                    b.Property<DateTimeOffset>("CreationDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<Guid>("ProductCategoryId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("ProductCategoryId")
                        .IsUnique();

                    b.HasIndex("TenantId", "ProductCategoryId")
                        .IsUnique();

                    b.ToTable("ProductCategoryAccountCodes");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.ProductColor", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("NEWSEQUENTIALID()");

                    b.Property<DateTimeOffset?>("ChangeDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("ChangedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<Guid>("ColorId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasDefaultValue("admin");

                    b.Property<DateTimeOffset>("CreationDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<Guid>("ProductId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("ColorId");

                    b.HasIndex("ProductId", "ColorId", "TenantId")
                        .IsUnique();

                    b.ToTable("ProductColors");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.ProductPathway", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("NEWSEQUENTIALID()");

                    b.Property<DateTimeOffset?>("ChangeDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("ChangedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasDefaultValue("admin");

                    b.Property<DateTimeOffset>("CreationDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<Guid?>("PathwayId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("ProductId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("ProductId");

                    b.ToTable("ProductPathways");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.ProductSuggestedProduct", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("NEWSEQUENTIALID()");

                    b.Property<DateTimeOffset?>("ChangeDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("ChangedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasDefaultValue("admin");

                    b.Property<DateTimeOffset>("CreationDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<Guid>("ProductId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("SuggestedProductId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("ProductId");

                    b.HasIndex("SuggestedProductId");

                    b.ToTable("ProductSuggestedProducts");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.Products.Product", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("NEWSEQUENTIALID()");

                    b.Property<bool>("AutoDeliver")
                        .HasColumnType("bit");

                    b.Property<Guid>("CategoryId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTimeOffset?>("ChangeDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("ChangedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Code")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<bool>("ControlledByStock")
                        .HasColumnType("bit");

                    b.Property<decimal?>("Cost")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasDefaultValue("admin");

                    b.Property<DateTimeOffset>("CreationDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("Description")
                        .HasMaxLength(4000)
                        .HasColumnType("nvarchar(4000)");

                    b.Property<string>("DescriptionValue")
                        .HasMaxLength(4000)
                        .HasColumnType("nvarchar(4000)");

                    b.Property<string>("Discriminator")
                        .IsRequired()
                        .HasMaxLength(13)
                        .HasColumnType("nvarchar(13)");

                    b.Property<Guid?>("HearingAidTypeId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<bool>("IsFastTrack")
                        .HasColumnType("bit");

                    b.Property<bool>("IsSellable")
                        .HasColumnType("bit");

                    b.Property<bool>("IsSerialized")
                        .HasColumnType("bit");

                    b.Property<int?>("LDWarranty")
                        .HasColumnType("int");

                    b.Property<Guid?>("ManufacturerId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<decimal?>("MaximumDiscount")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<bool>("PriceChangesAllowed")
                        .HasColumnType("bit");

                    b.Property<int>("Quantity")
                        .HasColumnType("int");

                    b.Property<decimal>("RetailPrice")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<Guid?>("SupplierId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("VendorProductNumber")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<int?>("Warranty")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("CategoryId");

                    b.HasIndex("HearingAidTypeId");

                    b.HasIndex("ManufacturerId");

                    b.HasIndex("SupplierId");

                    b.HasIndex("Name", "TenantId", "IsDeleted")
                        .IsUnique()
                        .HasFilter("IsDeleted = 0");

                    b.HasIndex("IsActive", "IsSellable", "TenantId", "IsDeleted");

                    b.ToTable("Products");

                    b.HasDiscriminator().HasValue("Product");

                    b.UseTphMappingStrategy();
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.Sku", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("NEWSEQUENTIALID()");

                    b.Property<Guid?>("BatteryTypeId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTimeOffset?>("ChangeDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("ChangedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<Guid?>("ColorId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasDefaultValue("admin");

                    b.Property<DateTimeOffset>("CreationDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<Guid>("ProductId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("SkuValue")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid>("SupplierId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<byte[]>("Version")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.HasKey("Id");

                    b.HasIndex("BatteryTypeId");

                    b.HasIndex("ColorId");

                    b.HasIndex("ProductId");

                    b.HasIndex("SupplierId");

                    b.ToTable("Skus");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.SkuAttribute", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("NEWSEQUENTIALID()");

                    b.Property<Guid>("AttributeId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTimeOffset?>("ChangeDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("ChangedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasDefaultValue("admin");

                    b.Property<DateTimeOffset>("CreationDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<Guid>("SkuId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("AttributeId");

                    b.HasIndex("SkuId");

                    b.ToTable("SkuAttributes");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.SkuConfig", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("NEWSEQUENTIALID()");

                    b.Property<Guid?>("AttributeId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTimeOffset?>("ChangeDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("ChangedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasDefaultValue("admin");

                    b.Property<DateTimeOffset>("CreationDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<Guid>("ProductId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("SkuAttributeType")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("AttributeId");

                    b.HasIndex("ProductId");

                    b.ToTable("SkuConfigs");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.State", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("NEWSEQUENTIALID()");

                    b.Property<int>("AttributeNumber")
                        .HasColumnType("int");

                    b.Property<DateTimeOffset?>("ChangeDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("ChangedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(5)
                        .HasColumnType("nvarchar(5)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasDefaultValue("admin");

                    b.Property<DateTimeOffset>("CreationDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<bool>("IsDefault")
                        .HasColumnType("bit");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.ToTable("States");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.Stock", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("NEWSEQUENTIALID()");

                    b.Property<DateTimeOffset?>("ChangeDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("ChangedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasDefaultValue("admin");

                    b.Property<DateTimeOffset>("CreationDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<Guid>("LocationId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid?>("ParentLocationId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("RegionId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("LocationId")
                        .IsUnique();

                    b.HasIndex("RegionId");

                    b.ToTable("Stocks");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.StockAdjustmentReason", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("NEWSEQUENTIALID()");

                    b.Property<DateTimeOffset?>("ChangeDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("ChangedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasDefaultValue("admin");

                    b.Property<DateTimeOffset>("CreationDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.ToTable("StockAdjustmentReasons");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.StockKeepingUnit", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("NEWSEQUENTIALID()");

                    b.Property<Guid?>("BatteryTypeId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTimeOffset?>("ChangeDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("ChangedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<Guid?>("ColorId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasDefaultValue("admin");

                    b.Property<DateTimeOffset>("CreationDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid>("ProductId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("SupplierId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<byte[]>("Version")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.HasKey("Id");

                    b.HasIndex("BatteryTypeId");

                    b.HasIndex("ColorId");

                    b.HasIndex("SupplierId");

                    b.HasIndex("Name", "TenantId", "IsDeleted")
                        .IsUnique()
                        .HasFilter("IsDeleted = 0");

                    b.HasIndex("ProductId", "SupplierId", "BatteryTypeId", "ColorId", "TenantId", "IsDeleted")
                        .IsUnique()
                        .HasFilter("IsDeleted = 0");

                    b.ToTable("StockKeepingUnits");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.StockProduct", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("NEWSEQUENTIALID()");

                    b.Property<DateTimeOffset?>("ChangeDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("ChangedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasDefaultValue("admin");

                    b.Property<DateTimeOffset>("CreationDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<Guid>("ProductId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("Quantity")
                        .HasColumnType("int");

                    b.Property<Guid>("StockId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<byte[]>("Version")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.Property<bool>("WasPlacedOnStock")
                        .HasColumnType("bit");

                    b.HasKey("Id");

                    b.HasIndex("StockId");

                    b.HasIndex("ProductId", "StockId", "TenantId")
                        .IsUnique()
                        .HasFilter("[IsDeleted] = 0");

                    b.ToTable("StockProducts");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.StockProductItem", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("NEWSEQUENTIALID()");

                    b.Property<Guid?>("BatteryTypeId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTimeOffset?>("ChangeDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("ChangedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<Guid?>("ColorId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasDefaultValue("admin");

                    b.Property<DateTimeOffset>("CreationDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<Guid?>("LnDInLocationId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("LnDOrderId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("NegativeAdjustmentReasonId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("OrderId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("PatientId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("RepairInLocationId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("RepairOrderId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ReturnedToSupplierNote")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid?>("SaleId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("SerialNumber")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<Guid>("StockProductId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("TransferId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<byte[]>("Version")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.HasKey("Id");

                    b.HasIndex("BatteryTypeId");

                    b.HasIndex("ColorId");

                    b.HasIndex("NegativeAdjustmentReasonId");

                    b.HasIndex("StockProductId");

                    b.ToTable("StockProductItems");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.StockProductItemLog", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("NEWSEQUENTIALID()");

                    b.Property<DateTimeOffset?>("ChangeDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("ChangedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasDefaultValue("admin");

                    b.Property<DateTimeOffset>("CreationDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("Description")
                        .HasMaxLength(2000)
                        .HasColumnType("nvarchar(2000)");

                    b.Property<Guid?>("StockId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("StockProductItemId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("User")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.HasKey("Id");

                    b.HasIndex("StockId");

                    b.HasIndex("StockProductItemId");

                    b.ToTable("StockProductItemLogs");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.StockProductItems.StockProductItemAttribute", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("NEWSEQUENTIALID()");

                    b.Property<Guid>("AttributeId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTimeOffset?>("ChangeDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("ChangedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasDefaultValue("admin");

                    b.Property<DateTimeOffset>("CreationDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<Guid>("StockProductItemId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("AttributeId");

                    b.HasIndex("StockProductItemId");

                    b.ToTable("StockProductItemAttributes");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.StockSetting", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("NEWSEQUENTIALID()");

                    b.Property<DateTimeOffset?>("ChangeDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("ChangedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasDefaultValue("admin");

                    b.Property<DateTimeOffset>("CreationDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<bool>("ShareStockInLocationNetwork")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<byte[]>("Version")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.HasKey("Id");

                    b.ToTable("StockSettings");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.StockTransaction", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("NEWSEQUENTIALID()");

                    b.Property<DateTimeOffset?>("ChangeDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("ChangedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasDefaultValue("admin");

                    b.Property<DateTimeOffset>("CreationDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<Guid>("ProductId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("Quantity")
                        .HasColumnType("int");

                    b.Property<Guid?>("SaleId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("StockId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("TotalQuantity")
                        .HasColumnType("int");

                    b.Property<string>("TransactionId")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<int>("TypeId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("ProductId");

                    b.HasIndex("StockId");

                    b.ToTable("StockTransactions");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.Supplier", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("NEWSEQUENTIALID()");

                    b.Property<string>("Address1")
                        .IsRequired()
                        .HasMaxLength(4000)
                        .HasColumnType("nvarchar(4000)");

                    b.Property<string>("Address2")
                        .HasMaxLength(4000)
                        .HasColumnType("nvarchar(4000)");

                    b.Property<DateTimeOffset?>("ChangeDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("ChangedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("City")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid?>("CountryId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasDefaultValue("admin");

                    b.Property<DateTimeOffset>("CreationDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("EmailAddress")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("FaxNumber")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<bool>("IsManufacturer")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("PhoneNumber")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("PostalCode")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("State")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid?>("StateId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Website")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.HasKey("Id");

                    b.HasIndex("CountryId");

                    b.HasIndex("Name", "TenantId", "IsDeleted")
                        .IsUnique()
                        .HasFilter("IsDeleted = 0");

                    b.ToTable("Suppliers");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.TransfersState", b =>
                {
                    b.Property<Guid>("TransferId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("AcceptedAt")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("CancelledAt")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("CorrelationId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CurrentState")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<Guid>("FromStockId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("RequestedAt")
                        .HasColumnType("datetime2");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.Property<Guid>("StockProductItemId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("ToStockId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("TransferId");

                    b.HasIndex("StockProductItemId");

                    b.ToTable("Transfers");
                });

            modelBuilder.Entity("MassTransit.EntityFrameworkCoreIntegration.InboxState", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<DateTime?>("Consumed")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("ConsumerId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("Delivered")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("ExpirationTime")
                        .HasColumnType("datetime2");

                    b.Property<long?>("LastSequenceNumber")
                        .HasColumnType("bigint");

                    b.Property<Guid>("LockId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("MessageId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("ReceiveCount")
                        .HasColumnType("int");

                    b.Property<DateTime>("Received")
                        .HasColumnType("datetime2");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.HasKey("Id");

                    b.HasIndex("Delivered");

                    b.ToTable("InboxState");
                });

            modelBuilder.Entity("MassTransit.EntityFrameworkCoreIntegration.OutboxMessage", b =>
                {
                    b.Property<long>("SequenceNumber")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("SequenceNumber"));

                    b.Property<string>("Body")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ContentType")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<Guid?>("ConversationId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("CorrelationId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("DestinationAddress")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime?>("EnqueueTime")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("ExpirationTime")
                        .HasColumnType("datetime2");

                    b.Property<string>("FaultAddress")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("Headers")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("InboxConsumerId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("InboxMessageId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("InitiatorId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("MessageId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("MessageType")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("OutboxId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Properties")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("RequestId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ResponseAddress")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("SentTime")
                        .HasColumnType("datetime2");

                    b.Property<string>("SourceAddress")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.HasKey("SequenceNumber");

                    b.HasIndex("EnqueueTime");

                    b.HasIndex("ExpirationTime");

                    b.HasIndex("OutboxId", "SequenceNumber")
                        .IsUnique()
                        .HasFilter("[OutboxId] IS NOT NULL");

                    b.HasIndex("InboxMessageId", "InboxConsumerId", "SequenceNumber")
                        .IsUnique()
                        .HasFilter("[InboxMessageId] IS NOT NULL AND [InboxConsumerId] IS NOT NULL");

                    b.ToTable("OutboxMessage");
                });

            modelBuilder.Entity("MassTransit.EntityFrameworkCoreIntegration.OutboxState", b =>
                {
                    b.Property<Guid>("OutboxId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("Created")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("Delivered")
                        .HasColumnType("datetime2");

                    b.Property<long?>("LastSequenceNumber")
                        .HasColumnType("bigint");

                    b.Property<Guid>("LockId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.HasKey("OutboxId");

                    b.HasIndex("Created");

                    b.ToTable("OutboxState");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.Products.AUProduct", b =>
                {
                    b.HasBaseType("Auditdata.InventoryService.Core.Entities.Products.Product");

                    b.Property<string>("HspCategory")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<decimal?>("HspClientPrice")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("HspCode")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<bool?>("HspMaintenance")
                        .HasColumnType("bit");

                    b.Property<string>("HspServiceNumber")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<int?>("HspServiceType")
                        .HasColumnType("int");

                    b.Property<bool?>("HspTopUp")
                        .HasColumnType("bit");

                    b.Property<bool>("IsHSP")
                        .HasColumnType("bit");

                    b.HasDiscriminator().HasValue("AUProduct");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.Products.NZProduct", b =>
                {
                    b.HasBaseType("Auditdata.InventoryService.Core.Entities.Products.Product");

                    b.Property<bool>("IsAcc")
                        .HasColumnType("bit");

                    b.HasDiscriminator().HasValue("NZProduct");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.Products.ROIProduct", b =>
                {
                    b.HasBaseType("Auditdata.InventoryService.Core.Entities.Products.Product");

                    b.HasDiscriminator().HasValue("ROIProduct");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.Products.UKProduct", b =>
                {
                    b.HasBaseType("Auditdata.InventoryService.Core.Entities.Products.Product");

                    b.Property<decimal?>("FirstVAT")
                        .ValueGeneratedOnAdd()
                        .HasPrecision(18, 6)
                        .HasColumnType("decimal(18,6)")
                        .HasDefaultValue(0m);

                    b.Property<bool>("IsNHS")
                        .HasColumnType("bit");

                    b.Property<decimal?>("NHSVAT")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<Guid?>("NhsServiceTariffId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<decimal?>("SecondVAT")
                        .ValueGeneratedOnAdd()
                        .HasPrecision(18, 6)
                        .HasColumnType("decimal(18,6)")
                        .HasDefaultValue(0m);

                    b.HasDiscriminator().HasValue("UKProduct");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.Products.USProduct", b =>
                {
                    b.HasBaseType("Auditdata.InventoryService.Core.Entities.Products.Product");

                    b.HasDiscriminator().HasValue("USProduct");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.Attribute", b =>
                {
                    b.OwnsMany("Auditdata.InventoryService.Core.Entities.AttributeValue", "Values", b1 =>
                        {
                            b1.Property<Guid>("AttributeId")
                                .HasColumnType("uniqueidentifier");

                            b1.Property<int>("Id")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("int");

                            b1.Property<bool>("IsActive")
                                .HasColumnType("bit");

                            b1.Property<string>("Value")
                                .IsRequired()
                                .HasColumnType("nvarchar(max)");

                            b1.Property<Guid>("ValueId")
                                .HasColumnType("uniqueidentifier");

                            b1.HasKey("AttributeId", "Id");

                            b1.ToTable("Attributes");

                            b1.ToJson("Values");

                            b1.WithOwner()
                                .HasForeignKey("AttributeId");
                        });

                    b.Navigation("Values");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.AttributeProductCategory", b =>
                {
                    b.HasOne("Auditdata.InventoryService.Core.Entities.Attribute", "Attribute")
                        .WithMany()
                        .HasForeignKey("AttributeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Auditdata.InventoryService.Core.Entities.ProductCategory", "ProductCategory")
                        .WithMany()
                        .HasForeignKey("ProductCategoryId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Attribute");

                    b.Navigation("ProductCategory");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.AuditRequests.AuditLocation", b =>
                {
                    b.HasOne("Auditdata.InventoryService.Core.Entities.AuditRequests.AuditRequest", "AuditRequest")
                        .WithMany("AuditLocations")
                        .HasForeignKey("AuditRequestId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Auditdata.InventoryService.Core.Entities.Stock", "Stock")
                        .WithMany()
                        .HasForeignKey("LocationId")
                        .HasPrincipalKey("LocationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("AuditRequest");

                    b.Navigation("Stock");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.AuditRequests.AuditProduct", b =>
                {
                    b.HasOne("Auditdata.InventoryService.Core.Entities.AuditRequests.AuditRequest", "AuditRequest")
                        .WithMany("AuditProducts")
                        .HasForeignKey("AuditRequestId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Auditdata.InventoryService.Core.Entities.Products.Product", "Product")
                        .WithMany()
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("AuditRequest");

                    b.Navigation("Product");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.AuditRequests.AuditProductSerialNumber", b =>
                {
                    b.HasOne("Auditdata.InventoryService.Core.Entities.AuditRequests.AuditProduct", "AuditProduct")
                        .WithMany("SerialNumbers")
                        .HasForeignKey("AuditProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("AuditProduct");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.AuditRequests.AuditRequestLog", b =>
                {
                    b.HasOne("Auditdata.InventoryService.Core.Entities.AuditRequests.AuditRequest", "AuditRequest")
                        .WithMany()
                        .HasForeignKey("AuditRequestId");

                    b.OwnsOne("Auditdata.InventoryService.Core.Entities.AuditRequests.AuditRequestChanges", "AuditRequestChanges", b1 =>
                        {
                            b1.Property<Guid>("AuditRequestLogId")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("uniqueidentifier");

                            b1.HasKey("AuditRequestLogId");

                            b1.ToTable("AuditRequestLogs");

                            b1.ToJson("AuditRequestChanges");

                            b1.WithOwner()
                                .HasForeignKey("AuditRequestLogId");

                            b1.OwnsMany("Auditdata.InventoryService.Core.Entities.AuditRequests.AuditProductChanges", "AuditProductsChanges", b2 =>
                                {
                                    b2.Property<Guid>("AuditRequestChangesAuditRequestLogId")
                                        .HasColumnType("uniqueidentifier");

                                    b2.Property<int>("Id")
                                        .ValueGeneratedOnAdd()
                                        .HasColumnType("int");

                                    b2.Property<string>("ProductName")
                                        .IsRequired()
                                        .HasColumnType("nvarchar(max)");

                                    b2.HasKey("AuditRequestChangesAuditRequestLogId", "Id");

                                    b2.ToTable("AuditRequestLogs");

                                    b2.WithOwner()
                                        .HasForeignKey("AuditRequestChangesAuditRequestLogId");

                                    b2.OwnsMany("Auditdata.InventoryService.Core.Entities.AuditRequests.PropertyChangeModel", "Changes", b3 =>
                                        {
                                            b3.Property<Guid>("AuditProductChangesAuditRequestChangesAuditRequestLogId")
                                                .HasColumnType("uniqueidentifier");

                                            b3.Property<int>("AuditProductChangesId")
                                                .HasColumnType("int");

                                            b3.Property<int>("Id")
                                                .ValueGeneratedOnAdd()
                                                .HasColumnType("int");

                                            b3.Property<string>("OriginalValue")
                                                .HasColumnType("nvarchar(max)");

                                            b3.Property<string>("PropertyName")
                                                .IsRequired()
                                                .HasColumnType("nvarchar(max)");

                                            b3.Property<string>("UpdatedValue")
                                                .HasColumnType("nvarchar(max)");

                                            b3.HasKey("AuditProductChangesAuditRequestChangesAuditRequestLogId", "AuditProductChangesId", "Id");

                                            b3.ToTable("AuditRequestLogs");

                                            b3.WithOwner()
                                                .HasForeignKey("AuditProductChangesAuditRequestChangesAuditRequestLogId", "AuditProductChangesId");
                                        });

                                    b2.OwnsMany("Auditdata.InventoryService.Core.Entities.AuditRequests.AuditSerialNumberChanges", "AuditSerialNumbersChanges", b3 =>
                                        {
                                            b3.Property<Guid>("AuditProductChangesAuditRequestChangesAuditRequestLogId")
                                                .HasColumnType("uniqueidentifier");

                                            b3.Property<int>("AuditProductChangesId")
                                                .HasColumnType("int");

                                            b3.Property<int>("Id")
                                                .ValueGeneratedOnAdd()
                                                .HasColumnType("int");

                                            b3.Property<string>("SerialNumber")
                                                .IsRequired()
                                                .HasColumnType("nvarchar(max)");

                                            b3.HasKey("AuditProductChangesAuditRequestChangesAuditRequestLogId", "AuditProductChangesId", "Id");

                                            b3.ToTable("AuditRequestLogs");

                                            b3.WithOwner()
                                                .HasForeignKey("AuditProductChangesAuditRequestChangesAuditRequestLogId", "AuditProductChangesId");

                                            b3.OwnsMany("Auditdata.InventoryService.Core.Entities.AuditRequests.PropertyChangeModel", "Changes", b4 =>
                                                {
                                                    b4.Property<Guid>("AuditSerialNumberChangesAuditProductChangesAuditRequestChangesAuditRequestLogId")
                                                        .HasColumnType("uniqueidentifier");

                                                    b4.Property<int>("AuditSerialNumberChangesAuditProductChangesId")
                                                        .HasColumnType("int");

                                                    b4.Property<int>("AuditSerialNumberChangesId")
                                                        .HasColumnType("int");

                                                    b4.Property<int>("Id")
                                                        .ValueGeneratedOnAdd()
                                                        .HasColumnType("int");

                                                    b4.Property<string>("OriginalValue")
                                                        .HasColumnType("nvarchar(max)");

                                                    b4.Property<string>("PropertyName")
                                                        .IsRequired()
                                                        .HasColumnType("nvarchar(max)");

                                                    b4.Property<string>("UpdatedValue")
                                                        .HasColumnType("nvarchar(max)");

                                                    b4.HasKey("AuditSerialNumberChangesAuditProductChangesAuditRequestChangesAuditRequestLogId", "AuditSerialNumberChangesAuditProductChangesId", "AuditSerialNumberChangesId", "Id");

                                                    b4.ToTable("AuditRequestLogs");

                                                    b4.WithOwner()
                                                        .HasForeignKey("AuditSerialNumberChangesAuditProductChangesAuditRequestChangesAuditRequestLogId", "AuditSerialNumberChangesAuditProductChangesId", "AuditSerialNumberChangesId");
                                                });

                                            b3.Navigation("Changes");
                                        });

                                    b2.Navigation("AuditSerialNumbersChanges");

                                    b2.Navigation("Changes");
                                });

                            b1.OwnsMany("Auditdata.InventoryService.Core.Entities.AuditRequests.PropertyChangeModel", "Changes", b2 =>
                                {
                                    b2.Property<Guid>("AuditRequestChangesAuditRequestLogId")
                                        .HasColumnType("uniqueidentifier");

                                    b2.Property<int>("Id")
                                        .ValueGeneratedOnAdd()
                                        .HasColumnType("int");

                                    b2.Property<string>("OriginalValue")
                                        .HasColumnType("nvarchar(max)");

                                    b2.Property<string>("PropertyName")
                                        .IsRequired()
                                        .HasColumnType("nvarchar(max)");

                                    b2.Property<string>("UpdatedValue")
                                        .HasColumnType("nvarchar(max)");

                                    b2.HasKey("AuditRequestChangesAuditRequestLogId", "Id");

                                    b2.ToTable("AuditRequestLogs");

                                    b2.WithOwner()
                                        .HasForeignKey("AuditRequestChangesAuditRequestLogId");
                                });

                            b1.Navigation("AuditProductsChanges");

                            b1.Navigation("Changes");
                        });

                    b.Navigation("AuditRequest");

                    b.Navigation("AuditRequestChanges");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.BundleProduct", b =>
                {
                    b.HasOne("Auditdata.InventoryService.Core.Entities.Bundle", "Bundle")
                        .WithMany("Products")
                        .HasForeignKey("BundleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Auditdata.InventoryService.Core.Entities.Products.Product", "Product")
                        .WithMany("Bundles")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Bundle");

                    b.Navigation("Product");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.ExternalProduct", b =>
                {
                    b.HasOne("Auditdata.InventoryService.Core.Entities.ExternalProductImport", "Import")
                        .WithMany()
                        .HasForeignKey("ImportId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Auditdata.InventoryService.Core.Entities.Products.Product", "Product")
                        .WithMany()
                        .HasForeignKey("ProductId");

                    b.Navigation("Import");

                    b.Navigation("Product");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.ExternalProductOption", b =>
                {
                    b.HasOne("Auditdata.InventoryService.Core.Entities.ExternalProduct", "ExternalProduct")
                        .WithMany("ExternalProductOptions")
                        .HasForeignKey("ExternalProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.OwnsOne("Auditdata.InventoryService.Core.Entities.ExternalProductOptionStyles", "Styles", b1 =>
                        {
                            b1.Property<Guid>("ExternalProductOptionId")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("uniqueidentifier");

                            b1.Property<string>("BatteryType")
                                .HasColumnType("nvarchar(max)");

                            b1.Property<string>("Color")
                                .HasColumnType("nvarchar(max)");

                            b1.HasKey("ExternalProductOptionId");

                            b1.ToTable("ExternalProductOptions");

                            b1.ToJson("Styles");

                            b1.WithOwner()
                                .HasForeignKey("ExternalProductOptionId");

                            b1.OwnsMany("Auditdata.InventoryService.Core.Entities.ExternalProductOptionStyle", "Attributes", b2 =>
                                {
                                    b2.Property<Guid>("ExternalProductOptionStylesExternalProductOptionId")
                                        .HasColumnType("uniqueidentifier");

                                    b2.Property<int>("Id")
                                        .ValueGeneratedOnAdd()
                                        .HasColumnType("int");

                                    b2.Property<string>("Caption")
                                        .HasColumnType("nvarchar(max)");

                                    b2.Property<string>("Code")
                                        .IsRequired()
                                        .HasColumnType("nvarchar(max)");

                                    b2.Property<string>("Value")
                                        .IsRequired()
                                        .HasColumnType("nvarchar(max)");

                                    b2.HasKey("ExternalProductOptionStylesExternalProductOptionId", "Id");

                                    b2.ToTable("ExternalProductOptions");

                                    b2.WithOwner()
                                        .HasForeignKey("ExternalProductOptionStylesExternalProductOptionId");
                                });

                            b1.Navigation("Attributes");
                        });

                    b.Navigation("ExternalProduct");

                    b.Navigation("Styles");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.ImportMetadata", b =>
                {
                    b.HasOne("Auditdata.InventoryService.Core.Entities.ProductCategory", "Category")
                        .WithMany("ImportMetadatas")
                        .HasForeignKey("CategoryId");

                    b.Navigation("Category");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.Manufacturer", b =>
                {
                    b.HasOne("Auditdata.InventoryService.Core.Entities.Country", "Country")
                        .WithMany()
                        .HasForeignKey("CountryId");

                    b.OwnsOne("Auditdata.InventoryService.Core.Entities.ManufacturerContact", "AccountReceivableContact", b1 =>
                        {
                            b1.Property<Guid>("ManufacturerId")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("uniqueidentifier");

                            b1.Property<string>("EmailAddress")
                                .HasColumnType("nvarchar(max)");

                            b1.Property<string>("Extension")
                                .HasColumnType("nvarchar(max)");

                            b1.Property<string>("Name")
                                .HasColumnType("nvarchar(max)");

                            b1.Property<string>("PhoneNumber")
                                .HasColumnType("nvarchar(max)");

                            b1.HasKey("ManufacturerId");

                            b1.ToTable("Manufacturers");

                            b1.ToJson("AccountReceivableContact");

                            b1.WithOwner()
                                .HasForeignKey("ManufacturerId");
                        });

                    b.OwnsOne("Auditdata.InventoryService.Core.Entities.ManufacturerContact", "SalesContact", b1 =>
                        {
                            b1.Property<Guid>("ManufacturerId")
                                .HasColumnType("uniqueidentifier");

                            b1.Property<string>("EmailAddress")
                                .HasColumnType("nvarchar(max)");

                            b1.Property<string>("Extension")
                                .HasColumnType("nvarchar(max)");

                            b1.Property<string>("Name")
                                .HasColumnType("nvarchar(max)");

                            b1.Property<string>("PhoneNumber")
                                .HasColumnType("nvarchar(max)");

                            b1.HasKey("ManufacturerId");

                            b1.ToTable("Manufacturers");

                            b1.ToJson("SalesContact");

                            b1.WithOwner()
                                .HasForeignKey("ManufacturerId");
                        });

                    b.Navigation("AccountReceivableContact");

                    b.Navigation("Country");

                    b.Navigation("SalesContact");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.NhsContractProduct", b =>
                {
                    b.HasOne("Auditdata.InventoryService.Core.Entities.Products.Product", "Product")
                        .WithMany("NhsContractProducts")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Product");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.ProductAttribute", b =>
                {
                    b.HasOne("Auditdata.InventoryService.Core.Entities.Attribute", "Attribute")
                        .WithMany()
                        .HasForeignKey("AttributeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Auditdata.InventoryService.Core.Entities.Products.Product", "Product")
                        .WithMany("Attributes")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.OwnsMany("Auditdata.InventoryService.Core.Entities.AttributeValue", "Value", b1 =>
                        {
                            b1.Property<Guid>("ProductAttributeId")
                                .HasColumnType("uniqueidentifier");

                            b1.Property<int>("Id")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("int");

                            b1.Property<bool>("IsActive")
                                .HasColumnType("bit");

                            b1.Property<string>("Value")
                                .IsRequired()
                                .HasColumnType("nvarchar(max)");

                            b1.Property<Guid>("ValueId")
                                .HasColumnType("uniqueidentifier");

                            b1.HasKey("ProductAttributeId", "Id");

                            b1.ToTable("ProductAttributes");

                            b1.ToJson("Value");

                            b1.WithOwner()
                                .HasForeignKey("ProductAttributeId");
                        });

                    b.Navigation("Attribute");

                    b.Navigation("Product");

                    b.Navigation("Value");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.ProductBatteryType", b =>
                {
                    b.HasOne("Auditdata.InventoryService.Core.Entities.BatteryType", "BatteryType")
                        .WithMany()
                        .HasForeignKey("BatteryTypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Auditdata.InventoryService.Core.Entities.Products.Product", "Product")
                        .WithMany("BatteryTypes")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("BatteryType");

                    b.Navigation("Product");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.ProductCPTCode", b =>
                {
                    b.HasOne("Auditdata.InventoryService.Core.Entities.CPTCode", "CPTCode")
                        .WithMany()
                        .HasForeignKey("CPTCodeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Auditdata.InventoryService.Core.Entities.Products.USProduct", "Product")
                        .WithMany("CPTCodes")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("CPTCode");

                    b.Navigation("Product");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.ProductCategoryAccountCode", b =>
                {
                    b.HasOne("Auditdata.InventoryService.Core.Entities.ProductCategory", "ProductCategory")
                        .WithOne()
                        .HasForeignKey("Auditdata.InventoryService.Core.Entities.ProductCategoryAccountCode", "ProductCategoryId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ProductCategory");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.ProductColor", b =>
                {
                    b.HasOne("Auditdata.InventoryService.Core.Entities.Color", "Color")
                        .WithMany()
                        .HasForeignKey("ColorId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Auditdata.InventoryService.Core.Entities.Products.Product", "Product")
                        .WithMany("Colors")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Color");

                    b.Navigation("Product");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.ProductPathway", b =>
                {
                    b.HasOne("Auditdata.InventoryService.Core.Entities.Products.Product", "Product")
                        .WithMany("ProductPathways")
                        .HasForeignKey("ProductId");

                    b.Navigation("Product");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.ProductSuggestedProduct", b =>
                {
                    b.HasOne("Auditdata.InventoryService.Core.Entities.Products.Product", "Product")
                        .WithMany("SuggestedProducts")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Auditdata.InventoryService.Core.Entities.Products.Product", "SuggestedProduct")
                        .WithMany()
                        .HasForeignKey("SuggestedProductId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("Product");

                    b.Navigation("SuggestedProduct");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.Products.Product", b =>
                {
                    b.HasOne("Auditdata.InventoryService.Core.Entities.ProductCategory", "Category")
                        .WithMany()
                        .HasForeignKey("CategoryId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Auditdata.InventoryService.Core.Entities.HearingAidType", "HearingAidType")
                        .WithMany()
                        .HasForeignKey("HearingAidTypeId");

                    b.HasOne("Auditdata.InventoryService.Core.Entities.Manufacturer", "Manufacturer")
                        .WithMany()
                        .HasForeignKey("ManufacturerId");

                    b.HasOne("Auditdata.InventoryService.Core.Entities.Supplier", "Supplier")
                        .WithMany()
                        .HasForeignKey("SupplierId");

                    b.Navigation("Category");

                    b.Navigation("HearingAidType");

                    b.Navigation("Manufacturer");

                    b.Navigation("Supplier");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.Sku", b =>
                {
                    b.HasOne("Auditdata.InventoryService.Core.Entities.BatteryType", "BatteryType")
                        .WithMany()
                        .HasForeignKey("BatteryTypeId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Auditdata.InventoryService.Core.Entities.Color", "Color")
                        .WithMany()
                        .HasForeignKey("ColorId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Auditdata.InventoryService.Core.Entities.Products.Product", "Product")
                        .WithMany("Skus")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Auditdata.InventoryService.Core.Entities.Supplier", "Supplier")
                        .WithMany()
                        .HasForeignKey("SupplierId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("BatteryType");

                    b.Navigation("Color");

                    b.Navigation("Product");

                    b.Navigation("Supplier");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.SkuAttribute", b =>
                {
                    b.HasOne("Auditdata.InventoryService.Core.Entities.Attribute", "Attribute")
                        .WithMany()
                        .HasForeignKey("AttributeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Auditdata.InventoryService.Core.Entities.Sku", "Sku")
                        .WithMany("Attributes")
                        .HasForeignKey("SkuId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.OwnsOne("Auditdata.InventoryService.Core.Entities.AttributeValue", "Value", b1 =>
                        {
                            b1.Property<Guid>("SkuAttributeId")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("uniqueidentifier");

                            b1.Property<bool>("IsActive")
                                .HasColumnType("bit");

                            b1.Property<string>("Value")
                                .IsRequired()
                                .HasColumnType("nvarchar(max)");

                            b1.Property<Guid>("ValueId")
                                .HasColumnType("uniqueidentifier");

                            b1.HasKey("SkuAttributeId");

                            b1.ToTable("SkuAttributes");

                            b1.ToJson("Value");

                            b1.WithOwner()
                                .HasForeignKey("SkuAttributeId");
                        });

                    b.Navigation("Attribute");

                    b.Navigation("Sku");

                    b.Navigation("Value")
                        .IsRequired();
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.SkuConfig", b =>
                {
                    b.HasOne("Auditdata.InventoryService.Core.Entities.Attribute", "Attribute")
                        .WithMany()
                        .HasForeignKey("AttributeId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Auditdata.InventoryService.Core.Entities.Products.Product", "Product")
                        .WithMany("SkuConfigs")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Attribute");

                    b.Navigation("Product");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.StockKeepingUnit", b =>
                {
                    b.HasOne("Auditdata.InventoryService.Core.Entities.BatteryType", "BatteryType")
                        .WithMany()
                        .HasForeignKey("BatteryTypeId");

                    b.HasOne("Auditdata.InventoryService.Core.Entities.Color", "Color")
                        .WithMany()
                        .HasForeignKey("ColorId");

                    b.HasOne("Auditdata.InventoryService.Core.Entities.Products.Product", "Product")
                        .WithMany()
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Auditdata.InventoryService.Core.Entities.Supplier", "Supplier")
                        .WithMany()
                        .HasForeignKey("SupplierId");

                    b.Navigation("BatteryType");

                    b.Navigation("Color");

                    b.Navigation("Product");

                    b.Navigation("Supplier");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.StockProduct", b =>
                {
                    b.HasOne("Auditdata.InventoryService.Core.Entities.Products.Product", "Product")
                        .WithMany("StockProducts")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Auditdata.InventoryService.Core.Entities.Stock", "Stock")
                        .WithMany()
                        .HasForeignKey("StockId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Product");

                    b.Navigation("Stock");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.StockProductItem", b =>
                {
                    b.HasOne("Auditdata.InventoryService.Core.Entities.BatteryType", "BatteryType")
                        .WithMany()
                        .HasForeignKey("BatteryTypeId");

                    b.HasOne("Auditdata.InventoryService.Core.Entities.Color", "Color")
                        .WithMany()
                        .HasForeignKey("ColorId");

                    b.HasOne("Auditdata.InventoryService.Core.Entities.StockAdjustmentReason", "NegativeAdjustmentReason")
                        .WithMany()
                        .HasForeignKey("NegativeAdjustmentReasonId");

                    b.HasOne("Auditdata.InventoryService.Core.Entities.StockProduct", "StockProduct")
                        .WithMany("StockProductItems")
                        .HasForeignKey("StockProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("BatteryType");

                    b.Navigation("Color");

                    b.Navigation("NegativeAdjustmentReason");

                    b.Navigation("StockProduct");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.StockProductItemLog", b =>
                {
                    b.HasOne("Auditdata.InventoryService.Core.Entities.Stock", "Stock")
                        .WithMany()
                        .HasForeignKey("StockId");

                    b.HasOne("Auditdata.InventoryService.Core.Entities.StockProductItem", "StockProductItem")
                        .WithMany("Logs")
                        .HasForeignKey("StockProductItemId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.OwnsOne("Auditdata.InventoryService.Core.Features.StockProductItemLogs.Models.TransactionHistoryData", "Data", b1 =>
                        {
                            b1.Property<Guid>("StockProductItemLogId")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("uniqueidentifier");

                            b1.Property<string>("Key")
                                .HasColumnType("nvarchar(max)");

                            b1.HasKey("StockProductItemLogId");

                            b1.ToTable("StockProductItemLogs");

                            b1.ToJson("Data");

                            b1.WithOwner()
                                .HasForeignKey("StockProductItemLogId");

                            b1.OwnsMany("Auditdata.InventoryService.Core.Features.StockProductItemLogs.Models.TransactionHistoryDataParam", "Params", b2 =>
                                {
                                    b2.Property<Guid>("TransactionHistoryDataStockProductItemLogId")
                                        .HasColumnType("uniqueidentifier");

                                    b2.Property<int>("Id")
                                        .ValueGeneratedOnAdd()
                                        .HasColumnType("int");

                                    b2.Property<string>("Param")
                                        .IsRequired()
                                        .HasColumnType("nvarchar(max)");

                                    b2.HasKey("TransactionHistoryDataStockProductItemLogId", "Id");

                                    b2.ToTable("StockProductItemLogs");

                                    b2.WithOwner()
                                        .HasForeignKey("TransactionHistoryDataStockProductItemLogId");
                                });

                            b1.Navigation("Params");
                        });

                    b.Navigation("Data");

                    b.Navigation("Stock");

                    b.Navigation("StockProductItem");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.StockProductItems.StockProductItemAttribute", b =>
                {
                    b.HasOne("Auditdata.InventoryService.Core.Entities.Attribute", "Attribute")
                        .WithMany()
                        .HasForeignKey("AttributeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Auditdata.InventoryService.Core.Entities.StockProductItem", "StockProductItem")
                        .WithMany("Attributes")
                        .HasForeignKey("StockProductItemId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.OwnsOne("Auditdata.InventoryService.Core.Entities.StockProductItems.StockProductItemAttributeValue", "Value", b1 =>
                        {
                            b1.Property<Guid>("StockProductItemAttributeId")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("uniqueidentifier");

                            b1.Property<string>("Value")
                                .IsRequired()
                                .HasColumnType("nvarchar(max)");

                            b1.Property<Guid>("ValueId")
                                .HasColumnType("uniqueidentifier");

                            b1.HasKey("StockProductItemAttributeId");

                            b1.ToTable("StockProductItemAttributes");

                            b1.ToJson("Value");

                            b1.WithOwner()
                                .HasForeignKey("StockProductItemAttributeId");
                        });

                    b.Navigation("Attribute");

                    b.Navigation("StockProductItem");

                    b.Navigation("Value")
                        .IsRequired();
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.StockTransaction", b =>
                {
                    b.HasOne("Auditdata.InventoryService.Core.Entities.Products.Product", "Product")
                        .WithMany()
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Auditdata.InventoryService.Core.Entities.Stock", "Stock")
                        .WithMany()
                        .HasForeignKey("StockId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Product");

                    b.Navigation("Stock");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.Supplier", b =>
                {
                    b.HasOne("Auditdata.InventoryService.Core.Entities.Country", "Country")
                        .WithMany()
                        .HasForeignKey("CountryId");

                    b.OwnsOne("Auditdata.InventoryService.Core.Entities.SupplierContact", "AccountReceivableContact", b1 =>
                        {
                            b1.Property<Guid>("SupplierId")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("uniqueidentifier");

                            b1.Property<string>("EmailAddress")
                                .HasColumnType("nvarchar(max)");

                            b1.Property<string>("Extension")
                                .HasColumnType("nvarchar(max)");

                            b1.Property<string>("Name")
                                .HasColumnType("nvarchar(max)");

                            b1.Property<string>("PhoneNumber")
                                .HasColumnType("nvarchar(max)");

                            b1.HasKey("SupplierId");

                            b1.ToTable("Suppliers");

                            b1.ToJson("AccountReceivableContact");

                            b1.WithOwner()
                                .HasForeignKey("SupplierId");
                        });

                    b.OwnsOne("Auditdata.InventoryService.Core.Entities.SupplierContact", "SalesContact", b1 =>
                        {
                            b1.Property<Guid>("SupplierId")
                                .HasColumnType("uniqueidentifier");

                            b1.Property<string>("EmailAddress")
                                .HasColumnType("nvarchar(max)");

                            b1.Property<string>("Extension")
                                .HasColumnType("nvarchar(max)");

                            b1.Property<string>("Name")
                                .HasColumnType("nvarchar(max)");

                            b1.Property<string>("PhoneNumber")
                                .HasColumnType("nvarchar(max)");

                            b1.HasKey("SupplierId");

                            b1.ToTable("Suppliers");

                            b1.ToJson("SalesContact");

                            b1.WithOwner()
                                .HasForeignKey("SupplierId");
                        });

                    b.Navigation("AccountReceivableContact");

                    b.Navigation("Country");

                    b.Navigation("SalesContact");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.TransfersState", b =>
                {
                    b.HasOne("Auditdata.InventoryService.Core.Entities.StockProductItem", "StockProductItem")
                        .WithMany()
                        .HasForeignKey("StockProductItemId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("StockProductItem");
                });

            modelBuilder.Entity("MassTransit.EntityFrameworkCoreIntegration.OutboxMessage", b =>
                {
                    b.HasOne("MassTransit.EntityFrameworkCoreIntegration.OutboxState", null)
                        .WithMany()
                        .HasForeignKey("OutboxId");

                    b.HasOne("MassTransit.EntityFrameworkCoreIntegration.InboxState", null)
                        .WithMany()
                        .HasForeignKey("InboxMessageId", "InboxConsumerId")
                        .HasPrincipalKey("MessageId", "ConsumerId");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.Products.NZProduct", b =>
                {
                    b.OwnsOne("Auditdata.InventoryService.Core.Entities.Products.NzProductAcc", "Acc", b1 =>
                        {
                            b1.Property<Guid>("NZProductId")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("uniqueidentifier");

                            b1.Property<string>("Code")
                                .IsRequired()
                                .HasMaxLength(50)
                                .HasColumnType("nvarchar(50)");

                            b1.Property<string>("Description")
                                .HasMaxLength(4000)
                                .HasColumnType("nvarchar(4000)");

                            b1.Property<decimal>("PriceExclGst")
                                .HasPrecision(18, 2)
                                .HasColumnType("decimal(18,2)");

                            b1.Property<decimal>("PriceInclGst")
                                .HasPrecision(18, 2)
                                .HasColumnType("decimal(18,2)");

                            b1.HasKey("NZProductId");

                            b1.ToTable("Products");

                            b1.WithOwner()
                                .HasForeignKey("NZProductId");
                        });

                    b.Navigation("Acc");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.AuditRequests.AuditProduct", b =>
                {
                    b.Navigation("SerialNumbers");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.AuditRequests.AuditRequest", b =>
                {
                    b.Navigation("AuditLocations");

                    b.Navigation("AuditProducts");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.Bundle", b =>
                {
                    b.Navigation("Products");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.ExternalProduct", b =>
                {
                    b.Navigation("ExternalProductOptions");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.ProductCategory", b =>
                {
                    b.Navigation("ImportMetadatas");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.Products.Product", b =>
                {
                    b.Navigation("Attributes");

                    b.Navigation("BatteryTypes");

                    b.Navigation("Bundles");

                    b.Navigation("Colors");

                    b.Navigation("NhsContractProducts");

                    b.Navigation("ProductPathways");

                    b.Navigation("SkuConfigs");

                    b.Navigation("Skus");

                    b.Navigation("StockProducts");

                    b.Navigation("SuggestedProducts");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.Sku", b =>
                {
                    b.Navigation("Attributes");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.StockProduct", b =>
                {
                    b.Navigation("StockProductItems");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.StockProductItem", b =>
                {
                    b.Navigation("Attributes");

                    b.Navigation("Logs");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.Products.USProduct", b =>
                {
                    b.Navigation("CPTCodes");
                });
#pragma warning restore 612, 618
        }
    }
}
