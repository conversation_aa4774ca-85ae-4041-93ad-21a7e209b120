using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Auditdata.InventoryService.Infrastructure.Migrations;

/// <inheritdoc />
public partial class ProductCPTCode : Migration
{
    /// <inheritdoc />
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.CreateTable(
            name: "ProductCPTCodes",
            columns: table => new
            {
                Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                ProductId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                CPTCodeId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                TenantId = table.Column<Guid>(type: "uniqueidentifier", nullable: false)
            },
            constraints: table =>
            {
                table.PrimaryKey("PK_ProductCPTCodes", x => x.Id);
                table.ForeignKey(
                    name: "FK_ProductCPTCodes_CPTCodes_CPTCodeId",
                    column: x => x.CPTCodeId,
                    principalTable: "CPTCodes",
                    principalColumn: "Id",
                    onDelete: ReferentialAction.Cascade);
                table.ForeignKey(
                    name: "FK_ProductCPTCodes_Products_ProductId",
                    column: x => x.ProductId,
                    principalTable: "Products",
                    principalColumn: "Id",
                    onDelete: ReferentialAction.Cascade);
            });

        migrationBuilder.CreateIndex(
            name: "IX_ProductCPTCodes_CPTCodeId",
            table: "ProductCPTCodes",
            column: "CPTCodeId");

        migrationBuilder.CreateIndex(
            name: "IX_ProductCPTCodes_ProductId_CPTCodeId_TenantId",
            table: "ProductCPTCodes",
            columns: new[] { "ProductId", "CPTCodeId", "TenantId" },
            unique: true);
    }

    /// <inheritdoc />
    protected override void Down(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.DropTable(
            name: "ProductCPTCodes");
    }
}
