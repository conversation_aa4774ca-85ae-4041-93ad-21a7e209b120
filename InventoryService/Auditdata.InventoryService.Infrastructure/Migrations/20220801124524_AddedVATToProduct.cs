using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Auditdata.InventoryService.Infrastructure.Migrations;

public partial class AddedVATToProduct : Migration
{
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.AddColumn<decimal>(
            name: "VATPercentage",
            table: "Products",
            type: "decimal(18,2)",
            nullable: false,
            defaultValue: 1m);

        migrationBuilder.AddColumn<int>(
            name: "VATType",
            table: "Products",
            type: "int",
            nullable: false,
            defaultValue: 1);
    }

    protected override void Down(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.DropColumn(
            name: "VATPercentage",
            table: "Products");

        migrationBuilder.DropColumn(
            name: "VATType",
            table: "Products");
    }
}
