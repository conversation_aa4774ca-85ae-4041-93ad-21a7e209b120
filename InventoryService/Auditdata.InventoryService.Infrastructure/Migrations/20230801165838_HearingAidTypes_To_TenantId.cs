using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Auditdata.InventoryService.Infrastructure.Migrations;

/// <inheritdoc />
public partial class HearingAidTypes_To_TenantId : Migration
{
    /// <inheritdoc />
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.AddColumn<Guid>(
            name: "TenantId",
            table: "HearingAidTypes",
            type: "uniqueidentifier",
            nullable: false,
            defaultValue: new Guid("00000000-0000-0000-0000-000000000000"));

        migrationBuilder.UpdateData(
            table: "HearingAidTypes",
            keyColumn: "Id",
            keyValue: new Guid("8267c588-abf9-436a-8160-a989b34c3a19"),
            column: "TenantId",
            value: new Guid("00000000-0000-0000-0000-000000000000"));

        migrationBuilder.UpdateData(
            table: "HearingAidTypes",
            keyColumn: "Id",
            keyValue: new Guid("8bb7392c-fce0-4522-97c8-1d246b136d01"),
            column: "TenantId",
            value: new Guid("00000000-0000-0000-0000-000000000000"));

        migrationBuilder.UpdateData(
            table: "HearingAidTypes",
            keyColumn: "Id",
            keyValue: new Guid("9c3d9f64-88a7-44c1-9138-056bea29cf44"),
            column: "TenantId",
            value: new Guid("00000000-0000-0000-0000-000000000000"));

        migrationBuilder.UpdateData(
            table: "HearingAidTypes",
            keyColumn: "Id",
            keyValue: new Guid("bdf916f7-cfb6-4016-bd05-0d1c2e2c5b10"),
            column: "TenantId",
            value: new Guid("00000000-0000-0000-0000-000000000000"));

        migrationBuilder.UpdateData(
            table: "HearingAidTypes",
            keyColumn: "Id",
            keyValue: new Guid("eadf48c0-7f9e-4158-90e5-aa976904cae0"),
            column: "TenantId",
            value: new Guid("00000000-0000-0000-0000-000000000000"));
    }

    /// <inheritdoc />
    protected override void Down(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.DropColumn(
            name: "TenantId",
            table: "HearingAidTypes");
    }
}
