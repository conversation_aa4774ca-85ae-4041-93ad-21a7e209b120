using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Auditdata.InventoryService.Infrastructure.Migrations;

/// <inheritdoc />
public partial class Attribute_ProductCategories : Migration
{
    /// <inheritdoc />
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.AddColumn<bool>(
            name: "IsActive",
            table: "Attributes",
            type: "bit",
            nullable: false,
            defaultValue: false);

        migrationBuilder.CreateTable(
            name: "AttributeProductCategories",
            columns: table => new
            {
                Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                AttributeId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                ProductCategoryId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                TenantId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                CreationDate = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                ChangeDate = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                CreatedBy = table.Column<string>(type: "nvarchar(max)", nullable: false, defaultValue: "admin"),
                ChangedBy = table.Column<string>(type: "nvarchar(max)", nullable: true)
            },
            constraints: table =>
            {
                table.PrimaryKey("PK_AttributeProductCategories", x => x.Id);
                table.ForeignKey(
                    name: "FK_AttributeProductCategories_Attributes_AttributeId",
                    column: x => x.AttributeId,
                    principalTable: "Attributes",
                    principalColumn: "Id",
                    onDelete: ReferentialAction.Cascade);
                table.ForeignKey(
                    name: "FK_AttributeProductCategories_ProductCategories_ProductCategoryId",
                    column: x => x.ProductCategoryId,
                    principalTable: "ProductCategories",
                    principalColumn: "Id",
                    onDelete: ReferentialAction.Cascade);
            });

        migrationBuilder.CreateIndex(
            name: "IX_AttributeProductCategories_AttributeId",
            table: "AttributeProductCategories",
            column: "AttributeId");

        migrationBuilder.CreateIndex(
            name: "IX_AttributeProductCategories_ProductCategoryId",
            table: "AttributeProductCategories",
            column: "ProductCategoryId");
    }

    /// <inheritdoc />
    protected override void Down(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.DropTable(
            name: "AttributeProductCategories");

        migrationBuilder.DropColumn(
            name: "IsActive",
            table: "Attributes");
    }
}
