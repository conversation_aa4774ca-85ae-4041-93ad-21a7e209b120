using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Auditdata.InventoryService.Infrastructure.Migrations;

public partial class AddStockIdToStockProduct : Migration
{
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.AddColumn<Guid>(
            name: "StockId",
            table: "StockProducts",
            type: "uniqueidentifier",
            nullable: true);

        migrationBuilder.CreateIndex(
            name: "IX_StockProducts_StockId",
            table: "StockProducts",
            column: "StockId");

        migrationBuilder.AddForeignKey(
            name: "FK_StockProducts_Stocks_StockId",
            table: "StockProducts",
            column: "StockId",
            principalTable: "Stocks",
            principalColumn: "Id");
    }

    protected override void Down(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.DropForeignKey(
            name: "FK_StockProducts_Stocks_StockId",
            table: "StockProducts");

        migrationBuilder.DropIndex(
            name: "IX_StockProducts_StockId",
            table: "StockProducts");

        migrationBuilder.DropColumn(
            name: "StockId",
            table: "StockProducts");
    }
}
