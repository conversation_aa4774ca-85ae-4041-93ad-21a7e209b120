using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Auditdata.InventoryService.Infrastructure.Migrations;

/// <inheritdoc />
public partial class ExternalProducts_Init : Migration
{
    /// <inheritdoc />
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.CreateTable(
            name: "ExternalProducts",
            columns: table => new
            {
                Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                ExternalId = table.Column<string>(type: "nvarchar(max)", nullable: false),
                ImportId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                ModifiedAt = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                Name = table.Column<string>(type: "nvarchar(max)", nullable: false),
                Category = table.Column<string>(type: "nvarchar(max)", nullable: false),
                Manufacturer = table.Column<string>(type: "nvarchar(max)", nullable: true),
                TenantId = table.Column<Guid>(type: "uniqueidentifier", nullable: false)
            },
            constraints: table =>
            {
                table.PrimaryKey("PK_ExternalProducts", x => x.Id);
                table.ForeignKey(
                    name: "FK_ExternalProducts_ExternalProductImports_ImportId",
                    column: x => x.ImportId,
                    principalTable: "ExternalProductImports",
                    principalColumn: "Id",
                    onDelete: ReferentialAction.Cascade);
            });

        migrationBuilder.CreateTable(
            name: "ExternalProductOptions",
            columns: table => new
            {
                Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                ExternalId = table.Column<string>(type: "nvarchar(max)", nullable: false),
                ExternalProductId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                Name = table.Column<string>(type: "nvarchar(max)", nullable: false),
                ModifiedAt = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                TenantId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                Styles = table.Column<string>(type: "nvarchar(max)", nullable: true)
            },
            constraints: table =>
            {
                table.PrimaryKey("PK_ExternalProductOptions", x => x.Id);
                table.ForeignKey(
                    name: "FK_ExternalProductOptions_ExternalProducts_ExternalProductId",
                    column: x => x.ExternalProductId,
                    principalTable: "ExternalProducts",
                    principalColumn: "Id",
                    onDelete: ReferentialAction.Cascade);
            });

        migrationBuilder.CreateIndex(
            name: "IX_ExternalProductOptions_ExternalProductId",
            table: "ExternalProductOptions",
            column: "ExternalProductId");

        migrationBuilder.CreateIndex(
            name: "IX_ExternalProducts_ImportId",
            table: "ExternalProducts",
            column: "ImportId");
    }

    /// <inheritdoc />
    protected override void Down(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.DropTable(
            name: "ExternalProductOptions");

        migrationBuilder.DropTable(
            name: "ExternalProducts");
    }
}
