using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Auditdata.InventoryService.Infrastructure.Migrations;

/// <inheritdoc />
public partial class AddSku : Migration
{
    /// <inheritdoc />
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.CreateTable(
            name: "SkuConfigs",
            columns: table => new
            {
                Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                ProductId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                AttributeId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                SkuAttributeType = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                TenantId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                CreationDate = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                ChangeDate = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                CreatedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false, defaultValue: "admin"),
                ChangedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true)
            },
            constraints: table =>
            {
                table.PrimaryKey("PK_SkuConfigs", x => x.Id);
                table.ForeignKey(
                    name: "FK_SkuConfigs_Attributes_AttributeId",
                    column: x => x.AttributeId,
                    principalTable: "Attributes",
                    principalColumn: "Id",
                    onDelete: ReferentialAction.Restrict);
                table.ForeignKey(
                    name: "FK_SkuConfigs_Products_ProductId",
                    column: x => x.ProductId,
                    principalTable: "Products",
                    principalColumn: "Id",
                    onDelete: ReferentialAction.Cascade);
            });

        migrationBuilder.CreateTable(
            name: "Skus",
            columns: table => new
            {
                Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                SkuValue = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                ProductId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                SupplierId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                ColorId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                BatteryTypeId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                IsDeleted = table.Column<bool>(type: "bit", nullable: false),
                TenantId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                Version = table.Column<byte[]>(type: "rowversion", rowVersion: true, nullable: false),
                CreationDate = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                ChangeDate = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                CreatedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false, defaultValue: "admin"),
                ChangedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true)
            },
            constraints: table =>
            {
                table.PrimaryKey("PK_Skus", x => x.Id);
                table.ForeignKey(
                    name: "FK_Skus_BatteryTypes_BatteryTypeId",
                    column: x => x.BatteryTypeId,
                    principalTable: "BatteryTypes",
                    principalColumn: "Id",
                    onDelete: ReferentialAction.Restrict);
                table.ForeignKey(
                    name: "FK_Skus_Colors_ColorId",
                    column: x => x.ColorId,
                    principalTable: "Colors",
                    principalColumn: "Id",
                    onDelete: ReferentialAction.Restrict);
                table.ForeignKey(
                    name: "FK_Skus_Products_ProductId",
                    column: x => x.ProductId,
                    principalTable: "Products",
                    principalColumn: "Id",
                    onDelete: ReferentialAction.Cascade);
                table.ForeignKey(
                    name: "FK_Skus_Suppliers_SupplierId",
                    column: x => x.SupplierId,
                    principalTable: "Suppliers",
                    principalColumn: "Id",
                    onDelete: ReferentialAction.Restrict);
            });

        migrationBuilder.CreateTable(
            name: "SkuAttributes",
            columns: table => new
            {
                Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                ProductId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                AttributeId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                IsDeleted = table.Column<bool>(type: "bit", nullable: false),
                TenantId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                SkuId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                CreationDate = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                ChangeDate = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                CreatedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false, defaultValue: "admin"),
                ChangedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                Value = table.Column<string>(type: "nvarchar(4000)", nullable: false)
            },
            constraints: table =>
            {
                table.PrimaryKey("PK_SkuAttributes", x => x.Id);
                table.ForeignKey(
                    name: "FK_SkuAttributes_Attributes_AttributeId",
                    column: x => x.AttributeId,
                    principalTable: "Attributes",
                    principalColumn: "Id",
                    onDelete: ReferentialAction.Cascade);
                table.ForeignKey(
                    name: "FK_SkuAttributes_Products_ProductId",
                    column: x => x.ProductId,
                    principalTable: "Products",
                    principalColumn: "Id",
                    onDelete: ReferentialAction.Cascade);
                table.ForeignKey(
                    name: "FK_SkuAttributes_Skus_SkuId",
                    column: x => x.SkuId,
                    principalTable: "Skus",
                    principalColumn: "Id");
            });

        migrationBuilder.CreateIndex(
            name: "IX_SkuAttributes_AttributeId",
            table: "SkuAttributes",
            column: "AttributeId");

        migrationBuilder.CreateIndex(
            name: "IX_SkuAttributes_ProductId",
            table: "SkuAttributes",
            column: "ProductId");

        migrationBuilder.CreateIndex(
            name: "IX_SkuAttributes_SkuId",
            table: "SkuAttributes",
            column: "SkuId");

        migrationBuilder.CreateIndex(
            name: "IX_SkuConfigs_AttributeId",
            table: "SkuConfigs",
            column: "AttributeId");

        migrationBuilder.CreateIndex(
            name: "IX_SkuConfigs_ProductId",
            table: "SkuConfigs",
            column: "ProductId");

        migrationBuilder.CreateIndex(
            name: "IX_Skus_BatteryTypeId",
            table: "Skus",
            column: "BatteryTypeId");

        migrationBuilder.CreateIndex(
            name: "IX_Skus_ColorId",
            table: "Skus",
            column: "ColorId");

        migrationBuilder.CreateIndex(
            name: "IX_Skus_ProductId",
            table: "Skus",
            column: "ProductId");

        migrationBuilder.CreateIndex(
            name: "IX_Skus_SupplierId",
            table: "Skus",
            column: "SupplierId");
    }

    /// <inheritdoc />
    protected override void Down(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.DropTable(
            name: "SkuAttributes");

        migrationBuilder.DropTable(
            name: "SkuConfigs");

        migrationBuilder.DropTable(
            name: "Skus");
    }
}
