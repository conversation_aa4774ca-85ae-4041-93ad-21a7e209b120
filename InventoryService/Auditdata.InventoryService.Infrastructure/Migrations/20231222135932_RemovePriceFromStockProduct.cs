using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Auditdata.InventoryService.Infrastructure.Migrations;

/// <inheritdoc />
public partial class RemovePriceFromStockProduct : Migration
{
    /// <inheritdoc />
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.DropForeignKey(
            name: "FK_StockProducts_Products_ProductId",
            table: "StockProducts");

        migrationBuilder.DropForeignKey(
            name: "FK_StockProducts_Stocks_StockId",
            table: "StockProducts");

        migrationBuilder.DropColumn(
            name: "Price",
            table: "StockProducts");

        migrationBuilder.AlterColumn<Guid>(
            name: "StockId",
            table: "StockProducts",
            type: "uniqueidentifier",
            nullable: false,
            defaultValue: new Guid("00000000-0000-0000-0000-000000000000"),
            oldClrType: typeof(Guid),
            oldType: "uniqueidentifier",
            oldNullable: true);

        migrationBuilder.AlterColumn<Guid>(
            name: "ProductId",
            table: "StockProducts",
            type: "uniqueidentifier",
            nullable: false,
            defaultValue: new Guid("00000000-0000-0000-0000-000000000000"),
            oldClrType: typeof(Guid),
            oldType: "uniqueidentifier",
            oldNullable: true);

        migrationBuilder.AddForeignKey(
            name: "FK_StockProducts_Products_ProductId",
            table: "StockProducts",
            column: "ProductId",
            principalTable: "Products",
            principalColumn: "Id",
            onDelete: ReferentialAction.Cascade);

        migrationBuilder.AddForeignKey(
            name: "FK_StockProducts_Stocks_StockId",
            table: "StockProducts",
            column: "StockId",
            principalTable: "Stocks",
            principalColumn: "Id",
            onDelete: ReferentialAction.Cascade);
    }

    /// <inheritdoc />
    protected override void Down(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.DropForeignKey(
            name: "FK_StockProducts_Products_ProductId",
            table: "StockProducts");

        migrationBuilder.DropForeignKey(
            name: "FK_StockProducts_Stocks_StockId",
            table: "StockProducts");

        migrationBuilder.AlterColumn<Guid>(
            name: "StockId",
            table: "StockProducts",
            type: "uniqueidentifier",
            nullable: true,
            oldClrType: typeof(Guid),
            oldType: "uniqueidentifier");

        migrationBuilder.AlterColumn<Guid>(
            name: "ProductId",
            table: "StockProducts",
            type: "uniqueidentifier",
            nullable: true,
            oldClrType: typeof(Guid),
            oldType: "uniqueidentifier");

        migrationBuilder.AddColumn<decimal>(
            name: "Price",
            table: "StockProducts",
            type: "decimal(18,2)",
            precision: 18,
            scale: 2,
            nullable: false,
            defaultValue: 0m);

        migrationBuilder.AddForeignKey(
            name: "FK_StockProducts_Products_ProductId",
            table: "StockProducts",
            column: "ProductId",
            principalTable: "Products",
            principalColumn: "Id");

        migrationBuilder.AddForeignKey(
            name: "FK_StockProducts_Stocks_StockId",
            table: "StockProducts",
            column: "StockId",
            principalTable: "Stocks",
            principalColumn: "Id");
    }
}
