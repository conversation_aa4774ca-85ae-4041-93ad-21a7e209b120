using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Auditdata.InventoryService.Infrastructure.Migrations;

public partial class LookupSeeding : Migration
{
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.Sql(@"
            DELETE FROM ManufacturerProductTypes;
            DELETE FROM ProductTypes;
            DELETE FROM BatteryTypes;
            DELETE FROM HearingAidTypes;
        ");

        migrationBuilder.Sql(@"
            INSERT INTO ProductTypes(Id, IsDeleted, Name)
            VALUES
                ('08300CA2-6A6A-4A25-AD16-3818E9550847', 0, 'Batteries'),
                ('30931946-6B02-4D5B-B645-77DCD545FF8C', 0, 'NHS Service'),
                ('A3E47357-481E-421A-BA04-D6F1946D9E69', 0, 'Repair Service'),
                ('47ACC31B-22AF-48DD-ACA7-09E3B4F1127C', 0, 'Earmolds'),
                ('B045B8B4-0B41-418F-B2D6-0724EA8177B7', 0, 'Hearing Aids'),
                ('9C26D46A-3B38-4D34-B723-387E1DEAA952', 0, 'Accessories'),
                ('F2AE794B-734C-4C41-AD84-A84F54000167', 0, 'Remote'),
                ('A9DC025B-75FD-4B30-B1D3-CA58FB7FB270', 0, 'Other')
        ");

        migrationBuilder.Sql(@"
            INSERT INTO BatteryTypes(Id, IsDeleted, Name)
            VALUES
                ('975340BB-D5FD-41E2-9BAB-F6F06109EDC9', 0, '10'),
                ('C33221BC-E71A-4B85-B92D-8632358CD37D', 0, '13'),
                ('AD270856-4D29-4E3E-B0BC-E9089D196FAB', 0, '312'),
                ('6B84BED9-740C-43E7-9855-FD8275231A43', 0, '675'),
                ('6E213806-80C7-40CF-9605-D58EAF9789FF', 0, 'Rechargeable')
        ");

        migrationBuilder.Sql(@"
            INSERT INTO HearingAidTypes(Id, IsDeleted, Name)
            VALUES
                ('BDF916F7-CFB6-4016-BD05-0D1C2E2C5B10', 0, 'BTE'),
                ('8BB7392C-FCE0-4522-97C8-1D246B136D01', 0, 'RIC'),
                ('EADF48C0-7F9E-4158-90E5-AA976904CAE0', 0, 'CIC'),
                ('8267C588-ABF9-436A-8160-A989B34C3A19', 0, 'ITE'),
                ('9C3D9F64-88A7-44C1-9138-056BEA29CF44', 0, 'Other')
        ");
    }

    protected override void Down(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.Sql(@"
            DELETE FROM ManufacturerProductTypes;
            DELETE FROM ProductTypes;
            DELETE FROM BatteryTypes;
            DELETE FROM HearingAidTypes;
        ");
        
        migrationBuilder.Sql(@"
            INSERT INTO ProductTypes (Id, IsDeleted, Name)
            VALUES
                (N'B045B8B4-0B41-418F-B2D6-0724EA8177B7', 0, N'Hearing Aids'),
                (N'47ACC31B-22AF-48DD-ACA7-09E3B4F1127C', 0, N'Earmold'),
                (N'08300CA2-6A6A-4A25-AD16-3818E9550847', 0, N'Battery'),
                (N'9C26D46A-3B38-4D34-B723-387E1DEAA952', 0, N'Accessories'),
                (N'30931946-6B02-4D5B-B645-77DCD545FF8C', 0, N'Service'),
                (N'F2AE794B-734C-4C41-AD84-A84F54000167', 0, N'Remote'),
                (N'A9DC025B-75FD-4B30-B1D3-CA58FB7FB270', 0, N'Receiver'),
                (N'A3E47357-481E-421A-BA04-D6F1946D9E69', 0, N'Repair type')
        ");

        migrationBuilder.Sql(@"
            INSERT INTO BatteryTypes (Id, IsDeleted, Name)
            VALUES
                (N'C33221BC-E71A-4B85-B92D-8632358CD37D', 0, N'13'),
                (N'AD270856-4D29-4E3E-B0BC-E9089D196FAB', 0, N'312'),
                (N'975340BB-D5FD-41E2-9BAB-F6F06109EDC9', 0, N'10'),
                (N'6B84BED9-740C-43E7-9855-FD8275231A43', 0, N'675')
        ");

        migrationBuilder.Sql(@"
            INSERT INTO HearingAidTypes (Id, IsDeleted, Name)
            VALUES
                (N'BDF916F7-CFB6-4016-BD05-0D1C2E2C5B10', 0, N'BTE'),
                (N'8BB7392C-FCE0-4522-97C8-1D246B136D01', 0, N'RIC'),
                (N'49DBB0E9-BB6B-4EF8-BE24-88FDEFAFB920', 0, N'IIC'),
                (N'B350B034-3C83-45D9-AF86-A1A0554684B9', 0, N'Power'),
                (N'14B2517F-33A2-4A46-BED3-A33CEA6DBE92', 0, N'ITC'),
                (N'8267C588-ABF9-436A-8160-A989B34C3A19', 0, N'ITE'),
                (N'EADF48C0-7F9E-4158-90E5-AA976904CAE0', 0, N'CIC'),
                (N'C9EC550F-4673-483F-B085-EDA334887E0C', 0, N'Open')
        ");
    }
}
