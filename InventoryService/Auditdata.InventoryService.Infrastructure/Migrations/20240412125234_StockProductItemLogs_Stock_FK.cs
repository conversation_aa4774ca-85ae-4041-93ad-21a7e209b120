using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Auditdata.InventoryService.Infrastructure.Migrations;

/// <inheritdoc />
public partial class StockProductItemLogs_Stock_FK : Migration
{
    /// <inheritdoc />
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.AlterColumn<string>(
            name: "MessageType",
            table: "OutboxMessage",
            type: "nvarchar(max)",
            nullable: false,
            oldClrType: typeof(string),
            oldType: "nvarchar(1000)",
            oldMaxLength: 1000);

        migrationBuilder.CreateIndex(
            name: "IX_StockProductItemLogs_StockId",
            table: "StockProductItemLogs",
            column: "StockId");

        migrationBuilder.AddForeignKey(
            name: "FK_StockProductItemLogs_Stocks_StockId",
            table: "StockProductItemLogs",
            column: "StockId",
            principalTable: "Stocks",
            principalColumn: "Id");
    }

    /// <inheritdoc />
    protected override void Down(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.DropForeignKey(
            name: "FK_StockProductItemLogs_Stocks_StockId",
            table: "StockProductItemLogs");

        migrationBuilder.DropIndex(
            name: "IX_StockProductItemLogs_StockId",
            table: "StockProductItemLogs");

        migrationBuilder.AlterColumn<string>(
            name: "MessageType",
            table: "OutboxMessage",
            type: "nvarchar(1000)",
            maxLength: 1000,
            nullable: false,
            oldClrType: typeof(string),
            oldType: "nvarchar(max)");
    }
}
