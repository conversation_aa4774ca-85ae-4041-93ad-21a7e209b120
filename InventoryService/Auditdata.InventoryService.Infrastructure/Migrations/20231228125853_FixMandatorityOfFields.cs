using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Auditdata.InventoryService.Infrastructure.Migrations;

/// <inheritdoc />
public partial class FixMandatorityOfFields : Migration
{
    /// <inheritdoc />
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.DropForeignKey(
            name: "FK_NhsContractProducts_Products_ProductId",
            table: "NhsContractProducts");

        // Commented out, because we have Australia HSP Maintenance products that can have ManufacturerId = null
        /*migrationBuilder.DropForeignKey(
            name: "FK_Products_Manufacturers_ManufacturerId",
            table: "Products");*/

        migrationBuilder.DropForeignKey(
            name: "FK_Products_ProductCategories_CategoryId",
            table: "Products");

        migrationBuilder.DropForeignKey(
            name: "FK_StockProductItems_StockProducts_StockProductId",
            table: "StockProductItems");

        migrationBuilder.DropForeignKey(
            name: "FK_StockTransactions_Products_ProductId",
            table: "StockTransactions");

        migrationBuilder.DropForeignKey(
            name: "FK_StockTransactions_Stocks_StockId",
            table: "StockTransactions");

        migrationBuilder.AlterColumn<string>(
            name: "Address1",
            table: "Suppliers",
            type: "nvarchar(4000)",
            maxLength: 4000,
            nullable: false,
            defaultValue: "",
            oldClrType: typeof(string),
            oldType: "nvarchar(4000)",
            oldMaxLength: 4000,
            oldNullable: true);

        migrationBuilder.AlterColumn<Guid>(
            name: "StockId",
            table: "StockTransactions",
            type: "uniqueidentifier",
            nullable: false,
            defaultValue: new Guid("00000000-0000-0000-0000-000000000000"),
            oldClrType: typeof(Guid),
            oldType: "uniqueidentifier",
            oldNullable: true);

        migrationBuilder.AlterColumn<Guid>(
            name: "ProductId",
            table: "StockTransactions",
            type: "uniqueidentifier",
            nullable: false,
            defaultValue: new Guid("00000000-0000-0000-0000-000000000000"),
            oldClrType: typeof(Guid),
            oldType: "uniqueidentifier",
            oldNullable: true);

        migrationBuilder.AlterColumn<Guid>(
            name: "StockProductId",
            table: "StockProductItems",
            type: "uniqueidentifier",
            nullable: false,
            defaultValue: new Guid("00000000-0000-0000-0000-000000000000"),
            oldClrType: typeof(Guid),
            oldType: "uniqueidentifier",
            oldNullable: true);

        // Commented out, because we have Australia HSP Maintenance products that can have ManufacturerId = null
        /*migrationBuilder.AlterColumn<Guid>(
            name: "ManufacturerId",
            table: "Products",
            type: "uniqueidentifier",
            nullable: false,
            defaultValue: new Guid("00000000-0000-0000-0000-000000000000"),
            oldClrType: typeof(Guid),
            oldType: "uniqueidentifier",
            oldNullable: true);*/

        migrationBuilder.AlterColumn<Guid>(
            name: "CategoryId",
            table: "Products",
            type: "uniqueidentifier",
            nullable: false,
            defaultValue: new Guid("00000000-0000-0000-0000-000000000000"),
            oldClrType: typeof(Guid),
            oldType: "uniqueidentifier",
            oldNullable: true);

        migrationBuilder.AlterColumn<Guid>(
            name: "ProductId",
            table: "NhsContractProducts",
            type: "uniqueidentifier",
            nullable: false,
            defaultValue: new Guid("00000000-0000-0000-0000-000000000000"),
            oldClrType: typeof(Guid),
            oldType: "uniqueidentifier",
            oldNullable: true);

        migrationBuilder.AlterColumn<Guid>(
            name: "ContractId",
            table: "NhsContractProducts",
            type: "uniqueidentifier",
            nullable: false,
            defaultValue: new Guid("00000000-0000-0000-0000-000000000000"),
            oldClrType: typeof(Guid),
            oldType: "uniqueidentifier",
            oldNullable: true);

        migrationBuilder.AlterColumn<string>(
            name: "State",
            table: "Manufacturers",
            type: "nvarchar(255)",
            maxLength: 255,
            nullable: false,
            defaultValue: "",
            oldClrType: typeof(string),
            oldType: "nvarchar(255)",
            oldMaxLength: 255,
            oldNullable: true);

        migrationBuilder.AlterColumn<string>(
            name: "PhoneNumber",
            table: "Manufacturers",
            type: "nvarchar(255)",
            maxLength: 255,
            nullable: false,
            defaultValue: "",
            oldClrType: typeof(string),
            oldType: "nvarchar(255)",
            oldMaxLength: 255,
            oldNullable: true);

        migrationBuilder.AlterColumn<string>(
            name: "City",
            table: "Manufacturers",
            type: "nvarchar(255)",
            maxLength: 255,
            nullable: false,
            defaultValue: "",
            oldClrType: typeof(string),
            oldType: "nvarchar(255)",
            oldMaxLength: 255,
            oldNullable: true);

        migrationBuilder.AlterColumn<string>(
            name: "Address1",
            table: "Manufacturers",
            type: "nvarchar(4000)",
            maxLength: 4000,
            nullable: false,
            defaultValue: "",
            oldClrType: typeof(string),
            oldType: "nvarchar(4000)",
            oldMaxLength: 4000,
            oldNullable: true);

        migrationBuilder.AddForeignKey(
            name: "FK_NhsContractProducts_Products_ProductId",
            table: "NhsContractProducts",
            column: "ProductId",
            principalTable: "Products",
            principalColumn: "Id",
            onDelete: ReferentialAction.Cascade);

        // Commented out, because we have Australia HSP Maintenance products that can have ManufacturerId = null
        /*migrationBuilder.AddForeignKey(
            name: "FK_Products_Manufacturers_ManufacturerId",
            table: "Products",
            column: "ManufacturerId",
            principalTable: "Manufacturers",
            principalColumn: "Id",
            onDelete: ReferentialAction.Cascade);*/

        migrationBuilder.AddForeignKey(
            name: "FK_Products_ProductCategories_CategoryId",
            table: "Products",
            column: "CategoryId",
            principalTable: "ProductCategories",
            principalColumn: "Id",
            onDelete: ReferentialAction.Cascade);

        migrationBuilder.AddForeignKey(
            name: "FK_StockProductItems_StockProducts_StockProductId",
            table: "StockProductItems",
            column: "StockProductId",
            principalTable: "StockProducts",
            principalColumn: "Id",
            onDelete: ReferentialAction.Cascade);

        migrationBuilder.AddForeignKey(
            name: "FK_StockTransactions_Products_ProductId",
            table: "StockTransactions",
            column: "ProductId",
            principalTable: "Products",
            principalColumn: "Id",
            onDelete: ReferentialAction.Cascade);

        migrationBuilder.AddForeignKey(
            name: "FK_StockTransactions_Stocks_StockId",
            table: "StockTransactions",
            column: "StockId",
            principalTable: "Stocks",
            principalColumn: "Id",
            onDelete: ReferentialAction.Cascade);
    }

    /// <inheritdoc />
    protected override void Down(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.DropForeignKey(
            name: "FK_NhsContractProducts_Products_ProductId",
            table: "NhsContractProducts");

        // Commented out, because we have Australia HSP Maintenance products that can have ManufacturerId = null
        /*migrationBuilder.DropForeignKey(
            name: "FK_Products_Manufacturers_ManufacturerId",
            table: "Products");*/

        migrationBuilder.DropForeignKey(
            name: "FK_Products_ProductCategories_CategoryId",
            table: "Products");

        migrationBuilder.DropForeignKey(
            name: "FK_StockProductItems_StockProducts_StockProductId",
            table: "StockProductItems");

        migrationBuilder.DropForeignKey(
            name: "FK_StockTransactions_Products_ProductId",
            table: "StockTransactions");

        migrationBuilder.DropForeignKey(
            name: "FK_StockTransactions_Stocks_StockId",
            table: "StockTransactions");

        migrationBuilder.AlterColumn<string>(
            name: "Address1",
            table: "Suppliers",
            type: "nvarchar(4000)",
            maxLength: 4000,
            nullable: true,
            oldClrType: typeof(string),
            oldType: "nvarchar(4000)",
            oldMaxLength: 4000);

        migrationBuilder.AlterColumn<Guid>(
            name: "StockId",
            table: "StockTransactions",
            type: "uniqueidentifier",
            nullable: true,
            oldClrType: typeof(Guid),
            oldType: "uniqueidentifier");

        migrationBuilder.AlterColumn<Guid>(
            name: "ProductId",
            table: "StockTransactions",
            type: "uniqueidentifier",
            nullable: true,
            oldClrType: typeof(Guid),
            oldType: "uniqueidentifier");

        migrationBuilder.AlterColumn<Guid>(
            name: "StockProductId",
            table: "StockProductItems",
            type: "uniqueidentifier",
            nullable: true,
            oldClrType: typeof(Guid),
            oldType: "uniqueidentifier");

        // Commented out, because we have Australia HSP Maintenance products that can have ManufacturerId = null
        /*migrationBuilder.AlterColumn<Guid>(
            name: "ManufacturerId",
            table: "Products",
            type: "uniqueidentifier",
            nullable: true,
            oldClrType: typeof(Guid),
            oldType: "uniqueidentifier");*/

        migrationBuilder.AlterColumn<Guid>(
            name: "CategoryId",
            table: "Products",
            type: "uniqueidentifier",
            nullable: true,
            oldClrType: typeof(Guid),
            oldType: "uniqueidentifier");

        migrationBuilder.AlterColumn<Guid>(
            name: "ProductId",
            table: "NhsContractProducts",
            type: "uniqueidentifier",
            nullable: true,
            oldClrType: typeof(Guid),
            oldType: "uniqueidentifier");

        migrationBuilder.AlterColumn<Guid>(
            name: "ContractId",
            table: "NhsContractProducts",
            type: "uniqueidentifier",
            nullable: true,
            oldClrType: typeof(Guid),
            oldType: "uniqueidentifier");

        migrationBuilder.AlterColumn<string>(
            name: "State",
            table: "Manufacturers",
            type: "nvarchar(255)",
            maxLength: 255,
            nullable: true,
            oldClrType: typeof(string),
            oldType: "nvarchar(255)",
            oldMaxLength: 255);

        migrationBuilder.AlterColumn<string>(
            name: "PhoneNumber",
            table: "Manufacturers",
            type: "nvarchar(255)",
            maxLength: 255,
            nullable: true,
            oldClrType: typeof(string),
            oldType: "nvarchar(255)",
            oldMaxLength: 255);

        migrationBuilder.AlterColumn<string>(
            name: "City",
            table: "Manufacturers",
            type: "nvarchar(255)",
            maxLength: 255,
            nullable: true,
            oldClrType: typeof(string),
            oldType: "nvarchar(255)",
            oldMaxLength: 255);

        migrationBuilder.AlterColumn<string>(
            name: "Address1",
            table: "Manufacturers",
            type: "nvarchar(4000)",
            maxLength: 4000,
            nullable: true,
            oldClrType: typeof(string),
            oldType: "nvarchar(4000)",
            oldMaxLength: 4000);

        migrationBuilder.AddForeignKey(
            name: "FK_NhsContractProducts_Products_ProductId",
            table: "NhsContractProducts",
            column: "ProductId",
            principalTable: "Products",
            principalColumn: "Id");

        // Commented out, because we have Australia HSP Maintenance products that can have ManufacturerId = null
        /*migrationBuilder.AddForeignKey(
            name: "FK_Products_Manufacturers_ManufacturerId",
            table: "Products",
            column: "ManufacturerId",
            principalTable: "Manufacturers",
            principalColumn: "Id");*/

        migrationBuilder.AddForeignKey(
            name: "FK_Products_ProductCategories_CategoryId",
            table: "Products",
            column: "CategoryId",
            principalTable: "ProductCategories",
            principalColumn: "Id");

        migrationBuilder.AddForeignKey(
            name: "FK_StockProductItems_StockProducts_StockProductId",
            table: "StockProductItems",
            column: "StockProductId",
            principalTable: "StockProducts",
            principalColumn: "Id");

        migrationBuilder.AddForeignKey(
            name: "FK_StockTransactions_Products_ProductId",
            table: "StockTransactions",
            column: "ProductId",
            principalTable: "Products",
            principalColumn: "Id");

        migrationBuilder.AddForeignKey(
            name: "FK_StockTransactions_Stocks_StockId",
            table: "StockTransactions",
            column: "StockId",
            principalTable: "Stocks",
            principalColumn: "Id");
    }
}
