// <auto-generated />
using System;
using Auditdata.InventoryService.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace Auditdata.InventoryService.Infrastructure.Migrations;

[DbContext(typeof(InventoryDbContext))]
[Migration("20221205100158_add-index-for-stock-products")]
partial class addindexforstockproducts
{
    protected override void BuildTargetModel(ModelBuilder modelBuilder)
    {
#pragma warning disable 612, 618
        modelBuilder
            .HasAnnotation("ProductVersion", "6.0.10")
            .HasAnnotation("Relational:MaxIdentifierLength", 128);

        SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder, 1L, 1);

        modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.BatteryType", b =>
        {
            b.Property<Guid>("Id")
                .ValueGeneratedOnAdd()
                .HasColumnType("uniqueidentifier");

            b.Property<bool>("IsDeleted")
                .HasColumnType("bit");

            b.Property<string>("Name")
                .IsRequired()
                .HasMaxLength(255)
                .HasColumnType("nvarchar(255)");

            b.HasKey("Id");

            b.ToTable("BatteryTypes");

            b.HasData(
                new
                {
                    Id = new Guid("975340bb-d5fd-41e2-9bab-f6f06109edc9"),
                    IsDeleted = false,
                    Name = "10"
                },
                new
                {
                    Id = new Guid("ad270856-4d29-4e3e-b0bc-e9089d196fab"),
                    IsDeleted = false,
                    Name = "312"
                },
                new
                {
                    Id = new Guid("c33221bc-e71a-4b85-b92d-8632358cd37d"),
                    IsDeleted = false,
                    Name = "13"
                },
                new
                {
                    Id = new Guid("6b84bed9-740c-43e7-9855-fd8275231a43"),
                    IsDeleted = false,
                    Name = "675"
                },
                new
                {
                    Id = new Guid("6e213806-80c7-40cf-9605-d58eaf9789ff"),
                    IsDeleted = false,
                    Name = "Rechargeable"
                },
                new
                {
                    Id = new Guid("1b4b298c-f1bf-469a-8286-18471ce7342f"),
                    IsDeleted = false,
                    Name = "Unknown"
                });
        });

        modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.HearingAidType", b =>
        {
            b.Property<Guid>("Id")
                .ValueGeneratedOnAdd()
                .HasColumnType("uniqueidentifier");

            b.Property<bool>("IsDeleted")
                .HasColumnType("bit");

            b.Property<string>("Name")
                .IsRequired()
                .HasMaxLength(255)
                .HasColumnType("nvarchar(255)");

            b.HasKey("Id");

            b.ToTable("HearingAidTypes");

            b.HasData(
                new
                {
                    Id = new Guid("bdf916f7-cfb6-4016-bd05-0d1c2e2c5b10"),
                    IsDeleted = false,
                    Name = "BTE"
                },
                new
                {
                    Id = new Guid("8bb7392c-fce0-4522-97c8-1d246b136d01"),
                    IsDeleted = false,
                    Name = "RIC"
                },
                new
                {
                    Id = new Guid("eadf48c0-7f9e-4158-90e5-aa976904cae0"),
                    IsDeleted = false,
                    Name = "CIC"
                },
                new
                {
                    Id = new Guid("8267c588-abf9-436a-8160-a989b34c3a19"),
                    IsDeleted = false,
                    Name = "ITE"
                },
                new
                {
                    Id = new Guid("9c3d9f64-88a7-44c1-9138-056bea29cf44"),
                    IsDeleted = false,
                    Name = "Other"
                });
        });

        modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.Manufacturer", b =>
        {
            b.Property<Guid>("Id")
                .ValueGeneratedOnAdd()
                .HasColumnType("uniqueidentifier");

            b.Property<DateTime>("CreatedOn")
                .HasColumnType("datetime2");

            b.Property<bool>("IsActive")
                .HasColumnType("bit");

            b.Property<bool>("IsDeleted")
                .HasColumnType("bit");

            b.Property<DateTime?>("ModifiedOn")
                .HasColumnType("datetime2");

            b.Property<string>("Name")
                .IsRequired()
                .HasMaxLength(255)
                .HasColumnType("nvarchar(255)");

            b.Property<Guid>("TenantId")
                .HasColumnType("uniqueidentifier");

            b.HasKey("Id");

            b.ToTable("Manufacturers");
        });

        modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.ManufacturerProductType", b =>
        {
            b.Property<Guid>("Id")
                .ValueGeneratedOnAdd()
                .HasColumnType("uniqueidentifier");

            b.Property<bool>("IsDeleted")
                .HasColumnType("bit");

            b.Property<Guid?>("ManufacturerId")
                .HasColumnType("uniqueidentifier");

            b.Property<Guid?>("ProductTypeId")
                .HasColumnType("uniqueidentifier");

            b.HasKey("Id");

            b.HasIndex("ManufacturerId");

            b.HasIndex("ProductTypeId");

            b.ToTable("ManufacturerProductTypes");
        });

        modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.NhsContractProduct", b =>
        {
            b.Property<Guid>("Id")
                .ValueGeneratedOnAdd()
                .HasColumnType("uniqueidentifier");

            b.Property<Guid?>("ContractId")
                .HasColumnType("uniqueidentifier");

            b.Property<Guid?>("ProductId")
                .HasColumnType("uniqueidentifier");

            b.Property<Guid>("TenantId")
                .HasColumnType("uniqueidentifier");

            b.HasKey("Id");

            b.HasIndex("ProductId");

            b.ToTable("NhsContractProducts");
        });

        modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.Pathway", b =>
        {
            b.Property<Guid>("Id")
                .ValueGeneratedOnAdd()
                .HasColumnType("uniqueidentifier");

            b.Property<bool>("IsDeleted")
                .HasColumnType("bit");

            b.Property<string>("Name")
                .IsRequired()
                .HasMaxLength(255)
                .HasColumnType("nvarchar(255)");

            b.Property<string>("Type")
                .IsRequired()
                .HasColumnType("nvarchar(max)");

            b.HasKey("Id");

            b.ToTable("Pathways");

            b.HasData(
                new
                {
                    Id = new Guid("4e7301d0-88c5-4f33-962d-ed06b4e64e4d"),
                    IsDeleted = false,
                    Name = "Wax Removal",
                    Type = "wax_removal"
                },
                new
                {
                    Id = new Guid("a8a63efd-3763-41f7-a568-b887b4341e24"),
                    IsDeleted = false,
                    Name = "HA Fitting",
                    Type = "ha_fitting"
                },
                new
                {
                    Id = new Guid("9d363ec1-d09a-4380-93af-e0324e9d028d"),
                    IsDeleted = false,
                    Name = "Recall",
                    Type = "recall"
                },
                new
                {
                    Id = new Guid("345201f6-4599-4871-bb28-2e44834f8a30"),
                    IsDeleted = false,
                    Name = "Aftercare",
                    Type = "aftercare"
                },
                new
                {
                    Id = new Guid("98dc257c-ec38-49c8-b658-5bb4afc71ce8"),
                    IsDeleted = false,
                    Name = "Remote Aftercare",
                    Type = "remote_aftercare"
                },
                new
                {
                    Id = new Guid("6b7d87cc-4f5b-4b00-8d55-6da6d5a8c9b3"),
                    IsDeleted = false,
                    Name = "Hearing Check",
                    Type = "hearing_check"
                },
                new
                {
                    Id = new Guid("b3943d8b-80c9-4822-9998-a79a3a42f239"),
                    IsDeleted = false,
                    Name = "Private Test",
                    Type = "private_test"
                },
                new
                {
                    Id = new Guid("19de7aa8-e174-47f8-83a8-953bbc15594e"),
                    IsDeleted = false,
                    Name = "NHS Assess & Fit",
                    Type = "nhs_assess_and_fit"
                },
                new
                {
                    Id = new Guid("5a674586-39fc-4ba3-9fd8-c3a2ca17c80e"),
                    IsDeleted = false,
                    Name = "Private Test & HA Consult",
                    Type = "private_test_ha_consult"
                },
                new
                {
                    Id = new Guid("440e198b-1089-4c28-99e5-cdcf79332407"),
                    IsDeleted = false,
                    Name = "Private HA Consultation",
                    Type = "private_ha_consultation"
                },
                new
                {
                    Id = new Guid("3e0a70b0-61e0-434d-a258-709583ea0f2b"),
                    IsDeleted = false,
                    Name = "Wax Triage",
                    Type = "wax_triage"
                },
                new
                {
                    Id = new Guid("0b9909d1-b2bb-4efc-8d24-fd6dcc994013"),
                    IsDeleted = false,
                    Name = "Follow Up",
                    Type = "follow_up"
                },
                new
                {
                    Id = new Guid("88c28659-77dc-4bd5-a2d3-31aa671e6210"),
                    IsDeleted = false,
                    Name = "Remote Follow Up",
                    Type = "remote_follow_up"
                },
                new
                {
                    Id = new Guid("ac95bdfd-f8a8-4d1f-89e8-7b4aa06e9165"),
                    IsDeleted = false,
                    Name = "Re-Assessment",
                    Type = "re_assessment"
                },
                new
                {
                    Id = new Guid("efff08a8-c1e2-4199-9488-5405506fc9ed"),
                    IsDeleted = false,
                    Name = "Hearing Protection",
                    Type = "hearing_protection"
                });
        });

        modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.Product", b =>
        {
            b.Property<Guid>("Id")
                .ValueGeneratedOnAdd()
                .HasColumnType("uniqueidentifier");

            b.Property<bool>("AutoDeliver")
                .HasColumnType("bit");

            b.Property<Guid?>("BatteryTypeId")
                .HasColumnType("uniqueidentifier");

            b.Property<string>("Code")
                .HasMaxLength(255)
                .HasColumnType("nvarchar(255)");

            b.Property<bool>("ControlledByStock")
                .HasColumnType("bit");

            b.Property<DateTime>("CreatedOn")
                .HasColumnType("datetime2");

            b.Property<string>("Description")
                .HasMaxLength(4000)
                .HasColumnType("nvarchar(4000)");

            b.Property<decimal>("FirstVAT")
                .HasColumnType("decimal(18,2)");

            b.Property<Guid?>("HearingAidTypeId")
                .HasColumnType("uniqueidentifier");

            b.Property<bool>("IsActive")
                .HasColumnType("bit");

            b.Property<bool>("IsDeleted")
                .HasColumnType("bit");

            b.Property<bool>("IsNHS")
                .HasColumnType("bit");

            b.Property<bool>("IsSellable")
                .HasColumnType("bit");

            b.Property<bool>("IsSerialized")
                .HasColumnType("bit");

            b.Property<Guid?>("ManufacturerId")
                .HasColumnType("uniqueidentifier");

            b.Property<DateTime?>("ModifiedOn")
                .HasColumnType("datetime2");

            b.Property<decimal?>("NHSVAT")
                .HasColumnType("decimal(18,2)");

            b.Property<string>("Name")
                .IsRequired()
                .HasMaxLength(255)
                .HasColumnType("nvarchar(255)");

            b.Property<Guid?>("NhsServiceTariffId")
                .HasColumnType("uniqueidentifier");

            b.Property<bool>("PriceChangesAllowed")
                .HasColumnType("bit");

            b.Property<int>("Quantity")
                .HasColumnType("int");

            b.Property<decimal>("RetailPrice")
                .HasColumnType("decimal(18,2)");

            b.Property<decimal>("SecondVAT")
                .HasColumnType("decimal(18,2)");

            b.Property<Guid?>("SupplierId")
                .HasColumnType("uniqueidentifier");

            b.Property<Guid>("TenantId")
                .HasColumnType("uniqueidentifier");

            b.Property<Guid?>("TypeId")
                .HasColumnType("uniqueidentifier");

            b.Property<int?>("Warranty")
                .HasColumnType("int");

            b.HasKey("Id");

            b.HasIndex("BatteryTypeId");

            b.HasIndex("HearingAidTypeId");

            b.HasIndex("ManufacturerId");

            b.HasIndex("SupplierId");

            b.HasIndex("TypeId");

            b.ToTable("Products");
        });

        modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.ProductPathway", b =>
        {
            b.Property<Guid>("Id")
                .ValueGeneratedOnAdd()
                .HasColumnType("uniqueidentifier");

            b.Property<bool>("IsDeleted")
                .HasColumnType("bit");

            b.Property<Guid?>("PathwayId")
                .HasColumnType("uniqueidentifier");

            b.Property<Guid?>("ProductId")
                .HasColumnType("uniqueidentifier");

            b.HasKey("Id");

            b.HasIndex("PathwayId");

            b.HasIndex("ProductId");

            b.ToTable("ProductPathways");
        });

        modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.ProductType", b =>
        {
            b.Property<Guid>("Id")
                .ValueGeneratedOnAdd()
                .HasColumnType("uniqueidentifier");

            b.Property<bool>("IsDeleted")
                .HasColumnType("bit");

            b.Property<string>("Name")
                .IsRequired()
                .HasMaxLength(255)
                .HasColumnType("nvarchar(255)");

            b.HasKey("Id");

            b.ToTable("ProductTypes");

            b.HasData(
                new
                {
                    Id = new Guid("08300ca2-6a6a-4a25-ad16-3818e9550847"),
                    IsDeleted = false,
                    Name = "Batteries"
                },
                new
                {
                    Id = new Guid("30931946-6b02-4d5b-b645-77dcd545ff8c"),
                    IsDeleted = false,
                    Name = "Service"
                },
                new
                {
                    Id = new Guid("a3e47357-481e-421a-ba04-d6f1946d9e69"),
                    IsDeleted = false,
                    Name = "Repair Service"
                },
                new
                {
                    Id = new Guid("47acc31b-22af-48dd-aca7-09e3b4f1127c"),
                    IsDeleted = false,
                    Name = "Earmolds"
                },
                new
                {
                    Id = new Guid("b045b8b4-0b41-418f-b2d6-0724ea8177b7"),
                    IsDeleted = false,
                    Name = "Hearing Aids"
                },
                new
                {
                    Id = new Guid("9c26d46a-3b38-4d34-b723-387e1deaa952"),
                    IsDeleted = false,
                    Name = "Accessories"
                },
                new
                {
                    Id = new Guid("f2ae794b-734c-4c41-ad84-a84f54000167"),
                    IsDeleted = false,
                    Name = "Remote"
                },
                new
                {
                    Id = new Guid("a9dc025b-75fd-4b30-b1d3-ca58fb7fb270"),
                    IsDeleted = false,
                    Name = "Other"
                });
        });

        modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.Stock", b =>
        {
            b.Property<Guid>("Id")
                .ValueGeneratedOnAdd()
                .HasColumnType("uniqueidentifier")
                .HasDefaultValueSql("NEWID()");

            b.Property<Guid?>("LocationId")
                .IsRequired()
                .HasColumnType("uniqueidentifier");

            b.Property<string>("Name")
                .IsRequired()
                .HasMaxLength(255)
                .HasColumnType("nvarchar(255)");

            b.Property<Guid>("TenantId")
                .HasColumnType("uniqueidentifier");

            b.HasKey("Id");

            b.HasIndex("LocationId")
                .IsUnique();

            b.ToTable("Stocks");
        });

        modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.StockAdjustmentReason", b =>
        {
            b.Property<Guid>("Id")
                .ValueGeneratedOnAdd()
                .HasColumnType("uniqueidentifier");

            b.Property<bool>("IsActive")
                .HasColumnType("bit");

            b.Property<bool>("IsDeleted")
                .HasColumnType("bit");

            b.Property<string>("Name")
                .IsRequired()
                .HasColumnType("nvarchar(max)");

            b.Property<Guid>("TenantId")
                .HasColumnType("uniqueidentifier");

            b.HasKey("Id");

            b.ToTable("StockAdjustmentReasons");
        });

        modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.StockProduct", b =>
        {
            b.Property<Guid>("Id")
                .ValueGeneratedOnAdd()
                .HasColumnType("uniqueidentifier");

            b.Property<DateTime>("CreatedOn")
                .HasColumnType("datetime2");

            b.Property<bool>("IsDeleted")
                .HasColumnType("bit");

            b.Property<DateTime?>("ModifiedOn")
                .HasColumnType("datetime2");

            b.Property<decimal>("Price")
                .HasColumnType("decimal(18,2)");

            b.Property<Guid?>("ProductId")
                .HasColumnType("uniqueidentifier");

            b.Property<int>("Quantity")
                .HasColumnType("int");

            b.Property<Guid?>("StockId")
                .HasColumnType("uniqueidentifier");

            b.Property<Guid>("TenantId")
                .HasColumnType("uniqueidentifier");

            b.HasKey("Id");

            b.HasIndex("StockId");

            b.HasIndex("ProductId", "StockId", "TenantId")
                .IsUnique()
                .HasFilter("[IsDeleted] = 0");

            b.ToTable("StockProducts");
        });

        modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.StockProductItem", b =>
        {
            b.Property<Guid>("Id")
                .ValueGeneratedOnAdd()
                .HasColumnType("uniqueidentifier");

            b.Property<DateTime>("CreatedOn")
                .HasColumnType("datetime2");

            b.Property<bool>("IsDeleted")
                .HasColumnType("bit");

            b.Property<DateTime?>("ModifiedOn")
                .HasColumnType("datetime2");

            b.Property<Guid?>("NegativeAdjustmentReasonId")
                .HasColumnType("uniqueidentifier");

            b.Property<Guid?>("SaleId")
                .HasColumnType("uniqueidentifier");

            b.Property<string>("SerialNumber")
                .HasMaxLength(255)
                .HasColumnType("nvarchar(255)");

            b.Property<int>("Status")
                .HasColumnType("int");

            b.Property<Guid?>("StockProductId")
                .HasColumnType("uniqueidentifier");

            b.Property<Guid>("TenantId")
                .HasColumnType("uniqueidentifier");

            b.HasKey("Id");

            b.HasIndex("NegativeAdjustmentReasonId");

            b.HasIndex("StockProductId");

            b.ToTable("StockProductItems");
        });

        modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.StockTransaction", b =>
        {
            b.Property<Guid>("Id")
                .ValueGeneratedOnAdd()
                .HasColumnType("uniqueidentifier");

            b.Property<DateTime>("CreatedOn")
                .HasColumnType("datetime2");

            b.Property<DateTime?>("ModifiedOn")
                .HasColumnType("datetime2");

            b.Property<Guid?>("ProductId")
                .HasColumnType("uniqueidentifier");

            b.Property<int>("Quantity")
                .HasColumnType("int");

            b.Property<Guid?>("SaleId")
                .HasColumnType("uniqueidentifier");

            b.Property<Guid?>("StockId")
                .HasColumnType("uniqueidentifier");

            b.Property<Guid>("TenantId")
                .HasColumnType("uniqueidentifier");

            b.Property<int>("TotalQuantity")
                .HasColumnType("int");

            b.Property<string>("TransactionId")
                .IsRequired()
                .HasColumnType("nvarchar(max)");

            b.Property<int>("TypeId")
                .HasColumnType("int");

            b.HasKey("Id");

            b.HasIndex("ProductId");

            b.HasIndex("StockId");

            b.ToTable("StockTransactions");
        });

        modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.Supplier", b =>
        {
            b.Property<Guid>("Id")
                .ValueGeneratedOnAdd()
                .HasColumnType("uniqueidentifier");

            b.Property<DateTime>("CreatedOn")
                .HasColumnType("datetime2");

            b.Property<bool>("IsActive")
                .HasColumnType("bit");

            b.Property<bool>("IsDeleted")
                .HasColumnType("bit");

            b.Property<DateTime?>("ModifiedOn")
                .HasColumnType("datetime2");

            b.Property<string>("Name")
                .IsRequired()
                .HasMaxLength(255)
                .HasColumnType("nvarchar(255)");

            b.Property<Guid>("TenantId")
                .HasColumnType("uniqueidentifier");

            b.HasKey("Id");

            b.ToTable("Suppliers");
        });

        modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.Transfer", b =>
        {
            b.Property<Guid>("Id")
                .ValueGeneratedOnAdd()
                .HasColumnType("uniqueidentifier");

            b.Property<DateTime>("CreatedOn")
                .HasColumnType("datetime2");

            b.Property<DateTime>("Date")
                .HasColumnType("datetime2");

            b.Property<DateTime?>("ModifiedOn")
                .HasColumnType("datetime2");

            b.Property<int>("Status")
                .HasColumnType("int");

            b.Property<Guid?>("StockFromId")
                .HasColumnType("uniqueidentifier");

            b.Property<Guid?>("StockToId")
                .HasColumnType("uniqueidentifier");

            b.Property<Guid>("TenantId")
                .HasColumnType("uniqueidentifier");

            b.HasKey("Id");

            b.HasIndex("StockFromId");

            b.HasIndex("StockToId");

            b.ToTable("Transfers");
        });

        modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.TransferItem", b =>
        {
            b.Property<Guid>("Id")
                .ValueGeneratedOnAdd()
                .HasColumnType("uniqueidentifier");

            b.Property<DateTime>("CreatedOn")
                .HasColumnType("datetime2");

            b.Property<DateTime?>("ModifiedOn")
                .HasColumnType("datetime2");

            b.Property<int>("Quantity")
                .HasColumnType("int");

            b.Property<string>("SerialNumbers")
                .HasColumnType("nvarchar(max)");

            b.Property<Guid?>("StockProductId")
                .HasColumnType("uniqueidentifier");

            b.Property<Guid>("TenantId")
                .HasColumnType("uniqueidentifier");

            b.Property<Guid?>("TransferId")
                .HasColumnType("uniqueidentifier");

            b.HasKey("Id");

            b.HasIndex("StockProductId");

            b.HasIndex("TransferId");

            b.ToTable("TransferItems");
        });

        modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.TransferTransaction", b =>
        {
            b.Property<Guid>("Id")
                .ValueGeneratedOnAdd()
                .HasColumnType("uniqueidentifier");

            b.Property<string>("Comments")
                .HasMaxLength(4000)
                .HasColumnType("nvarchar(4000)");

            b.Property<DateTime>("CreatedOn")
                .HasColumnType("datetime2");

            b.Property<int>("Direction")
                .HasColumnType("int");

            b.Property<DateTime?>("ModifiedOn")
                .HasColumnType("datetime2");

            b.Property<Guid?>("StockTransactionId")
                .HasColumnType("uniqueidentifier");

            b.Property<Guid>("TenantId")
                .HasColumnType("uniqueidentifier");

            b.Property<Guid?>("TransferId")
                .HasColumnType("uniqueidentifier");

            b.HasKey("Id");

            b.HasIndex("StockTransactionId");

            b.HasIndex("TransferId");

            b.ToTable("TransferTransactions");
        });

        modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.ManufacturerProductType", b =>
        {
            b.HasOne("Auditdata.InventoryService.Core.Entities.Manufacturer", "Manufacturer")
                .WithMany("ManufacturerProductTypes")
                .HasForeignKey("ManufacturerId");

            b.HasOne("Auditdata.InventoryService.Core.Entities.ProductType", "ProductType")
                .WithMany()
                .HasForeignKey("ProductTypeId");

            b.Navigation("Manufacturer");

            b.Navigation("ProductType");
        });

        modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.NhsContractProduct", b =>
        {
            b.HasOne("Auditdata.InventoryService.Core.Entities.Product", "Product")
                .WithMany("NhsContractProducts")
                .HasForeignKey("ProductId");

            b.Navigation("Product");
        });

        modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.Product", b =>
        {
            b.HasOne("Auditdata.InventoryService.Core.Entities.BatteryType", "BatteryType")
                .WithMany()
                .HasForeignKey("BatteryTypeId");

            b.HasOne("Auditdata.InventoryService.Core.Entities.HearingAidType", "HearingAidType")
                .WithMany()
                .HasForeignKey("HearingAidTypeId");

            b.HasOne("Auditdata.InventoryService.Core.Entities.Manufacturer", "Manufacturer")
                .WithMany()
                .HasForeignKey("ManufacturerId");

            b.HasOne("Auditdata.InventoryService.Core.Entities.Supplier", "Supplier")
                .WithMany()
                .HasForeignKey("SupplierId");

            b.HasOne("Auditdata.InventoryService.Core.Entities.ProductType", "Type")
                .WithMany()
                .HasForeignKey("TypeId");

            b.Navigation("BatteryType");

            b.Navigation("HearingAidType");

            b.Navigation("Manufacturer");

            b.Navigation("Supplier");

            b.Navigation("Type");
        });

        modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.ProductPathway", b =>
        {
            b.HasOne("Auditdata.InventoryService.Core.Entities.Pathway", "Pathway")
                .WithMany()
                .HasForeignKey("PathwayId");

            b.HasOne("Auditdata.InventoryService.Core.Entities.Product", "Product")
                .WithMany("ProductPathways")
                .HasForeignKey("ProductId");

            b.Navigation("Pathway");

            b.Navigation("Product");
        });

        modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.StockProduct", b =>
        {
            b.HasOne("Auditdata.InventoryService.Core.Entities.Product", "Product")
                .WithMany("StockProducts")
                .HasForeignKey("ProductId");

            b.HasOne("Auditdata.InventoryService.Core.Entities.Stock", "Stock")
                .WithMany()
                .HasForeignKey("StockId");

            b.Navigation("Product");

            b.Navigation("Stock");
        });

        modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.StockProductItem", b =>
        {
            b.HasOne("Auditdata.InventoryService.Core.Entities.StockAdjustmentReason", "NegativeAdjustmentReason")
                .WithMany()
                .HasForeignKey("NegativeAdjustmentReasonId");

            b.HasOne("Auditdata.InventoryService.Core.Entities.StockProduct", "StockProduct")
                .WithMany("StockProductItems")
                .HasForeignKey("StockProductId");

            b.Navigation("NegativeAdjustmentReason");

            b.Navigation("StockProduct");
        });

        modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.StockTransaction", b =>
        {
            b.HasOne("Auditdata.InventoryService.Core.Entities.Product", "Product")
                .WithMany()
                .HasForeignKey("ProductId");

            b.HasOne("Auditdata.InventoryService.Core.Entities.Stock", "Stock")
                .WithMany()
                .HasForeignKey("StockId");

            b.Navigation("Product");

            b.Navigation("Stock");
        });

        modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.Transfer", b =>
        {
            b.HasOne("Auditdata.InventoryService.Core.Entities.Stock", "StockFrom")
                .WithMany()
                .HasForeignKey("StockFromId");

            b.HasOne("Auditdata.InventoryService.Core.Entities.Stock", "StockTo")
                .WithMany()
                .HasForeignKey("StockToId");

            b.Navigation("StockFrom");

            b.Navigation("StockTo");
        });

        modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.TransferItem", b =>
        {
            b.HasOne("Auditdata.InventoryService.Core.Entities.StockProduct", "StockProduct")
                .WithMany()
                .HasForeignKey("StockProductId");

            b.HasOne("Auditdata.InventoryService.Core.Entities.Transfer", "Transfer")
                .WithMany("TransferItems")
                .HasForeignKey("TransferId");

            b.Navigation("StockProduct");

            b.Navigation("Transfer");
        });

        modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.TransferTransaction", b =>
        {
            b.HasOne("Auditdata.InventoryService.Core.Entities.StockTransaction", "StockTransaction")
                .WithMany("TransferTransactions")
                .HasForeignKey("StockTransactionId");

            b.HasOne("Auditdata.InventoryService.Core.Entities.Transfer", "Transfer")
                .WithMany()
                .HasForeignKey("TransferId");

            b.Navigation("StockTransaction");

            b.Navigation("Transfer");
        });

        modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.Manufacturer", b =>
        {
            b.Navigation("ManufacturerProductTypes");
        });

        modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.Product", b =>
        {
            b.Navigation("NhsContractProducts");

            b.Navigation("ProductPathways");

            b.Navigation("StockProducts");
        });

        modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.StockProduct", b =>
        {
            b.Navigation("StockProductItems");
        });

        modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.StockTransaction", b =>
        {
            b.Navigation("TransferTransactions");
        });

        modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.Transfer", b =>
        {
            b.Navigation("TransferItems");
        });
#pragma warning restore 612, 618
    }
}
