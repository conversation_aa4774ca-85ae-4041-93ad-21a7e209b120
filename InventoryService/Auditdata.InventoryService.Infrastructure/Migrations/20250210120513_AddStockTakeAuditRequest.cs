using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Auditdata.InventoryService.Infrastructure.Migrations;

/// <inheritdoc />
public partial class AddStockTakeAuditRequest : Migration
{
    /// <inheritdoc />
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.AddUniqueConstraint(
            name: "AK_Stocks_LocationId",
            table: "Stocks",
            column: "LocationId");

        migrationBuilder.CreateTable(
            name: "AuditRequests",
            columns: table => new
            {
                Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false, defaultValueSql: "NEWSEQUENTIALID()"),
                RequestNumber = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true),
                Status = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                DateTimeDue = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                Notes = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true),
                BlindStockCalculation = table.Column<bool>(type: "bit", nullable: false),
                IncludeOnTrialProducts = table.Column<bool>(type: "bit", nullable: false),
                AddAllProductsFromCatalog = table.Column<bool>(type: "bit", nullable: false),
                IndicateSerialNumber = table.Column<bool>(type: "bit", nullable: false),
                TenantId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                IsDeleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false),
                Version = table.Column<byte[]>(type: "rowversion", rowVersion: true, nullable: false),
                CreationDate = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                ChangeDate = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                CreatedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false, defaultValue: "admin"),
                ChangedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true)
            },
            constraints: table =>
            {
                table.PrimaryKey("PK_AuditRequests", x => x.Id);
            });

        migrationBuilder.CreateTable(
            name: "AuditLocations",
            columns: table => new
            {
                Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false, defaultValueSql: "NEWSEQUENTIALID()"),
                AuditRequestId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                LocationId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                TenantId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                Version = table.Column<byte[]>(type: "rowversion", rowVersion: true, nullable: false),
                CreationDate = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                ChangeDate = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                CreatedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false, defaultValue: "admin"),
                ChangedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true)
            },
            constraints: table =>
            {
                table.PrimaryKey("PK_AuditLocations", x => x.Id);
                table.ForeignKey(
                    name: "FK_AuditLocations_AuditRequests_AuditRequestId",
                    column: x => x.AuditRequestId,
                    principalTable: "AuditRequests",
                    principalColumn: "Id",
                    onDelete: ReferentialAction.Cascade);
                table.ForeignKey(
                    name: "FK_AuditLocations_Stocks_LocationId",
                    column: x => x.LocationId,
                    principalTable: "Stocks",
                    principalColumn: "LocationId",
                    onDelete: ReferentialAction.Cascade);
            });

        migrationBuilder.CreateTable(
            name: "AuditProducts",
            columns: table => new
            {
                Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false, defaultValueSql: "NEWSEQUENTIALID()"),
                AuditRequestId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                ProductId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                ExpectedQuantity = table.Column<int>(type: "int", nullable: false),
                CountedQuantity = table.Column<int>(type: "int", nullable: false),
                Comments = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true),
                IsDeleted = table.Column<bool>(type: "bit", nullable: false),
                TenantId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                Version = table.Column<byte[]>(type: "rowversion", rowVersion: true, nullable: false),
                CreationDate = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                ChangeDate = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                CreatedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false, defaultValue: "admin"),
                ChangedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true)
            },
            constraints: table =>
            {
                table.PrimaryKey("PK_AuditProducts", x => x.Id);
                table.ForeignKey(
                    name: "FK_AuditProducts_AuditRequests_AuditRequestId",
                    column: x => x.AuditRequestId,
                    principalTable: "AuditRequests",
                    principalColumn: "Id",
                    onDelete: ReferentialAction.Cascade);
                table.ForeignKey(
                    name: "FK_AuditProducts_Products_ProductId",
                    column: x => x.ProductId,
                    principalTable: "Products",
                    principalColumn: "Id",
                    onDelete: ReferentialAction.Restrict);
            });

        migrationBuilder.CreateTable(
            name: "AuditProductSerialNumbers",
            columns: table => new
            {
                Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false, defaultValueSql: "NEWSEQUENTIALID()"),
                AuditProductId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                SerialNumber = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                Status = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                TenantId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                Version = table.Column<byte[]>(type: "rowversion", rowVersion: true, nullable: false),
                CreationDate = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                ChangeDate = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                CreatedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false, defaultValue: "admin"),
                ChangedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true)
            },
            constraints: table =>
            {
                table.PrimaryKey("PK_AuditProductSerialNumbers", x => x.Id);
                table.ForeignKey(
                    name: "FK_AuditProductSerialNumbers_AuditProducts_AuditProductId",
                    column: x => x.AuditProductId,
                    principalTable: "AuditProducts",
                    principalColumn: "Id",
                    onDelete: ReferentialAction.Cascade);
            });

        migrationBuilder.CreateIndex(
            name: "IX_AuditLocations_AuditRequestId",
            table: "AuditLocations",
            column: "AuditRequestId");

        migrationBuilder.CreateIndex(
            name: "IX_AuditLocations_LocationId",
            table: "AuditLocations",
            column: "LocationId");

        migrationBuilder.CreateIndex(
            name: "IX_AuditProducts_AuditRequestId",
            table: "AuditProducts",
            column: "AuditRequestId");

        migrationBuilder.CreateIndex(
            name: "IX_AuditProducts_ProductId",
            table: "AuditProducts",
            column: "ProductId");

        migrationBuilder.CreateIndex(
            name: "IX_AuditProductSerialNumbers_AuditProductId",
            table: "AuditProductSerialNumbers",
            column: "AuditProductId");
    }

    /// <inheritdoc />
    protected override void Down(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.DropTable(
            name: "AuditLocations");

        migrationBuilder.DropTable(
            name: "AuditProductSerialNumbers");

        migrationBuilder.DropTable(
            name: "AuditProducts");

        migrationBuilder.DropTable(
            name: "AuditRequests");

        migrationBuilder.DropUniqueConstraint(
            name: "AK_Stocks_LocationId",
            table: "Stocks");
    }
}
