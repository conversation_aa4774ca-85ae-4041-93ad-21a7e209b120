using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Auditdata.InventoryService.Infrastructure.Migrations;

public partial class StockProductSerialNumberUnique : Migration
{
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.AlterColumn<string>(
            name: "SerialNumber",
            table: "StockProducts",
            type: "nvarchar(450)",
            nullable: true,
            oldClrType: typeof(string),
            oldType: "nvarchar(max)",
            oldNullable: true);

        migrationBuilder.CreateIndex(
            name: "IX_StockProducts_SerialNumber",
            table: "StockProducts",
            column: "SerialNumber",
            unique: true,
            filter: "[SerialNumber] IS NOT NULL");
    }

    protected override void Down(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.DropIndex(
            name: "IX_StockProducts_SerialNumber",
            table: "StockProducts");

        migrationBuilder.AlterColumn<string>(
            name: "SerialN<PERSON><PERSON>",
            table: "StockProducts",
            type: "nvarchar(max)",
            nullable: true,
            oldClrType: typeof(string),
            oldType: "nvarchar(450)",
            oldNullable: true);
    }
}
