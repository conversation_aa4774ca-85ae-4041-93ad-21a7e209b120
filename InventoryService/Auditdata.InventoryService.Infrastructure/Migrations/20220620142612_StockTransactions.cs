using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Auditdata.InventoryService.Infrastructure.Migrations;

public partial class StockTransactions : Migration
{
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.AddColumn<Guid>(
            name: "WarehouseId",
            table: "Stocks",
            type: "uniqueidentifier",
            nullable: true);

        migrationBuilder.CreateTable(
            name: "StockTransactions",
            columns: table => new
            {
                Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                CreatedOn = table.Column<DateTime>(type: "datetime2", nullable: false),
                ModifiedOn = table.Column<DateTime>(type: "datetime2", nullable: true),
                StockId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                ProductId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                TransactionId = table.Column<string>(type: "nvarchar(max)", nullable: false),
                TypeId = table.Column<int>(type: "int", nullable: false),
                Amount = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                Quantity = table.Column<int>(type: "int", nullable: false),
                TotalQuantity = table.Column<int>(type: "int", nullable: false),
                TenantId = table.Column<Guid>(type: "uniqueidentifier", nullable: false)
            },
            constraints: table =>
            {
                table.PrimaryKey("PK_StockTransactions", x => x.Id);
                table.ForeignKey(
                    name: "FK_StockTransactions_Products_ProductId",
                    column: x => x.ProductId,
                    principalTable: "Products",
                    principalColumn: "Id");
                table.ForeignKey(
                    name: "FK_StockTransactions_Stocks_StockId",
                    column: x => x.StockId,
                    principalTable: "Stocks",
                    principalColumn: "Id");
            });

        migrationBuilder.CreateTable(
            name: "Warehouses",
            columns: table => new
            {
                Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                TenantId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                Name = table.Column<string>(type: "nvarchar(max)", nullable: false)
            },
            constraints: table =>
            {
                table.PrimaryKey("PK_Warehouses", x => x.Id);
            });

        migrationBuilder.CreateIndex(
            name: "IX_Stocks_WarehouseId",
            table: "Stocks",
            column: "WarehouseId");

        migrationBuilder.CreateIndex(
            name: "IX_StockTransactions_ProductId",
            table: "StockTransactions",
            column: "ProductId");

        migrationBuilder.CreateIndex(
            name: "IX_StockTransactions_StockId",
            table: "StockTransactions",
            column: "StockId");

        migrationBuilder.AddForeignKey(
            name: "FK_Stocks_Warehouses_WarehouseId",
            table: "Stocks",
            column: "WarehouseId",
            principalTable: "Warehouses",
            principalColumn: "Id");
    }

    protected override void Down(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.DropForeignKey(
            name: "FK_Stocks_Warehouses_WarehouseId",
            table: "Stocks");

        migrationBuilder.DropTable(
            name: "StockTransactions");

        migrationBuilder.DropTable(
            name: "Warehouses");

        migrationBuilder.DropIndex(
            name: "IX_Stocks_WarehouseId",
            table: "Stocks");

        migrationBuilder.DropColumn(
            name: "WarehouseId",
            table: "Stocks");
    }
}
