// <auto-generated />
using System;
using Auditdata.InventoryService.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace Auditdata.InventoryService.Infrastructure.Migrations
{
    [DbContext(typeof(InventoryDbContext))]
    [Migration("20220706094911_ChangeTransferTransaction")]
    partial class ChangeTransferTransaction
    {
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "6.0.6")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder, 1L, 1);

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.BatteryType", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("BatteryTypes");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.HearingAidType", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("HearingAidTypes");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.Manufacturer", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("Manufacturers");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.ManufacturerProductType", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<Guid?>("ManufacturerId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("ProductTypeId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("ManufacturerId");

                    b.HasIndex("ProductTypeId");

                    b.ToTable("ManufacturerProductTypes");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.Product", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("BatteryTypeId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Code")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("HearingAidTypeId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<bool>("IsNHS")
                        .HasColumnType("bit");

                    b.Property<bool>("IsSellable")
                        .HasColumnType("bit");

                    b.Property<bool>("IsSerialized")
                        .HasColumnType("bit");

                    b.Property<Guid?>("ManufacturerId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("ModifiedOn")
                        .HasColumnType("datetime2");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Quantity")
                        .HasColumnType("int");

                    b.Property<decimal>("RetailPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("TypeId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Warranty")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("BatteryTypeId");

                    b.HasIndex("HearingAidTypeId");

                    b.HasIndex("ManufacturerId");

                    b.HasIndex("TypeId");

                    b.ToTable("Products");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.ProductType", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("ProductTypes");

                    b.HasData(
                        new
                        {
                            Id = new Guid("08300ca2-6a6a-4a25-ad16-3818e9550847"),
                            IsDeleted = false,
                            Name = "Hearing aids"
                        },
                        new
                        {
                            Id = new Guid("30931946-6b02-4d5b-b645-77dcd545ff8c"),
                            IsDeleted = false,
                            Name = "Hearing aids accessories"
                        },
                        new
                        {
                            Id = new Guid("a3e47357-481e-421a-ba04-d6f1946d9e69"),
                            IsDeleted = false,
                            Name = "Hearing aid batteries"
                        },
                        new
                        {
                            Id = new Guid("47acc31b-22af-48dd-aca7-09e3b4f1127c"),
                            IsDeleted = false,
                            Name = "Consumables"
                        },
                        new
                        {
                            Id = new Guid("b045b8b4-0b41-418f-b2d6-0724ea8177b7"),
                            IsDeleted = false,
                            Name = "Ear moulds"
                        },
                        new
                        {
                            Id = new Guid("9c26d46a-3b38-4d34-b723-387e1deaa952"),
                            IsDeleted = false,
                            Name = "Services"
                        });
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.Stock", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.ToTable("Stocks");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.StockProduct", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<bool>("IsSellable")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("ModifiedOn")
                        .HasColumnType("datetime2");

                    b.Property<decimal>("Price")
                        .HasColumnType("decimal(18,2)");

                    b.Property<Guid?>("ProductId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("Quantity")
                        .HasColumnType("int");

                    b.Property<Guid?>("StockId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("ProductId");

                    b.HasIndex("StockId");

                    b.ToTable("StockProducts");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.StockProductItem", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("ModifiedOn")
                        .HasColumnType("datetime2");

                    b.Property<string>("SerialNumber")
                        .HasColumnType("nvarchar(450)");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<Guid?>("StockProductId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("SerialNumber")
                        .IsUnique()
                        .HasFilter("[SerialNumber] IS NOT NULL");

                    b.HasIndex("StockProductId");

                    b.ToTable("StockProductItems");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.StockTransaction", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<decimal>("Amount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("ModifiedOn")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("ProductId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("Quantity")
                        .HasColumnType("int");

                    b.Property<Guid?>("StockId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("TotalQuantity")
                        .HasColumnType("int");

                    b.Property<string>("TransactionId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("TypeId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("ProductId");

                    b.HasIndex("StockId");

                    b.ToTable("StockTransactions");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.Transfer", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("Date")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("ModifiedOn")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("StockFromId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("StockToId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("StockFromId");

                    b.HasIndex("StockToId");

                    b.ToTable("Transfers");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.TransferItem", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("ModifiedOn")
                        .HasColumnType("datetime2");

                    b.Property<int>("Quantity")
                        .HasColumnType("int");

                    b.Property<string>("SerialNumbers")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("StockProductId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("TransferId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("StockProductId");

                    b.HasIndex("TransferId");

                    b.ToTable("TransferItems");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.TransferTransaction", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Comments")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("datetime2");

                    b.Property<int>("Direction")
                        .HasColumnType("int");

                    b.Property<DateTime?>("ModifiedOn")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("StockTransactionId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("TransferId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("StockTransactionId");

                    b.HasIndex("TransferId");

                    b.ToTable("TransferTransactions");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.ManufacturerProductType", b =>
                {
                    b.HasOne("Auditdata.InventoryService.Core.Entities.Manufacturer", "Manufacturer")
                        .WithMany("ManufacturerProductTypes")
                        .HasForeignKey("ManufacturerId");

                    b.HasOne("Auditdata.InventoryService.Core.Entities.ProductType", "ProductType")
                        .WithMany()
                        .HasForeignKey("ProductTypeId");

                    b.Navigation("Manufacturer");

                    b.Navigation("ProductType");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.Product", b =>
                {
                    b.HasOne("Auditdata.InventoryService.Core.Entities.BatteryType", "BatteryType")
                        .WithMany()
                        .HasForeignKey("BatteryTypeId");

                    b.HasOne("Auditdata.InventoryService.Core.Entities.HearingAidType", "HearingAidType")
                        .WithMany()
                        .HasForeignKey("HearingAidTypeId");

                    b.HasOne("Auditdata.InventoryService.Core.Entities.Manufacturer", "Manufacturer")
                        .WithMany()
                        .HasForeignKey("ManufacturerId");

                    b.HasOne("Auditdata.InventoryService.Core.Entities.ProductType", "Type")
                        .WithMany()
                        .HasForeignKey("TypeId");

                    b.Navigation("BatteryType");

                    b.Navigation("HearingAidType");

                    b.Navigation("Manufacturer");

                    b.Navigation("Type");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.StockProduct", b =>
                {
                    b.HasOne("Auditdata.InventoryService.Core.Entities.Product", "Product")
                        .WithMany()
                        .HasForeignKey("ProductId");

                    b.HasOne("Auditdata.InventoryService.Core.Entities.Stock", "Stock")
                        .WithMany()
                        .HasForeignKey("StockId");

                    b.Navigation("Product");

                    b.Navigation("Stock");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.StockProductItem", b =>
                {
                    b.HasOne("Auditdata.InventoryService.Core.Entities.StockProduct", "StockProduct")
                        .WithMany("StockProductItems")
                        .HasForeignKey("StockProductId");

                    b.Navigation("StockProduct");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.StockTransaction", b =>
                {
                    b.HasOne("Auditdata.InventoryService.Core.Entities.Product", "Product")
                        .WithMany()
                        .HasForeignKey("ProductId");

                    b.HasOne("Auditdata.InventoryService.Core.Entities.Stock", "Stock")
                        .WithMany()
                        .HasForeignKey("StockId");

                    b.Navigation("Product");

                    b.Navigation("Stock");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.Transfer", b =>
                {
                    b.HasOne("Auditdata.InventoryService.Core.Entities.Stock", "StockFrom")
                        .WithMany()
                        .HasForeignKey("StockFromId");

                    b.HasOne("Auditdata.InventoryService.Core.Entities.Stock", "StockTo")
                        .WithMany()
                        .HasForeignKey("StockToId");

                    b.Navigation("StockFrom");

                    b.Navigation("StockTo");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.TransferItem", b =>
                {
                    b.HasOne("Auditdata.InventoryService.Core.Entities.StockProduct", "StockProduct")
                        .WithMany()
                        .HasForeignKey("StockProductId");

                    b.HasOne("Auditdata.InventoryService.Core.Entities.Transfer", "Transfer")
                        .WithMany("TransferItems")
                        .HasForeignKey("TransferId");

                    b.Navigation("StockProduct");

                    b.Navigation("Transfer");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.TransferTransaction", b =>
                {
                    b.HasOne("Auditdata.InventoryService.Core.Entities.StockTransaction", "StockTransaction")
                        .WithMany()
                        .HasForeignKey("StockTransactionId");

                    b.HasOne("Auditdata.InventoryService.Core.Entities.Transfer", "Transfer")
                        .WithMany()
                        .HasForeignKey("TransferId");

                    b.Navigation("StockTransaction");

                    b.Navigation("Transfer");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.Manufacturer", b =>
                {
                    b.Navigation("ManufacturerProductTypes");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.StockProduct", b =>
                {
                    b.Navigation("StockProductItems");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.Transfer", b =>
                {
                    b.Navigation("TransferItems");
                });
#pragma warning restore 612, 618
        }
    }
}
