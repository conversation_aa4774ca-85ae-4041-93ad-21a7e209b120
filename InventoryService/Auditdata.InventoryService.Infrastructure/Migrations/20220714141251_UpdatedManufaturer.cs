using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Auditdata.InventoryService.Infrastructure.Migrations;

public partial class UpdatedManufaturer : Migration
{
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.AddColumn<DateTime>(
            name: "CreatedOn",
            table: "Manufacturers",
            type: "datetime2",
            nullable: false,
            defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

        migrationBuilder.AddColumn<bool>(
            name: "IsActive",
            table: "Manufacturers",
            type: "bit",
            nullable: false,
            defaultValue: false);

        migrationBuilder.AddColumn<DateTime>(
            name: "ModifiedOn",
            table: "Manufacturers",
            type: "datetime2",
            nullable: true);

        migrationBuilder.AddColumn<Guid>(
            name: "TenantId",
            table: "Manufacturers",
            type: "uniqueidentifier",
            nullable: false,
            defaultValue: new Guid("00000000-0000-0000-0000-000000000000"));
    }

    protected override void Down(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.DropColumn(
            name: "CreatedOn",
            table: "Manufacturers");

        migrationBuilder.DropColumn(
            name: "IsActive",
            table: "Manufacturers");

        migrationBuilder.DropColumn(
            name: "ModifiedOn",
            table: "Manufacturers");

        migrationBuilder.DropColumn(
            name: "TenantId",
            table: "Manufacturers");
    }
}
