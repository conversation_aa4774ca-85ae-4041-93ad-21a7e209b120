using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Auditdata.InventoryService.Infrastructure.Migrations;

public partial class RemoveIsActiveIsSellableFromStockProduct : Migration
{
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.DropColumn(
            name: "IsActive",
            table: "StockProducts");

        migrationBuilder.DropColumn(
            name: "IsSellable",
            table: "StockProducts");
    }

    protected override void Down(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.AddColumn<bool>(
            name: "IsActive",
            table: "StockProducts",
            type: "bit",
            nullable: false,
            defaultValue: false);

        migrationBuilder.AddColumn<bool>(
            name: "IsSellable",
            table: "StockProducts",
            type: "bit",
            nullable: false,
            defaultValue: false);
    }
}
