using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Auditdata.InventoryService.Infrastructure.Migrations;

/// <inheritdoc />
public partial class Add_Unique_Constrains : Migration
{
    /// <inheritdoc />
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.CreateIndex(
            name: "IX_ProductCategories_TenantId_Name_IsDeleted",
            table: "ProductCategories",
            columns: new[] { "TenantId", "Name", "IsDeleted" },
            unique: true);

        migrationBuilder.CreateIndex(
            name: "IX_HearingAidTypes_TenantId_Name_IsDeleted",
            table: "HearingAidTypes",
            columns: new[] { "TenantId", "Name", "IsDeleted" },
            unique: true);

        migrationBuilder.CreateIndex(
            name: "IX_Countries_TenantId_Name_IsDeleted",
            table: "Countries",
            columns: new[] { "TenantId", "Name", "IsDeleted" },
            unique: true);

        migrationBuilder.CreateIndex(
            name: "IX_Colors_TenantId_Name_IsDeleted",
            table: "Colors",
            columns: new[] { "TenantId", "Name", "IsDeleted" },
            unique: true);

        migrationBuilder.CreateIndex(
            name: "IX_BatteryTypes_TenantId_Name_IsDeleted",
            table: "BatteryTypes",
            columns: new[] { "TenantId", "Name", "IsDeleted" },
            unique: true);
    }

    /// <inheritdoc />
    protected override void Down(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.DropIndex(
            name: "IX_ProductCategories_TenantId_Name_IsDeleted",
            table: "ProductCategories");

        migrationBuilder.DropIndex(
            name: "IX_HearingAidTypes_TenantId_Name_IsDeleted",
            table: "HearingAidTypes");

        migrationBuilder.DropIndex(
            name: "IX_Countries_TenantId_Name_IsDeleted",
            table: "Countries");

        migrationBuilder.DropIndex(
            name: "IX_Colors_TenantId_Name_IsDeleted",
            table: "Colors");

        migrationBuilder.DropIndex(
            name: "IX_BatteryTypes_TenantId_Name_IsDeleted",
            table: "BatteryTypes");
    }
}
