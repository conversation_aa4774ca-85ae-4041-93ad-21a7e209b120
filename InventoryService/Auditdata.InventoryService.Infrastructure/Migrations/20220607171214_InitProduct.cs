using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Auditdata.InventoryService.Infrastructure.Migrations;

public partial class InitProduct : Migration
{
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.CreateTable(
            name: "Manufacturers",
            columns: table => new
            {
                Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                IsDeleted = table.Column<bool>(type: "bit", nullable: false),
                Name = table.Column<string>(type: "nvarchar(max)", nullable: false)
            },
            constraints: table =>
            {
                table.PrimaryKey("PK_Manufacturers", x => x.Id);
            });

        migrationBuilder.CreateTable(
            name: "ProductTypes",
            columns: table => new
            {
                Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                IsDeleted = table.Column<bool>(type: "bit", nullable: false),
                Name = table.Column<string>(type: "nvarchar(max)", nullable: false)
            },
            constraints: table =>
            {
                table.PrimaryKey("PK_ProductTypes", x => x.Id);
            });

        migrationBuilder.CreateTable(
            name: "Products",
            columns: table => new
            {
                Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                Name = table.Column<string>(type: "nvarchar(max)", nullable: false),
                Description = table.Column<string>(type: "nvarchar(max)", nullable: true),
                TypeId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                ManufacturerId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                Warranty = table.Column<string>(type: "nvarchar(max)", nullable: true),
                Quantity = table.Column<int>(type: "int", nullable: false),
                RetailPrice = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                IsSellable = table.Column<bool>(type: "bit", nullable: false),
                IsActive = table.Column<bool>(type: "bit", nullable: false),
                IsNHS = table.Column<bool>(type: "bit", nullable: false),
                IsSerialized = table.Column<bool>(type: "bit", nullable: false),
                CreatedOn = table.Column<DateTime>(type: "datetime2", nullable: false),
                ModifiedOn = table.Column<DateTime>(type: "datetime2", nullable: true),
                IsDeleted = table.Column<bool>(type: "bit", nullable: false),
                TenantId = table.Column<Guid>(type: "uniqueidentifier", nullable: false)
            },
            constraints: table =>
            {
                table.PrimaryKey("PK_Products", x => x.Id);
                table.ForeignKey(
                    name: "FK_Products_Manufacturers_ManufacturerId",
                    column: x => x.ManufacturerId,
                    principalTable: "Manufacturers",
                    principalColumn: "Id");
                table.ForeignKey(
                    name: "FK_Products_ProductTypes_TypeId",
                    column: x => x.TypeId,
                    principalTable: "ProductTypes",
                    principalColumn: "Id");
            });

        migrationBuilder.CreateIndex(
            name: "IX_Products_ManufacturerId",
            table: "Products",
            column: "ManufacturerId");

        migrationBuilder.CreateIndex(
            name: "IX_Products_TypeId",
            table: "Products",
            column: "TypeId");
    }

    protected override void Down(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.DropTable(
            name: "Products");

        migrationBuilder.DropTable(
            name: "Manufacturers");

        migrationBuilder.DropTable(
            name: "ProductTypes");
    }
}
