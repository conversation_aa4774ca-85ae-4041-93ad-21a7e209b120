using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Auditdata.InventoryService.Infrastructure.Migrations;

/// <inheritdoc />
public partial class ManufacturerSupplierNameUnique : Migration
{
    /// <inheritdoc />
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.CreateIndex(
            name: "IX_Suppliers_Name_TenantId_IsDeleted",
            table: "Suppliers",
            columns: new[] { "Name", "TenantId", "IsDeleted" },
            unique: true,
            filter: "IsDeleted = 0");

        migrationBuilder.CreateIndex(
            name: "IX_Manufacturers_Name_TenantId_IsDeleted",
            table: "Manufacturers",
            columns: new[] { "Name", "TenantId", "IsDeleted" },
            unique: true,
            filter: "IsDeleted = 0");
    }

    /// <inheritdoc />
    protected override void Down(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.DropIndex(
            name: "IX_Suppliers_Name_TenantId_IsDeleted",
            table: "Suppliers");

        migrationBuilder.DropIndex(
            name: "IX_Manufacturers_Name_TenantId_IsDeleted",
            table: "Manufacturers");
    }
}
