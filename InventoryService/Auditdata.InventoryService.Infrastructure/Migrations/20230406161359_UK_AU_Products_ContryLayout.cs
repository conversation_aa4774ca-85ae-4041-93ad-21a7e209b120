using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Auditdata.InventoryService.Infrastructure.Migrations;

/// <inheritdoc />
public partial class UK_AU_Products_ContryLayout : Migration
{
    /// <inheritdoc />
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.AlterColumn<bool>(
            name: "IsNHS",
            table: "Products",
            type: "bit",
            nullable: true,
            oldClrType: typeof(bool),
            oldType: "bit");

        migrationBuilder.AddColumn<string>(
            name: "Discriminator",
            table: "Products",
            type: "nvarchar(max)",
            nullable: false,
            defaultValue: "");

        migrationBuilder.AddColumn<string>(
            name: "HspCategory",
            table: "Products",
            type: "nvarchar(max)",
            nullable: true);

        migrationBuilder.AddColumn<decimal>(
            name: "HspClientPrice",
            table: "Products",
            type: "decimal(18,2)",
            nullable: true);

        migrationBuilder.AddColumn<string>(
            name: "HspCode",
            table: "Products",
            type: "nvarchar(max)",
            nullable: true);

        migrationBuilder.AddColumn<string>(
            name: "HspServiceNumber",
            table: "Products",
            type: "nvarchar(max)",
            nullable: true);

        migrationBuilder.AddColumn<int>(
            name: "HspServiceType",
            table: "Products",
            type: "int",
            nullable: true);

        migrationBuilder.AddColumn<bool>(
            name: "HspTopUp",
            table: "Products",
            type: "bit",
            nullable: true);

        migrationBuilder.AddColumn<bool>(
            name: "IsHSP",
            table: "Products",
            type: "bit",
            nullable: true);

        migrationBuilder.Sql("UPDATE Products SET Discriminator = 'UKProduct' where Discriminator = ''");
    }

    /// <inheritdoc />
    protected override void Down(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.Sql("UPDATE Products SET Discriminator = '' where Discriminator = 'UKProduct'");
        
        migrationBuilder.DropColumn(
            name: "Discriminator",
            table: "Products");

        migrationBuilder.DropColumn(
            name: "HspCategory",
            table: "Products");

        migrationBuilder.DropColumn(
            name: "HspClientPrice",
            table: "Products");

        migrationBuilder.DropColumn(
            name: "HspCode",
            table: "Products");

        migrationBuilder.DropColumn(
            name: "HspServiceNumber",
            table: "Products");

        migrationBuilder.DropColumn(
            name: "HspServiceType",
            table: "Products");

        migrationBuilder.DropColumn(
            name: "HspTopUp",
            table: "Products");

        migrationBuilder.DropColumn(
            name: "IsHSP",
            table: "Products");

        migrationBuilder.AlterColumn<bool>(
            name: "IsNHS",
            table: "Products",
            type: "bit",
            nullable: false,
            defaultValue: false,
            oldClrType: typeof(bool),
            oldType: "bit",
            oldNullable: true);
    }
}
