using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Auditdata.InventoryService.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class AddWasPlacedOnStock : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "WasPlacedOnStock",
                table: "StockProducts",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.Sql("EXEC('UPDATE StockProducts SET WasPlacedOnStock=1 WHERE Quantity > 0')");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "WasPlacedOnStock",
                table: "StockProducts");
        }
    }
}
