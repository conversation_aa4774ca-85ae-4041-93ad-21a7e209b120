// <auto-generated />
using System;
using Auditdata.InventoryService.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace Auditdata.InventoryService.Infrastructure.Migrations
{
    [DbContext(typeof(InventoryDbContext))]
    [Migration("20231108141650_DeleteCustomAuditableFields")]
    partial class DeleteCustomAuditableFields
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "7.0.11")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.Attribute", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTimeOffset?>("ChangeDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("ChangedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Code")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("nvarchar(max)")
                        .HasDefaultValue("admin");

                    b.Property<DateTimeOffset>("CreationDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ValueType")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("Attributes");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.BatteryType", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTimeOffset?>("ChangeDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("ChangedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("nvarchar(max)")
                        .HasDefaultValue("admin");

                    b.Property<DateTimeOffset>("CreationDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("Name", "TenantId", "IsDeleted")
                        .IsUnique()
                        .HasFilter("IsDeleted = 0");

                    b.ToTable("BatteryTypes");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.Bundle", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTimeOffset?>("ChangeDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("ChangedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasDefaultValue("admin");

                    b.Property<DateTimeOffset>("CreationDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<bool>("IsAutomaticDelivery")
                        .HasColumnType("bit");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<bool>("IsPriceChangeAllowed")
                        .HasColumnType("bit");

                    b.Property<bool>("IsSellable")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("nvarchar(250)");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<decimal>("TotalCost")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.HasKey("Id");

                    b.ToTable("Bundles");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.BundleProduct", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("BundleId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTimeOffset?>("ChangeDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("ChangedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasDefaultValue("admin");

                    b.Property<DateTimeOffset>("CreationDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<decimal>("Price")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<Guid>("ProductId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("Quantity")
                        .HasColumnType("int");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("BundleId");

                    b.HasIndex("ProductId");

                    b.ToTable("BundleProducts");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.CPTCode", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTimeOffset?>("ChangeDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("ChangedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("nvarchar(max)")
                        .HasDefaultValue("admin");

                    b.Property<DateTimeOffset>("CreationDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("Code", "TenantId", "IsDeleted")
                        .IsUnique()
                        .HasFilter("IsDeleted = 0");

                    b.ToTable("CPTCodes");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.Color", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTimeOffset?>("ChangeDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("ChangedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("nvarchar(max)")
                        .HasDefaultValue("admin");

                    b.Property<DateTimeOffset>("CreationDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("Name", "TenantId", "IsDeleted")
                        .IsUnique()
                        .HasFilter("IsDeleted = 0");

                    b.ToTable("Colors");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.Country", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTimeOffset?>("ChangeDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("ChangedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("nvarchar(max)")
                        .HasDefaultValue("admin");

                    b.Property<DateTimeOffset>("CreationDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("TenantId", "Name", "IsDeleted")
                        .IsUnique()
                        .HasFilter("IsDeleted = 0");

                    b.ToTable("Countries");

                    b.HasData(
                        new
                        {
                            Id = new Guid("2033baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Afghanistan",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("2133baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Albania",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("2233baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Algeria",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("2333baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Argentina",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("2433baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Armenia",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("2533baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Australia",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("2633baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Austria",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("2733baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Azerbaijan",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("2833baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Bahrain",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("2933baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Bangladesh",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("2a33baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Belarus",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("2b33baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Belgium",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("2c33baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Belize",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("2d33baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Bhutan",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("2e33baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Bolivia",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("2f33baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Bosnia & Herzegovina",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("3033baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Botswana",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("3133baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Brazil",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("3233baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Brunei",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("3333baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Bulgaria",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("3433baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Cambodia",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("3533baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Cameroon",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("3633baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Canada",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("3733baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Caribbean",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("3833baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Chile",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("3933baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "China",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("3a33baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Colombia",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("3b33baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Congo (DRC)",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("3c33baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Costa Rica",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("3d33baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Côte d’Ivoire",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("3e33baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Croatia",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("3f33baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Cuba",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("4033baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Czechia",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("4133baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Denmark",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("4233baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Dominican Republic",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("4333baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Ecuador",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("565a9699-066e-45cf-b960-411ac3d4ec20"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "England",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("4433baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Egypt",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("4533baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "El Salvador",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("4633baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Eritrea",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("4733baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Estonia",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("4833baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Ethiopia",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("4933baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Faroe Islands",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("4a33baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Finland",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("4b33baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "France",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("4c33baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Georgia",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("4d33baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Germany",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("4e33baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Greece",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("4f33baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Greenland",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("5033baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Guatemala",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("5133baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Haiti",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("5233baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Honduras",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("5333baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Hong Kong SAR",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("5433baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Hungary",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("5533baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Iceland",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("5633baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "India",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("5733baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Indonesia",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("5833baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Iran",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("5933baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Iraq",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("5a33baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Ireland",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("5b33baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Israel",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("5c33baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Italy",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("5d33baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Jamaica",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("5e33baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Japan",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("5f33baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Jordan",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("6033baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Kazakhstan",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("6133baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Kenya",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("6233baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Korea",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("6333baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Kuwait",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("6433baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Kyrgyzstan",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("6533baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Laos",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("6633baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Latin America",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("6733baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Latvia",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("6833baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Lebanon",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("6933baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Libya",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("6a33baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Liechtenstein",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("6b33baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Lithuania",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("6c33baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Luxembourg",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("6d33baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Malaysia",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("6e33baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Maldives",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("6f33baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Mali",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("7033baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Malta",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("7133baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Mexico",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("7233baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Moldova",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("7333baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Monaco",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("7433baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Mongolia",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("7533baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Montenegro",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("7633baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Morocco",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("7733baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Myanmar",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("7833baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Nepal",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("7933baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Netherlands",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("7a33baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "New Zealand",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("7b33baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Nicaragua",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("7c33baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Nigeria",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("7d33baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "North Macedonia",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("7e33baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Norway",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("b27ac0b5-a6a3-4af2-8a78-0b3d11215aca"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Northern Ireland",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("7f33baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Oman",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("8033baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Pakistan",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("8133baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Panama",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("8233baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Paraguay",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("8333baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Peru",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("8433baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Philippines",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("8533baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Poland",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("8633baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Portugal",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("8733baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Puerto Rico",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("8833baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Qatar",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("8933baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Réunion",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("8a33baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Romania",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("8b33baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Russia",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("8c33baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Rwanda",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("8d33baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Saudi Arabia",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("9e395310-8e3c-4284-9a4b-21824d9e34b3"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Scotland",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("8e33baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Senegal",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("8f33baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Serbia",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("9033baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Singapore",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("9133baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Slovakia",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("9233baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Slovenia",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("9333baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Somalia",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("9433baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "South Africa",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("9533baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Spain",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("9633baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Sri Lanka",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("9733baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Sweden",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("9833baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Switzerland",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("9933baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Syria",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("9a33baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Thailand",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("9b33baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Trinidad & Tobago",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("9c33baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Tunisia",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("9d33baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Turkey",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("9e33baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Turkmenistan",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("9f33baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Ukraine",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("a033baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "United Arab Emirates",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("a133baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "United Kingdom",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("a233baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "United States",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("a333baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Uruguay",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("a433baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Uzbekistan",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("a533baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Venezuela",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("a633baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Vietnam",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("3401508a-33f4-4fbd-8899-5acda30dcbb9"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Wales",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("a733baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "World",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("a833baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Yemen",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("a933baa3-2b72-ed11-9f60-00224899f97e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Zimbabwe",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        });
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.ExternalProduct", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Category")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset?>("ChangeDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("ChangedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("nvarchar(max)")
                        .HasDefaultValue("admin");

                    b.Property<DateTimeOffset>("CreationDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("ExternalId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("HearingAidType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("ImportId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Manufacturer")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("ModifiedAt")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("ImportId");

                    b.ToTable("ExternalProducts");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.ExternalProductImport", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTimeOffset?>("ChangeDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("ChangedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("nvarchar(max)")
                        .HasDefaultValue("admin");

                    b.Property<DateTimeOffset>("CreationDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("FileContent")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Source")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset?>("StartedAt")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("State")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTimeOffset?>("UploadedAt")
                        .HasColumnType("datetimeoffset");

                    b.HasKey("Id");

                    b.ToTable("ExternalProductImports");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.ExternalProductOption", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTimeOffset?>("ChangeDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("ChangedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("nvarchar(max)")
                        .HasDefaultValue("admin");

                    b.Property<DateTimeOffset>("CreationDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("ExternalId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("ExternalProductId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTimeOffset>("ModifiedAt")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("ExternalProductId");

                    b.ToTable("ExternalProductOptions");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.HearingAidType", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTimeOffset?>("ChangeDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("ChangedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("nvarchar(max)")
                        .HasDefaultValue("admin");

                    b.Property<DateTimeOffset>("CreationDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("TenantId", "Name", "IsDeleted")
                        .IsUnique();

                    b.ToTable("HearingAidTypes");

                    b.HasData(
                        new
                        {
                            Id = new Guid("bdf916f7-cfb6-4016-bd05-0d1c2e2c5b10"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "BTE",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("8bb7392c-fce0-4522-97c8-1d246b136d01"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "RIC",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("eadf48c0-7f9e-4158-90e5-aa976904cae0"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "CIC",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("8267c588-abf9-436a-8160-a989b34c3a19"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "ITE",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("9c3d9f64-88a7-44c1-9138-056bea29cf44"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Other",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        });
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.Manufacturer", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Address1")
                        .HasMaxLength(4000)
                        .HasColumnType("nvarchar(4000)");

                    b.Property<string>("Address2")
                        .HasMaxLength(4000)
                        .HasColumnType("nvarchar(4000)");

                    b.Property<DateTimeOffset?>("ChangeDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("ChangedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("City")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid?>("CountryId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("nvarchar(max)")
                        .HasDefaultValue("admin");

                    b.Property<DateTimeOffset>("CreationDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("EmailAddress")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("FaxNumber")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("PhoneNumber")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("PostalCode")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("State")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Website")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.HasKey("Id");

                    b.HasIndex("CountryId");

                    b.ToTable("Manufacturers");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.NhsContractProduct", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTimeOffset?>("ChangeDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("ChangedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("ContractId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("nvarchar(max)")
                        .HasDefaultValue("admin");

                    b.Property<DateTimeOffset>("CreationDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<Guid?>("ProductId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("ProductId");

                    b.ToTable("NhsContractProducts");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.Pathway", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTimeOffset?>("ChangeDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("ChangedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("nvarchar(max)")
                        .HasDefaultValue("admin");

                    b.Property<DateTimeOffset>("CreationDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("Pathways");

                    b.HasData(
                        new
                        {
                            Id = new Guid("4e7301d0-88c5-4f33-962d-ed06b4e64e4d"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Wax Removal",
                            Type = "wax_removal"
                        },
                        new
                        {
                            Id = new Guid("a8a63efd-3763-41f7-a568-b887b4341e24"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "HA Fitting",
                            Type = "ha_fitting"
                        },
                        new
                        {
                            Id = new Guid("9d363ec1-d09a-4380-93af-e0324e9d028d"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Recall",
                            Type = "recall"
                        },
                        new
                        {
                            Id = new Guid("345201f6-4599-4871-bb28-2e44834f8a30"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Aftercare",
                            Type = "aftercare"
                        },
                        new
                        {
                            Id = new Guid("98dc257c-ec38-49c8-b658-5bb4afc71ce8"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Remote Aftercare",
                            Type = "remote_aftercare"
                        },
                        new
                        {
                            Id = new Guid("6b7d87cc-4f5b-4b00-8d55-6da6d5a8c9b3"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Hearing Check",
                            Type = "hearing_check"
                        },
                        new
                        {
                            Id = new Guid("b3943d8b-80c9-4822-9998-a79a3a42f239"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Private Test",
                            Type = "private_test"
                        },
                        new
                        {
                            Id = new Guid("19de7aa8-e174-47f8-83a8-953bbc15594e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "NHS Assess & Fit",
                            Type = "nhs_assess_and_fit"
                        },
                        new
                        {
                            Id = new Guid("5a674586-39fc-4ba3-9fd8-c3a2ca17c80e"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Private Test & HA Consult",
                            Type = "private_test_ha_consult"
                        },
                        new
                        {
                            Id = new Guid("440e198b-1089-4c28-99e5-cdcf79332407"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Private HA Consultation",
                            Type = "private_ha_consultation"
                        },
                        new
                        {
                            Id = new Guid("3e0a70b0-61e0-434d-a258-709583ea0f2b"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Wax Triage",
                            Type = "wax_triage"
                        },
                        new
                        {
                            Id = new Guid("0b9909d1-b2bb-4efc-8d24-fd6dcc994013"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Follow Up",
                            Type = "follow_up"
                        },
                        new
                        {
                            Id = new Guid("88c28659-77dc-4bd5-a2d3-31aa671e6210"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Remote Follow Up",
                            Type = "remote_follow_up"
                        },
                        new
                        {
                            Id = new Guid("ac95bdfd-f8a8-4d1f-89e8-7b4aa06e9165"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Re-Assessment",
                            Type = "re_assessment"
                        },
                        new
                        {
                            Id = new Guid("efff08a8-c1e2-4199-9488-5405506fc9ed"),
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Hearing Protection",
                            Type = "hearing_protection"
                        });
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.ProductAttribute", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("AttributeId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTimeOffset?>("ChangeDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("ChangedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("nvarchar(max)")
                        .HasDefaultValue("admin");

                    b.Property<DateTimeOffset>("CreationDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<Guid>("ProductId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("AttributeId");

                    b.HasIndex("ProductId");

                    b.ToTable("ProductAttributes");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.ProductBatteryType", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("BatteryTypeId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTimeOffset?>("ChangeDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("ChangedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("nvarchar(max)")
                        .HasDefaultValue("admin");

                    b.Property<DateTimeOffset>("CreationDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<Guid>("ProductId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("BatteryTypeId");

                    b.HasIndex("ProductId");

                    b.ToTable("ProductBatteryTypes");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.ProductCPTCode", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("CPTCodeId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTimeOffset?>("ChangeDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("ChangedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("nvarchar(max)")
                        .HasDefaultValue("admin");

                    b.Property<DateTimeOffset>("CreationDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<Guid>("ProductId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("CPTCodeId");

                    b.HasIndex("ProductId", "CPTCodeId", "TenantId")
                        .IsUnique();

                    b.ToTable("ProductCPTCodes");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.ProductCategory", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTimeOffset?>("ChangeDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("ChangedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("nvarchar(max)")
                        .HasDefaultValue("admin");

                    b.Property<DateTimeOffset>("CreationDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("TenantId", "Name", "IsDeleted")
                        .IsUnique();

                    b.ToTable("ProductCategories");

                    b.HasData(
                        new
                        {
                            Id = new Guid("08300ca2-6a6a-4a25-ad16-3818e9550847"),
                            Code = "Batteries",
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Batteries",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("30931946-6b02-4d5b-b645-77dcd545ff8c"),
                            Code = "Service",
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Service",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("a3e47357-481e-421a-ba04-d6f1946d9e69"),
                            Code = "RepairService",
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Repair Service",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("47acc31b-22af-48dd-aca7-09e3b4f1127c"),
                            Code = "Earmolds",
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Earmolds",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("b045b8b4-0b41-418f-b2d6-0724ea8177b7"),
                            Code = "HearingAids",
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Hearing Aids",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("9c26d46a-3b38-4d34-b723-387e1deaa952"),
                            Code = "Accessories",
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Accessories",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("f2ae794b-734c-4c41-ad84-a84f54000167"),
                            Code = "Remote",
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Remote",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("b9ab271e-5c4c-4f2b-88f2-80bd07484813"),
                            Code = "LDWarranty",
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "L&D Warranty",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        },
                        new
                        {
                            Id = new Guid("a9dc025b-75fd-4b30-b1d3-ca58fb7fb270"),
                            Code = "Other",
                            CreatedBy = "admin",
                            CreationDate = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            IsDeleted = false,
                            Name = "Other",
                            TenantId = new Guid("********-0000-0000-0000-********0000")
                        });
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.ProductCategoryAccountCode", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("AccountCode")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<DateTimeOffset?>("ChangeDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("ChangedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("nvarchar(max)")
                        .HasDefaultValue("admin");

                    b.Property<DateTimeOffset>("CreationDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<Guid>("ProductCategoryId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("ProductCategoryId")
                        .IsUnique();

                    b.HasIndex("TenantId", "ProductCategoryId")
                        .IsUnique();

                    b.ToTable("ProductCategoryAccountCodes");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.ProductColor", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTimeOffset?>("ChangeDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("ChangedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("ColorId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("nvarchar(max)")
                        .HasDefaultValue("admin");

                    b.Property<DateTimeOffset>("CreationDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<Guid>("ProductId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("ColorId");

                    b.HasIndex("ProductId");

                    b.ToTable("ProductColors");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.ProductPathway", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTimeOffset?>("ChangeDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("ChangedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("nvarchar(max)")
                        .HasDefaultValue("admin");

                    b.Property<DateTimeOffset>("CreationDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<Guid?>("PathwayId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("ProductId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("ProductId");

                    b.ToTable("ProductPathways");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.Products.Product", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("AutoDeliver")
                        .HasColumnType("bit");

                    b.Property<Guid?>("BatteryTypeId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("CategoryId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTimeOffset?>("ChangeDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("ChangedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Code")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<bool>("ControlledByStock")
                        .HasColumnType("bit");

                    b.Property<decimal?>("Cost")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("nvarchar(max)")
                        .HasDefaultValue("admin");

                    b.Property<DateTimeOffset>("CreationDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("Description")
                        .HasMaxLength(4000)
                        .HasColumnType("nvarchar(4000)");

                    b.Property<string>("Discriminator")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ExternalId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal>("FirstVAT")
                        .HasPrecision(18, 6)
                        .HasColumnType("decimal(18,6)");

                    b.Property<Guid?>("HearingAidTypeId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<bool>("IsSellable")
                        .HasColumnType("bit");

                    b.Property<bool>("IsSerialized")
                        .HasColumnType("bit");

                    b.Property<int?>("LDWarranty")
                        .HasColumnType("int");

                    b.Property<Guid?>("ManufacturerId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<decimal?>("MaximumDiscount")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<bool>("PriceChangesAllowed")
                        .HasColumnType("bit");

                    b.Property<int>("Quantity")
                        .HasColumnType("int");

                    b.Property<decimal>("RetailPrice")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("SecondVAT")
                        .HasPrecision(18, 6)
                        .HasColumnType("decimal(18,6)");

                    b.Property<Guid?>("SupplierId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("VendorProductNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("Warranty")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("CategoryId");

                    b.HasIndex("HearingAidTypeId");

                    b.HasIndex("ManufacturerId");

                    b.HasIndex("SupplierId");

                    b.ToTable("Products");

                    b.HasDiscriminator<string>("Discriminator").HasValue("Product");

                    b.UseTphMappingStrategy();
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.Stock", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTimeOffset?>("ChangeDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("ChangedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("nvarchar(max)")
                        .HasDefaultValue("admin");

                    b.Property<DateTimeOffset>("CreationDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<Guid?>("LocationId")
                        .IsRequired()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("LocationId")
                        .IsUnique();

                    b.ToTable("Stocks");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.StockAdjustmentReason", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTimeOffset?>("ChangeDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("ChangedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("nvarchar(max)")
                        .HasDefaultValue("admin");

                    b.Property<DateTimeOffset>("CreationDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.ToTable("StockAdjustmentReasons");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.StockProduct", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTimeOffset?>("ChangeDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("ChangedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("nvarchar(max)")
                        .HasDefaultValue("admin");

                    b.Property<DateTimeOffset>("CreationDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<decimal>("Price")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<Guid?>("ProductId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("Quantity")
                        .HasColumnType("int");

                    b.Property<Guid?>("StockId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("StockId");

                    b.HasIndex("ProductId", "StockId", "TenantId")
                        .IsUnique()
                        .HasFilter("[IsDeleted] = 0");

                    b.ToTable("StockProducts");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.StockProductItem", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTimeOffset?>("ChangeDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("ChangedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("nvarchar(max)")
                        .HasDefaultValue("admin");

                    b.Property<DateTimeOffset>("CreationDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<Guid?>("NegativeAdjustmentReasonId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("OrderId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("Quantity")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(1);

                    b.Property<Guid?>("RepairOrderId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("SaleId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("SerialNumber")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<Guid?>("StockProductId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("TransferId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<byte[]>("Version")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.HasKey("Id");

                    b.HasIndex("NegativeAdjustmentReasonId");

                    b.HasIndex("StockProductId");

                    b.ToTable("StockProductItems");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.StockProductItemLog", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("NEWID()");

                    b.Property<DateTimeOffset?>("ChangeDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("ChangedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("nvarchar(max)")
                        .HasDefaultValue("admin");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("datetime2");

                    b.Property<DateTimeOffset>("CreationDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("Description")
                        .HasMaxLength(2000)
                        .HasColumnType("nvarchar(2000)");

                    b.Property<Guid?>("StockId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("StockProductItemId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("User")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.HasKey("Id");

                    b.HasIndex("StockProductItemId");

                    b.ToTable("StockProductItemLogs");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.StockTransaction", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTimeOffset?>("ChangeDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("ChangedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("nvarchar(max)")
                        .HasDefaultValue("admin");

                    b.Property<DateTimeOffset>("CreationDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<Guid?>("ProductId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("Quantity")
                        .HasColumnType("int");

                    b.Property<Guid?>("SaleId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("StockId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("TotalQuantity")
                        .HasColumnType("int");

                    b.Property<string>("TransactionId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("TypeId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("ProductId");

                    b.HasIndex("StockId");

                    b.ToTable("StockTransactions");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.Supplier", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Address1")
                        .HasMaxLength(4000)
                        .HasColumnType("nvarchar(4000)");

                    b.Property<string>("Address2")
                        .HasMaxLength(4000)
                        .HasColumnType("nvarchar(4000)");

                    b.Property<DateTimeOffset?>("ChangeDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("ChangedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("City")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid?>("CountryId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("nvarchar(max)")
                        .HasDefaultValue("admin");

                    b.Property<DateTimeOffset>("CreationDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("EmailAddress")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("FaxNumber")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<bool>("IsManufacturer")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("PhoneNumber")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("PostalCode")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("State")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Website")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.HasKey("Id");

                    b.HasIndex("CountryId");

                    b.ToTable("Suppliers");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.TransfersState", b =>
                {
                    b.Property<Guid>("TransferId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("AcceptedAt")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("CancelledAt")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("CorrelationId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CurrentState")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<Guid>("FromStockId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("RequestedAt")
                        .HasColumnType("datetime2");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.Property<Guid>("StockProductItemId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("ToStockId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("TransferId");

                    b.HasIndex("StockProductItemId");

                    b.ToTable("Transfers");
                });

            modelBuilder.Entity("MassTransit.EntityFrameworkCoreIntegration.InboxState", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<DateTime?>("Consumed")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("ConsumerId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("Delivered")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("ExpirationTime")
                        .HasColumnType("datetime2");

                    b.Property<long?>("LastSequenceNumber")
                        .HasColumnType("bigint");

                    b.Property<Guid>("LockId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("MessageId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("ReceiveCount")
                        .HasColumnType("int");

                    b.Property<DateTime>("Received")
                        .HasColumnType("datetime2");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.HasKey("Id");

                    b.HasAlternateKey("MessageId", "ConsumerId");

                    b.HasIndex("Delivered");

                    b.ToTable("InboxState");
                });

            modelBuilder.Entity("MassTransit.EntityFrameworkCoreIntegration.OutboxMessage", b =>
                {
                    b.Property<long>("SequenceNumber")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("SequenceNumber"));

                    b.Property<string>("Body")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ContentType")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<Guid?>("ConversationId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("CorrelationId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("DestinationAddress")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime?>("EnqueueTime")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("ExpirationTime")
                        .HasColumnType("datetime2");

                    b.Property<string>("FaultAddress")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("Headers")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("InboxConsumerId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("InboxMessageId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("InitiatorId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("MessageId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("MessageType")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("OutboxId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Properties")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("RequestId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ResponseAddress")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("SentTime")
                        .HasColumnType("datetime2");

                    b.Property<string>("SourceAddress")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.HasKey("SequenceNumber");

                    b.HasIndex("EnqueueTime");

                    b.HasIndex("ExpirationTime");

                    b.HasIndex("OutboxId", "SequenceNumber")
                        .IsUnique()
                        .HasFilter("[OutboxId] IS NOT NULL");

                    b.HasIndex("InboxMessageId", "InboxConsumerId", "SequenceNumber")
                        .IsUnique()
                        .HasFilter("[InboxMessageId] IS NOT NULL AND [InboxConsumerId] IS NOT NULL");

                    b.ToTable("OutboxMessage");
                });

            modelBuilder.Entity("MassTransit.EntityFrameworkCoreIntegration.OutboxState", b =>
                {
                    b.Property<Guid>("OutboxId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("Created")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("Delivered")
                        .HasColumnType("datetime2");

                    b.Property<long?>("LastSequenceNumber")
                        .HasColumnType("bigint");

                    b.Property<Guid>("LockId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.HasKey("OutboxId");

                    b.HasIndex("Created");

                    b.ToTable("OutboxState");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.NZProduct", b =>
                {
                    b.HasBaseType("Auditdata.InventoryService.Core.Entities.Products.Product");

                    b.HasDiscriminator().HasValue("NZProduct");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.Products.AUProduct", b =>
                {
                    b.HasBaseType("Auditdata.InventoryService.Core.Entities.Products.Product");

                    b.Property<string>("HspCategory")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("HspClientPrice")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("HspCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool?>("HspMaintenance")
                        .HasColumnType("bit");

                    b.Property<string>("HspServiceNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("HspServiceType")
                        .HasColumnType("int");

                    b.Property<bool?>("HspTopUp")
                        .HasColumnType("bit");

                    b.Property<bool>("IsHSP")
                        .HasColumnType("bit");

                    b.HasDiscriminator().HasValue("AUProduct");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.Products.ROIProduct", b =>
                {
                    b.HasBaseType("Auditdata.InventoryService.Core.Entities.Products.Product");

                    b.HasDiscriminator().HasValue("ROIProduct");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.Products.UKProduct", b =>
                {
                    b.HasBaseType("Auditdata.InventoryService.Core.Entities.Products.Product");

                    b.Property<bool>("IsNHS")
                        .HasColumnType("bit");

                    b.Property<decimal?>("NHSVAT")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<Guid?>("NhsServiceTariffId")
                        .HasColumnType("uniqueidentifier");

                    b.HasDiscriminator().HasValue("UKProduct");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.Products.USProduct", b =>
                {
                    b.HasBaseType("Auditdata.InventoryService.Core.Entities.Products.Product");

                    b.HasDiscriminator().HasValue("USProduct");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.Attribute", b =>
                {
                    b.OwnsOne("Auditdata.InventoryService.Core.Entities.AttributeSelectValues", "Values", b1 =>
                        {
                            b1.Property<Guid>("AttributeId")
                                .HasColumnType("uniqueidentifier");

                            b1.HasKey("AttributeId");

                            b1.ToTable("Attributes");

                            b1.ToJson("Values");

                            b1.WithOwner()
                                .HasForeignKey("AttributeId");

                            b1.OwnsMany("Auditdata.InventoryService.Core.Entities.AttributeSelectValue", "Values", b2 =>
                                {
                                    b2.Property<Guid>("AttributeSelectValuesAttributeId")
                                        .HasColumnType("uniqueidentifier");

                                    b2.Property<int>("Id")
                                        .ValueGeneratedOnAdd()
                                        .HasColumnType("int");

                                    b2.Property<Guid>("ValueId")
                                        .HasColumnType("uniqueidentifier");

                                    b2.Property<string>("ValueName")
                                        .IsRequired()
                                        .HasColumnType("nvarchar(max)");

                                    b2.HasKey("AttributeSelectValuesAttributeId", "Id");

                                    b2.ToTable("Attributes");

                                    b2.WithOwner()
                                        .HasForeignKey("AttributeSelectValuesAttributeId");
                                });

                            b1.Navigation("Values");
                        });

                    b.Navigation("Values");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.BundleProduct", b =>
                {
                    b.HasOne("Auditdata.InventoryService.Core.Entities.Bundle", "Bundle")
                        .WithMany("Products")
                        .HasForeignKey("BundleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Auditdata.InventoryService.Core.Entities.Products.Product", "Product")
                        .WithMany("Bundles")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Bundle");

                    b.Navigation("Product");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.ExternalProduct", b =>
                {
                    b.HasOne("Auditdata.InventoryService.Core.Entities.ExternalProductImport", "Import")
                        .WithMany()
                        .HasForeignKey("ImportId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Import");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.ExternalProductOption", b =>
                {
                    b.HasOne("Auditdata.InventoryService.Core.Entities.ExternalProduct", "ExternalProduct")
                        .WithMany("ExternalProductOptions")
                        .HasForeignKey("ExternalProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.OwnsOne("Auditdata.InventoryService.Core.Entities.ExternalProductOptionStyles", "Styles", b1 =>
                        {
                            b1.Property<Guid>("ExternalProductOptionId")
                                .HasColumnType("uniqueidentifier");

                            b1.Property<string>("BatteryType")
                                .HasColumnType("nvarchar(max)");

                            b1.Property<string>("Color")
                                .HasColumnType("nvarchar(max)");

                            b1.HasKey("ExternalProductOptionId");

                            b1.ToTable("ExternalProductOptions");

                            b1.ToJson("Styles");

                            b1.WithOwner()
                                .HasForeignKey("ExternalProductOptionId");

                            b1.OwnsMany("Auditdata.InventoryService.Core.Entities.ExternalProductOptionStyle", "Attributes", b2 =>
                                {
                                    b2.Property<Guid>("ExternalProductOptionStylesExternalProductOptionId")
                                        .HasColumnType("uniqueidentifier");

                                    b2.Property<int>("Id")
                                        .ValueGeneratedOnAdd()
                                        .HasColumnType("int");

                                    b2.Property<string>("Caption")
                                        .HasColumnType("nvarchar(max)");

                                    b2.Property<string>("Code")
                                        .IsRequired()
                                        .HasColumnType("nvarchar(max)");

                                    b2.Property<string>("Value")
                                        .IsRequired()
                                        .HasColumnType("nvarchar(max)");

                                    b2.HasKey("ExternalProductOptionStylesExternalProductOptionId", "Id");

                                    b2.ToTable("ExternalProductOptions");

                                    b2.WithOwner()
                                        .HasForeignKey("ExternalProductOptionStylesExternalProductOptionId");
                                });

                            b1.Navigation("Attributes");
                        });

                    b.Navigation("ExternalProduct");

                    b.Navigation("Styles");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.Manufacturer", b =>
                {
                    b.HasOne("Auditdata.InventoryService.Core.Entities.Country", "Country")
                        .WithMany()
                        .HasForeignKey("CountryId");

                    b.OwnsOne("Auditdata.InventoryService.Core.Entities.ManufacturerContact", "AccountReceivableContact", b1 =>
                        {
                            b1.Property<Guid>("ManufacturerId")
                                .HasColumnType("uniqueidentifier");

                            b1.Property<string>("EmailAddress")
                                .HasColumnType("nvarchar(max)");

                            b1.Property<string>("Extension")
                                .HasColumnType("nvarchar(max)");

                            b1.Property<string>("Name")
                                .HasColumnType("nvarchar(max)");

                            b1.Property<string>("PhoneNumber")
                                .HasColumnType("nvarchar(max)");

                            b1.HasKey("ManufacturerId");

                            b1.ToTable("Manufacturers");

                            b1.ToJson("AccountReceivableContact");

                            b1.WithOwner()
                                .HasForeignKey("ManufacturerId");
                        });

                    b.OwnsOne("Auditdata.InventoryService.Core.Entities.ManufacturerContact", "SalesContact", b1 =>
                        {
                            b1.Property<Guid>("ManufacturerId")
                                .HasColumnType("uniqueidentifier");

                            b1.Property<string>("EmailAddress")
                                .HasColumnType("nvarchar(max)");

                            b1.Property<string>("Extension")
                                .HasColumnType("nvarchar(max)");

                            b1.Property<string>("Name")
                                .HasColumnType("nvarchar(max)");

                            b1.Property<string>("PhoneNumber")
                                .HasColumnType("nvarchar(max)");

                            b1.HasKey("ManufacturerId");

                            b1.ToTable("Manufacturers");

                            b1.ToJson("SalesContact");

                            b1.WithOwner()
                                .HasForeignKey("ManufacturerId");
                        });

                    b.Navigation("AccountReceivableContact");

                    b.Navigation("Country");

                    b.Navigation("SalesContact");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.NhsContractProduct", b =>
                {
                    b.HasOne("Auditdata.InventoryService.Core.Entities.Products.Product", "Product")
                        .WithMany("NhsContractProducts")
                        .HasForeignKey("ProductId");

                    b.Navigation("Product");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.ProductAttribute", b =>
                {
                    b.HasOne("Auditdata.InventoryService.Core.Entities.Attribute", "Attribute")
                        .WithMany()
                        .HasForeignKey("AttributeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Auditdata.InventoryService.Core.Entities.Products.Product", "Product")
                        .WithMany("Attributes")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.OwnsOne("Auditdata.InventoryService.Core.Entities.AttributeValue", "Value", b1 =>
                        {
                            b1.Property<Guid>("ProductAttributeId")
                                .HasColumnType("uniqueidentifier");

                            b1.Property<string>("Value")
                                .HasColumnType("nvarchar(max)");

                            b1.HasKey("ProductAttributeId");

                            b1.ToTable("ProductAttributes");

                            b1.ToJson("Value");

                            b1.WithOwner()
                                .HasForeignKey("ProductAttributeId");

                            b1.OwnsMany("Auditdata.InventoryService.Core.Entities.AttributeSelectValue", "SelectValues", b2 =>
                                {
                                    b2.Property<Guid>("AttributeValueProductAttributeId")
                                        .HasColumnType("uniqueidentifier");

                                    b2.Property<int>("Id")
                                        .ValueGeneratedOnAdd()
                                        .HasColumnType("int");

                                    b2.Property<Guid>("ValueId")
                                        .HasColumnType("uniqueidentifier");

                                    b2.Property<string>("ValueName")
                                        .IsRequired()
                                        .HasColumnType("nvarchar(max)");

                                    b2.HasKey("AttributeValueProductAttributeId", "Id");

                                    b2.ToTable("ProductAttributes");

                                    b2.WithOwner()
                                        .HasForeignKey("AttributeValueProductAttributeId");
                                });

                            b1.Navigation("SelectValues");
                        });

                    b.Navigation("Attribute");

                    b.Navigation("Product");

                    b.Navigation("Value");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.ProductBatteryType", b =>
                {
                    b.HasOne("Auditdata.InventoryService.Core.Entities.BatteryType", "BatteryType")
                        .WithMany()
                        .HasForeignKey("BatteryTypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Auditdata.InventoryService.Core.Entities.Products.Product", "Product")
                        .WithMany("BatteryTypes")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("BatteryType");

                    b.Navigation("Product");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.ProductCPTCode", b =>
                {
                    b.HasOne("Auditdata.InventoryService.Core.Entities.CPTCode", "CPTCode")
                        .WithMany()
                        .HasForeignKey("CPTCodeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Auditdata.InventoryService.Core.Entities.Products.USProduct", "Product")
                        .WithMany("CPTCodes")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("CPTCode");

                    b.Navigation("Product");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.ProductCategoryAccountCode", b =>
                {
                    b.HasOne("Auditdata.InventoryService.Core.Entities.ProductCategory", "ProductCategory")
                        .WithOne()
                        .HasForeignKey("Auditdata.InventoryService.Core.Entities.ProductCategoryAccountCode", "ProductCategoryId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ProductCategory");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.ProductColor", b =>
                {
                    b.HasOne("Auditdata.InventoryService.Core.Entities.Color", "Color")
                        .WithMany()
                        .HasForeignKey("ColorId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Auditdata.InventoryService.Core.Entities.Products.Product", "Product")
                        .WithMany("Colors")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Color");

                    b.Navigation("Product");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.ProductPathway", b =>
                {
                    b.HasOne("Auditdata.InventoryService.Core.Entities.Products.Product", "Product")
                        .WithMany("ProductPathways")
                        .HasForeignKey("ProductId");

                    b.Navigation("Product");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.Products.Product", b =>
                {
                    b.HasOne("Auditdata.InventoryService.Core.Entities.ProductCategory", "Category")
                        .WithMany()
                        .HasForeignKey("CategoryId");

                    b.HasOne("Auditdata.InventoryService.Core.Entities.HearingAidType", "HearingAidType")
                        .WithMany()
                        .HasForeignKey("HearingAidTypeId");

                    b.HasOne("Auditdata.InventoryService.Core.Entities.Manufacturer", "Manufacturer")
                        .WithMany()
                        .HasForeignKey("ManufacturerId");

                    b.HasOne("Auditdata.InventoryService.Core.Entities.Supplier", "Supplier")
                        .WithMany()
                        .HasForeignKey("SupplierId");

                    b.Navigation("Category");

                    b.Navigation("HearingAidType");

                    b.Navigation("Manufacturer");

                    b.Navigation("Supplier");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.StockProduct", b =>
                {
                    b.HasOne("Auditdata.InventoryService.Core.Entities.Products.Product", "Product")
                        .WithMany("StockProducts")
                        .HasForeignKey("ProductId");

                    b.HasOne("Auditdata.InventoryService.Core.Entities.Stock", "Stock")
                        .WithMany()
                        .HasForeignKey("StockId");

                    b.Navigation("Product");

                    b.Navigation("Stock");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.StockProductItem", b =>
                {
                    b.HasOne("Auditdata.InventoryService.Core.Entities.StockAdjustmentReason", "NegativeAdjustmentReason")
                        .WithMany()
                        .HasForeignKey("NegativeAdjustmentReasonId");

                    b.HasOne("Auditdata.InventoryService.Core.Entities.StockProduct", "StockProduct")
                        .WithMany("StockProductItems")
                        .HasForeignKey("StockProductId");

                    b.Navigation("NegativeAdjustmentReason");

                    b.Navigation("StockProduct");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.StockProductItemLog", b =>
                {
                    b.HasOne("Auditdata.InventoryService.Core.Entities.StockProductItem", "StockProductItem")
                        .WithMany()
                        .HasForeignKey("StockProductItemId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.OwnsOne("Auditdata.InventoryService.Core.Features.StockProductItemLogs.Models.TransactionHistoryData", "Data", b1 =>
                        {
                            b1.Property<Guid>("StockProductItemLogId")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("uniqueidentifier");

                            b1.Property<string>("Key")
                                .HasColumnType("nvarchar(max)");

                            b1.HasKey("StockProductItemLogId");

                            b1.ToTable("StockProductItemLogs");

                            b1.ToJson("Data");

                            b1.WithOwner()
                                .HasForeignKey("StockProductItemLogId");

                            b1.OwnsMany("Auditdata.InventoryService.Core.Features.StockProductItemLogs.Models.TransactionHistoryDataParam", "Params", b2 =>
                                {
                                    b2.Property<Guid>("TransactionHistoryDataStockProductItemLogId")
                                        .HasColumnType("uniqueidentifier");

                                    b2.Property<int>("Id")
                                        .ValueGeneratedOnAdd()
                                        .HasColumnType("int");

                                    b2.Property<string>("Param")
                                        .IsRequired()
                                        .HasColumnType("nvarchar(max)");

                                    b2.HasKey("TransactionHistoryDataStockProductItemLogId", "Id");

                                    b2.ToTable("StockProductItemLogs");

                                    b2.WithOwner()
                                        .HasForeignKey("TransactionHistoryDataStockProductItemLogId");
                                });

                            b1.Navigation("Params");
                        });

                    b.Navigation("Data");

                    b.Navigation("StockProductItem");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.StockTransaction", b =>
                {
                    b.HasOne("Auditdata.InventoryService.Core.Entities.Products.Product", "Product")
                        .WithMany()
                        .HasForeignKey("ProductId");

                    b.HasOne("Auditdata.InventoryService.Core.Entities.Stock", "Stock")
                        .WithMany()
                        .HasForeignKey("StockId");

                    b.Navigation("Product");

                    b.Navigation("Stock");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.Supplier", b =>
                {
                    b.HasOne("Auditdata.InventoryService.Core.Entities.Country", "Country")
                        .WithMany()
                        .HasForeignKey("CountryId");

                    b.OwnsOne("Auditdata.InventoryService.Core.Entities.SupplierContact", "AccountReceivableContact", b1 =>
                        {
                            b1.Property<Guid>("SupplierId")
                                .HasColumnType("uniqueidentifier");

                            b1.Property<string>("EmailAddress")
                                .HasColumnType("nvarchar(max)");

                            b1.Property<string>("Extension")
                                .HasColumnType("nvarchar(max)");

                            b1.Property<string>("Name")
                                .HasColumnType("nvarchar(max)");

                            b1.Property<string>("PhoneNumber")
                                .HasColumnType("nvarchar(max)");

                            b1.HasKey("SupplierId");

                            b1.ToTable("Suppliers");

                            b1.ToJson("AccountReceivableContact");

                            b1.WithOwner()
                                .HasForeignKey("SupplierId");
                        });

                    b.OwnsOne("Auditdata.InventoryService.Core.Entities.SupplierContact", "SalesContact", b1 =>
                        {
                            b1.Property<Guid>("SupplierId")
                                .HasColumnType("uniqueidentifier");

                            b1.Property<string>("EmailAddress")
                                .HasColumnType("nvarchar(max)");

                            b1.Property<string>("Extension")
                                .HasColumnType("nvarchar(max)");

                            b1.Property<string>("Name")
                                .HasColumnType("nvarchar(max)");

                            b1.Property<string>("PhoneNumber")
                                .HasColumnType("nvarchar(max)");

                            b1.HasKey("SupplierId");

                            b1.ToTable("Suppliers");

                            b1.ToJson("SalesContact");

                            b1.WithOwner()
                                .HasForeignKey("SupplierId");
                        });

                    b.Navigation("AccountReceivableContact");

                    b.Navigation("Country");

                    b.Navigation("SalesContact");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.TransfersState", b =>
                {
                    b.HasOne("Auditdata.InventoryService.Core.Entities.StockProductItem", "StockProductItem")
                        .WithMany()
                        .HasForeignKey("StockProductItemId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("StockProductItem");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.Bundle", b =>
                {
                    b.Navigation("Products");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.ExternalProduct", b =>
                {
                    b.Navigation("ExternalProductOptions");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.Products.Product", b =>
                {
                    b.Navigation("Attributes");

                    b.Navigation("BatteryTypes");

                    b.Navigation("Bundles");

                    b.Navigation("Colors");

                    b.Navigation("NhsContractProducts");

                    b.Navigation("ProductPathways");

                    b.Navigation("StockProducts");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.StockProduct", b =>
                {
                    b.Navigation("StockProductItems");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.Products.USProduct", b =>
                {
                    b.Navigation("CPTCodes");
                });
#pragma warning restore 612, 618
        }
    }
}
