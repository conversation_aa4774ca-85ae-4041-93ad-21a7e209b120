using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Auditdata.InventoryService.Infrastructure.Migrations;

public partial class AddTransfersModels : Migration
{
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.CreateTable(
            name: "Transfers",
            columns: table => new
            {
                Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                StockFromId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                StockToId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                Date = table.Column<DateTime>(type: "datetime2", nullable: false),
                CreatedOn = table.Column<DateTime>(type: "datetime2", nullable: false),
                ModifiedOn = table.Column<DateTime>(type: "datetime2", nullable: true),
                TenantId = table.Column<Guid>(type: "uniqueidentifier", nullable: false)
            },
            constraints: table =>
            {
                table.PrimaryKey("PK_Transfers", x => x.Id);
                table.ForeignKey(
                    name: "FK_Transfers_Stocks_StockFromId",
                    column: x => x.Stock<PERSON>romId,
                    principalTable: "Stocks",
                    principalColumn: "Id");
                table.ForeignKey(
                    name: "FK_Transfers_Stocks_StockToId",
                    column: x => x.StockToId,
                    principalTable: "Stocks",
                    principalColumn: "Id");
            });

        migrationBuilder.CreateTable(
            name: "TransferItems",
            columns: table => new
            {
                Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                TransferId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                StockProductId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                Quantity = table.Column<int>(type: "int", nullable: false),
                SerialNumbers = table.Column<string>(type: "nvarchar(max)", nullable: true),
                CreatedOn = table.Column<DateTime>(type: "datetime2", nullable: false),
                ModifiedOn = table.Column<DateTime>(type: "datetime2", nullable: true),
                TenantId = table.Column<Guid>(type: "uniqueidentifier", nullable: false)
            },
            constraints: table =>
            {
                table.PrimaryKey("PK_TransferItems", x => x.Id);
                table.ForeignKey(
                    name: "FK_TransferItems_StockProducts_StockProductId",
                    column: x => x.StockProductId,
                    principalTable: "StockProducts",
                    principalColumn: "Id");
                table.ForeignKey(
                    name: "FK_TransferItems_Transfers_TransferId",
                    column: x => x.TransferId,
                    principalTable: "Transfers",
                    principalColumn: "Id");
            });

        migrationBuilder.CreateTable(
            name: "TransferTransactions",
            columns: table => new
            {
                Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                TransferId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                StockTransactionId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                Comments = table.Column<string>(type: "nvarchar(max)", nullable: true),
                TypeId = table.Column<int>(type: "int", nullable: false),
                CreatedOn = table.Column<DateTime>(type: "datetime2", nullable: false),
                ModifiedOn = table.Column<DateTime>(type: "datetime2", nullable: true),
                TenantId = table.Column<Guid>(type: "uniqueidentifier", nullable: false)
            },
            constraints: table =>
            {
                table.PrimaryKey("PK_TransferTransactions", x => x.Id);
                table.ForeignKey(
                    name: "FK_TransferTransactions_StockTransactions_StockTransactionId",
                    column: x => x.StockTransactionId,
                    principalTable: "StockTransactions",
                    principalColumn: "Id");
                table.ForeignKey(
                    name: "FK_TransferTransactions_Transfers_TransferId",
                    column: x => x.TransferId,
                    principalTable: "Transfers",
                    principalColumn: "Id");
            });

        migrationBuilder.CreateIndex(
            name: "IX_TransferItems_StockProductId",
            table: "TransferItems",
            column: "StockProductId");

        migrationBuilder.CreateIndex(
            name: "IX_TransferItems_TransferId",
            table: "TransferItems",
            column: "TransferId");

        migrationBuilder.CreateIndex(
            name: "IX_Transfers_StockFromId",
            table: "Transfers",
            column: "StockFromId");

        migrationBuilder.CreateIndex(
            name: "IX_Transfers_StockToId",
            table: "Transfers",
            column: "StockToId");

        migrationBuilder.CreateIndex(
            name: "IX_TransferTransactions_StockTransactionId",
            table: "TransferTransactions",
            column: "StockTransactionId");

        migrationBuilder.CreateIndex(
            name: "IX_TransferTransactions_TransferId",
            table: "TransferTransactions",
            column: "TransferId");
    }

    protected override void Down(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.DropTable(
            name: "TransferItems");

        migrationBuilder.DropTable(
            name: "TransferTransactions");

        migrationBuilder.DropTable(
            name: "Transfers");
    }
}
