using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Auditdata.InventoryService.Infrastructure.Migrations;

public partial class AddedBatteryType : Migration
{
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.AddColumn<Guid>(
            name: "BatteryTypeId",
            table: "Products",
            type: "uniqueidentifier",
            nullable: true);

        migrationBuilder.CreateTable(
            name: "BatteryTypes",
            columns: table => new
            {
                Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                IsDeleted = table.Column<bool>(type: "bit", nullable: false),
                Name = table.Column<string>(type: "nvarchar(max)", nullable: false)
            },
            constraints: table =>
            {
                table.PrimaryKey("PK_BatteryTypes", x => x.Id);
            });

        migrationBuilder.CreateIndex(
            name: "IX_Products_BatteryTypeId",
            table: "Products",
            column: "BatteryTypeId");

        migrationBuilder.AddForeignKey(
            name: "FK_Products_BatteryTypes_BatteryTypeId",
            table: "Products",
            column: "BatteryTypeId",
            principalTable: "BatteryTypes",
            principalColumn: "Id");
    }

    protected override void Down(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.DropForeignKey(
            name: "FK_Products_BatteryTypes_BatteryTypeId",
            table: "Products");

        migrationBuilder.DropTable(
            name: "BatteryTypes");

        migrationBuilder.DropIndex(
            name: "IX_Products_BatteryTypeId",
            table: "Products");

        migrationBuilder.DropColumn(
            name: "BatteryTypeId",
            table: "Products");
    }
}
