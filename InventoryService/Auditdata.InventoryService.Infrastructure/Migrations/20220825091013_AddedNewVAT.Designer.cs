// <auto-generated />
using System;
using Auditdata.InventoryService.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace Auditdata.InventoryService.Infrastructure.Migrations
{
    [DbContext(typeof(InventoryDbContext))]
    [Migration("20220825091013_AddedNewVAT")]
    partial class AddedNewVAT
    {
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "6.0.8")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder, 1L, 1);

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.BatteryType", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("BatteryTypes");

                    b.HasData(
                        new
                        {
                            Id = new Guid("975340bb-d5fd-41e2-9bab-f6f06109edc9"),
                            IsDeleted = false,
                            Name = "10"
                        },
                        new
                        {
                            Id = new Guid("ad270856-4d29-4e3e-b0bc-e9089d196fab"),
                            IsDeleted = false,
                            Name = "312"
                        },
                        new
                        {
                            Id = new Guid("c33221bc-e71a-4b85-b92d-8632358cd37d"),
                            IsDeleted = false,
                            Name = "13"
                        },
                        new
                        {
                            Id = new Guid("6b84bed9-740c-43e7-9855-fd8275231a43"),
                            IsDeleted = false,
                            Name = "675"
                        },
                        new
                        {
                            Id = new Guid("6e213806-80c7-40cf-9605-d58eaf9789ff"),
                            IsDeleted = false,
                            Name = "Rechargeable"
                        });
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.HearingAidType", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("HearingAidTypes");

                    b.HasData(
                        new
                        {
                            Id = new Guid("bdf916f7-cfb6-4016-bd05-0d1c2e2c5b10"),
                            IsDeleted = false,
                            Name = "BTE"
                        },
                        new
                        {
                            Id = new Guid("8bb7392c-fce0-4522-97c8-1d246b136d01"),
                            IsDeleted = false,
                            Name = "RIC"
                        },
                        new
                        {
                            Id = new Guid("eadf48c0-7f9e-4158-90e5-aa976904cae0"),
                            IsDeleted = false,
                            Name = "CIC"
                        },
                        new
                        {
                            Id = new Guid("8267c588-abf9-436a-8160-a989b34c3a19"),
                            IsDeleted = false,
                            Name = "ITE"
                        },
                        new
                        {
                            Id = new Guid("9c3d9f64-88a7-44c1-9138-056bea29cf44"),
                            IsDeleted = false,
                            Name = "Other"
                        });
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.Manufacturer", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("ModifiedOn")
                        .HasColumnType("datetime2");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.ToTable("Manufacturers");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.ManufacturerProductType", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<Guid?>("ManufacturerId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("ProductTypeId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("ManufacturerId");

                    b.HasIndex("ProductTypeId");

                    b.ToTable("ManufacturerProductTypes");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.NhsContractProduct", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("ContractId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("ProductId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("ProductId");

                    b.ToTable("NhsContractProducts");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.NhsServiceTariff", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("NhsServiceTariffs");

                    b.HasData(
                        new
                        {
                            Id = new Guid("5a2c1480-729b-4309-af5a-ffd04337a727"),
                            Name = "Assessment Only Tariff"
                        },
                        new
                        {
                            Id = new Guid("2a45a286-7fd4-445e-a87c-2c8a4d77d5a2"),
                            Name = "Monaural Tariff"
                        },
                        new
                        {
                            Id = new Guid("59efc888-e8ba-40e4-af5c-21cdb1201c15"),
                            Name = "Binaural Tariff"
                        },
                        new
                        {
                            Id = new Guid("1d735f43-c93a-476c-9dec-b842006ebcfe"),
                            Name = "Aftercare Tariff"
                        },
                        new
                        {
                            Id = new Guid("7a955d81-5e05-4fd4-95f8-b31f010e6d9b"),
                            Name = "Wax Removal Tariff"
                        },
                        new
                        {
                            Id = new Guid("bae28bd8-338f-4679-9000-807e227b7676"),
                            Name = "Replacement hearing aids tariff"
                        },
                        new
                        {
                            Id = new Guid("3c5fad9b-0d02-41f2-a37a-4aa18944777e"),
                            Name = "Use product price"
                        });
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.Pathway", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("Pathways");

                    b.HasData(
                        new
                        {
                            Id = new Guid("4e7301d0-88c5-4f33-962d-ed06b4e64e4d"),
                            IsDeleted = false,
                            Name = "Wax Removal"
                        },
                        new
                        {
                            Id = new Guid("a8a63efd-3763-41f7-a568-b887b4341e24"),
                            IsDeleted = false,
                            Name = "Test to Fit"
                        },
                        new
                        {
                            Id = new Guid("9d363ec1-d09a-4380-93af-e0324e9d028d"),
                            IsDeleted = false,
                            Name = "Rehab"
                        },
                        new
                        {
                            Id = new Guid("345201f6-4599-4871-bb28-2e44834f8a30"),
                            IsDeleted = false,
                            Name = "In Store Aftercare"
                        },
                        new
                        {
                            Id = new Guid("98dc257c-ec38-49c8-b658-5bb4afc71ce8"),
                            IsDeleted = false,
                            Name = "Telephone Aftercare"
                        });
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.Product", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("AutoDeliver")
                        .HasColumnType("bit");

                    b.Property<Guid?>("BatteryTypeId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Code")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal>("FirstVAT")
                        .HasColumnType("decimal(18,2)");

                    b.Property<Guid?>("HearingAidTypeId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<bool>("IsNHS")
                        .HasColumnType("bit");

                    b.Property<bool>("IsSellable")
                        .HasColumnType("bit");

                    b.Property<bool>("IsSerialized")
                        .HasColumnType("bit");

                    b.Property<Guid?>("ManufacturerId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("ModifiedOn")
                        .HasColumnType("datetime2");

                    b.Property<decimal?>("NHSVAT")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("NhsServiceTariffId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("PathwayId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("PriceChangesAllowed")
                        .HasColumnType("bit");

                    b.Property<int>("Quantity")
                        .HasColumnType("int");

                    b.Property<decimal>("RetailPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("SecondVAT")
                        .HasColumnType("decimal(18,2)");

                    b.Property<Guid?>("SupplierId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("TypeId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Warranty")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("BatteryTypeId");

                    b.HasIndex("HearingAidTypeId");

                    b.HasIndex("ManufacturerId");

                    b.HasIndex("NhsServiceTariffId");

                    b.HasIndex("PathwayId");

                    b.HasIndex("SupplierId");

                    b.HasIndex("TypeId");

                    b.ToTable("Products");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.ProductType", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("ProductTypes");

                    b.HasData(
                        new
                        {
                            Id = new Guid("08300ca2-6a6a-4a25-ad16-3818e9550847"),
                            IsDeleted = false,
                            Name = "Batteries"
                        },
                        new
                        {
                            Id = new Guid("30931946-6b02-4d5b-b645-77dcd545ff8c"),
                            IsDeleted = false,
                            Name = "NHS Service"
                        },
                        new
                        {
                            Id = new Guid("a3e47357-481e-421a-ba04-d6f1946d9e69"),
                            IsDeleted = false,
                            Name = "Repair Service"
                        },
                        new
                        {
                            Id = new Guid("47acc31b-22af-48dd-aca7-09e3b4f1127c"),
                            IsDeleted = false,
                            Name = "Earmolds"
                        },
                        new
                        {
                            Id = new Guid("b045b8b4-0b41-418f-b2d6-0724ea8177b7"),
                            IsDeleted = false,
                            Name = "Hearing Aids"
                        },
                        new
                        {
                            Id = new Guid("9c26d46a-3b38-4d34-b723-387e1deaa952"),
                            IsDeleted = false,
                            Name = "Accessories"
                        },
                        new
                        {
                            Id = new Guid("f2ae794b-734c-4c41-ad84-a84f54000167"),
                            IsDeleted = false,
                            Name = "Remote"
                        },
                        new
                        {
                            Id = new Guid("a9dc025b-75fd-4b30-b1d3-ca58fb7fb270"),
                            IsDeleted = false,
                            Name = "Other"
                        });
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.Stock", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("LocationId")
                        .IsRequired()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("LocationId")
                        .IsUnique();

                    b.ToTable("Stocks");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.StockProduct", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("ModifiedOn")
                        .HasColumnType("datetime2");

                    b.Property<decimal>("Price")
                        .HasColumnType("decimal(18,2)");

                    b.Property<Guid?>("ProductId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("Quantity")
                        .HasColumnType("int");

                    b.Property<Guid?>("StockId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("ProductId");

                    b.HasIndex("StockId");

                    b.ToTable("StockProducts");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.StockProductItem", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("ModifiedOn")
                        .HasColumnType("datetime2");

                    b.Property<string>("SerialNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<Guid?>("StockProductId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("StockProductId");

                    b.ToTable("StockProductItems");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.StockTransaction", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<decimal>("Amount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("ModifiedOn")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("ProductId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("Quantity")
                        .HasColumnType("int");

                    b.Property<Guid?>("StockId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("TotalQuantity")
                        .HasColumnType("int");

                    b.Property<string>("TransactionId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("TypeId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("ProductId");

                    b.HasIndex("StockId");

                    b.ToTable("StockTransactions");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.Supplier", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("ModifiedOn")
                        .HasColumnType("datetime2");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.ToTable("Suppliers");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.Transfer", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("Date")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("ModifiedOn")
                        .HasColumnType("datetime2");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<Guid?>("StockFromId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("StockToId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("StockFromId");

                    b.HasIndex("StockToId");

                    b.ToTable("Transfers");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.TransferItem", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("ModifiedOn")
                        .HasColumnType("datetime2");

                    b.Property<int>("Quantity")
                        .HasColumnType("int");

                    b.Property<string>("SerialNumbers")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("StockProductId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("TransferId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("StockProductId");

                    b.HasIndex("TransferId");

                    b.ToTable("TransferItems");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.TransferTransaction", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Comments")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("datetime2");

                    b.Property<int>("Direction")
                        .HasColumnType("int");

                    b.Property<DateTime?>("ModifiedOn")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("StockTransactionId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("TransferId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("StockTransactionId");

                    b.HasIndex("TransferId");

                    b.ToTable("TransferTransactions");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.ManufacturerProductType", b =>
                {
                    b.HasOne("Auditdata.InventoryService.Core.Entities.Manufacturer", "Manufacturer")
                        .WithMany("ManufacturerProductTypes")
                        .HasForeignKey("ManufacturerId");

                    b.HasOne("Auditdata.InventoryService.Core.Entities.ProductType", "ProductType")
                        .WithMany()
                        .HasForeignKey("ProductTypeId");

                    b.Navigation("Manufacturer");

                    b.Navigation("ProductType");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.NhsContractProduct", b =>
                {
                    b.HasOne("Auditdata.InventoryService.Core.Entities.Product", "Product")
                        .WithMany()
                        .HasForeignKey("ProductId");

                    b.Navigation("Product");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.Product", b =>
                {
                    b.HasOne("Auditdata.InventoryService.Core.Entities.BatteryType", "BatteryType")
                        .WithMany()
                        .HasForeignKey("BatteryTypeId");

                    b.HasOne("Auditdata.InventoryService.Core.Entities.HearingAidType", "HearingAidType")
                        .WithMany()
                        .HasForeignKey("HearingAidTypeId");

                    b.HasOne("Auditdata.InventoryService.Core.Entities.Manufacturer", "Manufacturer")
                        .WithMany()
                        .HasForeignKey("ManufacturerId");

                    b.HasOne("Auditdata.InventoryService.Core.Entities.NhsServiceTariff", "NhsServiceTariff")
                        .WithMany()
                        .HasForeignKey("NhsServiceTariffId");

                    b.HasOne("Auditdata.InventoryService.Core.Entities.Pathway", "Pathway")
                        .WithMany()
                        .HasForeignKey("PathwayId");

                    b.HasOne("Auditdata.InventoryService.Core.Entities.Supplier", "Supplier")
                        .WithMany()
                        .HasForeignKey("SupplierId");

                    b.HasOne("Auditdata.InventoryService.Core.Entities.ProductType", "Type")
                        .WithMany()
                        .HasForeignKey("TypeId");

                    b.Navigation("BatteryType");

                    b.Navigation("HearingAidType");

                    b.Navigation("Manufacturer");

                    b.Navigation("NhsServiceTariff");

                    b.Navigation("Pathway");

                    b.Navigation("Supplier");

                    b.Navigation("Type");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.StockProduct", b =>
                {
                    b.HasOne("Auditdata.InventoryService.Core.Entities.Product", "Product")
                        .WithMany()
                        .HasForeignKey("ProductId");

                    b.HasOne("Auditdata.InventoryService.Core.Entities.Stock", "Stock")
                        .WithMany()
                        .HasForeignKey("StockId");

                    b.Navigation("Product");

                    b.Navigation("Stock");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.StockProductItem", b =>
                {
                    b.HasOne("Auditdata.InventoryService.Core.Entities.StockProduct", "StockProduct")
                        .WithMany("StockProductItems")
                        .HasForeignKey("StockProductId");

                    b.Navigation("StockProduct");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.StockTransaction", b =>
                {
                    b.HasOne("Auditdata.InventoryService.Core.Entities.Product", "Product")
                        .WithMany()
                        .HasForeignKey("ProductId");

                    b.HasOne("Auditdata.InventoryService.Core.Entities.Stock", "Stock")
                        .WithMany()
                        .HasForeignKey("StockId");

                    b.Navigation("Product");

                    b.Navigation("Stock");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.Transfer", b =>
                {
                    b.HasOne("Auditdata.InventoryService.Core.Entities.Stock", "StockFrom")
                        .WithMany()
                        .HasForeignKey("StockFromId");

                    b.HasOne("Auditdata.InventoryService.Core.Entities.Stock", "StockTo")
                        .WithMany()
                        .HasForeignKey("StockToId");

                    b.Navigation("StockFrom");

                    b.Navigation("StockTo");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.TransferItem", b =>
                {
                    b.HasOne("Auditdata.InventoryService.Core.Entities.StockProduct", "StockProduct")
                        .WithMany()
                        .HasForeignKey("StockProductId");

                    b.HasOne("Auditdata.InventoryService.Core.Entities.Transfer", "Transfer")
                        .WithMany("TransferItems")
                        .HasForeignKey("TransferId");

                    b.Navigation("StockProduct");

                    b.Navigation("Transfer");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.TransferTransaction", b =>
                {
                    b.HasOne("Auditdata.InventoryService.Core.Entities.StockTransaction", "StockTransaction")
                        .WithMany("TransferTransactions")
                        .HasForeignKey("StockTransactionId");

                    b.HasOne("Auditdata.InventoryService.Core.Entities.Transfer", "Transfer")
                        .WithMany()
                        .HasForeignKey("TransferId");

                    b.Navigation("StockTransaction");

                    b.Navigation("Transfer");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.Manufacturer", b =>
                {
                    b.Navigation("ManufacturerProductTypes");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.StockProduct", b =>
                {
                    b.Navigation("StockProductItems");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.StockTransaction", b =>
                {
                    b.Navigation("TransferTransactions");
                });

            modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.Transfer", b =>
                {
                    b.Navigation("TransferItems");
                });
#pragma warning restore 612, 618
        }
    }
}
