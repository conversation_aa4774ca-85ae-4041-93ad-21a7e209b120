using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Auditdata.InventoryService.Infrastructure.Migrations;

/// <inheritdoc />
public partial class AddIsManualSNandAuditAdditionalNames : Migration
{
    /// <inheritdoc />
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.AddColumn<bool>(
            name: "IsManual",
            table: "AuditProductSerialNumbers",
            type: "bit",
            nullable: false,
            defaultValue: false);

        migrationBuilder.AddColumn<string>(
            name: "ProductName",
            table: "AuditProducts",
            type: "nvarchar(255)",
            maxLength: 255,
            nullable: true);

        migrationBuilder.AddColumn<string>(
            name: "Skus",
            table: "AuditProducts",
            type: "nvarchar(2000)",
            maxLength: 2000,
            nullable: true);

        migrationBuilder.AddColumn<string>(
            name: "SupplierName",
            table: "AuditProducts",
            type: "nvarchar(255)",
            maxLength: 255,
            nullable: true);

        migrationBuilder.AddColumn<string>(
            name: "LocationName",
            table: "AuditLocations",
            type: "nvarchar(255)",
            maxLength: 255,
            nullable: true);
    }

    /// <inheritdoc />
    protected override void Down(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.DropColumn(
            name: "IsManual",
            table: "AuditProductSerialNumbers");

        migrationBuilder.DropColumn(
            name: "ProductName",
            table: "AuditProducts");

        migrationBuilder.DropColumn(
            name: "Skus",
            table: "AuditProducts");

        migrationBuilder.DropColumn(
            name: "SupplierName",
            table: "AuditProducts");

        migrationBuilder.DropColumn(
            name: "LocationName",
            table: "AuditLocations");
    }
}
