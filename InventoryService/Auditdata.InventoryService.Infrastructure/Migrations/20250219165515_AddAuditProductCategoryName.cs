using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Auditdata.InventoryService.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class AddAuditProductCategoryName : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<string>(
                name: "S<PERSON>",
                table: "AuditProducts",
                type: "nvarchar(2000)",
                maxLength: 2000,
                nullable: false,
                defaultValue: "[]",
                oldClrType: typeof(string),
                oldType: "nvarchar(2000)",
                oldMaxLength: 2000,
                oldNullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ProductCategoryName",
                table: "AuditProducts",
                type: "nvarchar(255)",
                maxLength: 255,
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ProductCategoryName",
                table: "AuditProducts");

            migrationBuilder.AlterColumn<string>(
                name: "Skus",
                table: "AuditProducts",
                type: "nvarchar(2000)",
                maxLength: 2000,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(2000)",
                oldMaxLength: 2000,
                oldDefaultValue: "[]");
        }
    }
}
