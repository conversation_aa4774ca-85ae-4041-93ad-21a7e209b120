// <auto-generated />
using System;
using Auditdata.InventoryService.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace Auditdata.InventoryService.Infrastructure.Migrations;

[DbContext(typeof(InventoryDbContext))]
[Migration("20230217165216_AddedRepairOrderIdToStockProductItem")]
partial class AddedRepairOrderIdToStockProductItem
{
    /// <inheritdoc />
    protected override void BuildTargetModel(ModelBuilder modelBuilder)
    {
#pragma warning disable 612, 618
        modelBuilder
            .HasAnnotation("ProductVersion", "7.0.2")
            .HasAnnotation("Relational:MaxIdentifierLength", 128);

        SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

        modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.BatteryType", b =>
        {
            b.Property<Guid>("Id")
                .ValueGeneratedOnAdd()
                .HasColumnType("uniqueidentifier");

            b.Property<bool>("IsDeleted")
                .HasColumnType("bit");

            b.Property<string>("Name")
                .IsRequired()
                .HasMaxLength(255)
                .HasColumnType("nvarchar(255)");

            b.HasKey("Id");

            b.ToTable("BatteryTypes");

            b.HasData(
                new
                {
                    Id = new Guid("975340bb-d5fd-41e2-9bab-f6f06109edc9"),
                    IsDeleted = false,
                    Name = "10"
                },
                new
                {
                    Id = new Guid("ad270856-4d29-4e3e-b0bc-e9089d196fab"),
                    IsDeleted = false,
                    Name = "312"
                },
                new
                {
                    Id = new Guid("c33221bc-e71a-4b85-b92d-8632358cd37d"),
                    IsDeleted = false,
                    Name = "13"
                },
                new
                {
                    Id = new Guid("6b84bed9-740c-43e7-9855-fd8275231a43"),
                    IsDeleted = false,
                    Name = "675"
                },
                new
                {
                    Id = new Guid("6e213806-80c7-40cf-9605-d58eaf9789ff"),
                    IsDeleted = false,
                    Name = "Rechargeable"
                },
                new
                {
                    Id = new Guid("1b4b298c-f1bf-469a-8286-18471ce7342f"),
                    IsDeleted = false,
                    Name = "Unknown"
                });
        });

        modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.Country", b =>
        {
            b.Property<Guid>("Id")
                .ValueGeneratedOnAdd()
                .HasColumnType("uniqueidentifier");

            b.Property<bool>("IsDeleted")
                .HasColumnType("bit");

            b.Property<string>("Name")
                .IsRequired()
                .HasMaxLength(255)
                .HasColumnType("nvarchar(255)");

            b.HasKey("Id");

            b.ToTable("Countries");

            b.HasData(
                new
                {
                    Id = new Guid("2033baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Afghanistan"
                },
                new
                {
                    Id = new Guid("2133baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Albania"
                },
                new
                {
                    Id = new Guid("2233baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Algeria"
                },
                new
                {
                    Id = new Guid("2333baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Argentina"
                },
                new
                {
                    Id = new Guid("2433baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Armenia"
                },
                new
                {
                    Id = new Guid("2533baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Australia"
                },
                new
                {
                    Id = new Guid("2633baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Austria"
                },
                new
                {
                    Id = new Guid("2733baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Azerbaijan"
                },
                new
                {
                    Id = new Guid("2833baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Bahrain"
                },
                new
                {
                    Id = new Guid("2933baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Bangladesh"
                },
                new
                {
                    Id = new Guid("2a33baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Belarus"
                },
                new
                {
                    Id = new Guid("2b33baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Belgium"
                },
                new
                {
                    Id = new Guid("2c33baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Belize"
                },
                new
                {
                    Id = new Guid("2d33baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Bhutan"
                },
                new
                {
                    Id = new Guid("2e33baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Bolivia"
                },
                new
                {
                    Id = new Guid("2f33baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Bosnia & Herzegovina"
                },
                new
                {
                    Id = new Guid("3033baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Botswana"
                },
                new
                {
                    Id = new Guid("3133baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Brazil"
                },
                new
                {
                    Id = new Guid("3233baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Brunei"
                },
                new
                {
                    Id = new Guid("3333baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Bulgaria"
                },
                new
                {
                    Id = new Guid("3433baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Cambodia"
                },
                new
                {
                    Id = new Guid("3533baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Cameroon"
                },
                new
                {
                    Id = new Guid("3633baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Canada"
                },
                new
                {
                    Id = new Guid("3733baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Caribbean"
                },
                new
                {
                    Id = new Guid("3833baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Chile"
                },
                new
                {
                    Id = new Guid("3933baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "China"
                },
                new
                {
                    Id = new Guid("3a33baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Colombia"
                },
                new
                {
                    Id = new Guid("3b33baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Congo (DRC)"
                },
                new
                {
                    Id = new Guid("3c33baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Costa Rica"
                },
                new
                {
                    Id = new Guid("3d33baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Côte d’Ivoire"
                },
                new
                {
                    Id = new Guid("3e33baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Croatia"
                },
                new
                {
                    Id = new Guid("3f33baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Cuba"
                },
                new
                {
                    Id = new Guid("4033baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Czechia"
                },
                new
                {
                    Id = new Guid("4133baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Denmark"
                },
                new
                {
                    Id = new Guid("4233baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Dominican Republic"
                },
                new
                {
                    Id = new Guid("4333baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Ecuador"
                },
                new
                {
                    Id = new Guid("565a9699-066e-45cf-b960-411ac3d4ec20"),
                    IsDeleted = false,
                    Name = "England"
                },
                new
                {
                    Id = new Guid("4433baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Egypt"
                },
                new
                {
                    Id = new Guid("4533baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "El Salvador"
                },
                new
                {
                    Id = new Guid("4633baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Eritrea"
                },
                new
                {
                    Id = new Guid("4733baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Estonia"
                },
                new
                {
                    Id = new Guid("4833baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Ethiopia"
                },
                new
                {
                    Id = new Guid("4933baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Faroe Islands"
                },
                new
                {
                    Id = new Guid("4a33baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Finland"
                },
                new
                {
                    Id = new Guid("4b33baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "France"
                },
                new
                {
                    Id = new Guid("4c33baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Georgia"
                },
                new
                {
                    Id = new Guid("4d33baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Germany"
                },
                new
                {
                    Id = new Guid("4e33baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Greece"
                },
                new
                {
                    Id = new Guid("4f33baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Greenland"
                },
                new
                {
                    Id = new Guid("5033baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Guatemala"
                },
                new
                {
                    Id = new Guid("5133baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Haiti"
                },
                new
                {
                    Id = new Guid("5233baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Honduras"
                },
                new
                {
                    Id = new Guid("5333baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Hong Kong SAR"
                },
                new
                {
                    Id = new Guid("5433baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Hungary"
                },
                new
                {
                    Id = new Guid("5533baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Iceland"
                },
                new
                {
                    Id = new Guid("5633baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "India"
                },
                new
                {
                    Id = new Guid("5733baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Indonesia"
                },
                new
                {
                    Id = new Guid("5833baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Iran"
                },
                new
                {
                    Id = new Guid("5933baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Iraq"
                },
                new
                {
                    Id = new Guid("5a33baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Ireland"
                },
                new
                {
                    Id = new Guid("5b33baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Israel"
                },
                new
                {
                    Id = new Guid("5c33baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Italy"
                },
                new
                {
                    Id = new Guid("5d33baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Jamaica"
                },
                new
                {
                    Id = new Guid("5e33baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Japan"
                },
                new
                {
                    Id = new Guid("5f33baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Jordan"
                },
                new
                {
                    Id = new Guid("6033baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Kazakhstan"
                },
                new
                {
                    Id = new Guid("6133baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Kenya"
                },
                new
                {
                    Id = new Guid("6233baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Korea"
                },
                new
                {
                    Id = new Guid("6333baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Kuwait"
                },
                new
                {
                    Id = new Guid("6433baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Kyrgyzstan"
                },
                new
                {
                    Id = new Guid("6533baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Laos"
                },
                new
                {
                    Id = new Guid("6633baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Latin America"
                },
                new
                {
                    Id = new Guid("6733baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Latvia"
                },
                new
                {
                    Id = new Guid("6833baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Lebanon"
                },
                new
                {
                    Id = new Guid("6933baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Libya"
                },
                new
                {
                    Id = new Guid("6a33baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Liechtenstein"
                },
                new
                {
                    Id = new Guid("6b33baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Lithuania"
                },
                new
                {
                    Id = new Guid("6c33baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Luxembourg"
                },
                new
                {
                    Id = new Guid("6d33baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Malaysia"
                },
                new
                {
                    Id = new Guid("6e33baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Maldives"
                },
                new
                {
                    Id = new Guid("6f33baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Mali"
                },
                new
                {
                    Id = new Guid("7033baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Malta"
                },
                new
                {
                    Id = new Guid("7133baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Mexico"
                },
                new
                {
                    Id = new Guid("7233baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Moldova"
                },
                new
                {
                    Id = new Guid("7333baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Monaco"
                },
                new
                {
                    Id = new Guid("7433baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Mongolia"
                },
                new
                {
                    Id = new Guid("7533baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Montenegro"
                },
                new
                {
                    Id = new Guid("7633baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Morocco"
                },
                new
                {
                    Id = new Guid("7733baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Myanmar"
                },
                new
                {
                    Id = new Guid("7833baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Nepal"
                },
                new
                {
                    Id = new Guid("7933baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Netherlands"
                },
                new
                {
                    Id = new Guid("7a33baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "New Zealand"
                },
                new
                {
                    Id = new Guid("7b33baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Nicaragua"
                },
                new
                {
                    Id = new Guid("7c33baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Nigeria"
                },
                new
                {
                    Id = new Guid("7d33baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "North Macedonia"
                },
                new
                {
                    Id = new Guid("7e33baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Norway"
                },
                new
                {
                    Id = new Guid("b27ac0b5-a6a3-4af2-8a78-0b3d11215aca"),
                    IsDeleted = false,
                    Name = "Northern Ireland"
                },
                new
                {
                    Id = new Guid("7f33baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Oman"
                },
                new
                {
                    Id = new Guid("8033baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Pakistan"
                },
                new
                {
                    Id = new Guid("8133baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Panama"
                },
                new
                {
                    Id = new Guid("8233baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Paraguay"
                },
                new
                {
                    Id = new Guid("8333baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Peru"
                },
                new
                {
                    Id = new Guid("8433baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Philippines"
                },
                new
                {
                    Id = new Guid("8533baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Poland"
                },
                new
                {
                    Id = new Guid("8633baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Portugal"
                },
                new
                {
                    Id = new Guid("8733baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Puerto Rico"
                },
                new
                {
                    Id = new Guid("8833baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Qatar"
                },
                new
                {
                    Id = new Guid("8933baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Réunion"
                },
                new
                {
                    Id = new Guid("8a33baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Romania"
                },
                new
                {
                    Id = new Guid("8b33baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Russia"
                },
                new
                {
                    Id = new Guid("8c33baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Rwanda"
                },
                new
                {
                    Id = new Guid("8d33baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Saudi Arabia"
                },
                new
                {
                    Id = new Guid("9e395310-8e3c-4284-9a4b-21824d9e34b3"),
                    IsDeleted = false,
                    Name = "Scotland"
                },
                new
                {
                    Id = new Guid("8e33baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Senegal"
                },
                new
                {
                    Id = new Guid("8f33baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Serbia"
                },
                new
                {
                    Id = new Guid("9033baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Singapore"
                },
                new
                {
                    Id = new Guid("9133baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Slovakia"
                },
                new
                {
                    Id = new Guid("9233baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Slovenia"
                },
                new
                {
                    Id = new Guid("9333baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Somalia"
                },
                new
                {
                    Id = new Guid("9433baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "South Africa"
                },
                new
                {
                    Id = new Guid("9533baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Spain"
                },
                new
                {
                    Id = new Guid("9633baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Sri Lanka"
                },
                new
                {
                    Id = new Guid("9733baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Sweden"
                },
                new
                {
                    Id = new Guid("9833baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Switzerland"
                },
                new
                {
                    Id = new Guid("9933baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Syria"
                },
                new
                {
                    Id = new Guid("9a33baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Thailand"
                },
                new
                {
                    Id = new Guid("9b33baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Trinidad & Tobago"
                },
                new
                {
                    Id = new Guid("9c33baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Tunisia"
                },
                new
                {
                    Id = new Guid("9d33baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Turkey"
                },
                new
                {
                    Id = new Guid("9e33baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Turkmenistan"
                },
                new
                {
                    Id = new Guid("9f33baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Ukraine"
                },
                new
                {
                    Id = new Guid("a033baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "United Arab Emirates"
                },
                new
                {
                    Id = new Guid("a133baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "United Kingdom"
                },
                new
                {
                    Id = new Guid("a233baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "United States"
                },
                new
                {
                    Id = new Guid("a333baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Uruguay"
                },
                new
                {
                    Id = new Guid("a433baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Uzbekistan"
                },
                new
                {
                    Id = new Guid("a533baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Venezuela"
                },
                new
                {
                    Id = new Guid("a633baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Vietnam"
                },
                new
                {
                    Id = new Guid("3401508a-33f4-4fbd-8899-5acda30dcbb9"),
                    IsDeleted = false,
                    Name = "Wales"
                },
                new
                {
                    Id = new Guid("a733baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "World"
                },
                new
                {
                    Id = new Guid("a833baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Yemen"
                },
                new
                {
                    Id = new Guid("a933baa3-2b72-ed11-9f60-00224899f97e"),
                    IsDeleted = false,
                    Name = "Zimbabwe"
                });
        });

        modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.HearingAidType", b =>
        {
            b.Property<Guid>("Id")
                .ValueGeneratedOnAdd()
                .HasColumnType("uniqueidentifier");

            b.Property<bool>("IsDeleted")
                .HasColumnType("bit");

            b.Property<string>("Name")
                .IsRequired()
                .HasMaxLength(255)
                .HasColumnType("nvarchar(255)");

            b.HasKey("Id");

            b.ToTable("HearingAidTypes");

            b.HasData(
                new
                {
                    Id = new Guid("bdf916f7-cfb6-4016-bd05-0d1c2e2c5b10"),
                    IsDeleted = false,
                    Name = "BTE"
                },
                new
                {
                    Id = new Guid("8bb7392c-fce0-4522-97c8-1d246b136d01"),
                    IsDeleted = false,
                    Name = "RIC"
                },
                new
                {
                    Id = new Guid("eadf48c0-7f9e-4158-90e5-aa976904cae0"),
                    IsDeleted = false,
                    Name = "CIC"
                },
                new
                {
                    Id = new Guid("8267c588-abf9-436a-8160-a989b34c3a19"),
                    IsDeleted = false,
                    Name = "ITE"
                },
                new
                {
                    Id = new Guid("9c3d9f64-88a7-44c1-9138-056bea29cf44"),
                    IsDeleted = false,
                    Name = "Other"
                });
        });

        modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.Manufacturer", b =>
        {
            b.Property<Guid>("Id")
                .ValueGeneratedOnAdd()
                .HasColumnType("uniqueidentifier");

            b.Property<DateTime>("CreatedOn")
                .HasColumnType("datetime2");

            b.Property<bool>("IsActive")
                .HasColumnType("bit");

            b.Property<bool>("IsDeleted")
                .HasColumnType("bit");

            b.Property<DateTime?>("ModifiedOn")
                .HasColumnType("datetime2");

            b.Property<string>("Name")
                .IsRequired()
                .HasMaxLength(255)
                .HasColumnType("nvarchar(255)");

            b.Property<Guid>("TenantId")
                .HasColumnType("uniqueidentifier");

            b.HasKey("Id");

            b.ToTable("Manufacturers");
        });

        modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.NhsContractProduct", b =>
        {
            b.Property<Guid>("Id")
                .ValueGeneratedOnAdd()
                .HasColumnType("uniqueidentifier");

            b.Property<Guid?>("ContractId")
                .HasColumnType("uniqueidentifier");

            b.Property<Guid?>("ProductId")
                .HasColumnType("uniqueidentifier");

            b.Property<Guid>("TenantId")
                .HasColumnType("uniqueidentifier");

            b.HasKey("Id");

            b.HasIndex("ProductId");

            b.ToTable("NhsContractProducts");
        });

        modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.Pathway", b =>
        {
            b.Property<Guid>("Id")
                .ValueGeneratedOnAdd()
                .HasColumnType("uniqueidentifier");

            b.Property<bool>("IsDeleted")
                .HasColumnType("bit");

            b.Property<string>("Name")
                .IsRequired()
                .HasMaxLength(255)
                .HasColumnType("nvarchar(255)");

            b.Property<string>("Type")
                .IsRequired()
                .HasColumnType("nvarchar(max)");

            b.HasKey("Id");

            b.ToTable("Pathways");

            b.HasData(
                new
                {
                    Id = new Guid("4e7301d0-88c5-4f33-962d-ed06b4e64e4d"),
                    IsDeleted = false,
                    Name = "Wax Removal",
                    Type = "wax_removal"
                },
                new
                {
                    Id = new Guid("a8a63efd-3763-41f7-a568-b887b4341e24"),
                    IsDeleted = false,
                    Name = "HA Fitting",
                    Type = "ha_fitting"
                },
                new
                {
                    Id = new Guid("9d363ec1-d09a-4380-93af-e0324e9d028d"),
                    IsDeleted = false,
                    Name = "Recall",
                    Type = "recall"
                },
                new
                {
                    Id = new Guid("345201f6-4599-4871-bb28-2e44834f8a30"),
                    IsDeleted = false,
                    Name = "Aftercare",
                    Type = "aftercare"
                },
                new
                {
                    Id = new Guid("98dc257c-ec38-49c8-b658-5bb4afc71ce8"),
                    IsDeleted = false,
                    Name = "Remote Aftercare",
                    Type = "remote_aftercare"
                },
                new
                {
                    Id = new Guid("6b7d87cc-4f5b-4b00-8d55-6da6d5a8c9b3"),
                    IsDeleted = false,
                    Name = "Hearing Check",
                    Type = "hearing_check"
                },
                new
                {
                    Id = new Guid("b3943d8b-80c9-4822-9998-a79a3a42f239"),
                    IsDeleted = false,
                    Name = "Private Test",
                    Type = "private_test"
                },
                new
                {
                    Id = new Guid("19de7aa8-e174-47f8-83a8-953bbc15594e"),
                    IsDeleted = false,
                    Name = "NHS Assess & Fit",
                    Type = "nhs_assess_and_fit"
                },
                new
                {
                    Id = new Guid("5a674586-39fc-4ba3-9fd8-c3a2ca17c80e"),
                    IsDeleted = false,
                    Name = "Private Test & HA Consult",
                    Type = "private_test_ha_consult"
                },
                new
                {
                    Id = new Guid("440e198b-1089-4c28-99e5-cdcf79332407"),
                    IsDeleted = false,
                    Name = "Private HA Consultation",
                    Type = "private_ha_consultation"
                },
                new
                {
                    Id = new Guid("3e0a70b0-61e0-434d-a258-709583ea0f2b"),
                    IsDeleted = false,
                    Name = "Wax Triage",
                    Type = "wax_triage"
                },
                new
                {
                    Id = new Guid("0b9909d1-b2bb-4efc-8d24-fd6dcc994013"),
                    IsDeleted = false,
                    Name = "Follow Up",
                    Type = "follow_up"
                },
                new
                {
                    Id = new Guid("88c28659-77dc-4bd5-a2d3-31aa671e6210"),
                    IsDeleted = false,
                    Name = "Remote Follow Up",
                    Type = "remote_follow_up"
                },
                new
                {
                    Id = new Guid("ac95bdfd-f8a8-4d1f-89e8-7b4aa06e9165"),
                    IsDeleted = false,
                    Name = "Re-Assessment",
                    Type = "re_assessment"
                },
                new
                {
                    Id = new Guid("efff08a8-c1e2-4199-9488-5405506fc9ed"),
                    IsDeleted = false,
                    Name = "Hearing Protection",
                    Type = "hearing_protection"
                });
        });

        modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.Product", b =>
        {
            b.Property<Guid>("Id")
                .ValueGeneratedOnAdd()
                .HasColumnType("uniqueidentifier");

            b.Property<bool>("AutoDeliver")
                .HasColumnType("bit");

            b.Property<Guid?>("BatteryTypeId")
                .HasColumnType("uniqueidentifier");

            b.Property<Guid?>("CategoryId")
                .HasColumnType("uniqueidentifier");

            b.Property<string>("Code")
                .HasMaxLength(255)
                .HasColumnType("nvarchar(255)");

            b.Property<bool>("ControlledByStock")
                .HasColumnType("bit");

            b.Property<decimal?>("Cost")
                .HasPrecision(18, 2)
                .HasColumnType("decimal(18,2)");

            b.Property<DateTime>("CreatedOn")
                .HasColumnType("datetime2");

            b.Property<string>("Description")
                .HasMaxLength(4000)
                .HasColumnType("nvarchar(4000)");

            b.Property<decimal>("FirstVAT")
                .HasPrecision(18, 2)
                .HasColumnType("decimal(18,2)");

            b.Property<Guid?>("HearingAidTypeId")
                .HasColumnType("uniqueidentifier");

            b.Property<bool>("IsActive")
                .HasColumnType("bit");

            b.Property<bool>("IsDeleted")
                .HasColumnType("bit");

            b.Property<bool>("IsNHS")
                .HasColumnType("bit");

            b.Property<bool>("IsSellable")
                .HasColumnType("bit");

            b.Property<bool>("IsSerialized")
                .HasColumnType("bit");

            b.Property<Guid?>("ManufacturerId")
                .HasColumnType("uniqueidentifier");

            b.Property<decimal?>("MaximumDiscount")
                .HasPrecision(18, 2)
                .HasColumnType("decimal(18,2)");

            b.Property<DateTime?>("ModifiedOn")
                .HasColumnType("datetime2");

            b.Property<decimal?>("NHSVAT")
                .HasPrecision(18, 2)
                .HasColumnType("decimal(18,2)");

            b.Property<string>("Name")
                .IsRequired()
                .HasMaxLength(255)
                .HasColumnType("nvarchar(255)");

            b.Property<Guid?>("NhsServiceTariffId")
                .HasColumnType("uniqueidentifier");

            b.Property<bool>("PriceChangesAllowed")
                .HasColumnType("bit");

            b.Property<int>("Quantity")
                .HasColumnType("int");

            b.Property<decimal>("RetailPrice")
                .HasPrecision(18, 2)
                .HasColumnType("decimal(18,2)");

            b.Property<decimal>("SecondVAT")
                .HasPrecision(18, 2)
                .HasColumnType("decimal(18,2)");

            b.Property<Guid?>("SupplierId")
                .HasColumnType("uniqueidentifier");

            b.Property<Guid>("TenantId")
                .HasColumnType("uniqueidentifier");

            b.Property<string>("VendorProductNumber")
                .HasColumnType("nvarchar(max)");

            b.Property<int?>("Warranty")
                .HasColumnType("int");

            b.HasKey("Id");

            b.HasIndex("BatteryTypeId");

            b.HasIndex("CategoryId");

            b.HasIndex("HearingAidTypeId");

            b.HasIndex("ManufacturerId");

            b.HasIndex("SupplierId");

            b.ToTable("Products");
        });

        modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.ProductCategory", b =>
        {
            b.Property<Guid>("Id")
                .ValueGeneratedOnAdd()
                .HasColumnType("uniqueidentifier");

            b.Property<bool>("IsDeleted")
                .HasColumnType("bit");

            b.Property<string>("Name")
                .IsRequired()
                .HasMaxLength(255)
                .HasColumnType("nvarchar(255)");

            b.HasKey("Id");

            b.ToTable("ProductCategories");

            b.HasData(
                new
                {
                    Id = new Guid("08300ca2-6a6a-4a25-ad16-3818e9550847"),
                    IsDeleted = false,
                    Name = "Batteries"
                },
                new
                {
                    Id = new Guid("30931946-6b02-4d5b-b645-77dcd545ff8c"),
                    IsDeleted = false,
                    Name = "Service"
                },
                new
                {
                    Id = new Guid("a3e47357-481e-421a-ba04-d6f1946d9e69"),
                    IsDeleted = false,
                    Name = "Repair Service"
                },
                new
                {
                    Id = new Guid("47acc31b-22af-48dd-aca7-09e3b4f1127c"),
                    IsDeleted = false,
                    Name = "Earmolds"
                },
                new
                {
                    Id = new Guid("b045b8b4-0b41-418f-b2d6-0724ea8177b7"),
                    IsDeleted = false,
                    Name = "Hearing Aids"
                },
                new
                {
                    Id = new Guid("9c26d46a-3b38-4d34-b723-387e1deaa952"),
                    IsDeleted = false,
                    Name = "Accessories"
                },
                new
                {
                    Id = new Guid("f2ae794b-734c-4c41-ad84-a84f54000167"),
                    IsDeleted = false,
                    Name = "Remote"
                },
                new
                {
                    Id = new Guid("a9dc025b-75fd-4b30-b1d3-ca58fb7fb270"),
                    IsDeleted = false,
                    Name = "Other"
                });
        });

        modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.ProductPathway", b =>
        {
            b.Property<Guid>("Id")
                .ValueGeneratedOnAdd()
                .HasColumnType("uniqueidentifier");

            b.Property<bool>("IsDeleted")
                .HasColumnType("bit");

            b.Property<Guid?>("PathwayId")
                .HasColumnType("uniqueidentifier");

            b.Property<Guid?>("ProductId")
                .HasColumnType("uniqueidentifier");

            b.HasKey("Id");

            b.HasIndex("ProductId");

            b.ToTable("ProductPathways");
        });

        modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.Stock", b =>
        {
            b.Property<Guid>("Id")
                .ValueGeneratedOnAdd()
                .HasColumnType("uniqueidentifier")
                .HasDefaultValueSql("NEWID()");

            b.Property<Guid?>("LocationId")
                .IsRequired()
                .HasColumnType("uniqueidentifier");

            b.Property<string>("Name")
                .IsRequired()
                .HasMaxLength(255)
                .HasColumnType("nvarchar(255)");

            b.Property<Guid>("TenantId")
                .HasColumnType("uniqueidentifier");

            b.HasKey("Id");

            b.HasIndex("LocationId")
                .IsUnique();

            b.ToTable("Stocks");
        });

        modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.StockAdjustmentReason", b =>
        {
            b.Property<Guid>("Id")
                .ValueGeneratedOnAdd()
                .HasColumnType("uniqueidentifier");

            b.Property<bool>("IsActive")
                .HasColumnType("bit");

            b.Property<bool>("IsDeleted")
                .HasColumnType("bit");

            b.Property<string>("Name")
                .IsRequired()
                .HasColumnType("nvarchar(max)");

            b.Property<Guid>("TenantId")
                .HasColumnType("uniqueidentifier");

            b.HasKey("Id");

            b.ToTable("StockAdjustmentReasons");
        });

        modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.StockProduct", b =>
        {
            b.Property<Guid>("Id")
                .ValueGeneratedOnAdd()
                .HasColumnType("uniqueidentifier");

            b.Property<DateTime>("CreatedOn")
                .HasColumnType("datetime2");

            b.Property<bool>("IsDeleted")
                .HasColumnType("bit");

            b.Property<DateTime?>("ModifiedOn")
                .HasColumnType("datetime2");

            b.Property<decimal>("Price")
                .HasPrecision(18, 2)
                .HasColumnType("decimal(18,2)");

            b.Property<Guid?>("ProductId")
                .HasColumnType("uniqueidentifier");

            b.Property<int>("Quantity")
                .HasColumnType("int");

            b.Property<Guid?>("StockId")
                .HasColumnType("uniqueidentifier");

            b.Property<Guid>("TenantId")
                .HasColumnType("uniqueidentifier");

            b.HasKey("Id");

            b.HasIndex("StockId");

            b.HasIndex("ProductId", "StockId", "TenantId")
                .IsUnique()
                .HasFilter("[IsDeleted] = 0");

            b.ToTable("StockProducts");
        });

        modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.StockProductItem", b =>
        {
            b.Property<Guid>("Id")
                .ValueGeneratedOnAdd()
                .HasColumnType("uniqueidentifier");

            b.Property<DateTime>("CreatedOn")
                .HasColumnType("datetime2");

            b.Property<bool>("IsDeleted")
                .HasColumnType("bit");

            b.Property<DateTime?>("ModifiedOn")
                .HasColumnType("datetime2");

            b.Property<Guid?>("NegativeAdjustmentReasonId")
                .HasColumnType("uniqueidentifier");

            b.Property<Guid?>("OrderId")
                .HasColumnType("uniqueidentifier");

            b.Property<int>("Quantity")
                .ValueGeneratedOnAdd()
                .HasColumnType("int")
                .HasDefaultValue(1);

            b.Property<Guid?>("RepairOrderId")
                .HasColumnType("uniqueidentifier");

            b.Property<Guid?>("SaleId")
                .HasColumnType("uniqueidentifier");

            b.Property<string>("SerialNumber")
                .HasMaxLength(255)
                .HasColumnType("nvarchar(255)");

            b.Property<int>("Status")
                .HasColumnType("int");

            b.Property<Guid?>("StockProductId")
                .HasColumnType("uniqueidentifier");

            b.Property<Guid>("TenantId")
                .HasColumnType("uniqueidentifier");

            b.Property<Guid?>("TransferId")
                .HasColumnType("uniqueidentifier");

            b.HasKey("Id");

            b.HasIndex("NegativeAdjustmentReasonId");

            b.HasIndex("StockProductId");

            b.ToTable("StockProductItems");
        });

        modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.StockProductItemLog", b =>
        {
            b.Property<Guid>("Id")
                .ValueGeneratedOnAdd()
                .HasColumnType("uniqueidentifier");

            b.Property<DateTime>("CreatedOn")
                .HasColumnType("datetime2");

            b.Property<string>("Description")
                .HasColumnType("nvarchar(max)");

            b.Property<Guid>("StockProductItemId")
                .HasColumnType("uniqueidentifier");

            b.Property<string>("User")
                .HasColumnType("nvarchar(max)");

            b.HasKey("Id");

            b.HasIndex("StockProductItemId");

            b.ToTable("StockProductItemLogs");
        });

        modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.StockTransaction", b =>
        {
            b.Property<Guid>("Id")
                .ValueGeneratedOnAdd()
                .HasColumnType("uniqueidentifier");

            b.Property<DateTime>("CreatedOn")
                .HasColumnType("datetime2");

            b.Property<DateTime?>("ModifiedOn")
                .HasColumnType("datetime2");

            b.Property<Guid?>("ProductId")
                .HasColumnType("uniqueidentifier");

            b.Property<int>("Quantity")
                .HasColumnType("int");

            b.Property<Guid?>("SaleId")
                .HasColumnType("uniqueidentifier");

            b.Property<Guid?>("StockId")
                .HasColumnType("uniqueidentifier");

            b.Property<Guid>("TenantId")
                .HasColumnType("uniqueidentifier");

            b.Property<int>("TotalQuantity")
                .HasColumnType("int");

            b.Property<string>("TransactionId")
                .IsRequired()
                .HasColumnType("nvarchar(max)");

            b.Property<int>("TypeId")
                .HasColumnType("int");

            b.HasKey("Id");

            b.HasIndex("ProductId");

            b.HasIndex("StockId");

            b.ToTable("StockTransactions");
        });

        modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.Supplier", b =>
        {
            b.Property<Guid>("Id")
                .ValueGeneratedOnAdd()
                .HasColumnType("uniqueidentifier");

            b.Property<string>("Address1")
                .HasMaxLength(4000)
                .HasColumnType("nvarchar(4000)");

            b.Property<string>("Address2")
                .HasMaxLength(4000)
                .HasColumnType("nvarchar(4000)");

            b.Property<string>("City")
                .IsRequired()
                .HasMaxLength(255)
                .HasColumnType("nvarchar(255)");

            b.Property<Guid?>("CountryId")
                .HasColumnType("uniqueidentifier");

            b.Property<DateTime>("CreatedOn")
                .HasColumnType("datetime2");

            b.Property<string>("EmailAddress")
                .HasMaxLength(255)
                .HasColumnType("nvarchar(255)");

            b.Property<string>("FaxNumber")
                .HasMaxLength(255)
                .HasColumnType("nvarchar(255)");

            b.Property<bool>("IsActive")
                .HasColumnType("bit");

            b.Property<bool>("IsDeleted")
                .HasColumnType("bit");

            b.Property<bool>("IsManufacturer")
                .HasColumnType("bit");

            b.Property<DateTime?>("ModifiedOn")
                .HasColumnType("datetime2");

            b.Property<string>("Name")
                .IsRequired()
                .HasMaxLength(255)
                .HasColumnType("nvarchar(255)");

            b.Property<string>("PhoneNumber")
                .IsRequired()
                .HasMaxLength(255)
                .HasColumnType("nvarchar(255)");

            b.Property<string>("PostalCode")
                .HasMaxLength(255)
                .HasColumnType("nvarchar(255)");

            b.Property<string>("State")
                .IsRequired()
                .HasMaxLength(255)
                .HasColumnType("nvarchar(255)");

            b.Property<Guid>("TenantId")
                .HasColumnType("uniqueidentifier");

            b.Property<string>("Website")
                .HasMaxLength(255)
                .HasColumnType("nvarchar(255)");

            b.HasKey("Id");

            b.HasIndex("CountryId");

            b.ToTable("Suppliers");
        });

        modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.TransfersState", b =>
        {
            b.Property<Guid>("TransferId")
                .HasColumnType("uniqueidentifier");

            b.Property<DateTime?>("AcceptedAt")
                .HasColumnType("datetime2");

            b.Property<DateTime?>("CancelledAt")
                .HasColumnType("datetime2");

            b.Property<Guid>("CorrelationId")
                .HasColumnType("uniqueidentifier");

            b.Property<string>("CurrentState")
                .HasMaxLength(50)
                .HasColumnType("nvarchar(50)");

            b.Property<Guid>("FromStockId")
                .HasColumnType("uniqueidentifier");

            b.Property<DateTime?>("RequestedAt")
                .HasColumnType("datetime2");

            b.Property<byte[]>("RowVersion")
                .IsConcurrencyToken()
                .ValueGeneratedOnAddOrUpdate()
                .HasColumnType("rowversion");

            b.Property<Guid>("StockProductItemId")
                .HasColumnType("uniqueidentifier");

            b.Property<Guid>("ToStockId")
                .HasColumnType("uniqueidentifier");

            b.HasKey("TransferId");

            b.HasIndex("StockProductItemId");

            b.ToTable("Transfers");
        });

        modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.NhsContractProduct", b =>
        {
            b.HasOne("Auditdata.InventoryService.Core.Entities.Product", "Product")
                .WithMany("NhsContractProducts")
                .HasForeignKey("ProductId");

            b.Navigation("Product");
        });

        modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.Product", b =>
        {
            b.HasOne("Auditdata.InventoryService.Core.Entities.BatteryType", "BatteryType")
                .WithMany()
                .HasForeignKey("BatteryTypeId");

            b.HasOne("Auditdata.InventoryService.Core.Entities.ProductCategory", "Category")
                .WithMany()
                .HasForeignKey("CategoryId");

            b.HasOne("Auditdata.InventoryService.Core.Entities.HearingAidType", "HearingAidType")
                .WithMany()
                .HasForeignKey("HearingAidTypeId");

            b.HasOne("Auditdata.InventoryService.Core.Entities.Manufacturer", "Manufacturer")
                .WithMany()
                .HasForeignKey("ManufacturerId");

            b.HasOne("Auditdata.InventoryService.Core.Entities.Supplier", "Supplier")
                .WithMany()
                .HasForeignKey("SupplierId");

            b.Navigation("BatteryType");

            b.Navigation("Category");

            b.Navigation("HearingAidType");

            b.Navigation("Manufacturer");

            b.Navigation("Supplier");
        });

        modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.ProductPathway", b =>
        {
            b.HasOne("Auditdata.InventoryService.Core.Entities.Product", "Product")
                .WithMany("ProductPathways")
                .HasForeignKey("ProductId");

            b.Navigation("Product");
        });

        modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.StockProduct", b =>
        {
            b.HasOne("Auditdata.InventoryService.Core.Entities.Product", "Product")
                .WithMany("StockProducts")
                .HasForeignKey("ProductId");

            b.HasOne("Auditdata.InventoryService.Core.Entities.Stock", "Stock")
                .WithMany()
                .HasForeignKey("StockId");

            b.Navigation("Product");

            b.Navigation("Stock");
        });

        modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.StockProductItem", b =>
        {
            b.HasOne("Auditdata.InventoryService.Core.Entities.StockAdjustmentReason", "NegativeAdjustmentReason")
                .WithMany()
                .HasForeignKey("NegativeAdjustmentReasonId");

            b.HasOne("Auditdata.InventoryService.Core.Entities.StockProduct", "StockProduct")
                .WithMany("StockProductItems")
                .HasForeignKey("StockProductId");

            b.Navigation("NegativeAdjustmentReason");

            b.Navigation("StockProduct");
        });

        modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.StockProductItemLog", b =>
        {
            b.HasOne("Auditdata.InventoryService.Core.Entities.StockProductItem", "StockProductItem")
                .WithMany()
                .HasForeignKey("StockProductItemId")
                .OnDelete(DeleteBehavior.Cascade)
                .IsRequired();

            b.Navigation("StockProductItem");
        });

        modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.StockTransaction", b =>
        {
            b.HasOne("Auditdata.InventoryService.Core.Entities.Product", "Product")
                .WithMany()
                .HasForeignKey("ProductId");

            b.HasOne("Auditdata.InventoryService.Core.Entities.Stock", "Stock")
                .WithMany()
                .HasForeignKey("StockId");

            b.Navigation("Product");

            b.Navigation("Stock");
        });

        modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.Supplier", b =>
        {
            b.HasOne("Auditdata.InventoryService.Core.Entities.Country", "Country")
                .WithMany()
                .HasForeignKey("CountryId");

            b.OwnsOne("Auditdata.InventoryService.Core.Entities.SupplierContact", "AccountReceivableContact", b1 =>
            {
                b1.Property<Guid>("SupplierId")
                    .HasColumnType("uniqueidentifier");

                b1.Property<string>("EmailAddress")
                    .HasColumnType("nvarchar(max)");

                b1.Property<string>("Extension")
                    .HasColumnType("nvarchar(max)");

                b1.Property<string>("Name")
                    .HasColumnType("nvarchar(max)");

                b1.Property<string>("PhoneNumber")
                    .HasColumnType("nvarchar(max)");

                b1.HasKey("SupplierId");

                b1.ToTable("Suppliers");

                b1.ToJson("AccountReceivableContact");

                b1.WithOwner()
                    .HasForeignKey("SupplierId");
            });

            b.OwnsOne("Auditdata.InventoryService.Core.Entities.SupplierContact", "SalesContact", b1 =>
            {
                b1.Property<Guid>("SupplierId")
                    .HasColumnType("uniqueidentifier");

                b1.Property<string>("EmailAddress")
                    .HasColumnType("nvarchar(max)");

                b1.Property<string>("Extension")
                    .HasColumnType("nvarchar(max)");

                b1.Property<string>("Name")
                    .HasColumnType("nvarchar(max)");

                b1.Property<string>("PhoneNumber")
                    .HasColumnType("nvarchar(max)");

                b1.HasKey("SupplierId");

                b1.ToTable("Suppliers");

                b1.ToJson("SalesContact");

                b1.WithOwner()
                    .HasForeignKey("SupplierId");
            });

            b.Navigation("AccountReceivableContact");

            b.Navigation("Country");

            b.Navigation("SalesContact");
        });

        modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.TransfersState", b =>
        {
            b.HasOne("Auditdata.InventoryService.Core.Entities.StockProductItem", "StockProductItem")
                .WithMany()
                .HasForeignKey("StockProductItemId")
                .OnDelete(DeleteBehavior.Cascade)
                .IsRequired();

            b.Navigation("StockProductItem");
        });

        modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.Product", b =>
        {
            b.Navigation("NhsContractProducts");

            b.Navigation("ProductPathways");

            b.Navigation("StockProducts");
        });

        modelBuilder.Entity("Auditdata.InventoryService.Core.Entities.StockProduct", b =>
        {
            b.Navigation("StockProductItems");
        });
#pragma warning restore 612, 618
    }
}
