<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
      <TargetFramework>net8.0</TargetFramework>
      <ImplicitUsings>enable</ImplicitUsings>
      <Nullable>enable</Nullable>
      <EnforceCodeStyleInBuild>true</EnforceCodeStyleInBuild>
    </PropertyGroup>

    <ItemGroup>
      <PackageReference Include="Auditdata.Transport.Contracts" Version="9.3.30" />
      <PackageReference Include="Microsoft.AspNetCore.Http" Version="2.3.0" />
      <PackageReference Include="Microsoft.AspNetCore.Mvc.Core" Version="2.3.0" />
      <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    </ItemGroup>

</Project>
