namespace Auditdata.InventoryService.Contracts.Requests.Bundles;

public class CreateBundleRequest
{
    public string Name { get; set; } = null!;
    public string? Description { get; set; }
    public decimal TotalCost { get; set; }
    public bool IsActive { get; set; }
    public bool IsAutomaticDelivery { get; set; }
    public bool IsSellable { get; set; }
    public bool IsPriceChangeAllowed { get; set; }
    public IEnumerable<BundleProductRequest> Products { get; set; } = null!;
}
