using Auditdata.Transport.Contracts.Table;

namespace Auditdata.InventoryService.Contracts.Requests.AuditRequests;

public class SearchAuditRequestsRequest : TableQueryBase
{
    public string? SearchText { get; set; }
    public AuditStatusRequest? Status { get; set; }
    public Guid? RegionId { get; set; }
    public Guid[] LocationIds { get; set; } = [];
    public DateTimeOffset? DateDue { get; set; }
}
