using Auditdata.InventoryService.Contracts.Requests.Common;

namespace Auditdata.InventoryService.Contracts.Requests.StockProductItems;

public class UpdateStockProductItemRequest
{
    public string SerialNumber { get; set; } = null!;
    public Guid? ColorId { get; set; }
    public Guid? BatteryTypeId { get; set; }
    public IEnumerable<StockProductItemAttributeRequest> Attributes { get; set; } = null!;
}
