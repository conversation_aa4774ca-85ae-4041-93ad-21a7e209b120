using Auditdata.InventoryService.Contracts.Requests.Common;

namespace Auditdata.InventoryService.Contracts.Requests.Products;

public class AssignProductSerialNumberRequest
{
    public IEnumerable<string> SerialNumbers { get; set; } = null!;
    public IEnumerable<StockProductItemAttributeRequest> Attributes { get; set; } = null!;
    public Guid? BatteryTypeId { get; set; }
    public Guid? ColorId { get; set; }
}
