using Auditdata.InventoryService.Contracts.Requests.Common;

namespace Auditdata.InventoryService.Contracts.Requests.StockProducts;

public record AdjustSerializedProductRequest
{
    public List<string> SerialNumbers { get; set; } = null!;
    public Guid? BatteryTypeId { get; set; }
    public Guid? ColorId { get; set; }
    public IEnumerable<StockProductItemAttributeRequest> Attributes { get; set; } = null!;
}
