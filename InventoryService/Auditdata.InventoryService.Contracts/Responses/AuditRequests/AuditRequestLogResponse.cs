namespace Auditdata.InventoryService.Contracts.Responses.AuditRequests;

public record AuditRequestLogResponse
{
    public Guid Id { get; set; }
    public AuditRequestLogActionTypeResponse Action { get; set; }
    public DateTimeOffset CreationDate { get; set; }
    public string CreatedBy { get; set; } = null!;
    public AuditRequestChangesResponse? AuditRequestChanges { get; set; }
}

public enum AuditRequestLogActionTypeResponse
{
    Created = 1,
    Updated = 2,
    Copied = 3,
}

public record AuditRequestChangesResponse
{
    public List<PropertyChangeModelResponse> Changes { get; set; } = [];

    public List<AuditProductChangesResponse> AuditProductsChanges { get; set; } = [];
}

public record AuditProductChangesResponse
{
    public required string ProductName { get; set; }

    public List<PropertyChangeModelResponse> Changes { get; set; } = [];

    public List<AuditSerialNumberChangesResponse> AuditSerialNumbersChanges { get; set; } = [];
}

public record AuditSerialNumberChangesResponse
{
    public required string SerialNumber { get; set; }

    public List<PropertyChangeModelResponse> Changes { get; set; } = [];
}

public record PropertyChangeModelResponse
{
    public string PropertyName { get; set; }

    public string? OriginalValue { get; set; }

    public string? UpdatedValue { get; set; }

    public PropertyChangeModelResponse(string propertyName, string? originalValue, string? updatedValue)
    {
        PropertyName = propertyName;
        OriginalValue = originalValue;
        UpdatedValue = updatedValue;
    }
}
