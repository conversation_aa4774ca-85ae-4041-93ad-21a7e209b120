using System.Text.Json.Serialization;
using Auditdata.InventoryService.Contracts.Requests.AuditRequests;

namespace Auditdata.InventoryService.Contracts.Responses.AuditRequests;

public record AuditProductSerialNumberResponse
{
    public string SerialNumber { get; set; } = null!;
    [JsonConverter(typeof(JsonStringEnumConverter))]
    public AuditProductSNStatusRequest Status { get; set; }
    public bool IsManual { get; set; }
    public bool IsDisabledToDelete { get; set; }
    public bool AddedByManager { get; set; }
    public string? Comments { get; set; }
}
