using System.Text.Json.Serialization;
using Auditdata.InventoryService.Contracts.Requests.AuditRequests;
using Auditdata.InventoryService.Contracts.Responses.Common;

namespace Auditdata.InventoryService.Contracts.Responses.AuditRequests;

public record AuditRequestResponse
{
    public Guid Id { get; set; }
    public string? RequestNumber { get; set; }
    [JsonConverter(typeof(JsonStringEnumConverter))]
    public AuditStatusRequest Status { get; set; }
    public DateTimeOffset DateTimeDue { get; set; }
    public string? Notes { get; set; }
    public bool BlindStockCalculation { get; set; }
    public bool IncludeOnTrialProducts { get; set; }
    public bool AddAllProductsFromCatalog { get; set; }
    public bool IndicateSerialNumber { get; set; }
    public DateTimeOffset? MovedToReadyForCalculationDateTime { get; set; }
    public DateTimeOffset CreationDate { get; set; }
    public IEnumerable<DictionaryResponse> Locations { get; set; } = [];
    public IEnumerable<AuditRequestProductResponse> Products { get; set; } = [];
    public bool ReturnedToDraft { get; set; }
}
