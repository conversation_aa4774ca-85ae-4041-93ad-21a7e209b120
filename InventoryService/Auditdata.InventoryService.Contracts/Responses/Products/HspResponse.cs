using Auditdata.InventoryService.Contracts.Requests.Products.Hsp;

namespace Auditdata.InventoryService.Contracts.Responses.Products;

public class HspResponse
{
    public string? Code { get; set; }
    public string? Category { get; set; }
    public bool TopUp { get; set; }
    public decimal? ClientPrice { get; set; }
    public string? ServiceNumber { get; set; }
    public HspServiceTypeRequest? ServiceType { get; set; }
    public bool? IsMaintenance { get; set; }
}
