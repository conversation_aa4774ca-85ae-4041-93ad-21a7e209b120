using Auditdata.InventoryService.Contracts.Responses.Common;

namespace Auditdata.InventoryService.Contracts.Responses.Products;

public class GetProductResponse
{
    public Guid Id { get; set; }
    public Guid StockProductId { get; set; }
    public string Name { get; set; } = null!;
    public string? Description { get; set; }
    public Guid? CategoryId { get; set; }
    public string? Category { get; set; }
    public string? CategoryCode { get; set; }
    public Guid? ManufacturerId { get; set; }
    public string? Manufacturer { get; set; }
    public Guid? HearingAidTypeId { get; set; }
    public Guid? SupplierId { get; set; }
    public string? Supplier { get; set; }
    public string? HearingAidType { get; set; }
    public int? Warranty { get; set; }
    public int? LDWarranty { set; get; }
    public int Quantity { get; set; }
    public decimal RetailPrice { get; set; }
    public decimal FirstVAT { get; set; }
    public decimal SecondVAT { get; set; }
    public bool IsSellable { get; set; }
    public bool IsActive { get; set; }
    public bool IsSerialized { get; set; }
    public bool PriceChangesAllowed { get; set; }
    public bool IsFastTrack { get; set; }
    public bool AutoDeliver { get; set; }
    public bool ControlledByStock { get; set; }
    public string? VendorProductNumber { get; set; }
    public decimal? MaximumDiscount { get; set; }
    public decimal? Cost { get; set; }
    public bool IsInBundle { get; set; }
    public bool WasPlacedOnStock { get; set; }
    public bool HasSkuConfigs { get; set; }

    public IEnumerable<Guid>? Pathways { get; set; }
    public IEnumerable<DictionaryResponse>? Colors { get; set; }
    public IEnumerable<DictionaryResponse>? BatteryTypes { get; set; }
    public IEnumerable<ProductAttributeResponse>? Attributes { get; set; }
    public IEnumerable<Guid>? SuggestedProducts { get; set; }
}
