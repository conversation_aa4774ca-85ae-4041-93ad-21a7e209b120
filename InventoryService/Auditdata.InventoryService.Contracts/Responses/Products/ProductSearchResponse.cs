namespace Auditdata.InventoryService.Contracts.Responses.Products;

public class ProductSearchResponse
{
    public string Id { get; set; } = null!;
    public string Name { get; set; } = null!;
    public string? Description { get; set; }
    public Guid? CategoryId { get; set; }
    public string? Category { get; set; }
    public Guid? ManufacturerId { get; set; }
    public string? Manufacturer { get; set; }
    public string? Supplier { get; set; }
    public Guid? SupplierId { get; set; }
    public IEnumerable<string> Skus { get; set; } = [];
    public int? Warranty { get; set; }
    public bool IsActive { get; set; }
    public bool IsSellable { get; set; }
    public bool IsInBundle { get; set; }
    public decimal RetailPrice { get; set; }
}
