namespace Auditdata.InventoryService.Contracts.Responses.StockTransactions;

public class StockTransactionResponse
{
    public Guid Id { get; set; }
    public DateTimeOffset CreatedOn { get; set; }
    public string TransactionId { get; set; } = null!;
    public string? Stock { get; set; }
    public StockTransactionTypeResponse TypeId { get; set; }
    public string Type { get; set; } = null!;
    public Guid ProductId { get; set; }
    public int Quantity { get; set; }
    public int TotalQuantity { get; set; }
}
