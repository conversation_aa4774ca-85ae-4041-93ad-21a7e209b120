namespace Auditdata.InventoryService.Contracts.Responses.ProductItems;

public class StockProductItemResponse
{
    public Guid Id { get; set; }
    public Guid ProductId { get; set; }
    public string SerialNumber { get; set; } = null!;
    public DateTimeOffset CreatedOn { get; set; }
    public string Status { get; set; } = null!;
    public Guid LocationId { get; set; }
    public string Stock { get; set; } = null!;
    public Guid? TransferId { get; set; }
    public bool PendingTransferAcceptance { get; set; }
    public Guid? RepairOrderId { get; set; }
    public string? Color { get; set; }
    public string? BatteryType { get; set; }
    public bool AddToStockAllowed { get; set; }
}
