namespace Auditdata.InventoryService.Core.Constants;

public static class TransactionLogLocalizationConstants
{
    public const string TransferAccepted = "transactionhistorylogs.transferaccepted";
    public const string TransferRequested = "transactionhistorylogs.transferrequested";
    public const string TransferCancelled = "transactionhistorylogs.transfercancelled";
    public const string SerialNumberWasAdded = "transactionhistorylogs.serialnumberwasadded";
    public const string StockAdjustment = "transactionhistorylogs.stockadjustment";
    public const string RepairOrderCreated = "transactionhistorylogs.repairordercreated";
    public const string RepairOrderCancelled = "transactionhistorylogs.repairordercancelled";
    public const string LnDOrderCreated = "transactionhistorylogs.lndordercreated";
    public const string LnDOrderCancelled = "transactionhistorylogs.lndordercancelled";
    public const string ReservedWithInvoice = "transactionhistorylogs.reservedwithinvoice";
    public const string StockProductItemRemoved = "transactionhistorylogs.stockproductitemremoved";
    public const string ReplacedByRepairOrder = "transactionhistorylogs.replacedbyrepairorder";
    public const string ReplacedByLnDOrder = "transactionhistorylogs.replacedbylndorder";
    public const string ExchangedByLnDOrder = "transactionhistorylogs.exchangedbylndorder";
    public const string ReplacementByRepairOrder = "transactionhistorylogs.replacementbyrepairorder";
    public const string ReplacementByLnDOrder = "transactionhistorylogs.replacementbylndorder";
    public const string ExchangeByLnDOrder = "transactionhistorylogs.exchangementbylndorder";
    public const string Delivered = "transactionhistorylogs.delivered";
    public const string ReturnedAndDeliveredByRepairOrder = "transactionhistorylogs.returnedanddeliveredbyrepairorder";
    public const string ReturnedByRepairOrder = "transactionhistorylogs.returnedbyrepairorder";
    public const string ReplacementByCompleteRepairOrder = "transactionhistorylogs.replacementbycompleterepairorder";
    public const string ReturnedToStock = "transactionhistorylogs.returnedtostock";
    public const string ReturnedToSupplier = "transactionhistorylogs.returnedtosupplier";
    public const string ReservedByOrder = "transactionhistorylogs.reservedbyorder";
    public const string ReplacedBySale = "transactionhistorylogs.replacedbysale";
    public const string ReplacementBySale = "transactionhistorylogs.replacementbysale";
    public const string ExchangedBySale = "transactionhistorylogs.exchangedbysale";
    public const string ExchangeBySale = "transactionhistorylogs.exchangebysale";
    public const string StockProductItemSerialNumberUpdated = "transactionhistorylogs.stockproductitemserialnumberupdated";
    public const string StockProductItemColorUpdated = "transactionhistorylogs.stockproductitemcolorupdated";
    public const string StockProductItemBatteryTypeUpdated = "transactionhistorylogs.stockproductitembatterytypeupdated";
    public const string StockProductItemAttributeUpdated = "transactionhistorylogs.stockproductitemattributeupdated";
    public const string StockProductItemTrialStarted = "transactionhistorylogs.stockproductitemtrialstarted";
    public const string StockProductItemTrialCancelled = "transactionhistorylogs.stockproductitemtrialcancelled";
    public const string StockProductItemTrialCancelledToSupplier = "transactionhistorylogs.stockproductitemtrialcancelled.tosupplier";
    public const string StockProductItemTrialCancelledToStock = "transactionhistorylogs.stockproductitemtrialcancelled.tostock";
}
