using Auditdata.Transport.Contracts.Exceptions;

namespace Auditdata.InventoryService.Core.Exceptions;

public class ProductItemInsufficientQuantityException : BusinessException
{
    public Guid StockProductId { get; }
    public int Quantity { get; }

    public ProductItemInsufficientQuantityException(string errorCode, Guid stockProductId, int quantity) : base($"Insufficient quantity of reserved product {stockProductId} in stock", errorCode,stockProductId,quantity)
    {
        StockProductId = stockProductId;
        Quantity = quantity;
    }
}
