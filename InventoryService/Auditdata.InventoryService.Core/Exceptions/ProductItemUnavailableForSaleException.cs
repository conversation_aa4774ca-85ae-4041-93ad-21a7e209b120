using Auditdata.Transport.Contracts.Exceptions;

namespace Auditdata.InventoryService.Core.Exceptions;

public class ProductItemUnavailableForSaleException : BusinessException
{
    public Guid StockProductId { get; }
    public Guid? StockProductItemId { get; }

    public ProductItemUnavailableForSaleException(
        Guid stockProductId,
        string errorCode,
        Guid? stockProductItemId = null) :
        base(errorCode, stockProductId)
    {
        StockProductId = stockProductId;
        StockProductItemId = stockProductItemId;
    }
}
