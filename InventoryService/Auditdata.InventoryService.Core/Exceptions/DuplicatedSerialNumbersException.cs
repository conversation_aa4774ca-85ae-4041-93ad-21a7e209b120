using Auditdata.Transport.Contracts.Exceptions;

public class DuplicatedSerialNumbersException : BusinessException
{
    public int StatusCode { get; set; }
    public string [] DuplicatedSerialNumber { get; }

    public DuplicatedSerialNumbersException(string message) : base(message)
    {
    }

    public DuplicatedSerialNumbersException(string message, string errorCode, string[] placeholderValues, int statusCode = 500) : base(message, errorCode, placeholderValues)
    {
        StatusCode = statusCode;
        Data["PlaceholderValues"] = placeholderValues;
        Data["ErrorCode"] = errorCode;
        DuplicatedSerialNumber = placeholderValues;
    }
}
