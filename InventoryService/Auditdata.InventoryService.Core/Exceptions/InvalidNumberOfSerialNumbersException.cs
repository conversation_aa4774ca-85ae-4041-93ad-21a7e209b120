using Auditdata.Transport.Contracts.Exceptions;

namespace Auditdata.InventoryService.Core.Exceptions;

public class InvalidNumberOfSerialNumbersException : BusinessException
{
    public int Available { get; set; }
    public int Entered { get; set; }
    public InvalidNumberOfSerialNumbersException(string errorCode, int itemsAvailable, int serialNumbersCount) 
        : base("Invalid number of serial numbers", errorCode, itemsAvailable,serialNumbersCount)
    {
        Available = itemsAvailable;
        Entered = serialNumbersCount;
    }
}
