using Auditdata.Transport.Contracts.Exceptions;

namespace Auditdata.InventoryService.Core.Exceptions;

public class StockProductReservationException : BusinessException
{
    public Guid StockProductId { get; }
    public Guid StockProductItemId { get; }

    public StockProductReservationException(
        Guid stockProductId,
        Guid stockProductItemId,
        string errorCode) : base(errorCode, stockProductId, stockProductItemId!)
    {
        StockProductId = stockProductId;
        StockProductItemId = stockProductItemId;
    }
}
