using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Features.States.Commands;
using Microsoft.EntityFrameworkCore;

namespace Auditdata.InventoryService.Core.Features.States.Handlers;

public class DeleteStateHandler : IRequestHandler<DeleteStateCommand>
{
    private readonly IDbContext _dbContext;

    public DeleteStateHandler(IDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    public async Task Handle(DeleteStateCommand request, CancellationToken cancellationToken)
    {
        var state = await _dbContext.States.FirstOrDefaultAsync(x => x.Id == request.Id, cancellationToken);
        if (state is null)
        {
            return;
        }

        state.IsDeleted = true;

        await _dbContext.SaveChangesAsync(cancellationToken);
    }
}
