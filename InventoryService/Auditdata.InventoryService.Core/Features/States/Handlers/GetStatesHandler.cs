using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Features.States.Queries;
using Microsoft.EntityFrameworkCore;
using State = Auditdata.InventoryService.Core.Entities.State;

namespace Auditdata.InventoryService.Core.Features.States.Handlers;

public class GetStatesHandler : IRequestHandler<GetStatesQuery, IEnumerable<State>>
{
    private readonly IDbContext _dbContext;

    public GetStatesHandler(IDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    public async Task<IEnumerable<State>> Handle(GetStatesQuery request, CancellationToken cancellationToken)
    {
        var states = await _dbContext.States
            .AsNoTracking()
            .OrderBy(x => x.AttributeNumber)
            .ToListAsync(cancellationToken);

        return states;
    }
}
