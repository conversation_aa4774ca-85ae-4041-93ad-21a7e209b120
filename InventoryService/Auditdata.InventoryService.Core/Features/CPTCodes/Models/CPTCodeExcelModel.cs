using Auditdata.Infrastructure.Contracts.DataModel;
using Auditdata.Infrastructure.Excel.Models;

namespace Auditdata.InventoryService.Core.Features.CPTCodes.Models;

public class CPTCodeExcelModel : IIdentityGuidDataModel
{
    [ExcelColumn("ID", 1, Unique = true)]
    public Guid Id { get; set; }

    [ExcelColumn("Active", 2, Required = true)]
    public bool IsActive { get; set; }

    [ExcelColumn("CPT code", 3, Required = true, Unique = true)]
    public string Code { get; set; } = null!;

    [ExcelColumn("CPT code name", 4, Required = true)]
    public string Name { get; set; } = null!;

    [ExcelColumn("CPT description", 5)]
    public string? Description { get; set; }
}
