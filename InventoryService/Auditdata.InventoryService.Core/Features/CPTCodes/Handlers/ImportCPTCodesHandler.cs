using Auditdata.Infrastructure.Excel.Abstractions;
using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Features.CPTCodes.Commands;
using Auditdata.InventoryService.Core.Features.CPTCodes.Models;
using Auditdata.InventoryService.Core.Features.CPTCodes.Validators.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace Auditdata.InventoryService.Core.Features.CPTCodes.Handlers;

public class ImportCPTCodesHandler : IRequestHandler<ImportCPTCodesCommand, ImportCPTCodesResult>
{
    private const int MaxRowCount = 1000;

    private readonly IDbContext _dbContext;
    private readonly IMapper _mapper;
    private readonly IImportCPTCodesCoreValidator _validator;
    private readonly IExcelImporter _excelImporter;

    public ImportCPTCodesHandler(
        IDbContext dbContext,
        IMapper mapper,
        IImportCPTCodesCoreValidator validator,
        IExcelImporter excelImporter)
    {
        _dbContext = dbContext;
        _mapper = mapper;
        _validator = validator;
        _excelImporter = excelImporter;
    }

    public async Task<ImportCPTCodesResult> Handle(ImportCPTCodesCommand command, CancellationToken cancellationToken)
    {
        await using var fileStream = command.ExcelFile.OpenReadStream();

        var importResult = _excelImporter.FromXlsxStream<CPTCodeExcelModel>(fileStream, maxRowsCount: MaxRowCount);

        _validator.ValidateImportResult(importResult);

        var excelImportData = importResult.ImportData;
        var importData = importResult.ImportData.Select(x => x.Data).ToList();

        var codesWithFilledIds = excelImportData.Where(x => x.Data.Id != default).ToList();

        var filledIds = codesWithFilledIds.Select(x => x.Data.Id).ToList();

        await _validator.ValidateIfCodesAlreadyExistsAsync(filledIds, excelImportData, cancellationToken);

        var existingCptCodes = await _dbContext.CPTCodes
            .Where(c => filledIds.Contains(c.Id))
            .ToListAsync(cancellationToken);

        _validator.ValidateIsAllIdsFound(existingCptCodes, codesWithFilledIds);

        UpdateExistingCodes(importData, existingCptCodes);

        AddNewCodes(importData);

        await _dbContext.SaveChangesAsync(cancellationToken);

        return new ImportCPTCodesResult();
    }

    private void UpdateExistingCodes(
        IReadOnlyCollection<CPTCodeExcelModel> importData, IReadOnlyCollection<CPTCode> existingCptCodes)
    {
        var codesWithFilledIds = importData.Where(c => c.Id != default).ToList();
        foreach (var cptCode in existingCptCodes)
        {
            var updatedCodeModel = codesWithFilledIds.First(x => x.Id == cptCode.Id);
            _mapper.Map(updatedCodeModel, cptCode);
        }
    }

    private void AddNewCodes(IReadOnlyCollection<CPTCodeExcelModel> importData)
    {
        var newCptCodes = _mapper.Map<IReadOnlyCollection<CPTCode>>(importData.Where(c => c.Id == default));
        _dbContext.CPTCodes.AddRange(newCptCodes);
    }
}
