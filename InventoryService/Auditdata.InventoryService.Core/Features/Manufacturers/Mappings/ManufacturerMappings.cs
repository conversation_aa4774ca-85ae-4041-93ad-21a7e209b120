using Auditdata.InventoryService.Core.Features.Manufacturers.Commands;
using Auditdata.Microservice.Messages.Events.Manufacturer;
using Auditdata.Microservice.Messages.OriginEntities.Inventory;
using Manufacturer = Auditdata.InventoryService.Core.Entities.Manufacturer;

namespace Auditdata.InventoryService.Core.Features.Manufacturers.Mappings;

public class ManufacturerMappings : Profile
{
    public ManufacturerMappings()
    {
        CreateMap<CreateManufacturerCommand, Manufacturer>();
        CreateMap<UpdateManufacturerCommand, Manufacturer>();

        CreateMap<Manufacturer, ManufacturerCreated>()
            .ForMember(x => x.CreatedOn, opt => opt.MapFrom(y => y.CreationDate))
            .ForMember(x => x.ModifiedOn, opt => opt.MapFrom(y => y.ChangeDate));
        CreateMap<Manufacturer, ManufacturerUpdated>()
            .ForMember(x => x.CreatedOn, opt => opt.MapFrom(y => y.CreationDate))
            .ForMember(x => x.ModifiedOn, opt => opt.MapFrom(y => y.ChangeDate));
        CreateMap<Manufacturer, ManufacturerDeleted>()
            .ForMember(x => x.CreatedOn, opt => opt.MapFrom(y => y.CreationDate))
            .ForMember(x => x.ModifiedOn, opt => opt.MapFrom(y => y.ChangeDate));

        CreateMap<ManufacturerContact, ContactPerson>();
    }
}
