using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Abstractions.Validators;
using Auditdata.InventoryService.Core.Features.Manufacturers.Commands;
using Auditdata.InventoryService.Core.Features.Manufacturers.Models;

namespace Auditdata.InventoryService.Core.Features.Manufacturers.Handlers;

public class CreateManufacturerHandler : IRequestHandler<CreateManufacturerCommand, CreatedManufacturer>
{
    private readonly IMapper _mapper;
    private readonly IEventPublisher _eventPublisher;
    private readonly IDbContext _dbContext;
    private readonly IManufacturerValidator _validator;

    public CreateManufacturerHandler(
        IMapper mapper,
        IEventPublisher eventPublisher,
        IDbContext dbContext,
        IManufacturerValidator validator)
    {
        _mapper = mapper;
        _eventPublisher = eventPublisher;
        _dbContext = dbContext;
        _validator = validator;
    }

    public async Task<CreatedManufacturer> Handle(CreateManufacturerCommand request,
        CancellationToken cancellationToken)
    {
        await _validator.Validate(request.Name, cancellationToken);

        var manufacturer = _mapper.Map<Manufacturer>(request);
        manufacturer.Id = Guid.NewGuid();

        _dbContext.Manufacturers.Add(manufacturer);

        await _eventPublisher.ManufacturerCreated(manufacturer, cancellationToken);
        await _dbContext.SaveChangesAsync(cancellationToken);

        return new CreatedManufacturer(manufacturer);
    }
}
