using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Features.Manufacturers.Models;
using Auditdata.InventoryService.Core.Features.Manufacturers.Queries;
using Microsoft.EntityFrameworkCore;

namespace Auditdata.InventoryService.Core.Features.Manufacturers.Handlers;

public class GetManufacturersHandler : IRequestHandler<GetManufacturersQuery, GetManufacturersResult>
{
    private readonly IDbContext _dbContext;

    public GetManufacturersHandler(IDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    public async Task<GetManufacturersResult> Handle(GetManufacturersQuery request, CancellationToken cancellationToken)
    {
        var manufacturers = await _dbContext.Manufacturers
            .Include(x => x.Country)
            .Where(x => x.IsActive)
            .ToListAsync(cancellationToken);

        return new GetManufacturersResult(manufacturers);
    }
}
