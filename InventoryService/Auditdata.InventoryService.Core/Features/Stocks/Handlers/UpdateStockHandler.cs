using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Exceptions;
using Auditdata.InventoryService.Core.Features.Stocks.Commands;
using Microsoft.EntityFrameworkCore;

namespace Auditdata.InventoryService.Core.Features.Stocks.Handlers;

public class UpdateStockHandler : IRequestHandler<UpdateStockCommand>
{
    private readonly IDbContext _dbContext;
    private readonly IEventPublisher _eventPublisher;

    public UpdateStockHandler(IDbContext dbContext, IEventPublisher eventPublisher)
    {
        _dbContext = dbContext;
        _eventPublisher = eventPublisher;
    }

    public async Task Handle(UpdateStockCommand request, CancellationToken cancellationToken)
    {
        var stock = await _dbContext.Stocks
            .FirstOrDefaultAsync(x => x.LocationId == request.LocationId, cancellationToken)
            ?? throw new EntityNotFoundException<Stock>(Guid.Empty, $"Stock with location id {request.LocationId} not found");
        stock.Name = request.Name;
        stock.RegionId = request.RegionId;
        stock.ParentLocationId = request.ParentLocationId;

        await _eventPublisher.StockUpdated(stock, cancellationToken);

        await _dbContext.SaveChangesAsync(cancellationToken);
    }
}
