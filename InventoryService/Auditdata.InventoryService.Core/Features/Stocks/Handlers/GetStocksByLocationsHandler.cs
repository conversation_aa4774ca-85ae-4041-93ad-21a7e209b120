using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Features.Stocks.Models;
using Auditdata.InventoryService.Core.Features.Stocks.Queries;
using Microsoft.EntityFrameworkCore;

namespace Auditdata.InventoryService.Core.Features.Stocks.Handlers;

public class GetStocksByLocationsHandler : IRequestHandler<GetStocksByLocationsQuery, GetStocksResult>
{
    private readonly IDbContext _dbContext;

    public GetStocksByLocationsHandler(IDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    public async Task<GetStocksResult> Handle(GetStocksByLocationsQuery request, CancellationToken cancellationToken)
    {
        var query = _dbContext.Stocks
            .AsNoTracking();

        if (request.LocationIds?.Any() ?? false)
        {
            query = query.Where(x => request.LocationIds.Contains(x.LocationId));
        }

        if (request.RegionId is not null)
        {
            query = query.Where(x => x.RegionId == request.RegionId);
        }
        
        var stocks = await query
            .OrderBy(x => x.Name)
            .ToListAsync(cancellationToken);
        return new GetStocksResult(stocks);
    }
}
