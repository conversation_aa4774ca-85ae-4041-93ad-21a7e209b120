using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Features.Stocks.Models;
using Auditdata.InventoryService.Core.Features.Stocks.Queries;
using Microsoft.EntityFrameworkCore;

namespace Auditdata.InventoryService.Core.Features.Stocks.Handlers;

public class GetStocksHandler : IRequestHandler<GetStocksQuery, GetStocksResult>
{
    private readonly IDbContext _dbContext;

    public GetStocksHandler(IDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    public async Task<GetStocksResult> Handle(GetStocksQuery request, CancellationToken cancellationToken)
    {
        var query = _dbContext.Stocks.AsNoTracking();
        
        if (request.RegionId is not null)
        {
            query = query.Where(x => x.RegionId == request.RegionId);
        }
        
        var stocks = await query.ToListAsync(cancellationToken);
        return new GetStocksResult(stocks);
    }
}
