using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Abstractions.Services;
using Auditdata.InventoryService.Core.Abstractions.Validators;
using Auditdata.InventoryService.Core.Constants;
using Auditdata.InventoryService.Core.Entities.StockProductItems;
using Auditdata.InventoryService.Core.Exceptions;
using Auditdata.InventoryService.Core.Features.Orders.Commands;
using Auditdata.InventoryService.Core.Features.Orders.Events;
using Auditdata.Transport.Contracts.Exceptions;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Auditdata.InventoryService.Core.Features.Orders.Handlers;

public class ReserveProductItemsFromOrderHandler :
    IRequestHandler<ReserveProductItemsFromOrderCommand, ReserveProductItemsFromOrderResult>
{
    private readonly ILogger<ReserveProductItemsFromOrderHandler> _logger;
    private readonly ISerialNumbersValidator _serialNumbersValidator;
    private readonly ISerialNumbersService _serialNumbersService;
    private readonly IDbContext _dbContext;
    private readonly IMediator _mediator;

    public ReserveProductItemsFromOrderHandler(
        ILogger<ReserveProductItemsFromOrderHandler> logger,
        ISerialNumbersValidator serialNumbersValidator,
        ISerialNumbersService serialNumbersService,
        IDbContext dbContext,
        IMediator mediator)
    {
        _logger = logger;
        _serialNumbersValidator = serialNumbersValidator;
        _serialNumbersService = serialNumbersService;
        _dbContext = dbContext;
        _mediator = mediator;
    }

    public async Task<ReserveProductItemsFromOrderResult> Handle(
        ReserveProductItemsFromOrderCommand command, CancellationToken cancellationToken)
    {
        try
        {
            var serializedItemsData = new List<SerializedItemData>();
            var orderProductItemReservations = new List<OrderProductItemReservation>();
            foreach (var orderProductItem in command.OrderProductItems)
            {
                try
                {
                    if (orderProductItem is { IsSerialized: true, IsControlledByStock: true })
                    {
                        var serializedItemData = await ReserveSerializedItemAsync(
                            command, orderProductItem, cancellationToken);
                        serializedItemsData.Add(serializedItemData);
                        orderProductItemReservations.Add(new OrderProductItemReservation
                        {
                            OrderProductId = serializedItemData.OrderProductId,
                            SerializedProductItems = serializedItemData.StockProductItems
                                .Select(x => new SerializedProductItem(x.Id, x.SerialNumber!))
                        });
                        continue;
                    }

                    var nonSerializedReservation = await ReserveNonSerializedItemAsync(
                        command.LocationId, orderProductItem, cancellationToken);
                    orderProductItemReservations.Add(nonSerializedReservation);
                }
                catch (BusinessException businessException)
                {
                    throw new ReserveProductItemsFromOrderException(orderProductItem.OrderProductId, businessException);
                }
            }

            var serialNumbersByManufacturer = serializedItemsData
                .GroupBy(
                    x => x.ManufacturerId,
                    x => x.StockProductItems.Select(item => new { x.OrderProductId, SerialNumber = item.SerialNumber! })
                );
            foreach (var manufacturer in serialNumbersByManufacturer)
            {
                try
                {
                    await _serialNumbersValidator.CheckDuplicatedSerialNumbers(
                        manufacturer.Key,
                        manufacturer.SelectMany(x => x.Select(y => y.SerialNumber)),
                        cancellationToken);
                }
                catch (BusinessException businessException)
                {
                    var orderProductId = manufacturer.SelectMany(x => x).First().OrderProductId;
                    throw new ReserveProductItemsFromOrderException(orderProductId, businessException);
                }
            }

            await _dbContext.SaveChangesAsync(cancellationToken);

            return new ReserveProductItemsFromOrderResult(orderProductItemReservations);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unable to reserve the product item {@Command}", command);
            throw;
        }
    }

    private async Task<SerializedItemData> ReserveSerializedItemAsync(
        ReserveProductItemsFromOrderCommand command,
        OrderProductItem orderProductItem,
        CancellationToken cancellationToken)
    {
        var stockProduct = await _dbContext.StockProducts
            .Include(x => x.Product)
            .Include(x => x.Stock)
            .FirstOrDefaultAsync(
                x => x.ProductId == orderProductItem.ProductId && x.Stock.LocationId == command.LocationId, cancellationToken)
            ?? throw new BusinessException(
                "Stock product not found for reservation",
                ErrorCodes.StockProductNotFoundForReservation,
                command.LocationId,
                orderProductItem.ProductId);
        var serialNumbers = (orderProductItem.SerialNumbers?.Any() ?? false) ?
                    orderProductItem.SerialNumbers.ToList() :
                    GenerateSerialNumbersFromRange(orderProductItem);
        if (serialNumbers.Count != orderProductItem.Quantity)
        {
            throw new NotAllSerialNumbersAreProvidedForOrderException("Ensure each S/N is on a new line and all required numbers are provided.", 
                ErrorCodes.NotAllSerialNumbersAreProvided, serialNumbers);
        }

        await _serialNumbersValidator.CheckDuplicatedSerialNumbers(
            stockProduct.Product.ManufacturerId!.Value, serialNumbers, cancellationToken);

        var stockProductItems = CreateStockProductItems(
                stockProduct, serialNumbers, command.PatientId, command.OrderId, command.SaleId,
                command.IsPurchaseOrder, orderProductItem)
            .ToList();
        _dbContext.StockProductItems.AddRange(stockProductItems);

        await _mediator.Publish(
            new StockProductItemsReservedByOrderEvent(
                stockProductItems,
                command.PatientName,
                orderProductItem.Quantity,
                orderProductItem.StockProductItemShouldBeReserved),
            cancellationToken);

        return new SerializedItemData(orderProductItem.OrderProductId, stockProduct.Product.ManufacturerId.Value, stockProductItems);
    }

    private async Task<OrderProductItemReservation> ReserveNonSerializedItemAsync(
        Guid locationId, OrderProductItem orderProductItem, CancellationToken cancellationToken)
    {
        var stockProduct = await _dbContext.StockProducts.Include(x => x.Product).Include(x => x.Stock)
            .FirstOrDefaultAsync(
                x => x.ProductId == orderProductItem.ProductId && x.Stock.LocationId == locationId, cancellationToken)
            ?? throw new BusinessException(
                "Stock product not found for reservation",
                ErrorCodes.StockProductNotFoundForReservation,
                locationId,
                orderProductItem.ProductId);

        stockProduct.AdjustQuantity(orderProductItem.Quantity);
        _dbContext.StockProducts.Update(stockProduct);

        return new OrderProductItemReservation { OrderProductId = orderProductItem.OrderProductId };
    }

    private List<string> GenerateSerialNumbersFromRange(OrderProductItem orderProductItem)
    {
        var serialNumbers = _serialNumbersService.GenerateRange(
            orderProductItem.GenerateSerialNumberFromRange!.SerialNumberFrom,
            orderProductItem.GenerateSerialNumberFromRange.SerialNumberTo).ToList();
        if (serialNumbers.Count > orderProductItem.Quantity)
        {
            throw new InvalidNumberOfSerialNumbersException(ErrorCodes.InvalidNumberOfSerialNumbers, orderProductItem.Quantity, serialNumbers.Count);
        }

        return serialNumbers;
    }

    private static IEnumerable<StockProductItem> CreateStockProductItems(
        StockProduct stockProduct,
        IList<string> serialNumbers,
        Guid? patientId,
        Guid orderId,
        Guid? saleId,
        bool isPurchaseOrder,
        OrderProductItem orderProductItem)
    {
        var stockProductItemStatus = orderProductItem.StockProductItemShouldBeReserved
                ? StockProductItemStatus.ReservedByOrder
                : StockProductItemStatus.Available;

        foreach (var serialNumber in serialNumbers)
        {
            var stockProductItem = new StockProductItem
            {
                Status = stockProductItemStatus,
                SerialNumber = serialNumber,
                StockProduct = stockProduct,
                BatteryTypeId = orderProductItem.BatteryTypeId,
                ColorId = orderProductItem.ColorId,
                Attributes = orderProductItem.Attributes.Select(x =>
                    StockProductItemAttribute.Create(x.Id,
                        new StockProductItemAttributeValue { ValueId = x.Value.Id, Value = x.Value.Value })).ToList(),
                PatientId = patientId,
                OrderId = orderId,
                SaleId = saleId,
                Logs = new List<StockProductItemLog>()
            };
            
            if (isPurchaseOrder)
            {
                stockProductItem.Reserve(saleId);
            }

            if (stockProductItem.Status == StockProductItemStatus.Available)
            {
                stockProduct.AdjustQuantity(1);
            }

            yield return stockProductItem;
        }
    }

    private sealed record SerializedItemData(Guid OrderProductId, Guid ManufacturerId, List<StockProductItem> StockProductItems);
}
