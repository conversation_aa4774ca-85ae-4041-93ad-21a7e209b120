using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Features.Orders.Models;
using Auditdata.InventoryService.Core.Features.Orders.Queries;
using Auditdata.InventoryService.Core.OperationContext;
using Auditdata.Transport.Contracts.PageResults;
using Auditdata.Transport.Contracts.Table;
using Microsoft.EntityFrameworkCore;

namespace Auditdata.InventoryService.Core.Features.Orders.Handlers;

public class GetOrderProductsHandler : IRequestHandler<GetOrderProductsQuery, GetOrderProductsResult>
{
    private readonly IDbContext _dbContext;
    private readonly IMapper _mapper;
    private readonly IInventoryServiceOperationContext _operationContext;

    public GetOrderProductsHandler(
        IDbContext dbContext,
        IMapper mapper,
        IInventoryServiceOperationContext operationContext)
    {
        _dbContext = dbContext;
        _mapper = mapper;
        _operationContext = operationContext;
    }

    public async Task<GetOrderProductsResult> Handle(GetOrderProductsQuery request, CancellationToken cancellationToken)
    {
        var parameters = _mapper.Map<OrderSearchParameters>(request);
        var stockProducts = await GetOrderableStockProductsAsync(parameters);

        stockProducts.Rows = stockProducts.Rows!.Select(x =>
        {
            if (_operationContext.UserCanViewProductCost)
            {
                x.Model!.Product.Cost = x.Model.Product.Cost ?? decimal.Zero;
            }
            else
            {
                x.Model!.Product.Cost = null;
            }
            return x;
        }).ToArray();

        return new GetOrderProductsResult(stockProducts);
    }

    private async Task<TablePageResult<StockProduct>> GetOrderableStockProductsAsync(OrderSearchParameters parameters)
    {
        var query = _dbContext.StockProducts.AsNoTracking()
            .Include(x => x.Product)
                .ThenInclude(x => x.Manufacturer)
            .Include(x => x.Product)
                .ThenInclude(x => x.Category)
            .Include(x => x.Product)
                .ThenInclude(x => x.BatteryTypes)
                    .ThenInclude(x => x.BatteryType)
            .Include(x => x.Product)
                .ThenInclude(x => x.Colors)
                    .ThenInclude(x => x.Color)
            .Include(x => x.Product)
                .ThenInclude(x => x.Attributes)
                    .ThenInclude(x => x.Attribute)
            .Where(x =>
                x.Stock.LocationId == parameters.LocationId &&
                x.Product.SupplierId == parameters.SupplierId &&
            x.Product.IsActive &&
                x.Product.Category.Code != ProductCategoryCode.Service &&
                x.Product.Category.Code != ProductCategoryCode.RepairService);

        if (parameters.ManufacturerId != null)
        {
            query = query.Where(x => x.Product.ManufacturerId == parameters.ManufacturerId);
        }

        if (parameters.CategoryId != null)
        {
            query = query.Where(x => x.Product.CategoryId == parameters.CategoryId);
        }

        if (!string.IsNullOrEmpty(parameters.SearchText))
        {
            query = query.Where(x => x.Product.Name.Contains(parameters.SearchText));
        }

        var (result, total) = await query.ApplyTableQueryAsync(parameters.Page, parameters.PerPage, parameters.OrderBy);
        return new TablePageResult<StockProduct>(
            result.Select(x => new TableRow<StockProduct>(x)).ToArray(), parameters, total);
    }
}
