using Auditdata.InventoryService.Contracts.Commands;
using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Abstractions.Azure;
using Auditdata.InventoryService.Core.Features.Orders.Events;
using StockTransactionType = Auditdata.InventoryService.Contracts.StockTransactionType;

namespace Auditdata.InventoryService.Core.Features.Orders.Handlers;

public class StockProductItemReservedByOrderEventHandler : INotificationHandler<StockProductItemsReservedByOrderEvent>
{
    private readonly IDbContext _dbContext;
    private readonly IAzureServiceBusPublisher _azureServiceBusPublisher;
    private readonly IEventPublisher _eventPublisher;

    public StockProductItemReservedByOrderEventHandler(
        IDbContext dbContext,
        IAzureServiceBusPublisher azureServiceBusPublisher,
        IEventPublisher eventPublisher)
    {
        _dbContext = dbContext;
        _azureServiceBusPublisher = azureServiceBusPublisher;
        _eventPublisher = eventPublisher;
    }

    public async Task Handle(StockProductItemsReservedByOrderEvent notification, CancellationToken cancellationToken)
    {
        foreach (var stockProductItem in notification.StockProductItems)
        {
            var adjustedActionLog = StockProductItemLog.StockAdjustedActionLog(stockProductItem);
            var serialNumberAddedLog = StockProductItemLog.SerialNumberAddedActionLog(stockProductItem);

            _dbContext.StockProductItemLogs.Add(adjustedActionLog);
            _dbContext.StockProductItemLogs.Add(serialNumberAddedLog);

            await _eventPublisher.StockProductItemCreated(stockProductItem, cancellationToken);

            if (notification.ReserveWithOrder)
            {
                var reserveByOrderLog =
                    StockProductItemLog.ReservationByOrderActionLog(stockProductItem, notification.PatientName);
                _dbContext.StockProductItemLogs.Add(reserveByOrderLog);
            }

            await _eventPublisher.StockProductItemReservedByOrder(
                stockProductItem.Id,
                stockProductItem.OrderId!.Value,
                cancellationToken);
        }

        var stockProduct = notification.StockProductItems.First().StockProduct;

        await _eventPublisher.SerializedStockAdjusted(
            stockProduct,
            notification.Quantity,
            notification.StockProductItems,
            cancellationToken);

        await CreateTransactionAsync(stockProduct, notification.Quantity, StockTransactionType.StockAdjustment);
        await CreateTransactionAsync(stockProduct, notification.Quantity, StockTransactionType.Order);
    }

    private async Task CreateTransactionAsync(StockProduct stockProduct, int quantity, StockTransactionType stockTransactionType)
    {
        await _azureServiceBusPublisher.PublishAddStockTransactionCommand(new AddStockTransactionCommand
        {
            StockId = stockProduct.StockId,
            ProductId = stockProduct.ProductId,
            Type = stockTransactionType,
            Quantity = quantity
        });
    }
}
