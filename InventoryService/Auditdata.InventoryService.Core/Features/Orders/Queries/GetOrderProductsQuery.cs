using Auditdata.InventoryService.Core.Features.Orders.Models;
using Auditdata.Transport.Contracts.Table;

namespace Auditdata.InventoryService.Core.Features.Orders.Queries;

public class GetOrderProductsQuery : TableQueryBase, IRequest<GetOrderProductsResult>
{
    public Guid LocationId { get; set; }
    public Guid SupplierId { get; set; }
    public Guid? CategoryId { get; set; }
    public Guid? ManufacturerId { get; set; }
    public string? SearchText { get; set; }
}
