using Auditdata.InventoryService.Core.Features.Orders.Models;
using Auditdata.Microservice.Messages.OriginEntities.Inventory;

namespace Auditdata.InventoryService.Core.Features.Orders.Commands;

public class ReserveProductItemsFromOrderCommand : IRequest<ReserveProductItemsFromOrderResult>
{
    public Guid OrderId { get; set; }
    public Guid LocationId { get; set; }
    public Guid? PatientId { get; set; }
    public string? PatientName { get; set; }
    public Guid? SaleId { get; set; }
    public bool IsPurchaseOrder { get; set; }
    public IEnumerable<OrderProductItem> OrderProductItems { get; set; } = Enumerable.Empty<OrderProductItem>();
}

public class OrderProductItem
{
    public Guid OrderProductId { get; set; }
    public Guid ProductId { get; set; }
    public int Quantity { get; set; }
    public bool StockProductItemShouldBeReserved { get; set; }
    public bool IsControlledByStock { get; set; }
    public bool IsSerialized { get; set; }
    public IEnumerable<string> SerialNumbers { get; set; } = Enumerable.Empty<string>();
    public OrderProductItemSerialRange? GenerateSerialNumberFromRange { get; set; }
    public Guid? BatteryTypeId { get; set; }
    public Guid? ColorId { get; set; }
    public IEnumerable<OrderAttribute> Attributes { get; set; } = Enumerable.Empty<OrderAttribute>();
}

public record ReserveProductItemsFromOrderResult(IEnumerable<OrderProductItemReservation> OrderProductItemReservations);

public class OrderProductItemReservation
{
    public Guid OrderProductId { get; set; }
    public IEnumerable<SerializedProductItem> SerializedProductItems { get; set; } = Enumerable.Empty<SerializedProductItem>();
}

public record SerializedProductItem(Guid Id, string SerialNumber);
