using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Features.Pathways.Models;
using Auditdata.InventoryService.Core.Features.Pathways.Queries;
using Microsoft.EntityFrameworkCore;

namespace Auditdata.InventoryService.Core.Features.Pathways.Handlers;

public class GetPathwaysHandler : IRequestHandler<GetPathwaysQuery, GetPathwaysResult>
{
    private readonly IDbContext _dbContext;

    public GetPathwaysHandler(IDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    public async Task<GetPathwaysResult> Handle(GetPathwaysQuery request, CancellationToken cancellationToken)
    {
        var pathways = await _dbContext.Pathways.ToListAsync(cancellationToken);
        return new GetPathwaysResult(pathways);
    }
}
