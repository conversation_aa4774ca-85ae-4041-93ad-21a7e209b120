using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Features.StockTransactions.Models;
using Auditdata.InventoryService.Core.Features.StockTransactions.Queries;
using Microsoft.EntityFrameworkCore;

namespace Auditdata.InventoryService.Core.Features.StockTransactions.Handlers;

public class GetStockTransactionsHandler : IRequestHandler<GetStockTransactionsQuery, GetStockTransactionsResult>
{
    private readonly IDbContext _dbContext;

    public GetStockTransactionsHandler(IDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    public async Task<GetStockTransactionsResult> Handle(GetStockTransactionsQuery request, CancellationToken cancellationToken)
    {
        var stockTransactions = await _dbContext.StockTransactions
            .Include(x => x.Stock)
            .ToListAsync(cancellationToken);

        return new GetStockTransactionsResult(stockTransactions);
    }
}
