using Auditdata.InventoryService.Core.Features.StockTransactions.Models;
using Auditdata.InventoryService.Core.Features.StockTransactions.Queries;

namespace Auditdata.InventoryService.Core.Features.StockTransactions.Handlers;

public class GetStockTransactionsByProductHandler : IRequestHandler<GetStockTransactionsByProductQuery, GetStockTransactionsResult>
{
    private readonly IStockTransactionsRepository _stockTransactionsRepository;

    public GetStockTransactionsByProductHandler(IStockTransactionsRepository stockTransactionsRepository)
    {
        _stockTransactionsRepository = stockTransactionsRepository;
    }

    public async Task<GetStockTransactionsResult> Handle(GetStockTransactionsByProductQuery request, CancellationToken cancellationToken)
    {
        var stockTransactions = await _stockTransactionsRepository.GetByProductIdAndLocationIdsAsync(request.ProductId,
            request.LocationIds?.ToList() ?? new List<Guid>());
        return new GetStockTransactionsResult(stockTransactions);
    }
}
