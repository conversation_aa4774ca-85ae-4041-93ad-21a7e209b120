using Auditdata.InventoryService.Core.Abstractions;
using Microsoft.EntityFrameworkCore;

namespace Auditdata.InventoryService.Core.Features.StockTransactions;

public class TransactionNumberBuilder : ITransactionNumberBuilder
{
    private readonly IDbContext _dbContext;
    private readonly IDictionary<StockTransactionType, string> _numberPrefixes =
        new Dictionary<StockTransactionType, string>
        {
            [StockTransactionType.Order] = "SO",
            [StockTransactionType.Sales] = "SI",
            [StockTransactionType.Transfer] = "TX",
            [StockTransactionType.StockAdjustment] = "SA",
            [StockTransactionType.DeliveryNote] = "DN",
            [StockTransactionType.CreditNote] = "CN",
            [StockTransactionType.PreOrder] = "PO",
            [StockTransactionType.Replacement] = "RP",
            [StockTransactionType.Unreserved] = "UR",
            [StockTransactionType.ReturnToSupplier] = "RS",
            [StockTransactionType.Trial] = "TR",
            [StockTransactionType.TrialCancellation] = "TC"
        };

    public TransactionNumberBuilder(IDbContext dbContext)
    {
        _dbContext = dbContext;
    }
    
    public async Task<string> BuildAsync(StockTransactionType stockTransactionType, Guid stockId)
    {
        var prefix = _numberPrefixes[stockTransactionType];
        var typeCount = await _dbContext.StockTransactions
            .CountAsync(x => x.TypeId == stockTransactionType && x.StockId == stockId) + 1;

        return prefix + "-" + typeCount.ToString().PadLeft(7, '0');
    }
}
