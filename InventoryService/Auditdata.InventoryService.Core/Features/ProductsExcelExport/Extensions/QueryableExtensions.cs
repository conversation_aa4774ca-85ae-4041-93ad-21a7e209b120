using Auditdata.InventoryService.Core.Entities.Products;
using Auditdata.InventoryService.Core.Features.ProductsExcelExport.Queries;

namespace Auditdata.InventoryService.Core.Features.ProductsExcelExport.Extensions;

public static class QueryableExtensions
{
    public static IQueryable<Product> AddSearchQuery(this IQueryable<Product> query, ProductsExportQuery exportQuery)
    {
        if (exportQuery.ManufacturerId != null)
        {
            query = query.Where(x => x.ManufacturerId == exportQuery.ManufacturerId);
        }

        if (exportQuery.SearchText != null)
        {
            query = query.Where(x => x.Name.Contains(exportQuery.SearchText)
                || x.DescriptionValue!.Contains(exportQuery.SearchText));
        }

        return query.Where(x => !exportQuery.ActiveOnly || x.IsActive)
                    .Where(x => x.Category.Code == exportQuery.CategoryCode);
    }
}
