using Auditdata.InventoryService.Core.Features.ProductCategoryAccountCodes.Models;
using Auditdata.InventoryService.Core.Features.ProductCategoryAccountCodes.Queries;

namespace Auditdata.InventoryService.Core.Features.ProductCategoryAccountCodes.Handlers;

public class GetProductCategoryAccountCodesQueryHandler : IRequestHandler<GetProductCategoryAccountCodesQuery,
    List<GetProductCategoryAccountCodesResult>>
{
    private readonly IProductCategoryAccountCodesRepository _accountCodesRepository;
    private readonly IProductCategoriesRepository _productCategoriesRepository;


    public GetProductCategoryAccountCodesQueryHandler(IProductCategoryAccountCodesRepository accountCodesRepository, IProductCategoriesRepository productCategoriesRepository)
    {
        _accountCodesRepository = accountCodesRepository;
        _productCategoriesRepository = productCategoriesRepository;
    }

    public async Task<List<GetProductCategoryAccountCodesResult>> Handle(GetProductCategoryAccountCodesQuery request,
        CancellationToken cancellationToken)
    {
        var result = new List<GetProductCategoryAccountCodesResult>();
        var productCategoryAccountCodes =
            (await _accountCodesRepository.GetAsync()).ToList();

        var productCategories = await _productCategoriesRepository.GetAsync();

        foreach (var productCategory in productCategories)
        {
            string accountCode = string.Empty;

            var productCategoryAccountCode =
                productCategoryAccountCodes.FirstOrDefault(x => x.ProductCategoryId == productCategory.Id);

            if (productCategoryAccountCode is not null)
                accountCode = productCategoryAccountCode.AccountCode;

            result.Add(new GetProductCategoryAccountCodesResult(productCategory.Id, productCategory.Name, accountCode));
        }

        return result.OrderBy(x=> x.ProductCategoryName).ToList();
    }
}
