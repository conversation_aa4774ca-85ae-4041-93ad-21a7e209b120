using Auditdata.Infrastructure.Excel.Extensions;
using Auditdata.Infrastructure.Excel.Models;
using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Constants;
using Auditdata.InventoryService.Core.Entities.Products;
using Auditdata.InventoryService.Core.Extensions.Validation;
using Auditdata.InventoryService.Core.Features.StockKeepingUnits.Models.Excel.Abstractions;
using Auditdata.InventoryService.Core.Features.StockKeepingUnits.Validation.Interfaces;
using Auditdata.Transport.Contracts.Exceptions;
using Microsoft.EntityFrameworkCore;

namespace Auditdata.InventoryService.Core.Features.StockKeepingUnits.Validation;

public class ImportSkusCoreValidator : IImportSkusCoreValidator
{
    private readonly IDbContext _dbContext;

    public ImportSkusCoreValidator(IDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    public void ValidateImportResult<T>(ExcelImportResult<T> importResult) where T : SkuExcelModelBase
    {
        importResult.ValidateImportResult();
    }

    public void ValidateCategoryExists(ProductCategoryCode categoryCode, ProductCategory? category)
    {
        if (category == null)
        {
            throw new BusinessException($"Product category with code {categoryCode} not found",
                ErrorCodes.ProductCategoryNotFound,
                categoryCode);
        }
    }

    public async Task ValidateIfSkusAlreadyExistsAsync<T>(IReadOnlyCollection<Guid> filledIdsSet,
        IReadOnlyCollection<ExcelImportData<T>> excelImportData, CancellationToken cancellationToken)
        where T : SkuExcelModelBase
    {
        var filledNamesSet = excelImportData.Select(x => x.Data.Name).ToList();

        var alreadyExistsSkuNames = await _dbContext.StockKeepingUnits.AsNoTracking().Where(
            c => !filledIdsSet.Contains(c.Id) && filledNamesSet.Contains(c.Name.Trim()))
            .Select(c => c.Name.Trim())
            .ToListAsync(cancellationToken);
        if (alreadyExistsSkuNames.Count > 0)
        {
            var rows = excelImportData.Where(x => alreadyExistsSkuNames
                .Exists(y => string.Equals(x.Data.Name, y, StringComparison.OrdinalIgnoreCase)))
                .Select(x => x.RowNumber);
            throw new BusinessException(
                $"Cannot import. Names already exist in rows: {string.Join(", ", rows)}.",
                ErrorCodes.SkuNameAlreadyExists,
                rows.Cast<object>().ToArray());
        }
    }

    public void ValidateIsAllIdsFound<T>(IReadOnlyCollection<StockKeepingUnit> existingSkus,
        IReadOnlyCollection<ExcelImportData<T>> skusWithFilledIds) where T : SkuExcelModelBase
    {
        existingSkus.ValidateIsAllIdsFound(skusWithFilledIds);
    }

    public void ValidateProducts<T>(IReadOnlyDictionary<string, Product> products, ExcelImportResult<T> importResult)
        where T : SkuExcelModelBase
    {
        var notFoundProducts = importResult.ImportData.Where(x => !products.ContainsKey(x.Data.ProductName));
        ThrowIfAnyInvalidData(importResult, notFoundProducts, nameof(SkuExcelModelBase.ProductName));

        var notFoundSuppliers = importResult.ImportData.Where(x =>
        {
            var supplier = products[x.Data.ProductName].Supplier;
            return (supplier == null && !string.IsNullOrWhiteSpace(x.Data.Supplier))
                || (supplier != null && !string.Equals(supplier.Name.Trim(), x.Data.Supplier, StringComparison.OrdinalIgnoreCase));
        });
        ThrowIfAnyInvalidData(importResult, notFoundSuppliers, nameof(SkuExcelModelBase.Supplier));

        if (typeof(T).IsAssignableTo(typeof(ISkuModelWithBatteryType)))
        {
            var notFoundBatteryTypes = importResult.ImportData.Where(x => !products[x.Data.ProductName].BatteryTypes
                .Any(y =>
                string.Equals(y.BatteryType.Name.Trim(), ((ISkuModelWithBatteryType)x.Data).BatteryType, StringComparison.OrdinalIgnoreCase)));
            ThrowIfAnyInvalidData(importResult, notFoundBatteryTypes, nameof(ISkuModelWithBatteryType.BatteryType));
        }

        if (typeof(T).IsAssignableTo(typeof(ISkuModelWithColor)))
        {
            var notFoundColors = importResult.ImportData.Where(x => !products[x.Data.ProductName].Colors
                .Any(y => string.Equals(y.Color.Name.Trim(), ((ISkuModelWithColor)x.Data).Color, StringComparison.OrdinalIgnoreCase)));
            ThrowIfAnyInvalidData(importResult, notFoundColors, nameof(ISkuModelWithColor.Color));
        }
    }

    public async Task ValidateIfSkusUniqueAsync<T>(IReadOnlyCollection<Guid> filledIdsSet,
        IReadOnlyDictionary<string, Product> products,
        IEnumerable<StockKeepingUnit> existingSkus, IEnumerable<StockKeepingUnit> newSkus,
        IReadOnlyCollection<ExcelImportData<T>> excelImportData, CancellationToken cancellationToken)
        where T : SkuExcelModelBase
    {
        var productIds = products.Values.Select(x => x.Id).ToList();

        var notChangedSkus = await _dbContext.StockKeepingUnits.AsNoTracking().Where(c => 
            !filledIdsSet.Contains(c.Id) && productIds.Contains(c.ProductId))
            .Select(x => new { x.Id, x.ProductId, x.SupplierId, x.BatteryTypeId, x.ColorId })
            .ToListAsync(cancellationToken);

        var allSkus = notChangedSkus.Concat(existingSkus.Concat(newSkus)
            .Select(x => new { x.Id, x.ProductId, x.SupplierId, x.BatteryTypeId, x.ColorId }));

        var duplicates = allSkus.GroupBy(x => new { x.ProductId, x.SupplierId, x.BatteryTypeId, x.ColorId })
            .Where(g => g.Count() > 1)
            .SelectMany(g => g.Select(x => x.Id))
            .ToList();

        if (duplicates.Count > 0)
        {
            var rows = excelImportData.Where(x => duplicates.Contains(x.Data.Id))
                .Select(x => x.RowNumber);
            throw new BusinessException(
                $"Cannot import. Data duplicates found in rows: {string.Join(", ", rows)}.",
                ErrorCodes.SkuContainsDuplicates,
                rows.Cast<object>().ToArray());
        }
    }

    private static void ThrowIfAnyInvalidData<T>(
        ExcelImportResult<T> importResult, IEnumerable<ExcelImportData<T>> invalidData, string propertyName)
    {
        if (invalidData.Any())
        {
            var exception = importResult.GetExceptionForData(invalidData,
                propertyName);
            throw new BusinessException(exception.Message,
                ErrorCodes.ExcelInvalidRecords,
                exception.PlaceholderValues);
        }
    }
}
