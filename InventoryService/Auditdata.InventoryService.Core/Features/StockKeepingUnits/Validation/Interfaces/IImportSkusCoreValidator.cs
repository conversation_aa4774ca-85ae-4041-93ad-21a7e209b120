using Auditdata.Infrastructure.Excel.Models;
using Auditdata.InventoryService.Core.Entities.Products;
using Auditdata.InventoryService.Core.Features.StockKeepingUnits.Models.Excel.Abstractions;

namespace Auditdata.InventoryService.Core.Features.StockKeepingUnits.Validation.Interfaces;

public interface IImportSkusCoreValidator
{
    void ValidateCategoryExists(ProductCategoryCode categoryCode, ProductCategory? category);

    void ValidateImportResult<T>(ExcelImportResult<T> importResult) where T : SkuExcelModelBase;

    Task ValidateIfSkusAlreadyExistsAsync<T>(IReadOnlyCollection<Guid> filledIdsSet,
        IReadOnlyCollection<ExcelImportData<T>> excelImportData, CancellationToken cancellationToken)
        where T : SkuExcelModelBase;

    void ValidateIsAllIdsFound<T>(IReadOnlyCollection<StockKeepingUnit> existingSkus,
        IReadOnlyCollection<ExcelImportData<T>> skusWithFilledIds) where T : SkuExcelModelBase;

    void ValidateProducts<T>(IReadOnlyDictionary<string, Product> products, ExcelImportResult<T> importResult)
        where T : SkuExcelModelBase;

    Task ValidateIfSkusUniqueAsync<T>(IReadOnlyCollection<Guid> filledIdsSet,
        IReadOnlyDictionary<string, Product> products,
        IEnumerable<StockKeepingUnit> existingSkus, IEnumerable<StockKeepingUnit> newSkus,
        IReadOnlyCollection<ExcelImportData<T>> excelImportData, CancellationToken cancellationToken) where T : SkuExcelModelBase;
}
