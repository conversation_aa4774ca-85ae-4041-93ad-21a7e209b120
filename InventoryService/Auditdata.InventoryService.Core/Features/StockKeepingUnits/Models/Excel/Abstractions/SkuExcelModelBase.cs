using Auditdata.Infrastructure.Contracts.DataModel;
using Auditdata.Infrastructure.Excel.Models;

namespace Auditdata.InventoryService.Core.Features.StockKeepingUnits.Models.Excel.Abstractions;

public abstract class SkuExcelModelBase : IIdentityGuidDataModel
{
    public Guid Id { get => NullableId ?? Guid.Empty; set => NullableId = value; }

    [ExcelColumn("ID", 100, Unique = true)]
    public Guid? NullableId { get; set; }

    [ExcelColumn("SKU", 200, Required = true, Unique = true)]
    public string Name { get; set; } = null!;

    [ExcelColumn("Product name", 300, Required = true)]
    public string ProductName { get; set; } = null!;

    [ExcelColumn("Supplier", 400)]
    public string? Supplier { get; set; }
}
