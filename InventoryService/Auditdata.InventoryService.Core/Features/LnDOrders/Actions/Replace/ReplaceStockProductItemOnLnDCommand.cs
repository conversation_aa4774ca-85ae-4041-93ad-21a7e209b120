using Auditdata.InventoryService.Core.Features.LnDOrders.Commands;
using Auditdata.InventoryService.Core.Features.LnDOrders.Models;
using Auditdata.InventoryService.Core.Features.StockProductItems.Dtos;

namespace Auditdata.InventoryService.Core.Features.LnDOrders.Actions.Replace;

public class ReplaceStockProductItemOnLnDCommand : LnDOrderCommand, IRequest<ReplacedStockProductItemOnLnD>
{
    public string? SerialNumber { get; set; }
    public Guid? NewStockProductItemId { get; set; }
    public string? NewSerialNumber { get; set; }
    public Guid? ProductColorId { get; set; }
    public Guid? ProductBatteryTypeId { get; set; }
    public List<StockProductItemAttributeDto> Attributes { get; set; } = null!;
    public Guid ProductId { get; set; }
}
