using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Exceptions;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Auditdata.InventoryService.Core.Features.LnDOrders.Actions.Cancel;

public class CancelStockProductItemUnderLnDHandler : IRequestHandler<CancelStockProductItemUnderLnDCommand>
{
    private readonly ILogger<CancelStockProductItemUnderLnDHandler> _logger;
    private readonly IDbContext _dbContext;

    public CancelStockProductItemUnderLnDHandler(
        ILogger<CancelStockProductItemUnderLnDHandler> logger,
        IDbContext dbContext)
    {
        _logger = logger;
        _dbContext = dbContext;
    }

    public async Task Handle(CancelStockProductItemUnderLnDCommand request, CancellationToken cancellationToken)
    {
        var stockProductItem = await _dbContext.StockProductItems
            .Include(x => x.StockProduct)
                .ThenInclude(x => x.Product)
                .ThenInclude(x => x.Supplier)
            .Include(x => x.StockProduct)
                .ThenInclude(x => x.Stock)
            .FirstOrDefaultAsync(x => x.StockProduct.ProductId == request.ProductId &&
                x.SerialNumber == request.SerialNumber, cancellationToken) 
            ?? throw new EntityNotFoundException<StockProductItem>(Guid.Empty);

        stockProductItem.Status = request.Status;
        stockProductItem.ClearLnD(); 

        LogStockProductItemsHistory(stockProductItem, request.PatientName!);

        await _dbContext.SaveChangesAsync(cancellationToken);

        _logger.LogInformation(
            "StockProductItem '{@StockProductItemId}' cancelled as UnderLnD for LnDOrder '{@IndividualNumber}' and Patient '{@PatientName}'",
            stockProductItem.Id, request.Number, request.PatientName);
    }

    private void LogStockProductItemsHistory(
        StockProductItem stockProductItem,
        string lndOrderNumber)
    {
        _dbContext.StockProductItemLogs.Add(StockProductItemLog.LnDOrderCancelledLog(
            stockProductItem, lndOrderNumber));
    }
}
