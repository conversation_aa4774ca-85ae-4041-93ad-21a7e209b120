using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Exceptions;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Auditdata.InventoryService.Core.Features.LnDOrders.Actions.Complete;

public class CompleteStockProductItemUnderLnDHandler : IRequestHandler<CompleteStockProductItemUnderLnDCommand>
{
    private readonly ILogger<CompleteStockProductItemUnderLnDHandler> _logger;
    private readonly IDbContext _dbContext;

    public CompleteStockProductItemUnderLnDHandler(
        ILogger<CompleteStockProductItemUnderLnDHandler> logger,
        IDbContext dbContext)
    {
        _logger = logger;
        _dbContext = dbContext;
    }

    public async Task Handle(CompleteStockProductItemUnderLnDCommand request, CancellationToken cancellationToken)
    {
        var stockProductItem = await _dbContext.StockProductItems
            .Include(x => x.StockProduct)
                .ThenInclude(x => x.Product)
                .ThenInclude(x => x.Supplier)
            .Include(x => x.StockProduct)
                .ThenInclude(x => x.Stock)
            .FirstOrDefaultAsync(x => x.StockProduct.ProductId == request.ProductId &&
                x.SerialNumber == request.SerialNumber, cancellationToken)
            ?? throw new EntityNotFoundException<StockProductItem>(Guid.Empty);

        var lnDLocationId = stockProductItem.LnDInLocationId;

        stockProductItem.Status = request.Status;
        stockProductItem.ClearLnD();

        await LogStockProductItemsHistory(stockProductItem, request.PatientName!, lnDLocationId);

        await _dbContext.SaveChangesAsync(cancellationToken);

        _logger.LogInformation(
            "StockProductItem '{CompletedStockProductItemId}' completed for LnDOrder '{IndividualNumber}' and Patient '{PatientName}'",
            stockProductItem.Id, request.Number, request.PatientName);
    }

    private async Task LogStockProductItemsHistory(
        StockProductItem stockProductItem,
        string patientName,
        Guid? lnDLocationId)
    {
        var lnDStock = lnDLocationId is not null
            ? await _dbContext.Stocks.AsNoTracking().FirstOrDefaultAsync(x => x.LocationId == lnDLocationId)
            : null;

        _dbContext.StockProductItemLogs.Add(StockProductItemLog.ReturnedAndDeliveredByRepairOrLnDOrderLog(
            stockProductItem, patientName, lnDStock));
    }
}
