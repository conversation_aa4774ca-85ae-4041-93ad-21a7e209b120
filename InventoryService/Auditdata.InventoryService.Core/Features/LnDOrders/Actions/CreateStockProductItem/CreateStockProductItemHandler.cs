using Auditdata.InventoryService.Contracts.Commands;
using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Abstractions.Azure;
using Auditdata.InventoryService.Core.Abstractions.Validators;
using Auditdata.InventoryService.Core.Exceptions;
using Auditdata.InventoryService.Core.Features.LnDOrders.Models;
using Auditdata.InventoryService.Core.Features.Products.Models;
using Microsoft.EntityFrameworkCore;

namespace Auditdata.InventoryService.Core.Features.LnDOrders.Actions.CreateStockProductItem;

public class CreateStockProductItemHandler : IRequestHandler<CreateStockProductItemCommand, CreatedStockProductItem>
{
    private readonly ISerialNumbersValidator _serialNumbersValidator;
    private readonly IEventPublisher _eventPublisher;
    private readonly IAzureServiceBusPublisher _azureServiceBusPublisher;
    private readonly IDbContext _dbContext;

    public CreateStockProductItemHandler(
        IDbContext dbContext,
        IEventPublisher eventPublisher,
        ISerialNumbersValidator serialNumbersValidator,
        IAzureServiceBusPublisher azureServiceBusPublisher)
    {
        _dbContext = dbContext;
        _eventPublisher = eventPublisher;
        _serialNumbersValidator = serialNumbersValidator;
        _azureServiceBusPublisher = azureServiceBusPublisher;
    }

    public async Task<CreatedStockProductItem> Handle(CreateStockProductItemCommand request, CancellationToken cancellationToken)
    {
        var stockProduct = await _dbContext.StockProducts
            .Include(x => x.Product).ThenInclude(y => y.BatteryTypes).ThenInclude(y => y.BatteryType)
            .Include(x => x.Product).ThenInclude(y => y.Colors).ThenInclude(y => y.Color)
            .Include(x => x.Product).ThenInclude(y => y.Category)
            .Include(x => x.Product).ThenInclude(y => y.Attributes)
                .ThenInclude(y => y.Attribute).ThenInclude(y => y!.ProductCategories)
            .Include(x => x.Stock)
            .FirstOrDefaultAsync(x => x.Stock.LocationId == request.LocationId &&
                x.ProductId == request.ProductId, cancellationToken)
            ?? throw new EntityNotFoundException<StockProduct>(Guid.Empty);

        await _serialNumbersValidator.CheckDuplicatedSerialNumbers(
            stockProduct.Product.ManufacturerId!.Value,
            [request.SerialNumber],
            cancellationToken);

        var batteryType = await FetchBatteryTypeAsync(request.ProductBatteryTypeId, cancellationToken);
        var color = await FetchColorAsync(request.ProductColorId, cancellationToken);

        var stockProductItem = stockProduct.AdjustSerializedItems(
            [request.SerialNumber], color, batteryType, request.Attributes)[0];
        
        _dbContext.StockProductItems.Add(stockProductItem);

        await _eventPublisher.StockProductItemCreated(stockProductItem, cancellationToken);

        _dbContext.StockProductItemLogs.Add(StockProductItemLog.StockAdjustedActionLog(stockProductItem));
        _dbContext.StockProductItemLogs.Add(StockProductItemLog.SerialNumberAddedActionLog(stockProductItem));

        await _eventPublisher.SerializedStockAdjusted(
            stockProduct,
            1,
            [stockProductItem],
            cancellationToken);

        var transactionMessage = new TransactionMessage(stockProduct, 1, null, (Contracts.StockTransactionType)StockTransactionType.StockAdjustment);

        await CreateTransactionAsync(transactionMessage);
        await _dbContext.SaveChangesAsync(cancellationToken);

        return new CreatedStockProductItem(stockProductItem.Id);
    }

    private async Task CreateTransactionAsync(TransactionMessage message)
    {
        await _azureServiceBusPublisher.PublishAddStockTransactionCommand(new AddStockTransactionCommand
        {
            Quantity = message.Quantity,
            Type = message.Type,
            ProductId = message.StockProduct.ProductId,
            StockId = message.StockProduct.StockId
        });
    }

    private async Task<BatteryType?> FetchBatteryTypeAsync(Guid? batteryTypeId, CancellationToken cancellationToken)
    {
        if (batteryTypeId is null)
        {
            return null;
        }

        return await _dbContext.BatteryTypes.FindAsync([batteryTypeId.Value], cancellationToken) ??
               throw new EntityNotFoundException<BatteryType>(batteryTypeId.Value);
    }

    private async Task<Color?> FetchColorAsync(Guid? colorId, CancellationToken cancellationToken)
    {
        if (colorId is null)
        {
            return null;
        }

        return await _dbContext.Colors.FindAsync([colorId.Value], cancellationToken) ??
               throw new EntityNotFoundException<Color>(colorId.Value);
    }
}
