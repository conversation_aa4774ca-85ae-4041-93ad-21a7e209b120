namespace Auditdata.InventoryService.Core.Features.Repairs.Models;

public class GetRepairProductItem
{
    public Guid StockProductItemId { get; set; }
    public Guid ProductId { get; set; }
    public string ProductName { get; set; } = null!;
    public Guid StockId { get; set; }
    public Guid StockProductId { get; set; }
    public DateTimeOffset CreatedOn { get; set; }
    public string? SerialNumber { get; set; }
    public string? ManufacturerName { get; set; }
    public string? SupplierName { get; set; }
}
