using Auditdata.InventoryService.Core.Features.Repairs.Models;

namespace Auditdata.InventoryService.Core.Features.Repairs.Mappings;

public class RepairsMappings : Profile
{
    public RepairsMappings()
    {
        CreateMap<StockProductItem, GetRepairProductItem>()
            .ForMember(x => x.StockProductItemId, opt => opt.MapFrom(y => y.Id))
            .ForMember(x => x.ProductId, opt => opt.MapFrom(y => y.StockProduct.ProductId))
            .ForMember(x => x.ProductName, opt => opt.MapFrom(y => y.StockProduct.Product.Name))
            .ForMember(x => x.StockId, opt => opt.MapFrom(y => y.StockProduct.StockId))
            .ForMember(x => x.SerialNumber, opt => opt.MapFrom(y => y.SerialNumber))
            .ForMember(x => x.ManufacturerName, opt => opt.MapFrom(y => y.StockProduct.Product.Manufacturer.Name))
            .ForMember(x => x.SupplierName, opt => opt.MapFrom(y => y.StockProduct.Product.Supplier!.Name))
            .ForMember(x => x.CreatedOn, opt => opt.MapFrom(y => y.CreationDate));


        CreateMap<StockProduct, GetRepairProductItem>()
            .ForMember(x => x.ProductId, opt => opt.MapFrom(y => y!.ProductId))
            .ForMember(x => x.ProductName, opt => opt.MapFrom(y => y!.Product.Name))
            .ForMember(x => x.StockId, opt => opt.MapFrom(y => y!.StockId))
            .ForMember(x => x.ManufacturerName, opt => opt.MapFrom(y => y!.Product.Manufacturer.Name))
            .ForMember(x => x.SupplierName, opt => opt.MapFrom(y => y!.Product.Supplier!.Name))
            .ForMember(x => x.CreatedOn, opt => opt.MapFrom(y => y.CreationDate));
    }
}
