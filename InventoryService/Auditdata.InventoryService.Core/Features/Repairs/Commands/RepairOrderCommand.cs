using Auditdata.Microservice.Messages.OriginEntities.RepairOrders;

namespace Auditdata.InventoryService.Core.Features.Repairs.Commands;

public class RepairOrderCommand
{
    public Guid RepairOrderId { get; set; }
    public RepairMethod RepairMethod { get; set; }
    public Guid LocationId { get; set; }
    public string IndividualNumber { get; set; } = null!;
    public string? PatientName { get; set; }
}
