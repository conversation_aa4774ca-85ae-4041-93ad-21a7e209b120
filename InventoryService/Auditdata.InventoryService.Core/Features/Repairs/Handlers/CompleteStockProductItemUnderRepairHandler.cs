using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Exceptions;
using Auditdata.InventoryService.Core.Features.Repairs.Commands;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Auditdata.InventoryService.Core.Features.Repairs.Handlers;

public class CompleteStockProductItemUnderRepairHandler : IRequestHandler<CompleteStockProductItemUnderRepairCommand>
{
    private readonly ILogger<CompleteStockProductItemUnderRepairHandler> _logger;
    private readonly IDbContext _dbContext;
    private readonly IEventPublisher _eventPublisher;

    public CompleteStockProductItemUnderRepairHandler(
        ILogger<CompleteStockProductItemUnderRepairHandler> logger,
        IDbContext dbContext,
        IEventPublisher eventPublisher)
    {
        _logger = logger;
        _dbContext = dbContext;
        _eventPublisher = eventPublisher;
    }

    public async Task Handle(CompleteStockProductItemUnderRepairCommand request,
        CancellationToken cancellationToken)
    {
        var stockProductItem = await _dbContext.StockProductItems
            .Include(x => x.StockProduct)
                .ThenInclude(x => x.Product)
                .ThenInclude(x => x.Supplier)
            .Include(x => x.StockProduct)
                .ThenInclude(x => x.Stock)
            .FirstOrDefaultAsync(x => x.StockProduct.ProductId == request.ProductId &&
                x.SerialNumber == request.SerialNumber, cancellationToken) ??
          throw new EntityNotFoundException<StockProductItem>(Guid.Empty);

        var repairedInLocationId = stockProductItem.RepairInLocationId;
        
        stockProductItem.Status = request.Status;
        stockProductItem.ClearRepair();

        if (stockProductItem.Status == StockProductItemStatus.Available)
        {
            stockProductItem.StockProduct.AdjustQuantity(1);
            await _eventPublisher.SerializedStockAdjusted(
                stockProductItem.StockProduct,
                1,
                [stockProductItem],
                cancellationToken);
        }

        await LogStockProductItemsHistory(stockProductItem, request.PatientName!, repairedInLocationId);

        await _dbContext.SaveChangesAsync(cancellationToken);

        _logger.LogInformation(
            "StockProductItem '{CompletedStockProductItemId}' completed for RepairOrder '{IndividualNumber}' and Patient '{PatientName}'",
            stockProductItem.Id, request.IndividualNumber, request.PatientName);
    }

    private async Task LogStockProductItemsHistory(
        StockProductItem stockProductItem,
        string patientName,
        Guid? repairInLocationId)
    {
        var repairStock = repairInLocationId is not null
            ? await _dbContext.Stocks.AsNoTracking().FirstOrDefaultAsync(x => x.LocationId == repairInLocationId)
            : null;
        
        if (stockProductItem.Status is StockProductItemStatus.Sold)
        {
            _dbContext.StockProductItemLogs.Add(StockProductItemLog.ReturnedAndDeliveredByRepairOrLnDOrderLog(
                stockProductItem, patientName, repairStock));
        }

        if (stockProductItem.Status is StockProductItemStatus.Available)
        {
            _dbContext.StockProductItemLogs.Add(StockProductItemLog.ReturnedByRepairOrderLog(stockProductItem, repairStock));
        }
    }
}
