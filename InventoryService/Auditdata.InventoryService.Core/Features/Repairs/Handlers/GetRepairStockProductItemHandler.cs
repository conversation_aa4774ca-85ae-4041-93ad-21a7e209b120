using Auditdata.InventoryService.Core.Exceptions;
using Auditdata.InventoryService.Core.Features.Repairs.Models;
using Auditdata.InventoryService.Core.Features.Repairs.Queries;

namespace Auditdata.InventoryService.Core.Features.Repairs.Handlers;

public class GetRepairStockProductItemHandler : IRequestHandler<GetRepairStockProductItemQuery, GetRepairProductItem>
{
    private readonly IStockProductItemsRepository _stockProductItemsRepository;
    private readonly IMapper _mapper;

    public GetRepairStockProductItemHandler(
        IStockProductItemsRepository stockProductItemsRepository,
        IMapper mapper)
    {
        _stockProductItemsRepository = stockProductItemsRepository;
        _mapper = mapper;
    }

    public async Task<GetRepairProductItem> Handle(GetRepairStockProductItemQuery request, CancellationToken cancellationToken)
    {
        var stockProductItem = await _stockProductItemsRepository.GetRepairProductItemAsync(request.StockProductItemId);
        if (stockProductItem is null)
        {
            throw new EntityNotFoundException<StockProductItem>(request.StockProductItemId);
        }

        return _mapper.Map<GetRepairProductItem>(stockProductItem);
    }
}
