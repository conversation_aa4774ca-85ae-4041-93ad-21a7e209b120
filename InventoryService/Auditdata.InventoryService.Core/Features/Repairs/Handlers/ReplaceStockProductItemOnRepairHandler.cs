using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Abstractions.Validators;
using Auditdata.InventoryService.Core.Constants;
using Auditdata.InventoryService.Core.Exceptions;
using Auditdata.InventoryService.Core.Features.Repairs.Commands;
using Auditdata.InventoryService.Core.Features.Repairs.Models;
using Auditdata.Transport.Contracts.Exceptions;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Auditdata.InventoryService.Core.Features.Repairs.Handlers;

public class ReplaceStockProductItemOnRepairHandler : IRequestHandler<ReplaceStockProductItemOnRepairCommand,
    ReplacedStockProductItemOnRepair>
{
    private readonly ILogger<ReplaceStockProductItemOnRepairHandler> _logger;
    private readonly ISerialNumbersValidator _serialNumbersValidator;
    private readonly IEventPublisher _eventPublisher;
    private readonly IDbContext _dbContext;

    public ReplaceStockProductItemOnRepairHandler(
        ILogger<ReplaceStockProductItemOnRepairHandler> logger,
        IDbContext dbContext,
        ISerialNumbersValidator serialNumbersValidator,
        IEventPublisher eventPublisher)
    {
        _logger = logger;
        _dbContext = dbContext;
        _serialNumbersValidator = serialNumbersValidator;
        _eventPublisher = eventPublisher;
    }

    public async Task<ReplacedStockProductItemOnRepair> Handle(ReplaceStockProductItemOnRepairCommand request,
        CancellationToken cancellationToken)
    {
        var replaceToExisting = request.NewStockProductItemId.HasValue;
        var replaceToNew = !string.IsNullOrEmpty(request.NewSerialNumber);
        if (replaceToExisting && replaceToNew || !replaceToExisting && !replaceToNew)
        {
            throw new NotSupportedException("Unable to replace item");
        }

        var oldStockProductItem = request.SerialNumber is null ?
            null :
            await _dbContext.StockProductItems
                .Include(x => x.StockProduct)
                    .ThenInclude(x => x.Product)
                    .ThenInclude(x => x.BatteryTypes)
                    .ThenInclude(x => x.BatteryType)
                .Include(x => x.StockProduct)
                    .ThenInclude(x => x.Product)
                    .ThenInclude(x => x.Colors)
                    .ThenInclude(x => x.Color)
                .Include(x => x.StockProduct)
                    .ThenInclude(x => x.Product)
                    .ThenInclude(x => x.Category)
                .Include(x => x.StockProduct)
                    .ThenInclude(x => x.Product)
                    .ThenInclude(x => x.Attributes)
                    .ThenInclude(x => x.Attribute)
                    .ThenInclude(x => x!.ProductCategories)
                .Include(x => x.StockProduct)
                    .ThenInclude(x => x.Stock)
                .FirstOrDefaultAsync(x => x.StockProduct.ProductId == request.ProductId && 
                    x.Status == StockProductItemStatus.UnderRepair &&
                    x.SerialNumber == request.SerialNumber,
                    cancellationToken) ??
            throw new EntityNotFoundException<StockProductItem>(Guid.Empty);

        var newStockProductItem = replaceToExisting
            ? await ReplaceWithExistingAsync(request, oldStockProductItem, cancellationToken)
            : await ReplaceWithNewItemAsync(request, oldStockProductItem, cancellationToken);

        LogStockProductItemsHistory(
            oldStockProductItem,
            newStockProductItem,
            request.IndividualNumber,
            request.PatientName,
            request.IsEquipmentReplacement,
            replaceToNew);

        if (oldStockProductItem is not null)
        {
            await _eventPublisher.StockProductItemReplaced(oldStockProductItem.Id, newStockProductItem!.Id, cancellationToken);
        }

        await _dbContext.SaveChangesAsync(cancellationToken);

        var oldStockProductItemId = oldStockProductItem is not null ? $"{oldStockProductItem.Id}" : string.Empty;

        _logger.LogInformation(
            "StockProductItem '{@OriginalStockProductItemId}' replaced with '{@ReplacedStockProductItemId}' for RepairOrder '{@IndividualNumber}' and Patient '{@PatientName}'",
            oldStockProductItemId, newStockProductItem!.Id, request.IndividualNumber, request.PatientName);

        return new ReplacedStockProductItemOnRepair(newStockProductItem.Id);
    }

    private async Task<StockProductItem> ReplaceWithExistingAsync(
        ReplaceStockProductItemOnRepairCommand request,
        StockProductItem? oldStockProductItem,
        CancellationToken cancellationToken)
    {
        var saleId = oldStockProductItem?.SaleId;
        
        oldStockProductItem?.Replace();
        
        var newStockProductItem = await _dbContext.StockProductItems
                        .Include(x => x.StockProduct)
                            .ThenInclude(x => x.Product)
                            .ThenInclude(x => x.Supplier)
                        .Include(x => x.StockProduct)
                            .ThenInclude(x => x.Stock)
                        .FirstOrDefaultAsync(x => x.Id == request.NewStockProductItemId!.Value, cancellationToken) ??
                        throw new EntityNotFoundException<StockProductItem>(request.NewStockProductItemId!.Value);
        if (newStockProductItem.Status is not StockProductItemStatus.Available)
        {
            throw new NotSupportedException("Can't replace item with a sold item");
        }

        if (!request.IsEquipmentReplacement)
        {
            return newStockProductItem;
        }

        newStockProductItem.Reserve(saleId);
        newStockProductItem.Sell();
        newStockProductItem.StockProduct.AdjustQuantity(-1);
        await _eventPublisher.SerializedStockAdjusted(
            newStockProductItem.StockProduct,
            -1,
            [newStockProductItem],
            cancellationToken);

        if (saleId is not null)
        {
            await _eventPublisher.StockProductItemSold(
                newStockProductItem,
                cancellationToken);
        }

        return newStockProductItem;
    }

    private async Task<StockProductItem> ReplaceWithNewItemAsync(
        ReplaceStockProductItemOnRepairCommand request,
        StockProductItem? oldStockProductItem,
        CancellationToken cancellationToken)
    {
        var saleId = oldStockProductItem?.SaleId;
        
        var batteryType = await FetchBatteryTypeAsync(request.BatteryTypeId, cancellationToken);
        var color = await FetchColorAsync(request.ColorId, cancellationToken);

        var stockProduct = await _dbContext.StockProducts
            .Include(x => x.Product).ThenInclude(y => y.Supplier)
            .Include(x => x.Product).ThenInclude(y => y.BatteryTypes).ThenInclude(y => y.BatteryType)
            .Include(x => x.Product).ThenInclude(y => y.Colors).ThenInclude(y => y.Color)
            .Include(x => x.Product).ThenInclude(y => y.Category)
            .Include(x => x.Product).ThenInclude(y => y.Attributes)
                .ThenInclude(y => y.Attribute).ThenInclude(y => y!.ProductCategories)
            .Include(x => x.Stock)
            .FirstOrDefaultAsync(
                x => x.ProductId == request.ProductId && x.Stock.LocationId == request.LocationId,
                cancellationToken)
            ?? throw new BusinessException(
                "Stock product not found for replacement",
                ErrorCodes.StockProductNotFoundForReplacement,
                request.LocationId,
                request.ProductId);

        var serialNumbers = new List<string>
            {
                request.NewSerialNumber!
            };
        await _serialNumbersValidator.CheckDuplicatedSerialNumbers(
            stockProduct.Product.ManufacturerId!.Value,
            serialNumbers, cancellationToken);

        oldStockProductItem?.Replace();
        
        var newStockProductItems = stockProduct
            .AdjustSerializedItems(serialNumbers, color, batteryType, request.Attributes);
        var newStockProductItem = newStockProductItems[0];
        if (!request.IsEquipmentReplacement)
        {
            await _eventPublisher.SerializedStockAdjusted(
                newStockProductItem.StockProduct,
                1,
                [newStockProductItem],
                cancellationToken);
            return newStockProductItem;
        }

        newStockProductItem.Reserve(saleId);
        newStockProductItem.Sell();
        newStockProductItem.StockProduct.AdjustQuantity(-newStockProductItems.Count);

        if (saleId is not null)
        {
            await _eventPublisher.StockProductItemSold(newStockProductItem, cancellationToken);
        }

        return newStockProductItem;
    }

    private void LogStockProductItemsHistory(
        StockProductItem? oldStockProductItem,
        StockProductItem newStockProductItem,
        string repairOrderNumber,
        string? patientName,
        bool isEquipmentReplacement,
        bool replaceToNew)
    {
        if (oldStockProductItem is not null)
        {
            _dbContext.StockProductItemLogs.Add(StockProductItemLog.ReplacedByRepairOrderLog(
                oldStockProductItem, newStockProductItem.SerialNumber!, repairOrderNumber));
        }

        _dbContext.StockProductItemLogs.Add(StockProductItemLog.ReplacementByRepairOrderLog(
            newStockProductItem, oldStockProductItem?.SerialNumber, repairOrderNumber));
        if (isEquipmentReplacement)
        {
            _dbContext.StockProductItemLogs.Add(StockProductItemLog.DeliveredLog(
                newStockProductItem, patientName!));
        }

        if (replaceToNew)
        {
            _dbContext.StockProductItemLogs.Add(StockProductItemLog.StockAdjustedActionLog(newStockProductItem));
        }
    }

    private async Task<BatteryType?> FetchBatteryTypeAsync(Guid? batteryTypeId, CancellationToken cancellationToken)
    {
        if (batteryTypeId is null)
        {
            return null;
        }

        var batteryType = await _dbContext.BatteryTypes.FindAsync(
            [batteryTypeId], cancellationToken: cancellationToken);
        return batteryType ?? throw new EntityNotFoundException<BatteryType>(batteryTypeId.Value);
    }

    private async Task<Color?> FetchColorAsync(Guid? colorId, CancellationToken cancellationToken)
    {
        if (colorId is null)
        {
            return null;
        }

        var color = await _dbContext.Colors.FindAsync(
            [colorId], cancellationToken);
        return color ?? throw new EntityNotFoundException<Color>(colorId.Value);
    }
}
