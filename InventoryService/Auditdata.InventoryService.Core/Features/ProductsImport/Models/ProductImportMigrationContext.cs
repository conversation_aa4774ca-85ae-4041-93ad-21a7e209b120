using Attribute = Auditdata.InventoryService.Core.Entities.Attribute;

namespace Auditdata.InventoryService.Core.Features.ProductsImport.Models;

public record ProductImportMigrationContext(
    List<ProductCategory> ProductCategories,
    List<Color> Colors,
    List<Manufacturer> Manufacturers,
    List<BatteryType> BatteryTypes,
    List<HearingAidType> HearingAidTypes,
    List<Attribute> Attributes);
