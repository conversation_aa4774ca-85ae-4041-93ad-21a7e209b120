using Auditdata.Infrastructure.Contracts.OperationContext;
using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Abstractions.Services;
using Auditdata.InventoryService.Core.Constants;
using Auditdata.InventoryService.Core.Entities.Products;
using Auditdata.InventoryService.Core.Exceptions;
using Auditdata.InventoryService.Core.Features.ProductsImport.Models;
using Auditdata.Transport.Contracts.Exceptions;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Attribute = Auditdata.InventoryService.Core.Entities.Attribute;

namespace Auditdata.InventoryService.Core.Features.ProductsImport.MigrateImport;

public class MigrateImportHandler : IRequestHandler<MigrateImportCommand,Guid>
{
    private readonly IDbContext _dbContext;
    private readonly IDateTimeService _dateTimeService;
    private readonly IOperationContext _operationContext;
    private readonly IProductEventPublisher _productEventPublisher;
    private readonly ILogger<MigrateImportHandler> _logger;

    public MigrateImportHandler(
        IDbContext dbContext,
        IDateTimeService dateTimeService,
        IOperationContext operationContext,
        IProductEventPublisher productEventPublisher,
        ILogger<MigrateImportHandler> logger)
    {
        _dbContext = dbContext;
        _dateTimeService = dateTimeService;
        _operationContext = operationContext;
        _productEventPublisher = productEventPublisher;
        _logger = logger;
    }

    public async Task<Guid> Handle(MigrateImportCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Starting product import migration {@ImportId}", request.ImportId);

        var import =
            await _dbContext.ExternalProductImports.FirstOrDefaultAsync(x => x.Id == request.ImportId,
                cancellationToken);

        if (import is null)
        {
            _logger.LogWarning("Product import not found by {@ImportId}", request.ImportId);
            throw new EntityNotFoundException<ExternalProductImport>(request.ImportId);
        }

        if (import.IsCompleted())
        {
            _logger.LogWarning("Product import {@ImportId} already finished with {@State}", request.ImportId,
                import.State);

            throw new BusinessException("Product import {@ImportId} already finished with {@State}",
                ErrorCodes.ProductImportError, request.ImportId);
        }

        import.StartImport(_dateTimeService);
        await _dbContext.SaveChangesAsync(cancellationToken);

        var importSucceeded = true;

        try
        {
            var externalProducts = await _dbContext.ExternalProducts
                .Include(x => x.ExternalProductOptions)
                .Where(x => x.ImportId == request.ImportId &&
                            !_dbContext.ExternalProducts.Any(y => y.ExternalId == x.ExternalId && y.ProductId != null))
                .ToListAsync(cancellationToken);
            var migrationContext = await CreateContextAsync(cancellationToken);
            
            var products = MigrateExternalProducts(externalProducts, migrationContext);
            if (products.Count == 0)
            {
                _logger.LogInformation("No product found for import {@ImportId}", request.ImportId);
                return request.ImportId;
            }
            
            _dbContext.Products.AddRange(products);

            await _productEventPublisher.PublishProductsCreatedAsync(products, cancellationToken);
            await _dbContext.SaveChangesAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            importSucceeded = false;
            _logger.LogError(ex, "Errors occured during product import {@ImportId}", request.ImportId);
            throw new BusinessException($"Errors occured during product import {@request.ImportId}",
                ErrorCodes.ProductImportError, request.ImportId);
        }
        finally
        {
            FinalizeImportAsync(import, importSucceeded);
            await _dbContext.SaveChangesAsync(cancellationToken);
        }

        return request.ImportId;
    }

    private List<Product> MigrateExternalProducts(
        List<ExternalProduct> externalProducts,
        ProductImportMigrationContext context)
    {
        var products = new List<Product>(externalProducts.Count);

        foreach (var externalProduct in externalProducts.Where(x => x.Product is null))
        {
            var manufacturer = MergeManufacturer(context.Manufacturers, externalProduct.Manufacturer);

            var newProduct = CreateProduct(externalProduct, manufacturer.Id, context);
            externalProduct.Product = newProduct;

            var productColors = MergeColors(
                context.Colors,
                externalProduct.ExternalProductOptions.Select(x => x.Styles?.Color)
                    .Where(x => !string.IsNullOrEmpty(x))!,
                newProduct.Id);
            var batteryTypes = MergeBatteryTypes(
                context.BatteryTypes,
                externalProduct.ExternalProductOptions.Select(x => x.Styles?.BatteryType)
                    .Where(x => !string.IsNullOrEmpty(x))!);
            var productAttributes = MergeProductAttributes(
                context.Attributes,
                externalProduct.ExternalProductOptions.Select(x => x.Styles!).ToList(),
                newProduct);

            newProduct.Colors = productColors.ToList();
            newProduct.SetBatteryTypes(batteryTypes);
            newProduct.Attributes = productAttributes.ToList();
            products.Add(newProduct);
        }

        return products;
    }

    private Product CreateProduct(
        ExternalProduct externalProduct,
        Guid manufacturerId,
        ProductImportMigrationContext context)
    {
        var product = Product.Create(_operationContext.CountryId);
        var productCategory =
            context.ProductCategories.Find(x => x.Code == externalProduct.Category.ToProductCategoryCode());
        var hearingAidType = context.HearingAidTypes.Find(x => x.Name == externalProduct.HearingAidType);

        product.Id = Guid.NewGuid();
        product.Name = externalProduct.Name;
        product.Description = "\"\"";
        product.ManufacturerId = manufacturerId;
        product.Category = productCategory!;
        product.HearingAidType = hearingAidType;
        product.IsSellable = true;
        product.IsSerialized = true;
        product.IsActive = false;
        product.ControlledByStock = true;
        product.Quantity = 1;

        return product;
    }

    private Manufacturer MergeManufacturer(List<Manufacturer> manufacturers, string externalName)
    {
        var existingManufacturer = manufacturers.Find(x => x.Name == externalName);
        if (existingManufacturer is not null)
        {
            return existingManufacturer;
        }

        var newManufacturer = Manufacturer.Create(externalName);
        manufacturers.Add(newManufacturer);
        _dbContext.Manufacturers.Add(newManufacturer);
        return newManufacturer;
    }

    private IEnumerable<ProductColor> MergeColors(List<Color> colors, IEnumerable<string> externalColors, Guid productId)
    {
        foreach (var externalColor in externalColors.Distinct())
        {
            var existingColor = colors.Find(x => x.Name == externalColor);
            if (existingColor is null)
            {
                existingColor = Color.Create(externalColor);
                colors.Add(existingColor);
            }
            
            yield return new ProductColor
            {
                Color = existingColor,
                ProductId = productId
            };
        }
    }
    
    private IEnumerable<BatteryType> MergeBatteryTypes(
        List<BatteryType> batteryTypes,
        IEnumerable<string> externalBatteryTypes)
    {
        foreach (var externalBatteryType in externalBatteryTypes.Distinct())
        {
            var existingBatteryType = batteryTypes.Find(x => x.Name == externalBatteryType);
            if (existingBatteryType is null)
            {
                existingBatteryType = BatteryType.Create(externalBatteryType);
                batteryTypes.Add(existingBatteryType);
            }

            yield return existingBatteryType;
        }
    }

    private IEnumerable<ProductAttribute> MergeProductAttributes(
        List<Attribute> attributes,
        ICollection<ExternalProductOptionStyles> styles,
        Product product)
    {
        var mergedAttributes = styles
            .SelectMany(x => x.Attributes ?? Enumerable.Empty<ExternalProductOptionStyle>())
            .GroupBy(x => x.Code);

        foreach (var mergedAttribute in mergedAttributes)
        {
            var attribute = attributes.Find(x => x.Code == mergedAttribute.Key);
            if (attribute is null)
            {
                var distinctAttributeValues = mergedAttribute
                    .DistinctBy(x => x.Value)
                    .Select(x => new AttributeValue
                    {
                        ValueId = Guid.NewGuid(),
                        Value = x.Value,
                        IsActive = true
                    }).ToList();
                attribute = Attribute.CreateExternalAttribute(mergedAttribute.Key, distinctAttributeValues);
                _dbContext.Attributes.Add(attribute);
                attributes.Add(attribute);
            }

            var productAttributeValues = new List<AttributeValue>();
            foreach (var mergedAttributeValues in mergedAttribute.DistinctBy(x => x.Value))
            {
                var existingValue = attribute.Values?.Find(x => x.Value == mergedAttributeValues.Value);
                if (existingValue is null)
                {
                    existingValue = new AttributeValue
                    {
                        ValueId = Guid.NewGuid(),
                        Value = mergedAttributeValues.Value,
                        IsActive = true
                    };
                    attribute.Values = new List<AttributeValue>(attribute.Values ?? Enumerable.Empty<AttributeValue>())
                    {
                        existingValue
                    };
                }
                productAttributeValues.Add(existingValue with { });
            }

            attribute.LinkToCategory(product.Category);

            yield return ProductAttribute.Create(product.Id, attribute.Id, productAttributeValues);
        }
    }
    
    private void FinalizeImportAsync(ExternalProductImport import, bool importSucceeded)
    {
        var state = importSucceeded ? ExternalProductState.Completed : ExternalProductState.Failed;
        import.State = state;
    }

    private async Task<ProductImportMigrationContext> CreateContextAsync(CancellationToken cancellationToken)
    {
        var manufacturers = await _dbContext.Manufacturers.ToListAsync(cancellationToken);
        var categories = await _dbContext.ProductCategories.ToListAsync(cancellationToken);
        var colors = await _dbContext.Colors.ToListAsync(cancellationToken);
        var batteryTypes = await _dbContext.BatteryTypes.ToListAsync(cancellationToken);
        var attributes = await _dbContext.Attributes.Include(x => x.ProductCategories).ToListAsync(cancellationToken);
        var hearingAidTypes = await _dbContext.HearingAidTypes.ToListAsync(cancellationToken);

        return new ProductImportMigrationContext(
            categories, colors, manufacturers, batteryTypes, hearingAidTypes, attributes);
    }
}
