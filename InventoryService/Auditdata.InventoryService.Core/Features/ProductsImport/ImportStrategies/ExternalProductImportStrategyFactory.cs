using Microsoft.Extensions.DependencyInjection;

namespace Auditdata.InventoryService.Core.Features.ProductsImport.ImportStrategies;

public class ExternalProductImportStrategyFactory : IExternalProductImportStrategyFactory
{
    private readonly IServiceProvider _serviceProvider;

    public ExternalProductImportStrategyFactory(IServiceProvider serviceProvider)
    {
        _serviceProvider = serviceProvider;
    }
    
    public IExternalProductImportStrategy? GetStrategy(ExternalProductSource source)
    {
        return source switch
        {
            ExternalProductSource.HIMSA => _serviceProvider.GetService<HimsaProductImportStrategy>(),
            ExternalProductSource.iHear => null,
            _ => null
        };
    }
}
