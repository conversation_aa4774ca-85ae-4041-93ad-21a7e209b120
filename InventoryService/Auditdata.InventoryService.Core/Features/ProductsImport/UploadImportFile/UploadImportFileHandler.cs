using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Abstractions.Azure;
using Auditdata.InventoryService.Core.Abstractions.Services;
using Auditdata.InventoryService.Core.Configuration;
using Auditdata.InventoryService.Core.Features.ProductsImport.ImportStrategies;
using Auditdata.InventoryService.Core.Features.ProductsImport.Models;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace Auditdata.InventoryService.Core.Features.ProductsImport.UploadImportFile;

public class UploadImportFileHandler : IRequestHandler<UploadImportFileCommand, UploadImportFileResult>
{
    private readonly IAzureBlobStorageService _azureBlobStorageService;
    private readonly IDbContext _dbContext;
    private readonly IDateTimeService _dateTimeService;
    private readonly IExternalProductImportStrategyFactory _externalProductImportStrategyFactory;
    private readonly ILogger<UploadImportFileHandler> _logger;
    private readonly IOptions<ProductsImportSettings> _settings;

    public UploadImportFileHandler(
        IAzureBlobStorageService azureBlobStorageService,
        IDbContext dbContext,
        IDateTimeService dateTimeService,
        IExternalProductImportStrategyFactory externalProductImportStrategyFactory,
        ILogger<UploadImportFileHandler> logger,
        IOptions<ProductsImportSettings> settings)
    {
        _azureBlobStorageService = azureBlobStorageService;
        _dbContext = dbContext;
        _dateTimeService = dateTimeService;
        _externalProductImportStrategyFactory = externalProductImportStrategyFactory;
        _logger = logger;
        _settings = settings;
    }
    
    public async Task<UploadImportFileResult> Handle(UploadImportFileCommand request, CancellationToken cancellationToken)
    {
        var importId = Guid.NewGuid();
        var url = await UploadImportFileAsync(importId, request);

        var source = ExternalProductSource.HIMSA;
        var externalProductImport = ExternalProductImport.Create(
            importId,
            _dateTimeService.UtcNow,
            ExternalProductState.Uploaded,
            source,
            url);
        _dbContext.ExternalProductImports.Add(externalProductImport);
        
        var externalProducts = await ExecuteImportStrategy(source, importId, request.Content, cancellationToken);
        _dbContext.ExternalProducts.AddRange(externalProducts);

        await _dbContext.SaveChangesAsync(cancellationToken);

        return new UploadImportFileResult(importId);
    }

    private async Task<string> UploadImportFileAsync(Guid importId, UploadImportFileCommand request)
    {
        var fileName = $"{importId:N}_{request.FileName}";
        
        _logger.LogInformation("Uploading Products import file {@Payload}", new
        {
            ImportId = importId,
            Container = _settings.Value.BlobContainer,
            FileName = fileName
        });

        var url = await _azureBlobStorageService.PutAsync(
            _settings.Value.BlobContainer,
            new AzureBlobStorageFile(fileName, request.Content, request.ContentType));
        
        _logger.LogInformation("Products import file uploaded {@Payload}", new
        {
            ImportId = importId,
            Url = url
        });

        return url;
    }

    private Task<IEnumerable<ExternalProduct>> ExecuteImportStrategy(
        ExternalProductSource source,
        Guid importId,
        Stream importContent,
        CancellationToken cancellationToken)
    {
        var importStrategy = _externalProductImportStrategyFactory.GetStrategy(source);
        if (importStrategy is null)
        {
            _logger.LogWarning("External import is not implemented for {@Source}", new
            {
                Source = source
            });
            return Task.FromResult<IEnumerable<ExternalProduct>>(Array.Empty<ExternalProduct>());
        }

        return importStrategy.ImportAsync(importId, importContent, cancellationToken);
    }
}
