using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Features.StockProductItemLogs.Models;
using Auditdata.InventoryService.Core.Features.StockProductItemLogs.Queries;
using Microsoft.EntityFrameworkCore;

namespace Auditdata.InventoryService.Core.Features.StockProductItemLogs.Handlers;

public class GetStockProductItemLogHandler : IRequestHandler<GetStockProductItemLogQuery, GetStockProductItemLogResult>
{
    private readonly IDbContext _dbContext;

    public GetStockProductItemLogHandler(IDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    public async Task<GetStockProductItemLogResult> Handle(GetStockProductItemLogQuery request, CancellationToken cancellationToken)
    {
        var logs = await _dbContext.StockProductItemLogs
            .Include(x => x.Stock)
            .Include(x => x.StockProductItem)
                .ThenInclude(x => x.StockProduct)
                    .ThenInclude(x => x.Product)
            .Where(x => x.StockProductItemId == request.StockProductItemId)
            .OrderByDescending(x => x.CreationDate)
            .ToListAsync(cancellationToken);
        return new GetStockProductItemLogResult(logs);
    }
}
