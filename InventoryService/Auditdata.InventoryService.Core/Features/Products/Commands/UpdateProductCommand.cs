using Auditdata.InventoryService.Core.Features.Products.Models;

namespace Auditdata.InventoryService.Core.Features.Products.Commands;

public record UpdateProductCommand : IRequest
{
    public Guid Id { get; set; }
    public string Name { get; set; } = null!;
    public string? Description { get; set; }
    public Guid CategoryId { get; set; }
    public Guid? ManufacturerId { get; set; }
    public Guid? HearingAidTypeId { get; set; }
    public Guid? SupplierId { get; set; }
    public int? Warranty { get; set; }
    public int? LDWarranty { set; get; }
    public int Quantity { get; set; }
    public decimal RetailPrice { get; set; }
    public decimal FirstVAT { get; set; }
    public decimal SecondVAT { get; set; }
    public bool IsSellable { get; set; }
    public bool IsActive { get; set; }
    public bool IsSerialized { get; set; }
    public bool PriceChangesAllowed { get; set; }
    public bool IsFastTrack { get; set; }
    public bool AutoDeliver { get; set; }
    public string? VendorProductNumber { get; set; }
    public decimal? MaximumDiscount { get; set; }
    public decimal? Cost { get; set; }
    
    public IEnumerable<Guid>? Colors { get; set; }
    public IEnumerable<Guid>? PathwayIds { get; set; }
    public IEnumerable<Guid>? BatteryTypes { get; set; }
    public IEnumerable<ProductAttributeDto>? Attributes { get; set; }
    public IEnumerable<Guid>? SuggestedProductIds { get; set; }
}
