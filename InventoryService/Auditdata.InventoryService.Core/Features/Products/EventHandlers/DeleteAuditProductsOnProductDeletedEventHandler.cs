using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Entities.AuditRequests;
using Auditdata.InventoryService.Core.Features.Products.Events;
using Microsoft.EntityFrameworkCore;

namespace Auditdata.InventoryService.Core.Features.Products.EventHandlers;

public class DeleteAuditProductsOnProductDeletedEventHandler : INotificationHandler<ProductDeleted>
{
    private readonly IDbContext _dbContext;

    public DeleteAuditProductsOnProductDeletedEventHandler(IDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    public async Task Handle(ProductDeleted notification, CancellationToken cancellationToken)
    {
        var auditProducts = await _dbContext.AuditProducts
            .Include(t => t.AuditRequest)
            .Where(p => p.ProductId == notification.ProductId && p.AuditRequest.Status == AuditRequestStatus.Draft)
            .ToListAsync(cancellationToken);

        _dbContext.AuditProducts.RemoveRange(auditProducts);
    }
}
