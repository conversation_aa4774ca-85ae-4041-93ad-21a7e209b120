using Auditdata.InventoryService.Core.Entities.Products;
using Auditdata.InventoryService.Core.Features.Products.Queries;
using Auditdata.Transport.Contracts.PageResults;
using Auditdata.Transport.Contracts.Table;

namespace Auditdata.InventoryService.Core.Features.Products.Handlers;

public class GetProductListHandler : IRequestHandler<GetProductListQuery, TablePageResult<Product>>
{
    private readonly IProductsRepository _productsRepository;

    public GetProductListHandler(IProductsRepository productsRepository)
    {
        _productsRepository = productsRepository;
    }

    public async Task<TablePageResult<Product>> Handle(GetProductListQuery request, CancellationToken cancellationToken)
    {
        var products = await _productsRepository.GetProductsAsync(new TableQueryBase
        {
            Page = request.PageNumber,
            PerPage = request.PageSize,
            OrderBy = request.OrderBy
        });

        return products;
    }
}
