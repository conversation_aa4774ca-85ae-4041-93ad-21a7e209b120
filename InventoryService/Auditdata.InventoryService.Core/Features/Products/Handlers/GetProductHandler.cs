using Auditdata.Infrastructure.Contracts.CountryLayers;
using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Entities.Products;
using Auditdata.InventoryService.Core.Exceptions;
using Auditdata.InventoryService.Core.Features.Products.Extensions;
using Auditdata.InventoryService.Core.Features.Products.Models;
using Auditdata.InventoryService.Core.Features.Products.Queries;
using Auditdata.InventoryService.Core.OperationContext;
using Microsoft.EntityFrameworkCore;

namespace Auditdata.InventoryService.Core.Features.Products.Handlers;

public class GetProductHandler : IRequestHandler<GetProductQuery, ProductResult>
{
    private readonly IDbContext _dbContext;
    private readonly IMapper _mapper;
    private readonly IInventoryServiceOperationContext _operationContext;

    public GetProductHandler(
        IDbContext dbContext,
        IMapper mapper,
        IInventoryServiceOperationContext operationContext)
    {
        _dbContext = dbContext;
        _mapper = mapper;
        _operationContext = operationContext;
    }

    public async Task<ProductResult> Handle(GetProductQuery request, CancellationToken cancellationToken)
    {
        var productQuery = _dbContext.Products.AsNoTracking().AsSplitQuery()
            .Include(x => x.Category)
            .Include(x => x.Manufacturer)
            .Include(x => x.HearingAidType)
            .Include(x => x.Supplier)
            .Include(x => x.ProductPathways)
            .Include(x => x.Colors)
            .ThenInclude(x => x.Color)
            .Include(x => x.BatteryTypes)
            .ThenInclude(x => x.BatteryType)
            .Include(x => x.Attributes.Where(a => !a.IsDeleted))
            .ThenInclude(x => x.Attribute)
            .Include(x => x.Bundles)
            .Include(x => x.SuggestedProducts)
            .Include(x => x.SkuConfigs)
            .AsQueryable();

        if (_operationContext.CountryId == CountryEnum.UnitedStates)
        {
            productQuery = productQuery.Cast<USProduct>()
                .Include(x => x.CPTCodes.Where(x => x.CPTCode.IsActive == true))
                .ThenInclude(x => x.CPTCode);
        }
        
        var product = await productQuery.FirstOrDefaultAsync(x => x.Id == request.Id, cancellationToken);
        if (product is null)
        {
            throw new EntityNotFoundException<Product>(request.Id);
        }

        var result = _mapper.Map<ProductResult>(product);
        result.IsInBundle = product.Bundles.Any();
        result.WasPlacedOnStock = await product.WasPlacedOnStockAsync(_dbContext, cancellationToken);
        result.HasSkuConfigs = product.SkuConfigs.Any();
        if (!_operationContext.UserCanViewProductCost)
        {
            result.Cost = null;
        }

        return result;
    }
}
