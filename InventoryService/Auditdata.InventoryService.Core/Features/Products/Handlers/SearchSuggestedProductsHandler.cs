using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Features.Products.Extensions;
using Auditdata.InventoryService.Core.Features.Products.Models;
using Auditdata.InventoryService.Core.Features.Products.Queries;
using Microsoft.EntityFrameworkCore;

namespace Auditdata.InventoryService.Core.Features.Products.Handlers;

public class SearchSuggestedProductsHandler : IRequestHandler<SearchSuggestedProductsQuery, ProductsSearchResult>
{
    private readonly IDbContext _dbContext;

    public SearchSuggestedProductsHandler(IDbContext dbContext)
    {
        _dbContext = dbContext;
    }
    
    public async Task<ProductsSearchResult> Handle(SearchSuggestedProductsQuery request, CancellationToken cancellationToken)
    {
        var query = request.GetProductsQuery(_dbContext)
            .Where(x => x.Id != request.ProductId)
            .OrderBy(x => _dbContext.ProductSuggestedProducts
                .Any(y => y.ProductId == request.ProductId && x.Id == y.SuggestedProductId) ? 0 : 1);

        var (products, total) = await query.ApplyTableQueryAsync(request.Page, request.PerPage, request.OrderBy);

        var result = new ProductsSearchResult
        {
            Products = products.Select(x => x with { IsInBundle = x.Bundles.Any() }),
            TotalCount = total
        };

        return result;
    }
}
