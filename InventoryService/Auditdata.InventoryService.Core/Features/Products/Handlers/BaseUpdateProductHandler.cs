using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Abstractions.Services;
using Auditdata.InventoryService.Core.Constants;
using Auditdata.InventoryService.Core.Entities.Products;
using Auditdata.InventoryService.Core.Exceptions;
using Auditdata.InventoryService.Core.Extensions;
using Auditdata.InventoryService.Core.Features.Products.Commands;
using Auditdata.InventoryService.Core.Features.Products.Extensions;
using Auditdata.InventoryService.Core.Features.Products.Validation;
using Auditdata.InventoryService.Core.OperationContext;
using Auditdata.Transport.Contracts.Exceptions;
using Microsoft.EntityFrameworkCore;

namespace Auditdata.InventoryService.Core.Features.Products.Handlers;

public class BaseUpdateProductHandler<TCommand, TProduct>
    where TCommand : UpdateProductCommand
    where TProduct : Product
{
    private readonly IDbContext _dbContext;
    private readonly IInventoryServiceOperationContext _operationContext;
    private readonly IProductUniqueNameValidator _validator;
    private readonly IProductSkusValidator _productSkusValidator;
    private readonly IMapper _mapper;
    private readonly IProductEventPublisher _productEventPublisher;

    public BaseUpdateProductHandler(
        IDbContext dbContext,
        IInventoryServiceOperationContext operationContext,
        IProductUniqueNameValidator validator,
        IMapper mapper,
        IProductEventPublisher productEventPublisher,
        IProductSkusValidator productSkusValidator)
    {
        _dbContext = dbContext;
        _operationContext = operationContext;
        _validator = validator;
        _mapper = mapper;
        _productEventPublisher = productEventPublisher;
        _productSkusValidator = productSkusValidator;
    }

    public async Task UpdateAsync(
        TCommand command,
        CancellationToken cancellationToken)
    {
        var product = await GetProductQuery()
            .FirstOrDefaultAsync(x => x.Id == command.Id, cancellationToken: cancellationToken);
        if (product is null)
        {
            throw new EntityNotFoundException<Product>(command.Id);
        }

        var wasPlacedOnStock = await product.WasPlacedOnStockAsync(_dbContext, cancellationToken);
        
        ValidateProductConversion(command, product, wasPlacedOnStock);
        await _productSkusValidator.ValidateSupplierUsedInSku(product, command.SupplierId, cancellationToken);

        if (command.Attributes is not null)
        {
            _productSkusValidator.ValidateAttributesUsedInSku(product, command.Attributes, command.Colors, command.BatteryTypes);
        }

        var manufacturer = command.ManufacturerId == product.ManufacturerId
            ? product.Manufacturer
            : await _dbContext.Manufacturers.FindAsync(command.ManufacturerId);

        var category = command.CategoryId == product.CategoryId
            ? product.Category
            : await _dbContext.ProductCategories.FindAsync(command.CategoryId);

        var hearingAidType = command.HearingAidTypeId == product.HearingAidTypeId
            ? product.HearingAidType
            : command.HearingAidTypeId.HasValue
                ? await _dbContext.HearingAidTypes.FindAsync(command.HearingAidTypeId!.Value)
                : null;

        var cost = _operationContext.UserCanEditProductCost ? command.Cost : product.Cost;

        var isActiveChanged = command.IsActive != product.IsActive;

        product = _mapper.Map(command, product);

        await _validator.ValidateAndThrowAsync(product, command.Name, cancellationToken);

        product.Category = category;
        product.Manufacturer = manufacturer;
        product.HearingAidType = hearingAidType;
        product.Cost = cost;

        product.ProductPathways.Clear();
        if (command.PathwayIds is not null)
        {
            product.ProductPathways = command.PathwayIds.Select(x => ProductPathway.Create(product.Id, x)).ToList();
        }

        if (command.BatteryTypes is not null)
        {
            ValidateBatteryTypes(command.BatteryTypes);
            product.SetBatteryTypes(command.BatteryTypes);
        }

        product.Colors.Clear();
        if (command.Colors is not null)
        {
            ValidateColors(command.Colors);
            product.Colors = command.Colors.Select(x => ProductColor.Create(product.Id, x)).ToList();
        }

        foreach (var productAttribute in product.Attributes)
        {            
            productAttribute.IsDeleted = true;
        }

        if (command.Attributes is not null)
        {
            foreach (var productAttributeDto in command.Attributes)
            {
                var productAttribute = ProductAttribute.Create(
                    product.Id,
                    productAttributeDto.AttributeId,
                    productAttributeDto.Values.ToList());
                product.Attributes.Add(productAttribute);
            }
        }

        product.SuggestedProducts.Clear();
        if (command.SuggestedProductIds is not null)
        {
            product.SuggestedProducts = command.SuggestedProductIds
                .Select(x => ProductSuggestedProduct.Create(product.Id, x)).ToList();
        }

        product.DescriptionValue = product.Description.ToDescriptionValue();

        HandleCountrySpecific(command, product);
        
        await _productEventPublisher.PublishProductUpdatedAsync(product, isActiveChanged, cancellationToken);
        await _dbContext.SaveChangesAsync(cancellationToken);
    }

    private static void ValidateProductConversion(TCommand command, TProduct product, bool wasPlacedOnStock)
    {
        if (!product.ControlledByStock || wasPlacedOnStock)
        {
            if (!product.IsSerialized && command.IsSerialized)
            {
                throw new BusinessException(
                    "Can't change product to serialized. Product is not controlled by stock or was already placed on the stock",
                    ErrorCodes.ProductCantBeMadeSerialized,
                    command.Id);
            }

            if (product.CategoryId != command.CategoryId)
            {
                throw new BusinessException(
                    "Can't change product category. Product is not controlled by stock or was already placed on the stock",
                    ErrorCodes.ProductCantChangeCategory,
                    command.Id);
            }
        }
    }

    private void ValidateColors(IEnumerable<Guid> colorIds)
    {
        var activeColorIds = _dbContext.Colors.AsNoTracking()
               .Where(x => colorIds.Contains(x.Id) && x.IsActive)
               .Select(x => x.Id)
               .ToList();

        var inactiveColors = colorIds.Except(activeColorIds).ToList();

        if (inactiveColors.Any())
        {
            throw new BusinessException("Color is inactive.",
                ErrorCodes.ColorInactive, inactiveColors.First());
        }
    }

    private void ValidateBatteryTypes(IEnumerable<Guid> batteryTypeIds)
    {
        var activeBatteryTypeIds = _dbContext.BatteryTypes.AsNoTracking()
               .Where(x => batteryTypeIds.Contains(x.Id) && x.IsActive)
               .Select(x => x.Id)
               .ToList();

        var inactiveBatteryTypes = batteryTypeIds.Except(activeBatteryTypeIds).ToList();

        if (inactiveBatteryTypes.Any())
        {
            throw new BusinessException("BatteryType is inactive.",
                ErrorCodes.BatteryTypeInactive, inactiveBatteryTypes.First());
        }
    }

    protected virtual IQueryable<TProduct> GetProductQuery()
    {
        return _dbContext.Set<TProduct>()
            .Include(x => x.Category)
            .Include(x => x.HearingAidType)
            .Include(x => x.ProductPathways)
            .Include(x => x.BatteryTypes)
            .Include(x => x.Category)
            .Include(x => x.HearingAidType)
            .Include(x => x.Manufacturer)
            .Include(x => x.Colors)
            .Include(x => x.Attributes)
                .ThenInclude(x => x.Attribute)
            .Include(x => x.SuggestedProducts)
            .Include(x => x.SkuConfigs)
            .Include(x => x.Skus)
                .ThenInclude(x => x.Attributes);
    }

    protected virtual void HandleCountrySpecific(TCommand command, TProduct product)
    {
    }
}
