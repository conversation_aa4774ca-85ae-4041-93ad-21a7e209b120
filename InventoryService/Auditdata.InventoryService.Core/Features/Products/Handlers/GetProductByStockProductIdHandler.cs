using Auditdata.InventoryService.Core.Exceptions;
using Auditdata.InventoryService.Core.Features.Products.Models;
using Auditdata.InventoryService.Core.Features.Products.Queries;
using Auditdata.InventoryService.Core.OperationContext;

namespace Auditdata.InventoryService.Core.Features.Products.Handlers;

public class GetProductByStockProductIdHandler : IRequestHandler<GetProductByStockProductIdQuery, ProductResult>
{
    private readonly IStockProductsRepository _stockStockProductsRepository;
    private readonly IMapper _mapper;
    private readonly IInventoryServiceOperationContext _operationContext;

    public GetProductByStockProductIdHandler(
        IStockProductsRepository stockProductsRepository,
        IMapper mapper,
        IInventoryServiceOperationContext operationContext)
    {
        _stockStockProductsRepository = stockProductsRepository;
        _mapper = mapper;
        _operationContext = operationContext;
    }

    public async Task<ProductResult> Handle(GetProductByStockProductIdQuery request, CancellationToken cancellationToken)
    {
        var stockProduct = await _stockStockProductsRepository.GetWithProductAsync(request.StockProductId);
        if (stockProduct is null)
        {
            throw new EntityNotFoundException<StockProduct>(request.StockProductId);
        }

        var result = _mapper.Map<ProductResult>(stockProduct.Product);
        if (!_operationContext.UserCanViewProductCost)
        {
            result.Cost = null;
        }

        return result;
    }
}
