using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Abstractions.Services;
using Auditdata.InventoryService.Core.Entities.Products;
using Auditdata.InventoryService.Core.Features.Products.Commands;
using Auditdata.InventoryService.Core.Features.Products.Models;
using Auditdata.InventoryService.Core.Features.Products.Validation;

namespace Auditdata.InventoryService.Core.Features.Products.Handlers;

public class CreateProductHandler :
    BaseCreateProductHandler<CreateProductCommand, Product>,
    IRequestHandler<CreateUKProductCommand, ProductCreated>,
    IRequestHandler<CreateAUProductCommand, ProductCreated>,
    IRequestHandler<CreateROIProductCommand, ProductCreated>,
    IRequestHandler<CreateNZProductCommand, ProductCreated>
{
    private readonly IValidator<CreateUKProductCommand> _ukValidator;
    private readonly IValidator<CreateAUProductCommand> _auValidator;
    private readonly IValidator<CreateROIProductCommand> _roiValidator;
    private readonly IValidator<CreateNZProductCommand> _nzValidator;

    public CreateProductHandler(
        IMapper mapper,
        IProductEventPublisher productEventPublisher,
        IProductUniqueNameValidator productUniqueNameValidator,
        IValidator<CreateUKProductCommand> ukValidator,
        IValidator<CreateAUProductCommand> auValidator,
        IValidator<CreateROIProductCommand> roiValidator,
        IValidator<CreateNZProductCommand> nzValidator,
        IDbContext dbContext) : base(mapper, productEventPublisher, productUniqueNameValidator, dbContext)
    {
        _ukValidator = ukValidator;
        _auValidator = auValidator;
        _roiValidator = roiValidator;
        _nzValidator = nzValidator;
    }

    public async Task<ProductCreated> Handle(CreateUKProductCommand request, CancellationToken cancellationToken)
    {
        await _ukValidator.ValidateAndThrowAsync(request, cancellationToken);
        return await base.CreateAsync(request, cancellationToken);
    }

    public async Task<ProductCreated> Handle(CreateAUProductCommand request, CancellationToken cancellationToken)
    {
        await _auValidator.ValidateAndThrowAsync(request, cancellationToken);
        return await base.CreateAsync(request, cancellationToken);
    }

    public async Task<ProductCreated> Handle(CreateROIProductCommand request, CancellationToken cancellationToken)
    {
        await _roiValidator.ValidateAndThrowAsync(request, cancellationToken);
        return await base.CreateAsync(request, cancellationToken);
    }

    public async Task<ProductCreated> Handle(CreateNZProductCommand request, CancellationToken cancellationToken)
    {
        await _nzValidator.ValidateAndThrowAsync(request, cancellationToken);
        return await base.CreateAsync(request, cancellationToken);
    }
}
