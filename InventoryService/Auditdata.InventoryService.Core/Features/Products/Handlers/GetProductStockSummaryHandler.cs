using Auditdata.InventoryService.Core.Features.Products.Models;
using Auditdata.InventoryService.Core.Features.Products.Queries;

namespace Auditdata.InventoryService.Core.Features.Products.Handlers;

public class GetProductStockSummaryHandler : IRequestHandler<GetProductStockSummaryQuery, ProductStockSummary>
{
    private readonly IStockProductsRepository _stockProductsRepository;

    public GetProductStockSummaryHandler(
        IStockProductsRepository stockProductsRepository)
    {
        _stockProductsRepository = stockProductsRepository;
    }

    public async Task<ProductStockSummary> Handle(GetProductStockSummaryQuery request, CancellationToken cancellationToken)
    {
        var (available, reserved) = await _stockProductsRepository.GetStockProductsSummaryAsync(
            request.ProductId,
            request.LocationIds?.ToList() ?? new List<Guid>());

        var result = new ProductStockSummary(request.ProductId)
        {
            Available = available,
            Booked = reserved
        };

        return result;
    }
}
