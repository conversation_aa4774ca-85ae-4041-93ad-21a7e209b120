using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Abstractions.Services;
using Auditdata.InventoryService.Core.Entities.Products;
using Auditdata.InventoryService.Core.Extensions;
using Auditdata.InventoryService.Core.Features.Products.Commands;
using Auditdata.InventoryService.Core.Features.Products.Models;
using Auditdata.InventoryService.Core.Features.Products.Validation;

namespace Auditdata.InventoryService.Core.Features.Products.Handlers;

public class BaseCreateProductHandler<TCommand, TProduct>
    where TCommand : CreateProductCommand
    where TProduct : Product
{
    private readonly IMapper _mapper;
    private readonly IDbContext _dbContext;
    private readonly IProductUniqueNameValidator _productUniqueNameValidator;
    private readonly IProductEventPublisher _productEventPublisher;

    public BaseCreateProductHandler(
        IMapper mapper,
        IProductEventPublisher productEventPublisher,
        IProductUniqueNameValidator validator,
        IDbContext dbContext)
    {
        _mapper = mapper;
        _productEventPublisher = productEventPublisher;
        _productUniqueNameValidator = validator;
        _dbContext = dbContext;
    }

    public async Task<ProductCreated> CreateAsync(
        TCommand command,
        CancellationToken cancellationToken)
    {
        var product = _mapper.Map<TProduct>(command);
        product.Id = Guid.NewGuid();

        await _productUniqueNameValidator.ValidateAndThrowAsync(product, command.Name, cancellationToken);

        if (product.ManufacturerId.HasValue)
        {
            product.Manufacturer = await _dbContext.Manufacturers.FindAsync(product.ManufacturerId);
        }

        product.Category = await _dbContext.ProductCategories.FindAsync(product.CategoryId);

        product.Colors = command.Colors?.Select(x => ProductColor.Create(product.Id, x)).ToList();

        if (command.BatteryTypes != null && command.BatteryTypes.Any())
        {
            product.SetBatteryTypes(command.BatteryTypes);
        }

        product.ProductPathways = command.PathwayIds?.Select(x => ProductPathway.Create(product.Id, x)).ToList();
        product.Attributes = command.Attributes.Select(
            x => ProductAttribute.Create(product.Id, x.AttributeId, x.Values.ToList())).ToList();
        product.SuggestedProducts = command.SuggestedProductIds?
            .Select(x => ProductSuggestedProduct.Create(product.Id, x)).ToList()!;

        product.DescriptionValue = product.Description.ToDescriptionValue();

        HandleCountrySpecific(command, product);

        _dbContext.Products.Add(product);

        await _productEventPublisher.PublishProductCreatedAsync(product, cancellationToken);
        await _dbContext.SaveChangesAsync(cancellationToken);

        return new ProductCreated(product.Id, product.Name);
    }

    protected virtual void HandleCountrySpecific(TCommand command, TProduct product)
    {
    }
}
