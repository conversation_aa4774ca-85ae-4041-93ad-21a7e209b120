using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Abstractions.Services;
using Auditdata.InventoryService.Core.Entities.Products;
using Auditdata.InventoryService.Core.Features.Products.Commands;
using Auditdata.InventoryService.Core.Features.Products.Validation;
using Auditdata.InventoryService.Core.OperationContext;

namespace Auditdata.InventoryService.Core.Features.Products.Handlers;

public class UpdateProductHandler :
    BaseUpdateProductHandler<UpdateProductCommand, Product>,
    IRequestHandler<UpdateUKProductCommand>,
    IRequestHandler<UpdateAUProductCommand>,
    IRequestHandler<UpdateROIProductCommand>,
    IRequestHandler<UpdateNZProductCommand>
{
    private readonly IValidator<UpdateUKProductCommand> _ukValidator;
    private readonly IValidator<UpdateAUProductCommand> _auValidator;
    private readonly IValidator<UpdateROIProductCommand> _roiValidator;
    private readonly IValidator<UpdateNZProductCommand> _nzValidator;

    public UpdateProductHandler(
        IProductUniqueNameValidator productUniqueNameValidator,
        IValidator<UpdateUKProductCommand> ukValidator,
        IValidator<UpdateAUProductCommand> auValidator,
        IValidator<UpdateROIProductCommand> roiValidator,
        IValidator<UpdateNZProductCommand> nzValidator,
        IProductSkusValidator productSkusValidator,
        IMapper mapper,
        IInventoryServiceOperationContext operationContext, 
        IDbContext dbContext,
        IProductEventPublisher productEventPublisher) :
            base(dbContext, operationContext, productUniqueNameValidator, mapper, productEventPublisher, productSkusValidator)
    {
        _ukValidator = ukValidator;
        _auValidator = auValidator;
        _roiValidator = roiValidator;
        _nzValidator = nzValidator;
    }

    public async Task Handle(UpdateUKProductCommand request,
        CancellationToken cancellationToken)
    {
        await _ukValidator.ValidateAndThrowAsync(request, cancellationToken);
        await base.UpdateAsync(request, cancellationToken);
    }

    public async Task Handle(UpdateAUProductCommand request,
        CancellationToken cancellationToken)
    {
        await _auValidator.ValidateAndThrowAsync(request, cancellationToken);
        await base.UpdateAsync(request, cancellationToken);
    }

    public async Task Handle(UpdateROIProductCommand request,
        CancellationToken cancellationToken)
    {
        await _roiValidator.ValidateAndThrowAsync(request, cancellationToken);
        await base.UpdateAsync(request, cancellationToken);
    }

    public async Task Handle(UpdateNZProductCommand request,
        CancellationToken cancellationToken)
    {
        await _nzValidator.ValidateAndThrowAsync(request, cancellationToken);
        await base.UpdateAsync(request, cancellationToken);
    }
}
