using Auditdata.InventoryService.Contracts.Responses.StockProducts;
using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Constants;
using Auditdata.InventoryService.Core.Features.Products.Commands;
using Auditdata.InventoryService.Core.Features.StockProductItems.Events;
using Auditdata.Transport.Contracts.Exceptions;
using Microsoft.EntityFrameworkCore;

namespace Auditdata.InventoryService.Core.Features.Products.Handlers;

public class ReserveProductHandler : IRequestHandler<ReserveProductCommand, List<ReserveProductResponse>>
{
    private readonly IDbContext _dbContext;
    private readonly IMediator _mediator;

    public ReserveProductHandler(
        IDbContext dbContext,
        IMediator mediator)
    {
        _dbContext = dbContext;
        _mediator = mediator;
    }

    public async Task<List<ReserveProductResponse>> Handle(ReserveProductCommand request, CancellationToken cancellationToken)
    {
        var stockProductItemIds = request.Items.Select(x => x.StockProductItemId).ToList();
        if (stockProductItemIds.Exists(x => !x.HasValue))
        {
            throw new BusinessException(ErrorCodes.StockProductItemReservationEmptyId);
        }

        if (stockProductItemIds.Count != stockProductItemIds.Distinct().Count())
        {
            throw new BusinessException(ErrorCodes.StockProductItemReservationDuplicateItem);
        }

        var stockProductItems = await _dbContext.StockProductItems
            .Include(x => x.StockProduct).ThenInclude(x => x.Product)
            .Where(x => stockProductItemIds.Contains(x.Id))
            .ToListAsync(cancellationToken);

        var reservedProductItems = new List<StockProductItem>();
        foreach (var stockProductItem in stockProductItems)
        {
            var stockProduct = stockProductItem.StockProduct;
            stockProduct.ReserveItem(stockProductItem, request.SaleId, request.PatientId);
            reservedProductItems.Add(stockProductItem);
        }

        await _mediator.Publish(
            new StockProductItemsReservedEvent(stockProductItems, request.SaleId, request.PatientName),
            cancellationToken);

        await _dbContext.SaveChangesAsync(cancellationToken);

        return reservedProductItems.Select(x => new ReserveProductResponse(x.StockProductId, x.Id, x.SerialNumber!)).ToList();
    }
}
