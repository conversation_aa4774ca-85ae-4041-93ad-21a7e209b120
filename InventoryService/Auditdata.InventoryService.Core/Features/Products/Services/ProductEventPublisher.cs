using Auditdata.InventoryService.Contracts.Commands;
using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Abstractions.Azure;
using Auditdata.InventoryService.Core.Abstractions.Services;
using Auditdata.InventoryService.Core.Entities.Products;
using Auditdata.InventoryService.Core.Features.Products.Events;

namespace Auditdata.InventoryService.Core.Features.Products.Services;

public class ProductEventPublisher : IProductEventPublisher
{
    private readonly IAzureServiceBusPublisher _azureServiceBusPublisher;
    private readonly IEventPublisher _eventPublisher;
    private readonly IMediator _mediator;

    public ProductEventPublisher(
        IAzureServiceBusPublisher azureServiceBusPublisher,
        IEventPublisher eventPublisher,
        IMediator mediator)
    {
        _azureServiceBusPublisher = azureServiceBusPublisher;
        _eventPublisher = eventPublisher;
        _mediator = mediator;
    }

    public async Task PublishProductCreatedAsync(Product product, CancellationToken cancellationToken)
    {
        await _eventPublisher.ProductCreated(product, cancellationToken);
        await _azureServiceBusPublisher.PublishAddProductToAllStocksCommand(
            new AddProductToAllStocksCommand(product.Id));
    }

    public async Task PublishProductsCreatedAsync(IEnumerable<Product> products, CancellationToken cancellationToken)
    {
        await _eventPublisher.ProductsCreated(products, cancellationToken);
        await _azureServiceBusPublisher.PublishAddProductsToAllStocksCommand(
            products.Select(p => new AddProductToAllStocksCommand(p.Id)));
    }

    public async Task PublishProductUpdatedAsync(Product product, bool isActiveChanged, CancellationToken cancellationToken)
    {
        await _eventPublisher.ProductUpdated(product, cancellationToken);

        if (isActiveChanged)
        {
            await _mediator.Publish(new ProductIsActiveUpdated(product.Id, product.IsActive), cancellationToken);
        }
    }
}
