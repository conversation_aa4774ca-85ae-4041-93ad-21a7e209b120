using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Constants;
using Auditdata.InventoryService.Core.Exceptions;
using Auditdata.InventoryService.Core.Features.Products.Commands;
using Auditdata.Transport.Contracts.Exceptions;
using Microsoft.EntityFrameworkCore;

namespace Auditdata.InventoryService.Core.Features.Products.Validation;

public class CreateProductCommandValidator<TCreateProductCommand> : AbstractValidator<TCreateProductCommand>
    where TCreateProductCommand : CreateProductCommand
{
    private readonly IDbContext _dbContext;

    public CreateProductCommandValidator(IDbContext dbContext)
    {
        _dbContext = dbContext;

        WhenAsync(async (request, cancellationToken) =>
        {
            var category = await GetCategoryByIdAsync(request.CategoryId, cancellationToken);
            return category.Code == ProductCategoryCode.Batteries || category.Code == ProductCategoryCode.HearingAids;
        },
        () =>
        {
            RuleFor(y => y.BatteryTypes).NotEmpty();
        });
        
        WhenAsync(async (request, cancellationToken) =>
        {
            var category = await GetCategoryByIdAsync(request.CategoryId, cancellationToken);
            return category.Code == ProductCategoryCode.Accessories || category.Code == ProductCategoryCode.HearingAids;
        },
        () =>
        {
            RuleFor(y => y.Colors).NotEmpty();
        });
        
        WhenAsync(async (request, cancellationToken) =>
        {
            var category = await GetCategoryByIdAsync(request.CategoryId, cancellationToken);
            return category.Code == ProductCategoryCode.Service || category.Code == ProductCategoryCode.RepairService;
        },
        () =>
        {
            RuleFor(y => y.Attributes).Empty();
        });
        
        WhenAsync(async (request, cancellationToken) =>
        {
            var category = await GetCategoryByIdAsync(request.CategoryId, cancellationToken);
            return category.Code != ProductCategoryCode.Batteries && category.Code != ProductCategoryCode.HearingAids;
        },
        () =>
        {
            RuleFor(y => y.BatteryTypes).Empty();
        });
        
        WhenAsync(async (request, cancellationToken) =>
        {
            var category = await GetCategoryByIdAsync(request.CategoryId, cancellationToken);
            return category.Code != ProductCategoryCode.Accessories && category.Code != ProductCategoryCode.HearingAids;
        },
        () =>
        {
            RuleFor(y => y.Colors).Empty();
        });

        WhenAsync(async (request, cancellationToken) =>
        {
            var category = await GetCategoryByIdAsync(request.CategoryId, cancellationToken);
            return category.Code == ProductCategoryCode.HearingAids;
        },
        () =>
        {
            RuleFor(y => y.HearingAidTypeId).NotEmpty();
        });
        
        RuleFor(x => x)
        .CustomAsync(async (request, context, cancellationToken) =>
        {
            var category = await GetCategoryByIdAsync(request.CategoryId, cancellationToken);

            if (category.Code != ProductCategoryCode.Service &&
                category.Code != ProductCategoryCode.RepairService &&
                request.Attributes?.Any() == true)
            {
                var hasInvalidAttributes = await _dbContext.Attributes
                    .AsNoTracking()
                    .Where(a => request.Attributes.Select(x => x.AttributeId).Contains(a.Id))
                    .Where(a => a.ProductCategories.All(pc => pc.Id != category.Id))
                    .AnyAsync(cancellationToken);

                if (hasInvalidAttributes)
                {
                    throw new BusinessException("One or more attributes are not valid for the selected category",
                        ErrorCodes.InvalidAttributes);
                }
            }
        });
    }

    protected async Task<ProductCategory> GetCategoryByIdAsync(Guid categoryId, CancellationToken cancellationToken)
    {
        var cachedCategory = _dbContext.ProductCategories.Local
            .FirstOrDefault(x => x.Id == categoryId);

        if (cachedCategory is not null)
        {
            return cachedCategory;
        }

        return await _dbContext.ProductCategories
            .FirstOrDefaultAsync(x => x.Id == categoryId, cancellationToken)
            ?? throw new EntityNotFoundException<ProductCategory>(categoryId);
    }
}
