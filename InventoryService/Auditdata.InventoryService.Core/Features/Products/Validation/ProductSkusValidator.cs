using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Constants;
using Auditdata.InventoryService.Core.Entities.Products;
using Auditdata.InventoryService.Core.Features.Products.Models;
using Auditdata.Transport.Contracts.Exceptions;
using Microsoft.EntityFrameworkCore;

namespace Auditdata.InventoryService.Core.Features.Products.Validation;

public class ProductSkusValidator : IProductSkusValidator
{
    private readonly IDbContext _dbContext;

    public ProductSkusValidator(IDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    public void ValidateAttributesUsedInSku(
        Product product,
        IEnumerable<ProductAttributeDto> attributeDtos,
        IEnumerable<Guid>? colors,
        IEnumerable<Guid>? batteryTypes)
    {
        var usedAttributes = new List<string>();

        ValidateRemovedAttributes(product, attributeDtos, usedAttributes);

        ValidateRemovedEntities(
            product.Colors,
            colors,
            usedAttributes,
            "Colors",
            entity => entity.ColorId,
            product,
            SkuAttributeType.Color);

        ValidateRemovedEntities(
            product.BatteryTypes,
            batteryTypes,
            usedAttributes,
            "Battery Types",
            entity => entity.BatteryTypeId,
            product,
            SkuAttributeType.BatteryType);

        if (usedAttributes.Count != 0)
        {
            throw new BusinessException(
                "The updated attributes have been used for SKU creation. Please remove any existing SKUs before updating the product.",
                ErrorCodes.AttributesUsedInSkuAndCannotUpdate);
        }
    }

    public async Task ValidateSupplierUsedInSku(Product product, Guid? supplierId, CancellationToken cancellationToken)
    {
        if (product.SupplierId is not null)
        {
            var existingSku = await _dbContext.Skus
                .FirstOrDefaultAsync(x => x.SupplierId == product.SupplierId && x.ProductId == product.Id, cancellationToken);

            if (existingSku != null &&
                (product.SupplierId != supplierId || supplierId is null))
            {
                throw new BusinessException(
                    "The updated Supplier has been used for SKU creation. Please remove any existing SKUs before updating.",
                    ErrorCodes.SupplierUsedInSkuAndCannotUpdate);
            }
        }
    }

    private static void ValidateRemovedAttributes(
        Product product,
        IEnumerable<ProductAttributeDto> attributeDtos,
        List<string> usedAttributes)
    {
        var removedAttributes = product.Attributes
            .Where(pa =>
                !attributeDtos.Any(dto => dto.AttributeId == pa.AttributeId) ||
                pa.Value.Any(existingValue =>
                    !attributeDtos.Any(dto =>
                        dto.AttributeId == pa.AttributeId &&
                        dto.Values.Any(updatedValue => updatedValue.ValueId == existingValue.ValueId))))
            .ToList();

        foreach (var attribute in removedAttributes)
        {
            if (attribute.Attribute == null) continue;

            var removedValues = attribute.Value.Where(existingValue =>
                !attributeDtos.Any(dto =>
                    dto.AttributeId == attribute.AttributeId &&
                    dto.Values.Any(updatedValue => updatedValue.ValueId == existingValue.ValueId)));

            var isUsedInSku = removedValues.Any(value =>
                product.Skus.Any(sku => sku.Attributes.Any(skuAttr =>
                    skuAttr.AttributeId == attribute.AttributeId &&
                    skuAttr.Value.ValueId == value.ValueId)));

            if (isUsedInSku)
            {
                usedAttributes.Add(attribute.Attribute.Name);
                continue; 
            }

            var isAttributeFullyDeleted = !attributeDtos.Any(dto => dto.AttributeId == attribute.AttributeId);
            if (isAttributeFullyDeleted)
            {
                var isUsedInSkuConfig = product.SkuConfigs.Any(config =>
                    config.SkuAttributeType == SkuAttributeType.Attribute &&
                    config.AttributeId == attribute.AttributeId);

                if (isUsedInSkuConfig)
                {
                    usedAttributes.Add(attribute.Attribute.Name);
                }
            }
        }
    }

    private static void ValidateRemovedEntities<T>(
        IEnumerable<T>? entities,
        IEnumerable<Guid>? updatedEntityIds,
        List<string> usedAttributes,
        string entityName,
        Func<T, Guid> idSelector,
        Product product,
        SkuAttributeType attributeType)
    {
        if (entities == null || updatedEntityIds == null) return;

        var removedEntities = entities
            .Where(entity => updatedEntityIds.All(id => id != idSelector(entity)))
            .ToList();

        var isUsed = removedEntities.Any(entity =>
        {
            var entityId = idSelector(entity);

            var isUsedInSku = product.Skus.Any(sku =>
                attributeType switch
                {
                    SkuAttributeType.Color => sku.ColorId == entityId,
                    SkuAttributeType.BatteryType => sku.BatteryTypeId == entityId,
                    _ => false
                });

            var isUsedInSkuConfig = !isUsedInSku && product.SkuConfigs.Any(config =>
                config.SkuAttributeType == attributeType &&
                config.AttributeId == entityId);

            return isUsedInSku || isUsedInSkuConfig;
        });

        if (isUsed)
        {
            usedAttributes.Add(entityName);
        }
    }
}

public interface IProductSkusValidator
{
    void ValidateAttributesUsedInSku(
        Product product,
        IEnumerable<ProductAttributeDto> attributeDtos,
        IEnumerable<Guid>? colors,
        IEnumerable<Guid>? batteryTypes);

    Task ValidateSupplierUsedInSku(Product product, Guid? supplierId, CancellationToken cancellationToken);
}
