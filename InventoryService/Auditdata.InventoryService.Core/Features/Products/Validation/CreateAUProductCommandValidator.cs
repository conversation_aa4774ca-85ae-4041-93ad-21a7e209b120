using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Features.Products.Commands;

namespace Auditdata.InventoryService.Core.Features.Products.Validation;

public class CreateAUProductCommandValidator : CreateProductCommandValidator<CreateAUProductCommand>
{
    public CreateAUProductCommandValidator(IDbContext dbContext)
        : base(dbContext)
    {
        When(x => x.IsHsp, () =>
        {
            When(x => x.Hsp is not null, () =>
            {
                WhenAsync(async (x, cancellationToken) =>
                {
                    var category = await GetCategoryByIdAsync(x.CategoryId, cancellationToken);
                    return category.Code == ProductCategoryCode.HearingAids;
                },
                () =>
                {
                    RuleFor(x => x.Hsp!.Code).NotEmpty();
                    RuleFor(x => x.Hsp!.Category).NotEmpty();
                });

                WhenAsync(async (x, cancellationToken) =>
                {
                    var category = await GetCategoryByIdAsync(x.CategoryId, cancellationToken);
                    return category.Code == ProductCategoryCode.Service && x.Hsp!.IsMaintenance is not true;
                },
                () =>
                {
                    RuleFor(x => x.Hsp!.ServiceNumber).NotEmpty();
                    RuleFor(x => x.Hsp!.ServiceType).NotEmpty().IsInEnum();
                });
            });
        });
    }
}
