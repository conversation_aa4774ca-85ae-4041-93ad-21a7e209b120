using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Exceptions;
using Auditdata.InventoryService.Core.Features.Products.Commands;
using Microsoft.EntityFrameworkCore;

namespace Auditdata.InventoryService.Core.Features.Products.Validation;

public class UpdateProductCommandValidator<TUpdateProductCommand> : AbstractValidator<TUpdateProductCommand>
    where TUpdateProductCommand : UpdateProductCommand
{
    private readonly IDbContext _dbContext;

    public UpdateProductCommandValidator(IDbContext dbContext)
    {
        _dbContext = dbContext;

        WhenAsync(async (request, cancellationToken) =>
        {
            var category = await GetCategoryByIdAsync(request.CategoryId, cancellationToken);
            return category.Code == ProductCategoryCode.Batteries;
        },
        () =>
        {
            RuleFor(y => y.BatteryTypes).NotEmpty();
        });

        WhenAsync(async (request, cancellationToken) =>
        {
            var category = await GetCategoryByIdAsync(request.CategoryId, cancellationToken);
            return category.Code == ProductCategoryCode.HearingAids;
        },
        () =>
        {
            RuleFor(y => y.BatteryTypes).NotEmpty();
            RuleFor(y => y.HearingAidTypeId).NotEmpty();
        });
    }

    protected async Task<ProductCategory> GetCategoryByIdAsync(Guid categoryId, CancellationToken cancellationToken)
    {
        var cachedCategory = _dbContext.ProductCategories.Local
            .FirstOrDefault(x => x.Id == categoryId);

        if (cachedCategory is not null)
        {
            return cachedCategory;
        }

        return await _dbContext.ProductCategories
            .FirstOrDefaultAsync(x => x.Id == categoryId, cancellationToken)
            ?? throw new EntityNotFoundException<ProductCategory>(categoryId);
    }
}
