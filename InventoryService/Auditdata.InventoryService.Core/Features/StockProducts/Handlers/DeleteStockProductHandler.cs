using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Abstractions.Azure;
using Auditdata.InventoryService.Core.Constants;
using Auditdata.InventoryService.Core.Features.StockProducts.Commands;
using Auditdata.Transport.Contracts.Exceptions;
using Microsoft.EntityFrameworkCore;

namespace Auditdata.InventoryService.Core.Features.StockProducts.Handlers;

public class DeleteStockProductHandler : IRequestHandler<DeleteStockProductCommand>
{
    private readonly IAzureServiceBusPublisher _azureServiceBusPublisher;
    private readonly IDbContext _dbContext;

    public DeleteStockProductHandler(
        IAzureServiceBusPublisher azureServiceBusPublisher,
        IDbContext dbContext)
    {
        _azureServiceBusPublisher = azureServiceBusPublisher;
        _dbContext = dbContext;
    }

    public async Task Handle(
        DeleteStockProductCommand request, CancellationToken cancellationToken)
    {
        var stockProducts = await _dbContext.StockProducts.Where(x => x.ProductId == request.ProductId && !x.IsDeleted)
            .ToListAsync(cancellationToken);

        if (stockProducts.Count > stockProducts.Count(x => x.Quantity == 0))
        {
            throw new BusinessException($"Stock product can't be for a product {request.ProductId} deleted", ErrorCodes.StockProductCantDelete, request.ProductId);
        }

        foreach (var stockProductToDelete in stockProducts)
        {
            _dbContext.StockProducts.Remove(stockProductToDelete);

            await _azureServiceBusPublisher.PublishStockProductDeleted(
                new Microservice.Messages.Events.Inventory.StockProductDeleted { Id = stockProductToDelete.Id });
        }

        await _dbContext.SaveChangesAsync(cancellationToken);

    }
}
