using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Features.StockProducts.Models;
using Auditdata.InventoryService.Core.Features.StockProducts.Queries;
using Microsoft.EntityFrameworkCore;

namespace Auditdata.InventoryService.Core.Features.StockProducts.Handlers;

public class GetInStockAmountHandler : IRequestHandler<GetInStockAmountQuery, GetInStockAmountResult>
{
    private readonly IDbContext _dbContext;

    public GetInStockAmountHandler(IDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    public async Task<GetInStockAmountResult> Handle(GetInStockAmountQuery request, CancellationToken cancellationToken)
    {
        var inStock = await _dbContext.StockProducts
            .Include(x => x.StockProductItems)
            .Include(x => x.Stock)
            .Where(x => x.ProductId == request.ProductId
            && request.LocationIds.Contains(x.Stock.LocationId))
            .Select(x => new
            {
                x.Id,
                x.Quantity,
                Reserved = x.StockProductItems.Count(y => y.Status == StockProductItemStatus.Reserved
                    || y.Status == StockProductItemStatus.ReservedByOrder)
            })
            .GroupBy(x => new { x.Id, x.Quantity, x.Reserved }, (key, rows) =>
            new
            {
                Available = key.Quantity,
                key.Reserved
            })
            .SumAsync(x => x.Available + x.Reserved, cancellationToken);

        return new GetInStockAmountResult(inStock);
    }
}
