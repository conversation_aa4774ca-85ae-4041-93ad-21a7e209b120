using Auditdata.InventoryService.Contracts.Responses.StockProduct;
using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Abstractions.Validators;
using Auditdata.InventoryService.Core.Exceptions;
using Auditdata.InventoryService.Core.Features.StockProductItems.Events;
using Auditdata.InventoryService.Core.Features.StockProducts.Commands;
using Microsoft.EntityFrameworkCore;

namespace Auditdata.InventoryService.Core.Features.StockProducts.Handlers;

public class AdjustSerializedStockProductHandler :
    IRequestHandler<AdjustSerializedStockProductCommand, IEnumerable<AdjustSerializedProductResponse>>
{
    private readonly IDbContext _dbContext;
    private readonly IMediator _mediator;
    private readonly ISerialNumbersValidator _serialNumbersValidator;

    public AdjustSerializedStockProductHandler(
        IDbContext dbContext,
        IMediator mediator,
        ISerialNumbersValidator serialNumbersValidator)
    {
        _dbContext = dbContext;
        _mediator = mediator;
        _serialNumbersValidator = serialNumbersValidator;
    }

    public async Task<IEnumerable<AdjustSerializedProductResponse>> Handle(
        AdjustSerializedStockProductCommand request, CancellationToken cancellationToken)
    {
        var stockProduct = await _dbContext.StockProducts
            .Include(x => x.Stock)
            .Include(x => x.Product)
                .ThenInclude(x => x.Category)
            .Include(x => x.Product)
                .ThenInclude(x => x.BatteryTypes)
            .Include(x => x.Product)
                .ThenInclude(x => x.Colors)
            .Include(x => x.Product)
                .ThenInclude(x => x.Attributes)
                .ThenInclude(x => x.Attribute)
                .ThenInclude(x => x!.ProductCategories)
            .FirstOrDefaultAsync(x => x.Id == request.StockProductId, cancellationToken);
        if (stockProduct is null)
        {
            throw new EntityNotFoundException<StockProduct>(request.StockProductId);
        }

        await _serialNumbersValidator.CheckDuplicatedSerialNumbers(
            stockProduct.Product.ManufacturerId!.Value, request.SerialNumbers, cancellationToken);

        var batteryType = await FetchBatteryTypeAsync(request.BatteryTypeId, cancellationToken);
        var color = await FetchColorAsync(request.ColorId, cancellationToken);

        var stockProductItems = stockProduct.AdjustSerializedItems(request.SerialNumbers, color, batteryType, request.Attributes.ToList());

        await _mediator.Publish(new SerialNumbersAssignedEvent(stockProductItems, stockProduct), cancellationToken);
        await _dbContext.SaveChangesAsync(cancellationToken);

        return stockProductItems.Select(spi => new AdjustSerializedProductResponse(spi.Id, spi.SerialNumber!));
    }

    private async Task<BatteryType?> FetchBatteryTypeAsync(Guid? batteryTypeId, CancellationToken cancellationToken)
    {
        if (batteryTypeId is null)
        {
            return null;
        }

        var batteryType = await _dbContext.BatteryTypes.FindAsync(
            new object?[] { batteryTypeId }, cancellationToken: cancellationToken);
        return batteryType ?? throw new EntityNotFoundException<BatteryType>(batteryTypeId.Value);
    }

    private async Task<Color?> FetchColorAsync(Guid? colorId, CancellationToken cancellationToken)
    {
        if (colorId is null)
        {
            return null;
        }

        var color = await _dbContext.Colors.FindAsync(
            new object?[] { colorId }, cancellationToken);
        return color ?? throw new EntityNotFoundException<Color>(colorId.Value);
    }
}
