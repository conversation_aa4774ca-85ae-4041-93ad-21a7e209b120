using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Entities.Products;
using Auditdata.InventoryService.Core.Features.StockProducts.Queries;
using Microsoft.EntityFrameworkCore;

namespace Auditdata.InventoryService.Core.Features.StockProducts.Handlers;

public abstract class BaseGetInvoiceStockProductsHandler(IDbContext dbContext)
{
    protected Task<IQueryable<Product>> GetProductsBaseQueryAsync(GetInvoiceStockProductsQuery request)
    {
        if (string.IsNullOrEmpty(request.OrderBy))
        {
            request.OrderBy = $"{nameof(Product.Name)}";
        }

        var query = dbContext.Products.AsNoTracking()
            .AsSplitQuery()
            .Include(x => x.HearingAidType)
            .Include(x => x.Category)
            .Include(x => x.Manufacturer)
            .Include(x => x.ProductPathways)
            .Include(x => x.Colors)
                .ThenInclude(x => x.Color)
            .Include(x => x.BatteryTypes)
                .ThenInclude(x => x.BatteryType)
            .Include(x => x.Attributes)
                .ThenInclude(x => x.Attribute)
            .Where(x => x.IsActive && x.IsSellable);
        
        if (request.PathwayId.HasValue)
        {
            query = query.Where(x => x.ProductPathways.Any(y => y.PathwayId == request.PathwayId));
        }

        if (request.ManufacturerId.HasValue)
        {
            query = query.Where(x => x.ManufacturerId == request.ManufacturerId);
        }

        if (request.CategoryId.HasValue)
        {
            query = query.Where(x => x.CategoryId == request.CategoryId);
        }

        if (request.HearingAidTypeId.HasValue)
        {
            query = query.Where(x => x.HearingAidTypeId == request.HearingAidTypeId);
        }

        if (request.IsSerialized.HasValue)
        {
            query = query.Where(x => x.IsSerialized == request.IsSerialized);
        }
        
        if (request.CategoryCode.HasValue)
        {
            query = query.Where(x => x.Category.Code == request.CategoryCode);
        }

        if (request.IsFastTrack.HasValue)
        {
            query = query.Where(x => x.IsFastTrack == request.IsFastTrack.Value);
        }

        return Task.FromResult(query);
    }
    
    protected async Task<IEnumerable<StockProduct>> GetStockProductsAsync(
        GetInvoiceStockProductsQuery request, IEnumerable<Product> products, CancellationToken cancellationToken)
    {
        var stockProducts = await dbContext.StockProducts.AsNoTracking()
            .Include(x => x.StockProductItems)
            .Where(x => products.Select(y => y.Id).Contains(x.ProductId)
                        && x.Stock.LocationId == request.LocationId)
            .ToListAsync(cancellationToken);
        
        stockProducts.ForEach(x => x.Product = products.FirstOrDefault(y => y.Id == x.ProductId)!);
        
        return stockProducts.Where(x => x.Product != null!);
    }
}
