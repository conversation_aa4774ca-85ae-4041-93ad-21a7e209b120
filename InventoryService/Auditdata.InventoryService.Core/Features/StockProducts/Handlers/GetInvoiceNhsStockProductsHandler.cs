using Auditdata.InventoryService.Contracts.Responses.StockProducts.Nhs;
using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Entities.Products;
using Auditdata.InventoryService.Core.Features.StockProducts.Mappings;
using Auditdata.InventoryService.Core.Features.StockProducts.Models;
using Auditdata.InventoryService.Core.Features.StockProducts.Queries;
using Auditdata.Transport.Contracts.PageResults;
using Auditdata.Transport.Contracts.Table;
using Microsoft.EntityFrameworkCore;

namespace Auditdata.InventoryService.Core.Features.StockProducts.Handlers;

public class GetInvoiceNhsStockProductsHandler(IDbContext dbContext) :
    BaseGetInvoiceStockProductsHandler(dbContext),
    IRequestHandler<GetInvoiceNhsStockProductsQuery, GetNhsInvoiceStockProductsResult>
{
    public async Task<GetNhsInvoiceStockProductsResult> Handle(
        GetInvoiceNhsStockProductsQuery request, CancellationToken cancellationToken)
    {
        var productsQuery = await GetProductsBaseQueryAsync(request);
        
        if (!string.IsNullOrEmpty(request.SearchText))
        {
            productsQuery = productsQuery.Where(x =>
                x.Name.Contains(request.SearchText) || x.DescriptionValue!.Contains(request.SearchText));
        }
        
        if (request.ContractId.HasValue)
        {
            productsQuery =
                productsQuery.Where(x => 
                    x.NhsContractProducts.Any(y => y.ContractId == request.ContractId)
                    && ((UKProduct?)x)!.IsNHS);
        }

        if (request.PathwayIds.Count != 0)
        {
            productsQuery = productsQuery.Where(x =>
                x.ProductPathways.Any(y => request.PathwayIds.Contains(y.PathwayId!.Value)));
        }

        var (result, total) = await productsQuery.ApplyTableQueryAsync(
            request.Page, request.PerPage, request.OrderBy, cancellationToken);
        
        var stockProducts = await GetStockProductsAsync(request, result, cancellationToken);
        
        var invoiceProductResponses = stockProducts
            .Select(x => x.Product.MapToInvoiceNhsProductResponse(x));

        var tableResult = new TablePageResult<InvoiceNhsProductResponse>(
            invoiceProductResponses.Select(x => new TableRow<InvoiceNhsProductResponse>(x)).ToArray(), request, total);

        return new GetNhsInvoiceStockProductsResult(tableResult);
    }
}
