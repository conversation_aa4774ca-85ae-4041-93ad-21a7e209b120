using Auditdata.InventoryService.Contracts.Responses.StockProducts.Hsp;
using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Entities.Products;
using Auditdata.InventoryService.Core.Features.StockProducts.Mappings;
using Auditdata.InventoryService.Core.Features.StockProducts.Models;
using Auditdata.InventoryService.Core.Features.StockProducts.Queries;
using Auditdata.Transport.Contracts.PageResults;
using Auditdata.Transport.Contracts.Table;
using Microsoft.EntityFrameworkCore;

namespace Auditdata.InventoryService.Core.Features.StockProducts.Handlers;

public class GetInvoiceHspStockProductsHandler : BaseGetInvoiceStockProductsHandler,
    IRequestHandler<GetInvoiceHspStockProductsQuery, GetHspInvoiceStockProductsResult>
{
    public GetInvoiceHspStockProductsHandler(
        IDbContext dbContext) : base(dbContext)
    {
    }

    public async Task<GetHspInvoiceStockProductsResult> Handle(
        GetInvoiceHspStockProductsQuery request, CancellationToken cancellationToken)
    {
        var query = await GetProductsBaseQueryAsync(request);

        if (!string.IsNullOrEmpty(request.SearchText))
        {
            query = query.Where(x =>
                x.Name.Contains(request.SearchText) ||
                x.DescriptionValue!.Contains(request.SearchText) ||
                ((AUProduct)x).HspServiceNumber!.Contains(request.SearchText));
        }

        if (request.HspServiceType.HasValue)
        {
            query = query.Where(x => ((AUProduct)x).HspServiceType == request.HspServiceType);
        }
        else
        {
            query = query.Where(x => ((AUProduct)x).HspServiceType == null);
        }

        var (result, total) = await query.ApplyTableQueryAsync(
            request.Page, request.PerPage, request.OrderBy, cancellationToken);

        var stockProducts = await GetStockProductsAsync(request, result, cancellationToken);
        
        var invoiceProductResponses = stockProducts
            .Select(x => x.Product.MapToInvoiceHspProductResponse(x));

        var tableResult = new TablePageResult<InvoiceHspProductResponse>(
            invoiceProductResponses.Select(x => new TableRow<InvoiceHspProductResponse>(x)).ToArray(), request, total);

        return new GetHspInvoiceStockProductsResult(tableResult);
    }
}
