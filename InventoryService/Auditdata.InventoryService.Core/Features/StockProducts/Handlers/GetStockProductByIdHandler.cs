using Auditdata.InventoryService.Core.Exceptions;
using Auditdata.InventoryService.Core.Features.StockProducts.Queries;

namespace Auditdata.InventoryService.Core.Features.StockProducts.Handlers;

public class GetStockProductByIdHandler : IRequestHandler<GetStockProductByIdQuery, StockProduct>
{
    private readonly IStockProductsRepository _stockProductsRepository;

    public GetStockProductByIdHandler(IStockProductsRepository stockProductsRepository)
    {
        _stockProductsRepository = stockProductsRepository;
    }

    public async Task<StockProduct> Handle(GetStockProductByIdQuery request, CancellationToken cancellationToken)
    {
        var stockProduct = await _stockProductsRepository.GetWithStockAsync(request.Id);
        if (stockProduct is null)
        {
            throw new EntityNotFoundException<StockProduct>(request.Id);
        }

        return stockProduct;
    }
}
