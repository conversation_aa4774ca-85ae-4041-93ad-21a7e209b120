using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Abstractions.Azure;
using Auditdata.InventoryService.Core.Constants;
using Auditdata.InventoryService.Core.Features.StockProducts.Commands;
using Auditdata.Microservice.Messages.Events.Inventory;
using Auditdata.Transport.Contracts.Exceptions;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Auditdata.InventoryService.Core.Features.StockProducts.Handlers;

public class CreateStockProductsForAllStocksHandler : IRequestHandler<CreateStockProductsForAllStocksCommand>
{
    private readonly IMapper _mapper;
    private readonly IAzureServiceBusPublisher _azureServiceBusPublisher;
    private readonly IDbContext _dbContext;
    private readonly ILogger<CreateStockProductsForAllStocksHandler> _logger;

    public CreateStockProductsForAllStocksHandler(
        IMapper mapper,
        IAzureServiceBusPublisher azureServiceBusPublisher, IDbContext dbContext,
        ILogger<CreateStockProductsForAllStocksHandler> logger)
    {
        _mapper = mapper;
        _azureServiceBusPublisher = azureServiceBusPublisher;
        _dbContext = dbContext;
        _logger = logger;
    }

    public async Task Handle(CreateStockProductsForAllStocksCommand request, CancellationToken cancellationToken)
    {
        var stocks = await _dbContext.Stocks.ToListAsync(cancellationToken);
        var product = await _dbContext.Products
            .Include(x => x.Category)
            .Include(x => x.HearingAidType)
            .Include(x => x.Manufacturer)
            .FirstOrDefaultAsync(x => x.Id == request.ProductId, cancellationToken);
        
        if (product is null || !product.IsActive)
        {
            _logger.LogWarning("Unable to add inactive {@Product} to stocks", request.ProductId);
            throw new BusinessException("Unable to add inactive product to stocks", ErrorCodes.StockProductUnableToAddInactiveProduct);
        }

        var stockProducts = stocks.Select(stock => StockProduct.Create(stock, product)).ToList();

        _dbContext.StockProducts.AddRange(stockProducts);

        var createdStockProducts = stockProducts.Select(x =>
        {
            var @event = _mapper.Map<StockProductCreated>(product);
            @event.Id = x.Id;
            @event.StockId = x.StockId;
            @event.LocationId = x.Stock.LocationId;
            return @event;
        });

        await _azureServiceBusPublisher.PublishStockProductCreated(createdStockProducts);
        
        await _dbContext.SaveChangesAsync(cancellationToken);
    }
}
