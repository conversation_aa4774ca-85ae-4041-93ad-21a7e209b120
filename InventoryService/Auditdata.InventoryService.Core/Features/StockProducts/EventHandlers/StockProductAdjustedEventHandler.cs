using Auditdata.InventoryService.Contracts.Commands;
using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Abstractions.Azure;
using Auditdata.InventoryService.Core.Features.StockProducts.Events;
using StockTransactionType = Auditdata.InventoryService.Contracts.StockTransactionType;

namespace Auditdata.InventoryService.Core.Features.StockProducts.EventHandlers;

public class StockProductAdjustedEventHandler : INotificationHandler<StockProductAdjustedEvent>
{
    private readonly IAzureServiceBusPublisher _azureServiceBusPublisher;
    private readonly IDbContext _dbContext;
    private readonly IEventPublisher _eventPublisher;

    public StockProductAdjustedEventHandler(
        IAzureServiceBusPublisher azureServiceBusPublisher,
        IDbContext dbContext,
        IEventPublisher eventPublisher)
    {
        _azureServiceBusPublisher = azureServiceBusPublisher;
        _dbContext = dbContext;
        _eventPublisher = eventPublisher;
    }

    public async Task Handle(StockProductAdjustedEvent notification, CancellationToken cancellationToken)
    {
        foreach (var stockProductItem in notification.NewStockProductItems)
        {
            _dbContext.StockProductItemLogs.Add(StockProductItemLog.StockAdjustedActionLog(stockProductItem));

            await _eventPublisher.StockProductItemCreated(stockProductItem, cancellationToken);
        }

        if (!notification.StockProduct.Product.IsSerialized)
        {
            await _eventPublisher.NotSerializedStockAdjusted(
                notification.StockProduct,
                notification.Quantity,
                cancellationToken);
        }

        await _azureServiceBusPublisher.PublishAddStockTransactionCommand(new AddStockTransactionCommand
        {
            StockId = notification.StockProduct.StockId,
            ProductId = notification.StockProduct.ProductId,
            Type = StockTransactionType.StockAdjustment,
            Quantity = notification.Quantity
        });
    }
}
