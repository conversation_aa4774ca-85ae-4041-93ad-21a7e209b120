using Auditdata.Transport.Contracts.Table;

namespace Auditdata.InventoryService.Core.Features.StockProducts.Queries;

public abstract class GetInvoiceSuggestedStockProductsQuery : TableQueryBase
{
    public Guid LocationId { get; set; }
    public Guid ProductId { get; set; }
    public string? SearchText { get; set; }
    public Guid? ManufacturerId { get; set; }
    public Guid? CategoryId { get; set; }
    public ProductCategoryCode? CategoryCode { get; set; }
}
