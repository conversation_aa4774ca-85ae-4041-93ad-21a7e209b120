using Auditdata.Transport.Contracts.Table;

namespace Auditdata.InventoryService.Core.Features.StockProducts.Queries;

public abstract class GetInvoiceStockProductsQuery : TableQueryBase
{
    public string? SearchText { get; set; }
    public Guid LocationId { get; set; }
    public Guid? PathwayId { get; set; }
    public Guid? ManufacturerId { get; set; }
    public Guid? CategoryId { get; set; }
    public Guid? HearingAidTypeId { get; set; }
    public bool? IsSerialized { get; set; }
    public bool? IsFastTrack { get; set; }
    public ProductCategoryCode? CategoryCode { get; set; }
}
