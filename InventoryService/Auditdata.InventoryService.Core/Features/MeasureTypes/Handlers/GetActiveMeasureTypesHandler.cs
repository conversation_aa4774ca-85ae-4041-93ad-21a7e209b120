using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Features.MeasureTypes.Models;
using Auditdata.InventoryService.Core.Features.MeasureTypes.Queries;
using Microsoft.EntityFrameworkCore;

namespace Auditdata.InventoryService.Core.Features.MeasureTypes.Handlers;

public class GetActiveMeasureTypesHandler : IRequestHandler<GetActiveMeasureTypesQuery, GetActiveMeasureTypesResult>
{
    private readonly IDbContext _dbContext;

    public GetActiveMeasureTypesHandler(IDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    public async Task<GetActiveMeasureTypesResult> Handle(GetActiveMeasureTypesQuery request, CancellationToken cancellationToken)
    {
        var query = _dbContext.MeasureTypes
            .Where(x => x.IsActive && !x.IsDeleted);

        if (!string.IsNullOrEmpty(request.Search))
        {
            query = query.Where(x => x.Name.Contains(request.Search));
        }

        var measureTypes = await query
            .OrderBy(x => x.Name)
            .ToListAsync(cancellationToken);

        return new GetActiveMeasureTypesResult(measureTypes);
    }
}
