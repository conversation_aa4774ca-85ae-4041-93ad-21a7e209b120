using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Features.MeasureTypes.Commands;
using Microsoft.EntityFrameworkCore;

namespace Auditdata.InventoryService.Core.Features.MeasureTypes.Handlers;

public class UpdateMeasureTypeHandler : IRequestHandler<UpdateMeasureTypeCommand>
{
    private readonly IDbContext _dbContext;

    public UpdateMeasureTypeHandler(IDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    public async Task Handle(UpdateMeasureTypeCommand request, CancellationToken cancellationToken)
    {
        var measureType = await _dbContext.MeasureTypes
            .FirstOrDefaultAsync(x => x.Id == request.Id && !x.IsDeleted, cancellationToken);

        if (measureType == null)
        {
            throw new InvalidOperationException($"MeasureType with ID '{request.Id}' not found.");
        }

        // Check if another measure type with same name already exists
        var existingMeasureType = await _dbContext.MeasureTypes
            .FirstOrDefaultAsync(x => x.Name == request.Name && x.Id != request.Id && !x.IsDeleted, cancellationToken);

        if (existingMeasureType != null)
        {
            throw new InvalidOperationException($"MeasureType with name '{request.Name}' already exists.");
        }

        measureType.Name = request.Name;
        measureType.IsActive = request.IsActive;

        await _dbContext.SaveChangesAsync(cancellationToken);
    }
}
