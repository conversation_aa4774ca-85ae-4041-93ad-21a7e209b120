using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Features.MeasureTypes.Models;
using Auditdata.InventoryService.Core.Features.MeasureTypes.Queries;
using Auditdata.Transport.Contracts.PageResults;
using Auditdata.Transport.Contracts.Table;
using Microsoft.EntityFrameworkCore;

namespace Auditdata.InventoryService.Core.Features.MeasureTypes.Handlers;

public class GetMeasureTypesHandler : IRequestHandler<GetMeasureTypesQuery, GetMeasureTypesResult>
{
    private readonly IDbContext _dbContext;

    public GetMeasureTypesHandler(IDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    public async Task<GetMeasureTypesResult> Handle(GetMeasureTypesQuery request, CancellationToken cancellationToken)
    {
        var query = _dbContext.MeasureTypes
            .Where(x => !x.IsDeleted)
            .AsQueryable();

        if (!string.IsNullOrEmpty(request.Name))
        {
            query = query.Where(x => x.Name.Contains(request.Name));
        }

        if (request.IsActive.HasValue)
        {
            query = query.Where(x => x.IsActive == request.IsActive.Value);
        }

        if (string.IsNullOrEmpty(request.OrderBy))
        {
            query = query.OrderBy(x => x.Name);
        }

        var (result, total) = await query.ApplyTableQueryAsync(
            request.Page, request.PerPage, request.OrderBy);
        
        var measureTypes = new TablePageResult<MeasureType>(
            result.Select(x => new TableRow<MeasureType>(x)).ToArray(), request, total);

        return new GetMeasureTypesResult(measureTypes);
    }
}
