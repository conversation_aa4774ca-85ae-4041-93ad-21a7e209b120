using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Exceptions;
using Microsoft.EntityFrameworkCore;

namespace Auditdata.InventoryService.Core.Features.Countries.GetCountryByIso2Code;

public class GetCountryByIso2CodeHandler : IRequestHandler<GetCountryByIso2CodeQuery, Country>
{
    private readonly IDbContext _dbContext;

    public GetCountryByIso2CodeHandler(IDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    public async Task<Country> Handle(GetCountryByIso2CodeQuery request, CancellationToken cancellationToken)
    {
        var country = await _dbContext.Countries
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.Iso2Code == request.Iso2Code, cancellationToken) ??
            throw new EntityNotFoundException<Country>(Guid.Empty);
        return country;
    }
}
