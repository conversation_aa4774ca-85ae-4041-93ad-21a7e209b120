using Auditdata.InventoryService.Core.Abstractions;
using Microsoft.EntityFrameworkCore;

namespace Auditdata.InventoryService.Core.Features.Countries.GetCountries;

public class GetCountriesHandler : IRequestHandler<GetCountriesQuery, IEnumerable<Country>>
{
    private readonly IDbContext _dbContext;

    public GetCountriesHandler(IDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    public async Task<IEnumerable<Country>> Handle(GetCountriesQuery request, CancellationToken cancellationToken)
    {
        var countries = await _dbContext.Countries
            .AsNoTracking()
            .OrderBy(x => x.Name)
            .ToListAsync(cancellationToken);

        return countries;
    }
}
