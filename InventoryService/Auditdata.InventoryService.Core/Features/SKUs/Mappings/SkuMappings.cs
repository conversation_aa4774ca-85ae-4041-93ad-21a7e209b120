using Auditdata.Microservice.Messages.Events.Inventory.SKU;
using SkuAttribute = Auditdata.InventoryService.Core.Entities.SkuAttribute;

namespace Auditdata.InventoryService.Core.Features.SKUs.Mappings;

public class SkuMappings : Profile
{
    public SkuMappings()
    {
        CreateMap<Sku, SkuCreated>();
        CreateMap<Sku, SkuUpdated>();
        CreateMap<SkuAttribute, Microservice.Messages.Events.Inventory.SKU.SkuAttribute>();
    }
}
