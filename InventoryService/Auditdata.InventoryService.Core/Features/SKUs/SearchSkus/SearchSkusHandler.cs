using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Features.Skus.Extensions;
using Auditdata.InventoryService.Core.Features.SKUs.Models;
using Microsoft.EntityFrameworkCore;

namespace Auditdata.InventoryService.Core.Features.SKUs.SearchSkus;

public class SearchSkusHandler : IRequestHandler<SearchSkusQuery, SkuSearchResultDto>
{
    private readonly IDbContext _dbContext;

    public SearchSkusHandler(IDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    public async Task<SkuSearchResultDto> Handle(SearchSkusQuery request, CancellationToken cancellationToken)
    {
        var skuQuery = _dbContext.Skus.AsNoTracking()
            .Include(sku => sku.BatteryType)
            .Include(sku => sku.Color)
            .Include(sku => sku.Attributes)
                .ThenInclude(p => p.Attribute)
            .Include(sku => sku.Attributes)
                .ThenInclude(p => p.Value)
            .Where(sku => sku.ProductId == request.ProductId);

        var (result, total) = await skuQuery
            .ApplyTableQueryAsync(request.Page, request.PerPage, request.OrderBy);

        return new SkuSearchResultDto(result.Select(x => x.ToSkuDto()), total);
    }
}
