using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Exceptions;
using Microsoft.EntityFrameworkCore;

namespace Auditdata.InventoryService.Core.Features.Skus.DeleteSku;

public class DeleteSkuHandler : IRequestHandler<DeleteSkuCommand>
{
    private readonly IDbContext _dbContext;
    private readonly IEventPublisher _eventPublisher;

    public DeleteSkuHandler(IDbContext dbContext, IEventPublisher eventPublisher)
    {
        _dbContext = dbContext;
        _eventPublisher = eventPublisher;
    }

    public async Task Handle(DeleteSkuCommand request, CancellationToken cancellationToken)
    {
        var sku = await _dbContext.Skus
            .Include(x => x.Attributes)
            .FirstOrDefaultAsync(s => s.Id == request.SkuId, cancellationToken)
            ?? throw new EntityNotFoundException<Sku>(request.SkuId);

        _dbContext.SkuAttributes.RemoveRange(sku.Attributes);
        _dbContext.Skus.Remove(sku);

        await _eventPublisher.SkuDeleted(sku, cancellationToken);

        await _dbContext.SaveChangesAsync(cancellationToken);
    }
}
