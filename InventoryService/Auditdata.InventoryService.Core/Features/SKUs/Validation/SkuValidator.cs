using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Abstractions.Validators;
using Auditdata.InventoryService.Core.Constants;
using Auditdata.InventoryService.Core.Entities.Products;
using Auditdata.InventoryService.Core.Features.Skus.Models;
using Auditdata.InventoryService.Core.Features.SKUs.AssignSkus;
using Auditdata.Transport.Contracts.Exceptions;
using Microsoft.EntityFrameworkCore;

namespace Auditdata.InventoryService.Core.Features.Skus.Validation;

public class SkuValidator : ISkuValidator
{
    private readonly IDbContext _dbContext;

    public SkuValidator(IDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    public async Task Validate(
        string skuValue, 
        Product product, 
        Guid? colorId,
        Guid? batteryTypeId,
        List<AttributeDto>? attributes, 
        CancellationToken ct)
    {
        ValidateProductFields(product, colorId, batteryTypeId, attributes);
        ValidateSkuConfigFields(product, colorId, batteryTypeId, attributes);
        ValidateRequiredFields(product, colorId, batteryTypeId, attributes);
        await ValidateSkusForSupplier([skuValue], product.SupplierId!.Value, null, ct);
        await ValidateSkuCombinationForProduct(null, product.Id, colorId, batteryTypeId, attributes, ct);
    }

    public async Task Validate(
        Product product,
        Sku sku,
        string skuValue,
        Guid? colorId,
        Guid? batteryTypeId,
        List<AttributeDto>? attributes,
        CancellationToken ct)
    {
        ValidateProductFields(product, colorId, batteryTypeId, attributes);
        ValidateSkuConfigFields(product, colorId, batteryTypeId, attributes);
        ValidateRequiredFields(product, colorId, batteryTypeId, attributes);
        await ValidateSkuForSupplier(sku.Id, skuValue, sku.SupplierId, ct);
        await ValidateSkuCombinationForProduct(sku.Id, sku.ProductId, colorId, batteryTypeId, attributes, ct);
    }

    public async Task ValidateAssignSkus(Product product, List<AssignSkuDto> assignSkuDtos, CancellationToken ct)
    {
        ValidateAssignSkusProductFields(product, assignSkuDtos);
        ValidateAssignSkusSkuConfigFields(product, assignSkuDtos);
        ValidateSkusRequiredFields(product, assignSkuDtos);
        ValidateSkusDuplicates(assignSkuDtos);
        await ValidateSkusForSupplier(assignSkuDtos.Select(x => x.SkuValue), product.SupplierId!.Value, product.Id, ct);
    }

    private static void ValidateRequiredFields(
        Product product,
        Guid? colorId,
        Guid? batteryTypeId,
        List<AttributeDto>? attributes)
    {
        foreach (var skuConfig in product.SkuConfigs)
        {
            if (skuConfig.SkuAttributeType is SkuAttributeType.BatteryType && batteryTypeId is null)
            {
                throw new BusinessException("Battery type is required.", ErrorCodes.BatteryTypeIsRequired);
            }

            if (skuConfig.SkuAttributeType is SkuAttributeType.Color && colorId is null)
            {
                throw new BusinessException("Color is required.", ErrorCodes.ColorIsRequired);
            }

            if (skuConfig.SkuAttributeType is SkuAttributeType.Attribute
                && ((attributes is not null && !attributes.Any(x => x.AttributeId == skuConfig.AttributeId))
                || (attributes is null)))
            {
                throw new BusinessException("Attribute is required.", ErrorCodes.AttributeIsRequired);
            }

            ValidateSkuAttributeValues(attributes, skuConfig, product);
        }
    }

    private static void ValidateAssignSkusProductFields(Product product, List<AssignSkuDto> assignSkuDtos)
    {
        foreach (var createSkuDto in assignSkuDtos)
        {
            ValidateProductFields(product, createSkuDto.ColorId, createSkuDto.BatteryTypeId, createSkuDto.Attributes);
        }
    }

    private static void ValidateProductFields(
        Product product,
        Guid? colorId,
        Guid? batteryTypeId,
        List<AttributeDto>? attributes)
    {
        if (colorId.HasValue && !product.Colors.Any(x => x.ColorId == colorId))
        {
            throw new BusinessException("Color is invalid.", ErrorCodes.ColorIsInvalid);
        }

        if (batteryTypeId.HasValue && !product.BatteryTypes.Any(x => x.BatteryTypeId == batteryTypeId))
        {
            throw new BusinessException("Battery type is invalid.", ErrorCodes.BatteryTypeIsInvalid);
        }

        if (attributes is null || attributes.Count == 0)
        {
            return;
        }

        if (attributes.Any(x => !product.Attributes.Select(y => y.AttributeId).Contains(x.AttributeId)))
        {
            throw new BusinessException("Attribute is invalid.", ErrorCodes.AttributeIsInvalid);
        }
    }

    private static void ValidateAssignSkusSkuConfigFields(Product product, List<AssignSkuDto> assignSkuDtos)
    {
        foreach (var createSkuDto in assignSkuDtos)
        {
            ValidateSkuConfigFields(product, createSkuDto.ColorId, createSkuDto.BatteryTypeId, createSkuDto.Attributes);
        }
    }

    private static void ValidateSkuConfigFields(
        Product product,
        Guid? colorId,
        Guid? batteryTypeId,
        List<AttributeDto>? attributes)
    {
        if (colorId.HasValue && !product.SkuConfigs.Any(x => x.SkuAttributeType == SkuAttributeType.Color))
        {
            throw new BusinessException("Color is not part of SKU.", ErrorCodes.ColorIsNotPartOfSku);
        }

        if (batteryTypeId.HasValue && !product.SkuConfigs.Any(x => x.SkuAttributeType == SkuAttributeType.BatteryType))
        {
            throw new BusinessException("Battery type is not part of SKU.", ErrorCodes.BatteryTypeIsNotPartOfSku);
        }

        if (attributes is null || attributes.Count == 0)
        {
            return;
        }

        if (attributes.Any(x => !product.SkuConfigs.Where(y => y.SkuAttributeType == SkuAttributeType.Attribute)
            .Select(y => y.AttributeId).Contains(x.AttributeId)))
        {
            throw new BusinessException("Attribute is not part of SKU.", ErrorCodes.AttributeIsNotPartOfSku);
        }
    }

    private static void ValidateSkuAttributeValues(List<AttributeDto>? attributes, SkuConfig skuConfig, Product product)
    {
        if (skuConfig.SkuAttributeType is not SkuAttributeType.Attribute || attributes is null)
        {
            return;
        }

        var attributeDto = attributes.FirstOrDefault(x => x.AttributeId == skuConfig.AttributeId);
        if (attributeDto is null)
        {
            return;
        }

        var productAttribute = product.Attributes
            .FirstOrDefault(x => x.AttributeId == skuConfig.AttributeId)
            ?? throw new InvalidOperationException("Product doesn't have an attribute which exists in sku config.");

        if ((attributeDto.Value.Id != Guid.Empty
                        && !productAttribute.Value.Select(y => y.ValueId).Contains(attributeDto.Value.Id))
                    || (attributeDto.Value.Id == Guid.Empty
                        && !productAttribute.Value.Select(y => y.Value)
                            .Contains(attributeDto.Value.Value, StringComparer.OrdinalIgnoreCase)))
        {
            throw new BusinessException("Attribute value is invalid.", ErrorCodes.AttributeValueIsInvalid);
        }
    }

    private async Task ValidateSkuCombinationForProduct(
        Guid? skuId,
        Guid productId,
        Guid? colorId,
        Guid? batteryTypeId,
        List<AttributeDto>? attributes,
        CancellationToken ct)
    {
        var potentialSkus = await _dbContext.Skus.AsNoTracking()
            .Include(sku => sku.Attributes)
                .ThenInclude(attribute => attribute.Value)
            .Where(sku =>
                sku.ProductId == productId &&
                sku.ColorId == colorId &&
                sku.BatteryTypeId == batteryTypeId &&
                (!skuId.HasValue || sku.Id != skuId))
            .ToListAsync(ct);

        var skuExists = potentialSkus.Any(sku => IsMatchingSku(sku.Attributes, attributes ?? []));

        if (skuExists)
        {
            throw new BusinessException(
                "SKU for the selected attributes already exists.",
                ErrorCodes.SkuCombinationExistsForProduct);
        }
    }

    private static bool IsMatchingSku(
        ICollection<SkuAttribute> actualAttributes,
        List<AttributeDto> expectedAttributes)
    {
        if (actualAttributes.Count != expectedAttributes.Count)
            return false;

        return actualAttributes.All(actual =>
            expectedAttributes.Any(expected =>
                expected.AttributeId == actual.AttributeId
                && ((expected.Value.Id != Guid.Empty
                && expected.Value.Id == actual.Value.ValueId)
                || (expected.Value.Id == Guid.Empty && actual.Value.ValueId == Guid.Empty
                && string.Equals(expected.Value.Value, actual.Value.Value, StringComparison.OrdinalIgnoreCase)))));
    }

    private async Task ValidateSkuForSupplier(Guid skuId, string skuValue, Guid supplierId, CancellationToken ct)
    {
        var skuExistsForSupplier = await _dbContext.Skus
            .AnyAsync(s => s.SkuValue == skuValue && s.SupplierId == supplierId && s.Id != skuId, ct);

        if (skuExistsForSupplier)
        {
            throw new BusinessException("SKU must be unique for the supplier.", ErrorCodes.DuplicateSkuForSupplier);
        }
    }

    private static void ValidateSkusRequiredFields(Product product, List<AssignSkuDto> assignSkuDtos)
    {
        foreach (var createSkuDto in assignSkuDtos)
        {
            ValidateRequiredFields(product, createSkuDto.ColorId, createSkuDto.BatteryTypeId, createSkuDto.Attributes);
        }
    }

    private async Task ValidateSkusForSupplier(
        IEnumerable<string> skuValues,
        Guid supplierId,
        Guid? productIdToIgnore,
        CancellationToken ct)
    {
        var skusExistsForSupplier = await _dbContext.Skus
            .AnyAsync(s =>
                skuValues.Contains(s.SkuValue)
                && s.SupplierId == supplierId
                && (productIdToIgnore == null || s.ProductId != productIdToIgnore),
            ct);

        if (skusExistsForSupplier)
        {
            throw new BusinessException("SKU must be unique for the supplier.", ErrorCodes.DuplicateSkuForSupplier);
        }
    }

    private static void ValidateSkusDuplicates(List<AssignSkuDto> assignSkuDtos)
    {
        var skusHasDuplicateAttributes = assignSkuDtos
            .Any(x => x.Attributes.Count != x.Attributes.Select(x => x.AttributeId).Distinct().Count());
        var skusHasDuplicateCombinations = !skusHasDuplicateAttributes
            && assignSkuDtos.Any(x1 => assignSkuDtos.Count(x2 => 
            string.Equals(x1.SkuValue, x2.SkuValue, StringComparison.OrdinalIgnoreCase)
            || (x1.ColorId == x2.ColorId
            && x1.BatteryTypeId == x2.BatteryTypeId
            && CompareAttributeValues(x1, x2))) > 1);

        if (skusHasDuplicateAttributes || skusHasDuplicateCombinations)
        {
            throw new BusinessException("SKUs contain duplicates.", ErrorCodes.SkusHasDuplicates);
        }
    }

    private static bool CompareAttributeValues(AssignSkuDto sku1, AssignSkuDto sku2)
    {
        foreach (var attribute1 in sku1.Attributes)
        {
            var attribute2 = sku2.Attributes
                .FirstOrDefault(x => x.AttributeId == attribute1.AttributeId);
            if (attribute2 == null)
            {
                return false;
            }

            if (attribute1.Value.Id != Guid.Empty && attribute1.Value.Id != attribute2.Value.Id)
            {
                return false;
            }

            if (attribute1.Value.Id == Guid.Empty
                && !attribute1.Value.Value.Equals(attribute2.Value.Value, StringComparison.OrdinalIgnoreCase))
            {
                return false;
            }
        }

        return true;
    }
}
