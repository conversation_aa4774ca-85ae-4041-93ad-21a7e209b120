using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Entities.Products;
using Auditdata.InventoryService.Core.Exceptions;
using Auditdata.InventoryService.Core.Features.Skus.Models;
using Auditdata.InventoryService.Core.Features.SKUs.Models;
using Microsoft.EntityFrameworkCore;

namespace Auditdata.InventoryService.Core.Features.Skus.GetSKUAttributes;

public class GetSkuAttributesHandler : IRequestHandler<GetSkuAttributesQuery, SkuAttributesResultDto>
{
    private readonly IDbContext _dbContext;

    public GetSkuAttributesHandler(IDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    public async Task<SkuAttributesResultDto> Handle(GetSkuAttributesQuery request, CancellationToken cancellationToken)
    {
        var product = await _dbContext.Products.AsNoTracking()
            .Where(x => x.Id == request.ProductId)
            .Include(x => x.BatteryTypes)
                .ThenInclude(x => x.BatteryType)
            .Include(x => x.Colors)
                .ThenInclude(x => x.Color)
            .Include(x => x.Attributes)
                .ThenInclude(x => x.Attribute)
            .Include(x => x.SkuConfigs)
            .Select(x => new { x.BatteryTypes, x.Colors, x.Attributes, x.SkuConfigs })
            .FirstOrDefaultAsync(cancellationToken)
            ?? throw new EntityNotFoundException<Product>(request.ProductId);

        var result = new SkuAttributesResultDto
        {
            BatteryTypes = product.SkuConfigs.Any(x => x.SkuAttributeType == SkuAttributeType.BatteryType)
                ? MapBatteryTypes(product.BatteryTypes) : [],
            Colors = product.SkuConfigs.Any(x => x.SkuAttributeType == SkuAttributeType.Color)
                ? MapColors(product.Colors) : [],
            Attributes = product.SkuConfigs.Any(x => x.SkuAttributeType == SkuAttributeType.Attribute)
                ? MapAttributes(product.Attributes
                    .Where(x => product.SkuConfigs.Any(y => x.AttributeId == y.AttributeId))) : []
        };

        return result;
    }

    private static IEnumerable<DictionaryDto> MapBatteryTypes(IEnumerable<ProductBatteryType> batteryTypes)
    {
        return batteryTypes.Select(bt => new DictionaryDto(bt.BatteryType.Id, bt.BatteryType.Name));
    }

    private static IEnumerable<DictionaryDto> MapColors(IEnumerable<ProductColor> colors)
    {
        return colors.Select(c => new DictionaryDto(c.Color.Id, c.Color.Name));
    }

    private static List<AttributeResultDto> MapAttributes(IEnumerable<ProductAttribute> attributes)
    {
        return attributes.Select(
            x => new AttributeResultDto(
                x.Value
                    .Select(value => new AttributeValueDto(value.ValueId, value.Value))
                    .ToList(),
                new AttributeInfoDto
                {
                    Id = x.AttributeId,
                    Name = x.Attribute!.Name,
                    ValueType = x.Attribute?.ValueType ?? default
                }
            )).ToList();
    }
}
