using Auditdata.InventoryService.Core.Features.StockAdjustmentReasons.Commands;
using Auditdata.InventoryService.Core.Features.StockAdjustmentReasons.Models;

namespace Auditdata.InventoryService.Core.Features.StockAdjustmentReasons.Handlers;

public class CreateStockAdjustmentReasonHandler : IRequestHandler<CreateStockAdjustmentReasonCommand, StockAdjustmentReasonCreated>
{
    private readonly IStockAdjustmentReasonsRepository _stockAdjustmentReasonsRepository;
    private readonly IMapper _mapper;

    public CreateStockAdjustmentReasonHandler(
        IStockAdjustmentReasonsRepository stockAdjustmentReasonsRepository,
        IMapper mapper)
    {
        _stockAdjustmentReasonsRepository = stockAdjustmentReasonsRepository;
        _mapper = mapper;
    }

    public async Task<StockAdjustmentReasonCreated> Handle(CreateStockAdjustmentReasonCommand request, CancellationToken cancellationToken)
    {
        var stockAdjustmentReason = _mapper.Map<StockAdjustmentReason>(request);

        await _stockAdjustmentReasonsRepository.CreateAsync(stockAdjustmentReason);

        return new StockAdjustmentReasonCreated(stockAdjustmentReason.Id);
    }
}
