using Auditdata.InventoryService.Core.Features.StockAdjustmentReasons.Commands;

namespace Auditdata.InventoryService.Core.Features.StockAdjustmentReasons.Handlers;

public class UpdateStockAdjustmentReasonHandler : IRequestHandler<UpdateStockAdjustmentReasonCommand>
{
    private readonly IStockAdjustmentReasonsRepository _stockAdjustmentReasonsRepository;
    private readonly IMapper _mapper;

    public UpdateStockAdjustmentReasonHandler(
        IStockAdjustmentReasonsRepository stockAdjustmentReasonsRepository,
        IMapper mapper)
    {
        _stockAdjustmentReasonsRepository = stockAdjustmentReasonsRepository;
        _mapper = mapper;
    }

    public async Task Handle(UpdateStockAdjustmentReasonCommand request, CancellationToken cancellationToken)
    {
        var stockAdjustmentReason = await _stockAdjustmentReasonsRepository.GetByIdAsync(request.Id);
        if (stockAdjustmentReason is null)
        {
            return;
        }

        stockAdjustmentReason = _mapper.Map(request, stockAdjustmentReason);

        await _stockAdjustmentReasonsRepository.UpdateAsync(stockAdjustmentReason);
    }
}
