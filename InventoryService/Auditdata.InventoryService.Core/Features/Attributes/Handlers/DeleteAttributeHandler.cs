using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Abstractions.Azure;
using Auditdata.InventoryService.Core.Features.Attributes.Commands;
using Auditdata.InventoryService.Core.Features.Attributes.Validators;
using Auditdata.Microservice.Messages.Events.Inventory.Attributes;
using Microsoft.EntityFrameworkCore;

namespace Auditdata.InventoryService.Core.Features.Attributes.Handlers;

public class DeleteAttributeHandler : IRequestHandler<DeleteAttributeCommand>
{
    private readonly IDbContext _dbContext;
    private readonly IAttributesValidator _attributesValidator;
    private readonly IAzureServiceBusPublisher _serviceBusPublisher;
    private readonly IMapper _mapper;

    public DeleteAttributeHandler(
        IDbContext dbContext,
        IAttributesValidator attributesValidator,
        IAzureServiceBusPublisher serviceBusPublisher,
        IMapper mapper)
    {
        _dbContext = dbContext;
        _attributesValidator = attributesValidator;
        _serviceBusPublisher = serviceBusPublisher;
        _mapper = mapper;
    }

    public async Task Handle(DeleteAttributeCommand request, CancellationToken cancellationToken)
    {
        await _attributesValidator.ValidateCanDeleteAsync(request.Id, cancellationToken);

        var attribute = await _dbContext.Attributes.FirstOrDefaultAsync(x => x.Id == request.Id, cancellationToken);
        if (attribute is null)
        {
            return;
        }

        attribute.Delete();

        await _serviceBusPublisher.PublishAttributeDeleted(_mapper.Map<AttributeDeleted>(attribute));
        await _dbContext.SaveChangesAsync(cancellationToken);
    }
}
