using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Abstractions.Azure;
using Auditdata.InventoryService.Core.Exceptions;
using Auditdata.InventoryService.Core.Features.Attributes.Commands;
using Auditdata.InventoryService.Core.Features.Attributes.Validators;
using Auditdata.Microservice.Messages.Events.Inventory.Attributes;
using Microsoft.EntityFrameworkCore;
using Attribute = Auditdata.InventoryService.Core.Entities.Attribute;

namespace Auditdata.InventoryService.Core.Features.Attributes.Handlers;

public class UpdateAttributeHandler : IRequestHandler<UpdateAttributeCommand>
{
    private readonly IDbContext _dbContext;
    private readonly IAttributesValidator _attributesValidator;
    private readonly IAzureServiceBusPublisher _serviceBusPublisher;
    private readonly IMapper _mapper;

    public UpdateAttributeHandler(
        IDbContext dbContext,
        IAttributesValidator attributesValidator,
        IAzureServiceBusPublisher serviceBusPublisher,
        IMapper mapper)
    {
        _dbContext = dbContext;
        _attributesValidator = attributesValidator;
        _serviceBusPublisher = serviceBusPublisher;
        _mapper = mapper;
    }
    
    public async Task Handle(UpdateAttributeCommand request, CancellationToken cancellationToken)
    {
        _attributesValidator.ValidateUniqueValue(request.Values.ToList());

        var attribute = await _dbContext.Attributes
            .Include(x => x.ProductCategories)
            .FirstOrDefaultAsync(x => x.Id == request.Id, cancellationToken);
        if (attribute is null)
        {
            throw new EntityNotFoundException<Attribute>(request.Id);
        }

        attribute.IsActive = request.IsActive;

        var categoriesToRemove = attribute.ProductCategories
            .Where(x => !request.ProductCategories.Contains(x.Id))
            .ToList();

        await _attributesValidator.ValidateRemovingCategories(categoriesToRemove, attribute.Id, cancellationToken);
        
        attribute.ProductCategories.RemoveRange(categoriesToRemove);
        
        var categoriesToAdd = request.ProductCategories
            .Where(x => attribute.ProductCategories.All(y => y.Id != x))
            .Select(x => new ProductCategory { Id = x })
            .ToList();
        _dbContext.ProductCategories.AttachRange(categoriesToAdd);
        attribute.ProductCategories.AddRange(categoriesToAdd);

        attribute.Values = request.Values.ToList();

        await _serviceBusPublisher.PublishAttributeUpdated(_mapper.Map<AttributeUpdated>(attribute));
        await _dbContext.SaveChangesAsync(cancellationToken);
    }
}
