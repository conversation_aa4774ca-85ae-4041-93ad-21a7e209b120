using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Constants;
using Auditdata.Transport.Contracts.Exceptions;
using Microsoft.EntityFrameworkCore;
using ValidationException = Auditdata.InventoryService.Core.Exceptions.ValidationException;

namespace Auditdata.InventoryService.Core.Features.Attributes.Validators;

public class AttributesValidator : IAttributesValidator
{
    private readonly IDbContext _dbContext;

    public AttributesValidator(IDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    public async Task ValidateUniqueNameAsync(string name, Guid exceptId, CancellationToken cancellationToken)
    {
        var nameExists = await _dbContext.Attributes.AnyAsync(x => x.Name == name && x.Id != exceptId, cancellationToken);
        if (nameExists)
        {
            throw new ValidationException(ErrorCodes.AttributeNameAlreadyExists);
        }
    }

    public void ValidateUniqueValue(ICollection<AttributeValue> values)
    {
        var distinctCount = values.DistinctBy(x => x.Value).Count();

        var duplicateExists = distinctCount != values.Count;
        if (duplicateExists)
        {
            throw new ValidationException(ErrorCodes.AttributeValueAlreadyExists);
        }
    }

    public async Task ValidateRemovingCategories(List<ProductCategory> productCategories, Guid attributeId, CancellationToken cancellationToken)
    {
        var categoryIds = productCategories.Select(c => c.Id).ToList();

        var blockedCategoryNames = await _dbContext.Products
            .AsNoTracking()
            .Where(p =>
                categoryIds.Contains(p.Category.Id) &&
                p.Attributes.Any(a => a.AttributeId == attributeId))
            .GroupBy(p => new { p.Category.Id, p.Category.Name })
            .Select(g => g.Key.Name)
            .ToListAsync(cancellationToken);

        if (blockedCategoryNames.Any())
        {
            throw new BusinessException(
                $"Cannot unassign {string.Join(", ", blockedCategoryNames)} from attribute because there are products in those categories using this attribute.",
                ErrorCodes.AttributeCategoryUnassignmentBlocked);
        }
    }

    public async Task ValidateCanDeleteAsync(Guid attributeId, CancellationToken cancellationToken)
    {
        var productAttributesExist = await _dbContext.ProductAttributes
            .AnyAsync(x => x.AttributeId == attributeId, cancellationToken);

        if (productAttributesExist)
        {
            throw new ValidationException(ErrorCodes.AttributeCannotDelete);
        }
    }
}
