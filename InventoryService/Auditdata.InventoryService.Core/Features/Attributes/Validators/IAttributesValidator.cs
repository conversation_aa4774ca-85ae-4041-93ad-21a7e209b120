namespace Auditdata.InventoryService.Core.Features.Attributes.Validators;

public interface IAttributesValidator
{
    Task ValidateUniqueNameAsync(string name, Guid exceptId, CancellationToken cancellationToken);
    void ValidateUniqueValue(ICollection<AttributeValue> values);
    Task ValidateCanDeleteAsync(Guid attributeId, CancellationToken cancellationToken);
    Task ValidateRemovingCategories(List<ProductCategory> productCategories, Guid attributeId, CancellationToken cancellationToken);
}
