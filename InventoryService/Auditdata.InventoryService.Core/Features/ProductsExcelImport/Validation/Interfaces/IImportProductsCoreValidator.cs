using Auditdata.Infrastructure.Contracts.CountryLayers;
using Auditdata.Infrastructure.Excel.Models;
using Auditdata.InventoryService.Core.Entities.Products;
using Auditdata.InventoryService.Core.Features.ProductsExcelImport.Models.Excel.Abstractions;

namespace Auditdata.InventoryService.Core.Features.ProductsExcelImport.Validation.Interfaces;

public interface IImportProductsCoreValidator
{
    void ValidateImportResult<T>(ExcelImportResult<T> importResult, CountryEnum country)
        where T : ProductExcelModelBase;

    Task ValidateIfProductsAlreadyExistsAsync<T>(IReadOnlyCollection<Guid> filledIdsSet,
        IReadOnlyCollection<ExcelImportData<T>> excelImportData, CancellationToken cancellationToken)
        where T : ProductExcelModelBase;

    void ValidateIsAllIdsFound<T>(IReadOnlyCollection<Product> existingProducts,
        IReadOnlyCollection<ExcelImportData<T>> productsWithFilledIds)
        where T : ProductExcelModelBase;

    void ValidateCategories<T>(ProductCategoryCode categoryCode, string? existingCategoryName,
        ExcelImportResult<T> importResult)
        where T : ProductExcelModelBase;

    void ValidateCptCodes<T>(IReadOnlyDictionary<string, Guid> existingCptCodeIds,
        ExcelImportResult<T> importResult, CountryEnum country)
        where T : ProductExcelModelBase;

    Task ValidateHearingAidsAsync<T>(IReadOnlyDictionary<string, Guid> existingHearingAidTypeIds,
        ExcelImportResult<T> importResult, CountryEnum country, CancellationToken cancellationToken)
        where T : ProductExcelModelBase;

    Task ValidateServiceNumbersAsync<T>(ExcelImportResult<T> importResult, CountryEnum country,
        CancellationToken cancellationToken)
        where T : ProductExcelModelBase;

    void ValidateAttributesUsedInSkuWithExcelResult<T>(
        ExcelImportResult<T> importResult,
        IReadOnlyCollection<Product> existingProducts,
        IReadOnlyDictionary<string, Guid> existingColorIds,
        IReadOnlyDictionary<string, Guid> existingBatteryIds)
    where T : ProductExcelModelBase;

    void ValidateSupplierUsedInSkuWithExcelResult<T>(
        ExcelImportResult<T> importResult,
        List<Product> existingProducts,
        IReadOnlyDictionary<string, Guid> existingSupplierIds,
        CancellationToken cancellationToken)
        where T : ProductExcelModelBase;
}
