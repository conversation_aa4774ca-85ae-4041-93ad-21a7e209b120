using Auditdata.Infrastructure.Contracts.CountryLayers;
using Auditdata.Infrastructure.Excel.Models;
using Auditdata.InventoryService.Core.Constants;
using Auditdata.InventoryService.Core.Features.Products.Models.Australia;
using Auditdata.InventoryService.Core.Features.ProductsExcelImport.Models.Excel;
using Auditdata.InventoryService.Core.Features.ProductsExcelImport.Models.Excel.Abstractions;

namespace Auditdata.InventoryService.Core.Features.ProductsExcelImport.Validation;

public class ImportExcelModelsCoreValidator<TProductExcelModel> : AbstractValidator<ExcelImportData<TProductExcelModel>>
    where TProductExcelModel : ProductExcelModelBase
{
    public ImportExcelModelsCoreValidator(CountryEnum country)
    {
        RuleFor(x => ((BatteryExcelModel)(ProductExcelModelBase)x.Data).Quantity)
            .GreaterThanOrEqualTo(0)
            .When(x => x.Data is BatteryExcelModel)
            .WithState(x => x)
            .WithName(_ => nameof(BatteryExcelModel.Quantity));

        RuleFor(x => x.Data.Warranty)
            .GreaterThanOrEqualTo(0)
            .WithState(x => x)
            .WithName(_ => nameof(ProductExcelModelBase.Warranty));

        RuleFor(x => x.Data.RetailPrice)
            .GreaterThanOrEqualTo(0)
            .WithState(x => x)
            .WithName(_ => nameof(ProductExcelModelBase.RetailPrice));

        RuleFor(x => x.Data.Cost)
            .GreaterThanOrEqualTo(0)
            .WithState(x => x)
            .WithName(_ => nameof(ProductExcelModelBase.Cost));

        RuleFor(x => x.Data.MaximumDiscount)
            .GreaterThanOrEqualTo(0)
            .WithState(x => x)
            .WithName(_ => nameof(ProductExcelModelBase.MaximumDiscount));

        RuleFor(x => x.Data.FirstVAT)
            .Must((importData, value) => value >= 0 && value <= 1 && value + importData.Data.SecondVAT == 1)
            .When(_ => country == CountryEnum.UnitedKingdom)
            .WithState(x => x)
            .WithName(_ => nameof(ProductExcelModelBase.FirstVAT));

        RuleFor(x => x.Data.SecondVAT)
            .Must((importData, value) => value >= 0 && value <= 1 && value + importData.Data.FirstVAT == 1)
            .When(_ => country == CountryEnum.UnitedKingdom)
            .WithState(x => x)
            .WithName(_ => nameof(ProductExcelModelBase.SecondVAT));

        // TODO: NHS value
        //RuleFor(x => x.Data.NHSVAT)
        //    .GreaterThanOrEqualTo(0)
        //    .When(_ => country == CountryEnum.UnitedKingdom)
        //    .WithState(x => x)
        //    .WithName(_ => nameof(ProductExcelModelBase.NHSVAT));

        RuleFor(x => ((IModelWithLdWarranty)x.Data).LDWarranty)
            .GreaterThanOrEqualTo(0)
            .When(x => x.Data is IModelWithLdWarranty)
            .WithState(x => x)
            .WithName(_ => nameof(IModelWithLdWarranty.LDWarranty));

        RuleFor(x => x.Data.VendorProductNumber)
            .Matches(ValidationConstants.AlphanumericsOnlyRegex)
            .When(x => !string.IsNullOrEmpty(x.Data.VendorProductNumber))
            .WithState(x => x)
            .WithName(_ => nameof(ProductExcelModelBase.VendorProductNumber));

        RuleFor(x => ((IModelWithBatteryTypes)x.Data).BatteryTypes)
            .Must(value => !string.IsNullOrWhiteSpace(value))
            .When(x => x.Data is IModelWithBatteryTypes)
            .WithState(x => x)
            .WithName(_ => nameof(IModelWithBatteryTypes.BatteryTypes));

        // TODO: Change comparison to Category type after custom categories implementation
        When(x => string.Equals(x.Data.Category, "Service", StringComparison.OrdinalIgnoreCase)
            || string.Equals(x.Data.Category, "Repair Service", StringComparison.OrdinalIgnoreCase), () =>
        {
            RuleFor(x => x.Data.IsSerialized)
            .Must(value => !value)
            .WithState(x => x)
            .WithName(_ => nameof(ProductExcelModelBase.IsSerialized));

            RuleFor(x => x.Data.ControlledByStock)
                .Must(value => !value)
                .WithState(x => x)
                .WithName(_ => nameof(ProductExcelModelBase.ControlledByStock));
        });

        RuleFor(x => x.Data.IsHSP)
            .Must(value => !value)
            .When(_ => country != CountryEnum.Australia)
            .WithState(x => x)
            .WithName(_ => nameof(ProductExcelModelBase.IsHSP));

        RuleFor(x => x.Data.Manufacturer)
            .NotEmpty()
            .When(x => country != CountryEnum.Australia || !x.Data.IsHSP 
                || x.Data is not ServiceExcelModel serviceModel || serviceModel.HspMaintenance != true)
            .WithState(x => x)
            .WithName(_ => nameof(ProductExcelModelBase.Manufacturer));

        When(x => x.Data.IsHSP && country == CountryEnum.Australia, () =>
        {
            RuleFor(x => ((ServiceExcelModel)(ProductExcelModelBase)x.Data).HspServiceType)
                .NotEmpty().Must(value => value != null && Enum.IsDefined(typeof(HspServiceType), value))
                .When(x => x.Data is ServiceExcelModel serviceModel && serviceModel.HspMaintenance != true)
                .WithState(x => x)
                .WithName(_ => nameof(ServiceExcelModel.HspServiceType));

            RuleFor(x => ((ServiceExcelModel)(ProductExcelModelBase)x.Data).HspServiceNumber)
                .NotEmpty()
                .When(x => x.Data is ServiceExcelModel serviceModel && serviceModel.HspMaintenance != true)
                .WithState(x => x)
                .WithName(_ => nameof(ServiceExcelModel.HspServiceNumber));

            RuleFor(x => ((HearingAidExcelModel)(ProductExcelModelBase)x.Data).HspCode)
                .NotEmpty()
                .When(x => x.Data is HearingAidExcelModel)
                .WithState(x => x)
                .WithName(_ => nameof(HearingAidExcelModel.HspCode));

            RuleFor(x => ((HearingAidExcelModel)(ProductExcelModelBase)x.Data).HspCategory)
                .NotEmpty()
                .When(x => x.Data is HearingAidExcelModel)
                .WithState(x => x)
                .WithName(_ => nameof(HearingAidExcelModel.HspCategory));

            RuleFor(x => ((HearingAidExcelModel)(ProductExcelModelBase)x.Data).HspClientPrice)
                .NotEmpty().GreaterThanOrEqualTo(0)
                .When(x => x.Data is HearingAidExcelModel hearingAidModel && hearingAidModel.HspTopUp == true)
                .WithState(x => x)
                .WithName(_ => nameof(HearingAidExcelModel.HspClientPrice));
        });
        
        // New Zealand

        RuleFor(x => x.Data.IsAcc)
            .Must(value => !value)
            .When(_ => country != CountryEnum.NewZealand)
            .WithState(x => x)
            .WithName(_ => nameof(ProductExcelModelBase.IsAcc));
        
        When(x => x.Data.IsAcc && country == CountryEnum.NewZealand, () =>
        {
            RuleFor(x => ((ServiceExcelModel)(ProductExcelModelBase)x.Data).AccCode)
                .NotEmpty()
                .When(x => x.Data is ServiceExcelModel)
                .WithState(x => x)
                .WithName(_ => nameof(ServiceExcelModel.AccCode));

            RuleFor(x => ((ServiceExcelModel)(ProductExcelModelBase)x.Data).AccPriceExclGst)
                .NotEmpty()
                .When(x => x.Data is ServiceExcelModel)
                .WithState(x => x)
                .WithName(_ => nameof(ServiceExcelModel.AccPriceExclGst));
            
            RuleFor(x => ((ServiceExcelModel)(ProductExcelModelBase)x.Data).AccPriceInclGst)
                .NotEmpty()
                .When(x => x.Data is ServiceExcelModel)
                .WithState(x => x)
                .WithName(_ => nameof(ServiceExcelModel.AccPriceInclGst));
        });
    }
}
