using Auditdata.Infrastructure.Contracts.CountryLayers;
using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Abstractions.Azure;
using Auditdata.InventoryService.Core.Features.Products.Mappings;
using Auditdata.InventoryService.Core.Features.ProductsExcelImport.Models;
using Auditdata.InventoryService.Core.Features.ProductsExcelImport.Models.Excel;
using Auditdata.InventoryService.Core.Features.ProductsExcelImport.Models.Excel.Abstractions;
using Auditdata.InventoryService.Core.Features.ProductsExcelImport.Services.Interfaces;
using Auditdata.InventoryService.Core.OperationContext;
using Auditdata.Microservice.Messages.Events.Inventory;
using Microsoft.EntityFrameworkCore;

namespace Auditdata.InventoryService.Core.Features.ProductsExcelImport.Services;

public class ProductEntitiesProvider : IProductEntitiesProvider
{
    private readonly IDbContext _dbContext;
    private readonly IInventoryServiceOperationContext _operationContext;
    private readonly IEventPublisher _eventPublisher;
    private readonly IAzureServiceBusPublisher _azureServiceBusPublisher;

    public ProductEntitiesProvider(
        IDbContext dbContext,
        IInventoryServiceOperationContext operationContext,
        IEventPublisher eventPublisher,
        IAzureServiceBusPublisher azureServiceBusPublisher)
    {
        _dbContext = dbContext;
        _operationContext = operationContext;
        _eventPublisher = eventPublisher;
        _azureServiceBusPublisher = azureServiceBusPublisher;
    }

    public async Task<ProductEntitiesDto> GetOrCreateProductsEntitiesAsync<T>(
        IEnumerable<T> importData, CancellationToken cancellationToken) where T : ProductExcelModelBase
    {
        var manufacturers = await GetOrCreateManufacturerIdsAsync(importData, cancellationToken);
        var suppliers = await GetOrCreateSupplierIdsAsync(importData, cancellationToken);
        var batteryTypes = await GetOrCreateBatteryTypeIdsAsync(importData, cancellationToken);
        var colors = await GetOrCreateColorIdsAsync(importData, cancellationToken);
        var hearingAidTypes = await GetHearingAidTypeIdsAsync(importData, cancellationToken);
        var cptCodes = await GetCptCodeIdsAsync(importData, cancellationToken);

        return new ProductEntitiesDto(manufacturers, suppliers, batteryTypes, colors, hearingAidTypes, cptCodes);
    }

    private async Task<Dictionary<string, Guid>> GetOrCreateManufacturerIdsAsync<T>(
        IEnumerable<T> importData,
        CancellationToken cancellationToken) where T : ProductExcelModelBase
    {
        var manufacturerNames = importData.Where(p => p.Manufacturer != null).Select(p => p.Manufacturer!)
            .ToHashSet(StringComparer.OrdinalIgnoreCase);
        var manufacturerIds = await _dbContext.Manufacturers
            .Where(m => manufacturerNames.Contains(m.Name))
            .Select(x => new { x.Id, x.Name })
            .ToDictionaryAsync(x => x.Name.Trim(), x => x.Id, StringComparer.OrdinalIgnoreCase, cancellationToken);
        foreach (var manufacturerName in manufacturerNames)
        {
            if (manufacturerIds.ContainsKey(manufacturerName))
            {
                continue;
            }

            var manufacturer = Manufacturer.Create(manufacturerName);
            manufacturer.Id = Guid.NewGuid();
            _dbContext.Manufacturers.Add(manufacturer);

            await _eventPublisher.ManufacturerCreated(manufacturer, cancellationToken);
            manufacturerIds.Add(manufacturerName, manufacturer.Id);
        }

        return manufacturerIds;
    }

    private async Task<Dictionary<string, Guid>> GetOrCreateSupplierIdsAsync<T>(
        IEnumerable<T> importData,
        CancellationToken cancellationToken) where T : ProductExcelModelBase
    {
        var supplierNames = importData.Where(p => p.Supplier != null).Select(p => p.Supplier!)
            .ToHashSet(StringComparer.OrdinalIgnoreCase);
        var supplierIds = await _dbContext.Suppliers
            .Where(s => supplierNames.Contains(s.Name))
            .Select(x => new { x.Id, x.Name })
            .ToDictionaryAsync(x => x.Name.Trim(), x => x.Id, StringComparer.OrdinalIgnoreCase, cancellationToken);
        foreach (var supplierName in supplierNames)
        {
            if (supplierIds.ContainsKey(supplierName))
            {
                continue;
            }

            var supplier = Supplier.Create(supplierName);
            supplier.Id = Guid.NewGuid();
            _dbContext.Suppliers.Add(supplier);

            await _eventPublisher.SupplierCreated(supplier, cancellationToken);
            supplierIds.Add(supplierName, supplier.Id);
        }

        return supplierIds;
    }

    private async Task<Dictionary<string, Guid>> GetOrCreateBatteryTypeIdsAsync<T>(
        IEnumerable<T> importData,
        CancellationToken cancellationToken) where T : ProductExcelModelBase
    {
        if (!typeof(T).IsAssignableTo(typeof(IModelWithBatteryTypes)))
        {
            return new Dictionary<string, Guid>();
        }

        var batteryTypeNames = importData.Cast<IModelWithBatteryTypes>()
            .SelectMany(p => ProductMappings.ParseMultiValueString(p.BatteryTypes))
            .ToHashSet(StringComparer.OrdinalIgnoreCase);
        var batteryTypeIds = await _dbContext.BatteryTypes
            .Where(s => batteryTypeNames.Contains(s.Name))
            .Select(x => new { x.Id, x.Name })
            .ToDictionaryAsync(x => x.Name.Trim(), x => x.Id, StringComparer.OrdinalIgnoreCase, cancellationToken);
        foreach (var batteryTypeName in batteryTypeNames)
        {
            if (batteryTypeIds.ContainsKey(batteryTypeName))
            {
                continue;
            }

            var batteryType = BatteryType.Create(batteryTypeName);
            _dbContext.BatteryTypes.Add(batteryType);

            var batteryTypeCreatedEvent = new BatteryTypeCreated
            {
                Id = batteryType.Id,
                Name = batteryType.Name,
                IsActive = batteryType.IsActive,
            };
            await _azureServiceBusPublisher.PublishBatteryTypeCreated(batteryTypeCreatedEvent);
            batteryTypeIds.Add(batteryTypeName, batteryType.Id);
        }

        return batteryTypeIds;
    }

    private async Task<Dictionary<string, Guid>> GetOrCreateColorIdsAsync<T>(
        IEnumerable<T> importData,
        CancellationToken cancellationToken) where T : ProductExcelModelBase
    {
        if (!typeof(T).IsAssignableTo(typeof(IModelWithColors)))
        {
            return new Dictionary<string, Guid>();
        }

        var colorNames = importData.Cast<IModelWithColors>()
            .SelectMany(p => ProductMappings.ParseMultiValueString(p.Colors))
            .ToHashSet(StringComparer.OrdinalIgnoreCase);
        var colorIds = await _dbContext.Colors
            .Where(s => colorNames.Contains(s.Name))
            .Select(x => new { x.Id, x.Name })
            .ToDictionaryAsync(x => x.Name.Trim(), x => x.Id, StringComparer.OrdinalIgnoreCase, cancellationToken);
        foreach (var colorName in colorNames)
        {
            if (colorIds.ContainsKey(colorName))
            {
                continue;
            }

            var color = Color.Create(colorName);
            _dbContext.Colors.Add(color);

            await _eventPublisher.ColorCreated(color, cancellationToken);
            colorIds.Add(colorName, color.Id);
        }

        return colorIds;
    }

    private async Task<Dictionary<string, Guid>> GetHearingAidTypeIdsAsync<T>(
        IEnumerable<T> importData, CancellationToken cancellationToken) where T : ProductExcelModelBase
    {
        if (!typeof(T).IsAssignableTo(typeof(HearingAidExcelModel)))
        {
            return new Dictionary<string, Guid>();
        }

        var hearingAidTypeNames = importData.Cast<HearingAidExcelModel>().Select(p => p.Type)
            .ToHashSet(StringComparer.OrdinalIgnoreCase);
        var hearingAidTypeIds = await _dbContext.HearingAidTypes
            .Where(x => hearingAidTypeNames.Contains(x.Name))
            .Select(x => new { x.Id, x.Name })
            .ToDictionaryAsync(x => x.Name.Trim(), x => x.Id, StringComparer.OrdinalIgnoreCase, cancellationToken);

        return hearingAidTypeIds;
    }

    private async Task<Dictionary<string, Guid>> GetCptCodeIdsAsync<T>(
        IEnumerable<T> importData, CancellationToken cancellationToken) where T : ProductExcelModelBase
    {
        if (_operationContext.CountryId != CountryEnum.UnitedStates)
        {
            return new Dictionary<string, Guid>();
        }

        var cptCodeCodes = importData
            .SelectMany(x => ProductMappings.ParseMultiValueString(x.CPTCodes))
            .ToHashSet(StringComparer.OrdinalIgnoreCase);
        var cptCodeIds = await _dbContext.CPTCodes
            .Where(x => x.IsActive && cptCodeCodes.Contains(x.Code))
            .Select(x => new { x.Id, x.Code })
            .ToDictionaryAsync(x => x.Code.Trim(), x => x.Id, StringComparer.OrdinalIgnoreCase, cancellationToken);

        return cptCodeIds;
    }
}
