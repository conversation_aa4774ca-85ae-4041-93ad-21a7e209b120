using Auditdata.Infrastructure.Excel.Models;
using Auditdata.InventoryService.Core.Features.ProductsExcelImport.Models.Excel.Abstractions;

namespace Auditdata.InventoryService.Core.Features.ProductsExcelImport.Models.Excel;

public class BatteryExcelModel : ProductExcelModelBase, IModelWithBatteryTypes, IModelWithLdWarranty
{
    [ExcelColumn("BatteryType", 350)]
    public string BatteryTypes { get; set; } = null!;

    [ExcelColumn("L&D Warranty", 625)]
    public int? LDWarranty { set; get; }

    [ExcelColumn("Quantity", 650)]
    public int Quantity { get; set; }
}
