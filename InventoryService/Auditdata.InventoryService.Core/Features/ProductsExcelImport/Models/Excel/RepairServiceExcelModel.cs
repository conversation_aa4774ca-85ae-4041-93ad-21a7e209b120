using Auditdata.Infrastructure.Excel.Models;
using Auditdata.InventoryService.Core.Features.ProductsExcelImport.Models.Excel.Abstractions;

namespace Auditdata.InventoryService.Core.Features.ProductsExcelImport.Models.Excel;

public class RepairServiceExcelModel : ProductExcelModelBase, IModelWithLdWarranty
{
    [ExcelColumn("L&D Warranty", 625)]
    public int? LDWarranty { get; set; }
    
    [ExcelColumn("ACC Code", 2710, Country = CountryFlag.NZ)]
    public string? AccCode { get; set; }
    
    [ExcelColumn("ACC description", 2720, Country = CountryFlag.NZ)]
    public string? AccDescription { get; set; }
    
    [ExcelColumn("ACC price excl. GST", 2730, Country = CountryFlag.NZ)]
    public decimal AccPriceExclGst { get; set; }
    
    [ExcelColumn("ACC price incl. GST", 2740, Country = CountryFlag.NZ)]
    public decimal AccPriceInclGst { get; set; }
}
