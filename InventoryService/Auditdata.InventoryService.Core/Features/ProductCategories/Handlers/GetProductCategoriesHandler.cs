using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Features.ProductCategories.Models;
using Auditdata.InventoryService.Core.Features.ProductCategories.Queries;
using Microsoft.EntityFrameworkCore;

namespace Auditdata.InventoryService.Core.Features.ProductCategories.Handlers;

public class GetProductCategoriesHandler : IRequestHandler<GetProductCategoriesQuery, GetProductCategoriesResult>
{
    private readonly IDbContext _dbContext;
    private readonly IMapper _mapper;

    public GetProductCategoriesHandler(IDbContext dbContext, IMapper mapper)
    {
        _dbContext = dbContext;
        _mapper = mapper;
    }

    public async Task<GetProductCategoriesResult> Handle(GetProductCategoriesQuery request, CancellationToken cancellationToken)
    {
        var productCategories = await _dbContext
            .ProductCategories.AsNoTracking()
            .Include(x => x.ImportMetadatas.OrderByDescending(x => x.ImportDate).Take(1))
            .ToListAsync(cancellationToken);

        var productCategoryDtos = productCategories.Select(x =>
        {
            var dto = _mapper.Map<ProductCategoryDto>(x);
            dto.LastSuccessfulSkuImportDate = x.ImportMetadatas?.FirstOrDefault()?.ImportDate;
            return dto;
        });

        return new GetProductCategoriesResult(productCategoryDtos);
    }
}
