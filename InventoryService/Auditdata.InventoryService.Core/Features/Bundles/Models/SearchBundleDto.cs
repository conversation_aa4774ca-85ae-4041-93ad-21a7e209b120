namespace Auditdata.InventoryService.Core.Features.Bundles.Models;

public class SearchBundleDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = null!;
    public string? Description { get; set; }
    public decimal TotalCost { get; set; }
    public bool IsActive { get; set; }
    public bool IsSellable { get; set; }
    public bool HasInactiveProducts { get; set; }
    public bool HasDeletedProducts { get; set; }

    public static SearchBundleDto FromBundle(Bundle bundle)
    {
        return new SearchBundleDto
        {
            Id = bundle.Id,
            Name = bundle.Name,
            Description = bundle.Description,
            IsSellable = bundle.IsSellable,
            TotalCost = bundle.TotalCost,
            IsActive = bundle.IsActive,
            HasInactiveProducts = bundle.Products.Any(y => !y.IsDeleted && !y.Product!.IsActive),
            HasDeletedProducts = bundle.Products.Any(y => !y.IsDeleted && y.Product!.IsDeleted)
        };
    }
}
