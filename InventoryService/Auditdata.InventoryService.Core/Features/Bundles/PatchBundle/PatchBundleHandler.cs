using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Exceptions;
using Microsoft.EntityFrameworkCore;

namespace Auditdata.InventoryService.Core.Features.Bundles.PatchBundle;

public class PatchBundleHandler : IRequestHandler<PatchBundleCommand>
{
    private readonly IDbContext _dbContext;
    private readonly IMapper _mapper;

    public PatchBundleHandler(
        IDbContext dbContext,
        IMapper mapper)
    {
        _dbContext = dbContext;
        _mapper = mapper;
    }

    public async Task Handle(PatchBundleCommand command, CancellationToken cancellationToken)
    {
        var bundle = await _dbContext.Bundles
                         .Include(x => x.Products)
                         .FirstOrDefaultAsync(x => x.Id == command.Id, cancellationToken: cancellationToken)
                     ?? throw new EntityNotFoundException<Bundle>(command.Id, "Bundle not found");


        bundle = _mapper.Map(command, bundle);

        await _dbContext.SaveChangesAsync(cancellationToken);
    }
}
