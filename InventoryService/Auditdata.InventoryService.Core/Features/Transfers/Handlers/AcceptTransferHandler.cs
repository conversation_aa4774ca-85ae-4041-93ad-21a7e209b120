using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Events.Transfers;
using Auditdata.InventoryService.Core.Exceptions;
using Auditdata.InventoryService.Core.Features.StockTransactions.Commands;
using Auditdata.InventoryService.Core.Features.Transfers.Commands;
using Microsoft.EntityFrameworkCore;

namespace Auditdata.InventoryService.Core.Features.Transfers.Handlers;

public class AcceptTransferHandler : IRequestHandler<AcceptTransferCommand>
{
    private readonly IMediator _mediator;
    private readonly IRequestClient<AcceptTransfer> _acceptTransferClient;
    private readonly IRequestClient<GetTransferStatus> _getTransferStatusClient;
    private readonly IDbContext _dbContext;
    private readonly IEventPublisher _eventPublisher;

    public AcceptTransferHandler(
        IMediator mediator,
        IRequestClient<AcceptTransfer> acceptTransferClient,
        IRequestClient<GetTransferStatus> getTransferStatusClient,
        IDbContext dbContext,
        IEventPublisher eventPublisher)
    {
        _mediator = mediator;
        _acceptTransferClient = acceptTransferClient;
        _getTransferStatusClient = getTransferStatusClient;
        _dbContext = dbContext;
        _eventPublisher = eventPublisher;
    }

    public async Task Handle(AcceptTransferCommand request, CancellationToken cancellationToken)
    {
        var transfer = (await _getTransferStatusClient.GetResponse<TransfersState>(
            new GetTransferStatus(request.TransferId), cancellationToken)).Message;
        if (transfer is null)
        {
            throw new EntityNotFoundException<TransfersState>(request.TransferId);
        }

        if (transfer.CurrentState == "Accepted")
        {
            return;
        }

        await _acceptTransferClient.GetResponse<TransferAccepted>(new AcceptTransfer(request.TransferId), cancellationToken);
        var stockProductItem = await _dbContext.StockProductItems
            .Include(x => x.StockProduct).ThenInclude(x => x.Stock)
            .Include(x => x.StockProduct).ThenInclude(x => x.Product)
            .FirstOrDefaultAsync(x => x.Id == transfer.StockProductItemId, cancellationToken);
        if (stockProductItem is null)
        {
            throw new EntityNotFoundException<StockProductItem>(transfer.StockProductItemId, $"$StockProductItem was not found for transfer {transfer.TransferId}");
        }

        var newStockProduct = await _dbContext.StockProducts
            .Include(x => x.Stock).Include(x => x.Product)
            .FirstAsync(x => x.StockId == transfer.ToStockId && x.ProductId == stockProductItem.StockProduct.ProductId, cancellationToken);

        var originalStock = stockProductItem.StockProduct.Stock;
        newStockProduct.AcceptTransfer(stockProductItem);

        await _eventPublisher.StockProductItemTransferred(stockProductItem, originalStock, cancellationToken);
        await CreateStockProductTransactionAsync(transfer, stockProductItem);

        _dbContext.StockProductItemLogs.Add(StockProductItemLog.TransferAcceptedLog(
            stockProductItem, originalStock));

        await _dbContext.SaveChangesAsync(cancellationToken);
    }

    private async Task CreateStockProductTransactionAsync(TransfersState transfer, StockProductItem stockProductItem)
    {
        await _mediator.Send(new CreateStockTransactionCommand
        {
            StockId = transfer.ToStockId,
            ProductId = stockProductItem.StockProduct.ProductId,
            Type = StockTransactionType.StockAdjustment,
            Quantity = 1
        });

        await _mediator.Send(new CreateStockTransactionCommand
        {
            StockId = transfer.FromStockId,
            ProductId = stockProductItem.StockProduct.ProductId,
            Type = StockTransactionType.StockAdjustment,
            Quantity = -1
        });
    }
}
