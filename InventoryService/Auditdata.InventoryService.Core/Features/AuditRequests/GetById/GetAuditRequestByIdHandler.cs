using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Entities.AuditRequests;
using Auditdata.InventoryService.Core.Exceptions;
using Auditdata.InventoryService.Core.Features.AuditRequests.Models;
using Auditdata.InventoryService.Core.OperationContext;
using Microsoft.EntityFrameworkCore;

namespace Auditdata.InventoryService.Core.Features.AuditRequests.GetById;

public class GetAuditRequestByIdHandler : IRequestHandler<GetAuditRequestByIdQuery, AuditRequestResult>
{
    private readonly IDbContext _dbContext;
    private readonly IInventoryServiceOperationContext _operationContext;

    public GetAuditRequestByIdHandler(IDbContext dbContext, IInventoryServiceOperationContext operationContext)
    {
        _dbContext = dbContext;
        _operationContext = operationContext;
    }

    public async Task<AuditRequestResult> Handle(GetAuditRequestByIdQuery query, CancellationToken cancellationToken)
    {
        var auditRequest = await GetDbQuery(query)
            .FirstOrDefaultAsync(cancellationToken)
            ?? throw new EntityNotFoundException<AuditRequest>(query.Id);

        if (query.IncludeProducts == true &&
            ((auditRequest.BlindStockCalculation && !_operationContext.UserAuditRequestManager) || 
            (!_operationContext.UserAuditRequestSpecialist && !_operationContext.UserAuditRequestManager)))
        {
            foreach (var product in auditRequest.Products)
            {
                product.Expected = null;
            }
        }

        return auditRequest;
    }

    private IQueryable<AuditRequestResult> GetDbQuery(GetAuditRequestByIdQuery query)
    {
        var dbQuery = _dbContext.AuditRequests.AsNoTracking()
                    .Include(x => x.AuditLocations).ThenInclude(x => x.Stock).AsQueryable();

        if (query.IncludeProducts == true)
        {
            dbQuery = dbQuery.Include(x => x.AuditProducts).ThenInclude(x => x.SerialNumbers)
                .Include(x => x.AuditProducts).ThenInclude(x => x.Product).ThenInclude(x => x.Category)
                .Include(x => x.AuditProducts).ThenInclude(x => x.Product).ThenInclude(x => x.Supplier)
                .Include(x => x.AuditProducts).ThenInclude(x => x.Product).ThenInclude(x => x.Skus);
        }

        var isSpecialist = _operationContext.UserAuditRequestSpecialist && !_operationContext.UserAuditRequestManager;
        var login = _operationContext.Login;

        var resultedQuery = dbQuery.Where(x => x.Id == query.Id)
            .Select(x => new AuditRequestResult
            {
                Id = x.Id,
                Status = x.Status,
                RequestNumber = x.RequestNumber,
                DateTimeDue = x.DateTimeDue,
                AddAllProductsFromCatalog = x.AddAllProductsFromCatalog,
                BlindStockCalculation = x.BlindStockCalculation,
                IndicateSerialNumber = x.IndicateSerialNumber,
                IncludeOnTrialProducts = x.IncludeOnTrialProducts,
                MovedToReadyForCalculationDateTime = x.MovedToReadyForCalculationDateTime,
                CreationDate = x.CreationDate,
                Notes = x.Notes,
                ReturnedToDraft = x.ReturnedToDraft,
                Locations = x.Status == AuditRequestStatus.Draft
                ? x.AuditLocations.OrderBy(y => y.Stock!.Name)
                    .Select(y => new AuditLocationResult(y.LocationId, y.Stock!.Name)).ToList()
                : x.AuditLocations.OrderBy(y => y.LocationName)
                    .Select(y => new AuditLocationResult(y.LocationId, y.LocationName!)).ToList(),
                Products = query.IncludeProducts == true
                    ? x.AuditProducts.Select(y => new AuditProductResult
                    {
                        Id = y.ProductId,
                        Name = x.Status == AuditRequestStatus.Draft ? y.Product.Name : y.ProductName!,
                        CategoryId = y.Product.CategoryId,
                        Category = x.Status == AuditRequestStatus.Draft ? y.Product.Category!.Name : y.ProductCategoryName!,
                        SupplierId = y.Product.SupplierId,
                        SupplierName = x.Status == AuditRequestStatus.Draft ? y.Product.Supplier!.Name : y.SupplierName!,
                        Skus = x.Status == AuditRequestStatus.Draft
                            ? y.Product.Skus.OrderBy(z => z.SkuValue).Select(z => z.SkuValue).ToList()
                            : y.Skus.Order().ToList(),
                        Expected = y.ExpectedQuantity,
                        Counted = y.CountedQuantity,
                        IsManual = y.IsManual,
                        AddedBy = y.CreatedBy,
                        AddedByManager = y.AddedByManager,
                        IsDisabledToDelete = (y.AddedByManager && isSpecialist) || (!y.AddedByManager && isSpecialist && y.CreatedBy != login),
                        IsSerialized = y.Product.IsSerialized,
                        HasDiscrepancy =
                            y.ExpectedQuantity != y.CountedQuantity
                            || (y.IsManual && !y.AddedByManager)
                            || y.SerialNumbers.Any(z =>
                                z.IsManual
                                || z.Status == AuditProductSNStatus.Unverified
                                || z.Status == AuditProductSNStatus.Missing),
                        SerialNumbers = y.SerialNumbers.OrderBy(z => z.SerialNumber)
                            .Select(z => new AuditProductSerialNumberResult(
                                            z.SerialNumber,
                                            z.Status,
                                            z.IsManual,
                                            (z.AddedByManager && isSpecialist)
                                                    || (!z.AddedByManager && isSpecialist && z.CreatedBy != login),
                                            z.AddedByManager,
                                            z.Comments)).ToList()
                    }).OrderBy(z => z.Name).ToList()
                    : new List<AuditProductResult>(),
            });

        return resultedQuery;
    }
}
