using System.Data;
using Auditdata.Infrastructure.Contracts.OperationContext;
using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Entities.AuditRequests;
using Auditdata.InventoryService.Core.Features.AuditRequests.AddLog;
using Auditdata.InventoryService.Core.Features.AuditRequests.Models;
using Microsoft.EntityFrameworkCore;

namespace Auditdata.InventoryService.Core.Features.AuditRequests.Create;

public class CreateAuditRequestHandler : IRequestHandler<CreateAuditRequestCommand, AuditRequestCreateResult>
{
    private readonly IDbContext _dbContext;
    private readonly IMediator _mediator;
    private readonly IOperationContext _operationContext;
    private readonly TimeProvider _timeProvider;

    public CreateAuditRequestHandler(
        IDbContext dbContext,
        IMediator mediator,
        IOperationContext operationContext,
        TimeProvider timeProvider)
    {
        _dbContext = dbContext;
        _mediator = mediator;
        _operationContext = operationContext;
        _timeProvider = timeProvider;
    }

    public async Task<AuditRequestCreateResult> Handle(CreateAuditRequestCommand request, CancellationToken cancellationToken)
    {
        _dbContext.SetOperationTimeout(DbConst.LongDbOperationTimeout);
        
        var locationIds = request.Locations.Select(t => t.LocationId).ToList();

        var productsQuery = _dbContext.Products.AsNoTracking()
            .Where(x => x.IsActive && x.ControlledByStock);
        if (!request.AddAllProductsFromCatalog)
        {
            productsQuery = productsQuery.Where(x =>
                x.StockProducts.Any(y => locationIds.Contains(y.Stock.LocationId)
                && (x.IsSerialized
                    ? y.StockProductItems.Any(spi =>
                        spi.Status == StockProductItemStatus.Available ||
                        spi.Status == StockProductItemStatus.Reserved ||
                        spi.Status == StockProductItemStatus.ReservedByOrder ||
                        spi.Status == StockProductItemStatus.TransferInProcess ||
                        (request.IncludeOnTrialProducts && spi.Status == StockProductItemStatus.OnTrial))
                    : y.Quantity > 0)));
        }

        var productIds = await productsQuery.Select(x => x.Id).ToListAsync(cancellationToken);

        var auditRequest = AuditRequest.Create(
            AuditRequestStatus.Draft,
            request.DateTimeDue,
            request.Notes,
            request.BlindStockCalculation,
            request.IncludeOnTrialProducts,
            request.AddAllProductsFromCatalog,
            request.IndicateSerialNumber
        );
        auditRequest.AuditLocations.AddRange(request.Locations.Select(t => AuditLocation.Create(t.LocationId, null, auditRequest)));

        _dbContext.AuditRequests.Add(auditRequest);
        await _mediator.Send(
            new AddAuditRequestLogCommand(AuditRequestLogActionType.Created, auditRequest.Id), cancellationToken);

        var auditProducts = productIds.Select(productId => AuditProduct.Create(productId, auditRequest, false, true, []))
            .ToList();
        var utcNow = _timeProvider.GetUtcNow();
        auditProducts.ForEach(auditProduct =>
        {
            auditProduct.TenantId = _operationContext.TenantId;
            auditProduct.CreationDate = utcNow;
            auditProduct.CreatedBy = _operationContext.Login;
        });

        await using (var transaction = await _dbContext.Database.BeginTransactionAsync(
                         IsolationLevel.RepeatableRead, cancellationToken))
        {
            await _dbContext.SaveChangesAsync(cancellationToken);
            await _dbContext.InsertBulkAsync(auditProducts, cancellationToken);
            await transaction.CommitAsync(cancellationToken);
        }

        var auditRequestCreated = new AuditRequestCreateResult(auditRequest.Id);

        return auditRequestCreated;
    }
}
