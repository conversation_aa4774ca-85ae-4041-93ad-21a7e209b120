using Auditdata.Infrastructure.Excel.Models;

namespace Auditdata.InventoryService.Core.Features.AuditRequests.Models.Export;

public record AuditSerialNumberNonBlindExcelDto
{
    [ExcelColumn("S/N", 10)]
    public string SerialNumber { get; set; } = null!;
    [ExcelColumn("Status", 20)]
    public string Status { get; set; } = null!;
    [ExcelColumn("Comments", 30)]
    public string? Comments { get; set; }
}
