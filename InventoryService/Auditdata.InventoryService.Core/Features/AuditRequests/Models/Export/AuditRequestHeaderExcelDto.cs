using Auditdata.Infrastructure.Excel.Models;

namespace Auditdata.InventoryService.Core.Features.AuditRequests.Models.Export;

public record AuditRequestHeaderExcelDto
{
    [ExcelDocumentHeader(1, 1, IsBold = true)]
    public string Header { get; set; } = null!;
    [ExcelDocumentHeader(1, 2)]
    public string LocationLabel { get; set; } = null!;
    [ExcelDocumentHeader(2, 2)]
    public string Locations { get; set; } = null!;
}
