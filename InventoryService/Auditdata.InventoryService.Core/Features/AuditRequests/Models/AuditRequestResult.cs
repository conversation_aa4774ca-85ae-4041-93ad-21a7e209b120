using Auditdata.InventoryService.Core.Entities.AuditRequests;

namespace Auditdata.InventoryService.Core.Features.AuditRequests.Models;

public record AuditRequestResult
{
    public Guid Id { get; set; }
    public string? RequestNumber { get; set; }
    public AuditRequestStatus Status { get; set; }
    public DateTimeOffset DateTimeDue { get; set; }
    public string? Notes { get; set; }
    public bool BlindStockCalculation { get; set; }
    public bool IncludeOnTrialProducts { get; set; }
    public bool AddAllProductsFromCatalog { get; set; }
    public bool IndicateSerialNumber { get; set; }
    public DateTimeOffset? MovedToReadyForCalculationDateTime { get; set; }
    public DateTimeOffset CreationDate { get; set; }
    public List<AuditLocationResult> Locations { get; set; } = [];
    public List<AuditProductResult> Products { get; set; } = [];
    public bool ReturnedToDraft { get; set; }
}
