using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Abstractions.Services;
using Auditdata.InventoryService.Core.Constants;
using Auditdata.InventoryService.Core.Entities.AuditRequests;
using Auditdata.InventoryService.Core.Entities.Products;
using Auditdata.InventoryService.Core.Exceptions;
using Auditdata.InventoryService.Core.Features.AuditRequests.Models;
using Auditdata.InventoryService.Core.Features.Models;
using Auditdata.InventoryService.Core.OperationContext;
using Auditdata.Transport.Contracts.Exceptions;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;

namespace Auditdata.InventoryService.Core.Features.AuditRequests.Services;

public class AuditRequestService : IAuditRequestService
{
    private readonly IDbContext _dbContext;
    private readonly IInventoryServiceOperationContext _operationContext;

    public AuditRequestService(IDbContext dbContext, IInventoryServiceOperationContext operationContext)
    {
        _dbContext = dbContext;
        _operationContext = operationContext;
    }

    public async Task<AuditRequest> GetAuditRequestAsync(
        Guid auditRequestId, bool includeProducts, CancellationToken cancellationToken)
    {
        var auditRequestQuery = _dbContext.AuditRequests
            .Include(ar => ar.AuditLocations)
            .AsQueryable();

        if (includeProducts)
        {
            auditRequestQuery = auditRequestQuery
                .Include(ar => ar.AuditProducts)
                .ThenInclude(ap => ap.Product)
                .Include(ar => ar.AuditProducts)
                .ThenInclude(ap => ap.SerialNumbers);
        }

        return await auditRequestQuery
            .FirstOrDefaultAsync(ar => ar.Id == auditRequestId, cancellationToken)
            ?? throw new EntityNotFoundException<AuditRequest>(auditRequestId);
    }

    public async Task<int> GetAuditRequestNumberAsync(CancellationToken cancellationToken)
    {
        var maxRequestNumber = await _dbContext.AuditRequests
            .MaxAsync(ar => ar.RequestNumber, cancellationToken) ?? "AUD000001";
        return int.Parse(maxRequestNumber[3..]) + 1;
    }

    public async Task CheckDuplicatedSerialNumbersAsync(
        AuditRequest auditRequest,
        IReadOnlyCollection<string> newSerialNumbers,
        Guid productId,
        CancellationToken cancellationToken)
    {
        await CheckDuplicatesInAuditRequestAsync(auditRequest, productId, newSerialNumbers, cancellationToken);

        if (!auditRequest.BlindStockCalculation)
        {
            await CheckOtherStocksForSNDuplicatesAsync(auditRequest, productId, newSerialNumbers, cancellationToken);

            await CheckSNsFromNonCalculatedProductsAsync(auditRequest, productId, newSerialNumbers, cancellationToken);
        }
    }

    private async Task CheckDuplicatesInAuditRequestAsync(
        AuditRequest auditRequest,
        Guid productId,
        IReadOnlyCollection<string> newSerialNumbers,
        CancellationToken cancellationToken)
    {
        var duplicatesInPatchRequest = newSerialNumbers
            .GroupBy(sn => sn, StringComparer.OrdinalIgnoreCase)
            .Where(g => g.Count() > 1)
            .Select(g => g.Key);

        var manufacturerId = await _dbContext.Products
            .Where(p => p.Id == productId)
            .Select(p => p.ManufacturerId)
            .FirstOrDefaultAsync(cancellationToken);

        var duplicatesInAuditProducts = manufacturerId is null ? []
            : await _dbContext.AuditProductSerialNumbers
                .Where(x => x.AuditProduct.AuditRequestId == auditRequest.Id
                    && x.AuditProduct.ProductId != productId
                    && newSerialNumbers.Contains(x.SerialNumber)
                    && x.AuditProduct.Product.ManufacturerId == manufacturerId)
                .Select(x => x.SerialNumber)
                .ToListAsync(cancellationToken);

        var duplicates = duplicatesInAuditProducts.Concat(duplicatesInPatchRequest).ToArray();

        if (duplicates.Length > 0)
        {
            throw new DuplicatedSerialNumbersException(
                "S/N has already been added to this request",
                ErrorCodes.DuplicatedSNssInAuditRequest,
                duplicates,
                StatusCodes.Status409Conflict
            );
        }
    }

    private async Task CheckSNsFromNonCalculatedProductsAsync(
        AuditRequest auditRequest, Guid productId, IEnumerable<string> newSerialNumbers, CancellationToken cancellationToken)
    {
        var manufacturerId = await _dbContext.Products
            .Where(p => p.Id == productId)
            .Select(p => p.ManufacturerId)
            .FirstOrDefaultAsync(cancellationToken);
        if (manufacturerId is null)
        {
            return;
        }

        var duplicates = await _dbContext.StockProductItems.AsNoTracking()
            .Where(x =>
            x.SerialNumber != null
            && x.StockProduct.Product.IsSerialized && x.StockProduct.Product.ControlledByStock && x.StockProduct.Product.IsActive
            && x.StockProduct.Product.ManufacturerId == manufacturerId
            && x.StockProduct.ProductId != productId
            && (x.Status == StockProductItemStatus.Available ||
                x.Status == StockProductItemStatus.Reserved ||
                x.Status == StockProductItemStatus.ReservedByOrder ||
                x.Status == StockProductItemStatus.TransferInProcess ||
                (auditRequest.IncludeOnTrialProducts && x.Status == StockProductItemStatus.OnTrial))
            && newSerialNumbers.Contains(x.SerialNumber))
            .Select(x => x.SerialNumber)
            .ToArrayAsync(cancellationToken);

        if (duplicates.Length > 0)
        {
            throw new DuplicatedSerialNumbersException(
                "S/N assigned to a different product",
                ErrorCodes.DuplicatedSNssWithNotCalculated,
                duplicates!,
                StatusCodes.Status409Conflict
            );
        }
    }

    private async Task CheckOtherStocksForSNDuplicatesAsync(
        AuditRequest auditRequest, Guid productId, IEnumerable<string> newSerialNumbers, CancellationToken cancellationToken)
    {
        var locationId = auditRequest.AuditLocations.First().LocationId;

        var duplicates = await _dbContext.StockProductItems.AsNoTracking()
            .Where(x =>
            x.SerialNumber != null
            && x.StockProduct.ProductId == productId
            && x.StockProduct.Product.IsSerialized
            && x.StockProduct.Stock.LocationId != locationId
            && (x.Status == StockProductItemStatus.Available ||
                x.Status == StockProductItemStatus.Reserved ||
                x.Status == StockProductItemStatus.ReservedByOrder ||
                x.Status == StockProductItemStatus.TransferInProcess ||
                (auditRequest.IncludeOnTrialProducts && x.Status == StockProductItemStatus.OnTrial))
            && newSerialNumbers.Contains(x.SerialNumber))
            .Select(x => x.SerialNumber)
            .ToArrayAsync(cancellationToken);

        if (duplicates.Length > 0)
        {
            throw new DuplicatedSerialNumbersException(
                "S/N assigned to another location",
                ErrorCodes.DuplicatedSNsInOtherStocks,
                duplicates!,
                StatusCodes.Status409Conflict
            );
        }
    }

    private async Task<List<AuditProductInfo>> GetProductInfosAsync(
        IEnumerable<Guid> productIds, CancellationToken cancellationToken)
    {
        return await _dbContext.Products.AsNoTracking()
            .Where(p => productIds.Contains(p.Id))
            .Select(p => new AuditProductInfo
            {
                ProductId = p.Id,
                IsSerialized = p.IsSerialized,
                SupplierName = p.Supplier != null ? p.Supplier.Name : null,
                ProductName = p.Name,
                ProductCategoryName = p.Category.Name,
                ManufacturerName = p.Manufacturer != null ? p.Manufacturer.Name : null,
                Skus = p.Skus.Select(t => t.SkuValue).ToList(),
            })
            .ToListAsync(cancellationToken);
    }

    public async Task<List<AuditProductInfo>> GetProductsInfoWithStockAsync(
        AuditRequest auditRequest,
        Guid locationId,
        CancellationToken cancellationToken)
    {
        var includeOnTrial = auditRequest.IncludeOnTrialProducts;

        var addedProductIdsQuery = _dbContext.AuditProducts
            .Where(x => x.AuditRequestId == auditRequest.Id)
            .Select(x => x.ProductId);

        var productsQuery = _dbContext.Products.AsNoTracking()
            .Where(x => x.IsActive
                        && x.ControlledByStock
                        && addedProductIdsQuery.Contains(x.Id));
        if (!auditRequest.AddAllProductsFromCatalog)
        {
            var manualProductIdsQuery = _dbContext.AuditProducts
                .Where(x => x.AuditRequestId == auditRequest.Id && x.IsManual)
                .Select(x => x.ProductId);

            productsQuery = productsQuery.Where(x =>
                manualProductIdsQuery.Contains(x.Id)
                || x.StockProducts.Any(y => y.Stock.LocationId == locationId
                    && (x.IsSerialized
                        ? y.StockProductItems.Any(spi =>
                            spi.Status == StockProductItemStatus.Available ||
                            spi.Status == StockProductItemStatus.Reserved ||
                            spi.Status == StockProductItemStatus.ReservedByOrder ||
                            spi.Status == StockProductItemStatus.TransferInProcess ||
                            (includeOnTrial && spi.Status == StockProductItemStatus.OnTrial))
                        : y.Quantity > 0)));
        }

        return await productsQuery
            .Select(p =>
                new AuditProductInfo
                {
                    ProductId = p.Id,
                    IsSerialized = p.IsSerialized,
                    SupplierName = p.Supplier != null ? p.Supplier.Name : null,
                    ProductName = p.Name,
                    ProductCategoryName = p.Category.Name,
                    ManufacturerName = p.Manufacturer != null ? p.Manufacturer.Name : null,
                    Skus = p.Skus.Select(t => t.SkuValue).ToList(),

                    SerialNumbers =
                        !p.StockProducts.Any(sp => sp.Stock.LocationId == locationId)
                        ? new List<string?>()
                        : p.StockProducts
                            .First(sp => sp.Stock.LocationId == locationId)
                            .StockProductItems
                                .Where(spi =>
                                    spi.SerialNumber != null
                                    && (spi.Status == StockProductItemStatus.Available ||
                                    spi.Status == StockProductItemStatus.Reserved ||
                                    spi.Status == StockProductItemStatus.ReservedByOrder ||
                                    spi.Status == StockProductItemStatus.TransferInProcess ||
                                    (includeOnTrial && spi.Status == StockProductItemStatus.OnTrial))
                                )
                                .Select(spi => spi.SerialNumber)
                                .ToList(),
                    Quantity =
                        !p.StockProducts.Any(sp => sp.Stock.LocationId == locationId)
                        ? 0
                        : p.IsSerialized
                                ? p.StockProducts
                                    .First(sp => sp.Stock.LocationId == locationId)
                                    .StockProductItems
                                    .Count(stockProductItem =>
                                        stockProductItem.Status == StockProductItemStatus.Available ||
                                        stockProductItem.Status == StockProductItemStatus.Reserved ||
                                        stockProductItem.Status == StockProductItemStatus.ReservedByOrder ||
                                        stockProductItem.Status == StockProductItemStatus.TransferInProcess ||
                                        (includeOnTrial && stockProductItem.Status == StockProductItemStatus.OnTrial))
                                : p.StockProducts
                                    .First(sp => sp.Stock.LocationId == locationId).Quantity
                })
            .ToListAsync(cancellationToken);
    }

    public async Task<List<AuditProductInfo>> GetProductsInfoWithStockByIdsAsync(
        IEnumerable<Guid> productIds,
        Guid locationId,
        bool includeOnTrial,
        CancellationToken cancellationToken)
    {
        return await _dbContext.Products.AsNoTracking()
            .Where(p => productIds.Contains(p.Id))
            .Select(p =>
                new AuditProductInfo
                {
                    ProductId = p.Id,
                    IsSerialized = p.IsSerialized,
                    SupplierName = p.Supplier != null ? p.Supplier.Name : null,
                    ProductName = p.Name,
                    ProductCategoryName = p.Category.Name,
                    ManufacturerName = p.Manufacturer != null ? p.Manufacturer.Name : null,
                    Skus = p.Skus.Select(t => t.SkuValue).ToList(),

                    SerialNumbers =
                        !p.StockProducts.Any(sp => sp.Stock.LocationId == locationId)
                        ? new List<string?>()
                        : p.StockProducts
                            .First(sp => sp.Stock.LocationId == locationId)
                            .StockProductItems
                                .Where(spi =>
                                    spi.SerialNumber != null
                                    && (spi.Status == StockProductItemStatus.Available ||
                                    spi.Status == StockProductItemStatus.Reserved ||
                                    spi.Status == StockProductItemStatus.ReservedByOrder ||
                                    spi.Status == StockProductItemStatus.TransferInProcess ||
                                    (includeOnTrial && spi.Status == StockProductItemStatus.OnTrial))
                                )
                                .Select(spi => spi.SerialNumber)
                                .ToList(),
                    Quantity =
                        !p.StockProducts.Any(sp => sp.Stock.LocationId == locationId)
                        ? 0
                        : p.IsSerialized
                                ? p.StockProducts
                                    .First(sp => sp.Stock.LocationId == locationId)
                                    .StockProductItems
                                    .Count(stockProductItem =>
                                        stockProductItem.Status == StockProductItemStatus.Available ||
                                        stockProductItem.Status == StockProductItemStatus.Reserved ||
                                        stockProductItem.Status == StockProductItemStatus.ReservedByOrder ||
                                        stockProductItem.Status == StockProductItemStatus.TransferInProcess ||
                                        (includeOnTrial && stockProductItem.Status == StockProductItemStatus.OnTrial))
                                : p.StockProducts
                                    .First(sp => sp.Stock.LocationId == locationId).Quantity
                })
            .ToListAsync(cancellationToken);
    }

    public async Task AddNewAuditProductsInDraftAsync(
        List<AuditProductDto> auditProductDtos,
        AuditRequest auditRequest,
        CancellationToken cancellationToken)
    {
        var existingProductIds = await _dbContext.AuditProducts.AsNoTracking()
            .Where(x => x.AuditRequestId == auditRequest.Id
                && auditProductDtos.Select(y => y.ProductId).Contains(x.ProductId))
            .Select(x => x.ProductId)
            .ToListAsync(cancellationToken);

        var newProductIds = auditProductDtos.Select(x => x.ProductId).Except(existingProductIds).ToList();

        var auditProductsInfo = await GetProductInfosAsync(
            newProductIds,
            cancellationToken);

        foreach (var requestProductId in newProductIds)
        {
            var auditProductInfo = auditProductsInfo.FirstOrDefault(info => info.ProductId == requestProductId);
            if (auditProductInfo == null)
            {
                continue;
            }

            var newAuditProduct = AuditProduct.Create(
                requestProductId,
                auditRequest,
                true,
                true,
                auditProductInfo.Skus,
                auditProductInfo.SupplierName,
                auditProductInfo.ProductName,
                auditProductInfo.ProductCategoryName,
                auditProductInfo.ManufacturerName);

            auditRequest.AuditProducts.Add(newAuditProduct);
            _dbContext.AuditProducts.Add(newAuditProduct);
        }
    }

    public void UpdateAuditLocations(List<AuditLocationDto> auditLocationDtos, AuditRequest auditRequest)
    {
        if (auditRequest.ReturnedToDraft)
        {
            return;
        }

        var locationsToRemove = auditRequest.AuditLocations
            .Where(existingLocation => !auditLocationDtos.Any(r => r.LocationId == existingLocation.LocationId))
            .ToList();

        foreach (var location in locationsToRemove)
        {
            auditRequest.AuditLocations.Remove(location);
            _dbContext.AuditLocations.Remove(location);
        }

        foreach (var locationId in auditLocationDtos.Select(locationDto => locationDto.LocationId))
        {
            var existingAuditLocation = auditRequest.AuditLocations
                .FirstOrDefault(ap => ap.LocationId == locationId);

            if (existingAuditLocation == null)
            {
                var newLocation = AuditLocation.Create(locationId, null, auditRequest);
                _dbContext.AuditLocations.Add(newLocation);
            }
        }
    }

    public async Task ReplaceAuditProductsAsync(AuditRequest auditRequest, CancellationToken cancellationToken)
    {
        if (auditRequest.AddAllProductsFromCatalog)
        {
            return;
        }

        var auditProductIdsToUpdate = await _dbContext.Products.AsNoTracking()
            .Include(p => p.StockProducts)
                .ThenInclude(sp => sp.StockProductItems)
            .Include(p => p.StockProducts)
                .ThenInclude(sp => sp.Stock)
            .Where(p =>
                p.ControlledByStock && p.IsActive && (auditRequest.AddAllProductsFromCatalog ||

                p.StockProducts.Any(sp => auditRequest.AuditLocations.Select(t => t.LocationId).Contains(sp.Stock.LocationId) &&
                     (p.IsSerialized ?
                        sp.StockProductItems.Any(spi =>
                            spi.Status == StockProductItemStatus.Available ||
                            spi.Status == StockProductItemStatus.Reserved ||
                            spi.Status == StockProductItemStatus.ReservedByOrder ||
                            spi.Status == StockProductItemStatus.TransferInProcess ||
                            (auditRequest.IncludeOnTrialProducts && spi.Status == StockProductItemStatus.OnTrial)
                        )
                    :
                        sp.Quantity > 0))))
            .Select(p => p.Id)
            .ToListAsync(cancellationToken);
        
        var existingProductIds = await _dbContext.AuditProducts
            .Where(x => x.AuditRequestId == auditRequest.Id && (auditProductIdsToUpdate.Contains(x.ProductId) || x.IsManual))
            .Select(x => x.ProductId)
            .ToListAsync(cancellationToken);

        var productIdsToAdd = auditProductIdsToUpdate.Except(existingProductIds);

        var productsToRemove = await _dbContext.AuditProducts
            .Where(x => x.AuditRequestId == auditRequest.Id && !auditProductIdsToUpdate.Contains(x.ProductId) && !x.IsManual)
            .ToListAsync(cancellationToken);

        foreach (var product in productsToRemove)
        {
            auditRequest.AuditProducts.Remove(product);
            _dbContext.AuditProducts.Remove(product);
        }

        foreach (var productId in productIdsToAdd)
        {
            var newAuditProduct = AuditProduct.Create(productId, auditRequest, false, true, []);
            auditRequest.AuditProducts.Add(newAuditProduct);
            _dbContext.AuditProducts.Add(newAuditProduct);
        }
    }

    public async Task UpdateAuditProducts(
        List<AuditProductDto> auditProductDtos,
        AuditRequest auditRequest,
        bool isPatch,
        CancellationToken cancellationToken)
    {
        var auditProducts = await _dbContext.AuditProducts
            .Include(x => x.AuditRequest)
            .Include(x => x.Product)
            .Include(x => x.SerialNumbers)
            .Where(x => x.AuditRequestId == auditRequest.Id
                && auditProductDtos.Select(y => y.ProductId).Contains(x.ProductId))
            .ToListAsync(cancellationToken);

        var newProductIds = auditProductDtos.Select(x => x.ProductId).Except(auditProducts.Select(x => x.ProductId));
        var auditProductsInfo = await GetProductsInfoWithStockByIdsAsync(
            newProductIds,
            auditRequest.AuditLocations.First().LocationId,
            auditRequest.IncludeOnTrialProducts,
            cancellationToken);

        foreach (var requestProduct in auditProductDtos)
        {
            var auditProduct = auditProducts.FirstOrDefault(ap => ap.ProductId == requestProduct.ProductId);

            if (auditProduct is null)
            {
                var (newAuditProduct, isSerialized) = CreateAuditProduct(auditRequest, auditProductsInfo, requestProduct);
                UpdateSerialNumbers(newAuditProduct, requestProduct);
                SetCountedQuantity(newAuditProduct, requestProduct, isSerialized, isPatch);
                continue;
            }

            if (!requestProduct.IsAdd)
            {
                UpdateSerialNumbers(auditProduct, requestProduct);
                SetCountedQuantity(auditProduct, requestProduct, auditProduct.Product.IsSerialized, isPatch);
            }
        }
    }

    private (AuditProduct AuditProduct, bool IsSerialized) CreateAuditProduct(
        AuditRequest auditRequest, List<AuditProductInfo> auditProductsInfo, AuditProductDto requestProduct)
    {
        var auditProductInfo = auditProductsInfo.FirstOrDefault(info => info.ProductId == requestProduct.ProductId)
            ?? throw new EntityNotFoundException<Product>(requestProduct.ProductId);

        var addedByManager = _operationContext.UserAuditRequestManager;

        var newAuditProduct = AuditProduct.Create(
            requestProduct.ProductId,
            auditRequest,
            true,
            addedByManager,
            auditProductInfo.Skus,
            auditProductInfo.SupplierName,
            auditProductInfo.ProductName,
            auditProductInfo.ProductCategoryName,
            auditProductInfo.ManufacturerName);

        auditRequest.AuditProducts.Add(newAuditProduct);

        var serialNumbers = auditRequest.IndicateSerialNumber && !auditRequest.BlindStockCalculation
            ? auditProductInfo.SerialNumbers
                .Where(x => x != null)
                .Select(serialNumber => AuditProductSerialNumber.Create(
                    serialNumber!,
                    newAuditProduct,
                    AuditProductSNStatus.Unverified,
                    false,
                    _operationContext.UserAuditRequestManager,
                    null))
                .ToList() : [];
        newAuditProduct.SerialNumbers.AddRange(serialNumbers);
        newAuditProduct.ExpectedQuantity = auditProductInfo.Quantity;

        _dbContext.AuditProductSerialNumbers.AddRange(serialNumbers);
        _dbContext.AuditProducts.Add(newAuditProduct);
        return (newAuditProduct, auditProductInfo.IsSerialized);
    }

    private void UpdateSerialNumbers(AuditProduct auditProduct, AuditProductDto requestProduct)
    {
        var serialNumbersToRemove = auditProduct.SerialNumbers
            .Where(auditSerial => !requestProduct.SerialNumbers
                .Any(reqSerial => string.Equals(reqSerial.SerialNumber, auditSerial.SerialNumber, StringComparison.OrdinalIgnoreCase)) &&
                !requestProduct.IsAdd)
            .ToList();
        ValidateDeleteSerialNumbersPermission(serialNumbersToRemove);
        foreach (var serialNumberToRemove in serialNumbersToRemove)
        {
            auditProduct.SerialNumbers.Remove(serialNumberToRemove);
            _dbContext.AuditProductSerialNumbers.Remove(serialNumberToRemove);
        }

        foreach (var serialNumber in requestProduct.SerialNumbers)
        {
            var auditProductSerialNumber = auditProduct.SerialNumbers
                .FirstOrDefault(t => t.SerialNumber == serialNumber.SerialNumber && t.AuditProductId == auditProduct.Id);

            if (auditProductSerialNumber != null)
            {
                if (!auditProductSerialNumber.IsManual)
                {
                    auditProductSerialNumber.Status = serialNumber.Status ?? AuditProductSNStatus.Unverified;
                }

                auditProductSerialNumber.Comments = serialNumber.Comments;
            }
            else
            {
                var addedByManager = _operationContext.UserAuditRequestManager;
                var newSerialNumber = AuditProductSerialNumber
                    .Create(
                    serialNumber.SerialNumber,
                    auditProduct,
                    AuditProductSNStatus.Undefined,
                    true,
                    addedByManager,
                    serialNumber.Comments);
                auditProduct.SerialNumbers.Add(newSerialNumber);
                _dbContext.AuditProductSerialNumbers.Add(newSerialNumber);
            }
        }
    }

    private static void SetCountedQuantity(
        AuditProduct auditProduct, AuditProductDto requestProduct, bool isSerialized, bool isPatch)
    {
        if (!isSerialized || !auditProduct.AuditRequest.IndicateSerialNumber)
        {
            if (!isPatch || requestProduct.Counted.HasValue)
            {
                auditProduct.CountedQuantity = requestProduct.Counted ?? 0;
            }
        }
        else
        {
            if (auditProduct.AuditRequest.BlindStockCalculation)
            {
                auditProduct.CountedQuantity = auditProduct.SerialNumbers.Count(s => s.IsManual);
            }
            else
            {
                auditProduct.CountedQuantity = auditProduct.SerialNumbers
                    .Count(s => s.IsManual || s.Status == AuditProductSNStatus.Verified);
            }
        }
    }

    public async Task ValidateDraftProductsAsync(IEnumerable<Guid> productIds, CancellationToken cancellationToken)
    {
        var unavailableProductName = await _dbContext.Products
            .Where(x => productIds.Contains(x.Id) && !x.IsActive)
            .Select(x => x.Name)
            .FirstOrDefaultAsync(cancellationToken);

        if (unavailableProductName is not null)
        {
            throw new BusinessException(
                $"Product {unavailableProductName} is not available anymore and should be removed from the Products list before saving.",
                ErrorCodes.ProductNotAvailableShouldBeRemovedFromProducts);
        }
    }

    public void ValidateUpdatePermissions(AuditRequest auditRequest)
    {
        if ((auditRequest.Status is AuditRequestStatus.Draft or AuditRequestStatus.Finalized) &&
            _operationContext.UserAuditRequestSpecialist && !_operationContext.UserAuditRequestManager)
        {
            throw new BusinessException($"Specialist cannot update Audit request in {auditRequest.Status} status",
                ErrorCodes.SpecialistCannotUpdateStatus);
        }
    }

    public void ValidateDeleteProductsPermission(IEnumerable<AuditProduct> auditProductsToRemove)
    {
        if (_operationContext.UserAuditRequestManager)
        {
            return;
        }

        foreach (var auditProduct in auditProductsToRemove)
        {
            if (auditProduct.CreatedBy != _operationContext.Login)
            {
                throw new BusinessException(
                    "Specialist can't delete products added by others",
                    ErrorCodes.SpecialistCannotDeleteProductAddedByOthers,
                    _operationContext.Login);
            }
        }
    }

    private void ValidateDeleteSerialNumbersPermission(IEnumerable<AuditProductSerialNumber> auditSerialNumbersToRemove)
    {
        if (_operationContext.UserAuditRequestManager)
        {
            return;
        }

        foreach (var auditSerialNumber in auditSerialNumbersToRemove)
        {
            if (!auditSerialNumber.IsManual || auditSerialNumber.CreatedBy != _operationContext.Login)
            {
                throw new BusinessException(
                    "Specialist can't delete serial numbers added by others",
                    ErrorCodes.SpecialistCannotDeleteSNAddedByOthers,
                    _operationContext.Login);
            }
        }
    }
}
