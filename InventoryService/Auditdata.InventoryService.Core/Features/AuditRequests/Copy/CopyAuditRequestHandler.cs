using Auditdata.Infrastructure.Contracts.OperationContext;
using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Entities.AuditRequests;
using Auditdata.InventoryService.Core.Exceptions;
using Auditdata.InventoryService.Core.Features.AuditRequests.AddLog;
using Auditdata.InventoryService.Core.Features.AuditRequests.Models;
using Microsoft.EntityFrameworkCore;

namespace Auditdata.InventoryService.Core.Features.AuditRequests.Copy;

public class CopyAuditRequestHandler : IRequestHandler<CopyAuditRequestCommand, AuditRequestCreateResult>
{
    private readonly IDbContext _dbContext;
    private readonly IMediator _mediator;
    private readonly IOperationContext _operationContext;
    private readonly TimeProvider _timeProvider;

    public CopyAuditRequestHandler(
        IDbContext dbContext,
        IMediator mediator,
        IOperationContext operationContext,
        TimeProvider timeProvider)
    {
        _dbContext = dbContext;
        _mediator = mediator;
        _operationContext = operationContext;
        _timeProvider = timeProvider;
    }

    public async Task<AuditRequestCreateResult> Handle(CopyAuditRequestCommand command, CancellationToken cancellationToken)
    {
        var auditRequest = await _dbContext.AuditRequests.AsNoTracking()
            .Include(x => x.AuditProducts).Include(x => x.AuditLocations)
            .Where(x => x.Id == command.Id).FirstOrDefaultAsync(cancellationToken)
            ?? throw new EntityNotFoundException<AuditRequest>(command.Id);

        var newAuditRequest = AuditRequest.Create(
            AuditRequestStatus.Draft,
            auditRequest.DateTimeDue,
            auditRequest.Notes,
            auditRequest.BlindStockCalculation,
            auditRequest.IncludeOnTrialProducts,
            auditRequest.AddAllProductsFromCatalog,
            auditRequest.IndicateSerialNumber);

        var auditLocations = auditRequest.AuditLocations
            .Select(l => AuditLocation.Create(l.LocationId, l.LocationName, auditRequest));
        newAuditRequest.AuditLocations.AddRange(auditLocations);

        _dbContext.AuditRequests.Add(newAuditRequest);
        await _mediator.Send(
            new AddAuditRequestLogCommand(AuditRequestLogActionType.Copied, newAuditRequest.Id), cancellationToken);

        var auditProducts = auditRequest.AuditProducts
            .Select(x => AuditProduct.Create(x.ProductId, newAuditRequest, x.IsManual, x.AddedByManager, [])).ToList();
        var utcNow = _timeProvider.GetUtcNow();
        auditProducts.ForEach(auditProduct =>
        {
            auditProduct.TenantId = _operationContext.TenantId;
            auditProduct.CreationDate = utcNow;
            auditProduct.CreatedBy = _operationContext.Login;
        });

        await using (var transaction = await _dbContext.Database.BeginTransactionAsync(cancellationToken))
        {
            await _dbContext.SaveChangesAsync(cancellationToken);
            await _dbContext.InsertBulkAsync(auditProducts, cancellationToken);
            await transaction.CommitAsync(cancellationToken);
        }

        return new AuditRequestCreateResult(newAuditRequest.Id);
    }
}
