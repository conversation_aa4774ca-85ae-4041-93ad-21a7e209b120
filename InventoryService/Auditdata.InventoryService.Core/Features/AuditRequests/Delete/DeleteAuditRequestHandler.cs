using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Constants;
using Auditdata.InventoryService.Core.Entities.AuditRequests;
using Auditdata.InventoryService.Core.Exceptions;
using Auditdata.InventoryService.Core.Features.AuditRequests.AddLog;
using Auditdata.Transport.Contracts.Exceptions;
using Microsoft.EntityFrameworkCore;

namespace Auditdata.InventoryService.Core.Features.AuditRequests.Delete;

public class DeleteAuditRequestHandler : IRequestHandler<DeleteAuditRequestCommand>
{
    private readonly IDbContext _dbContext;
    private readonly IMediator _mediator;

    public DeleteAuditRequestHandler(IDbContext dbContext, IMediator mediator)
    {
        _dbContext = dbContext;
        _mediator = mediator;
    }

    public async Task Handle(DeleteAuditRequestCommand command, CancellationToken cancellationToken)
    {
        var auditRequest = await _dbContext.AuditRequests
            .Where(x => x.Id == command.Id).FirstOrDefaultAsync(cancellationToken)
            ?? throw new EntityNotFoundException<AuditRequest>(command.Id);

        if (auditRequest.Status is AuditRequestStatus.InProgress or AuditRequestStatus.Finalized or AuditRequestStatus.Done)
        {
            throw new BusinessException(
                $"Audit request with status {auditRequest.Status} cannot be deleted.",
                ErrorCodes.CannotBeDeleted,
                command.Id,
                auditRequest.Status);
        }

        _dbContext.AuditRequests.Remove(auditRequest);
        await _mediator.Send(
            new AddAuditRequestLogCommand(AuditRequestLogActionType.Updated, auditRequest.Id), cancellationToken);
        await _dbContext.SaveChangesAsync(cancellationToken);
    }
}
