using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Abstractions.Services;
using Auditdata.InventoryService.Core.Constants;
using Auditdata.InventoryService.Core.Entities.AuditRequests;
using Auditdata.InventoryService.Core.Features.AuditRequests.AddLog;
using Auditdata.Transport.Contracts.Exceptions;

namespace Auditdata.InventoryService.Core.Features.AuditRequests.GoToDraft;

public class GoToDraftHandler : IRequestHandler<GoToDraftCommand>
{
    private readonly IDbContext _dbContext;
    private readonly IAuditRequestService _auditRequestService;
    private readonly IMediator _mediator;

    public GoToDraftHandler(IDbContext dbContext, IAuditRequestService auditRequestService, IMediator mediator)
    {
        _dbContext = dbContext;
        _auditRequestService = auditRequestService;
        _mediator = mediator;
    }

    public async Task Handle(GoToDraftCommand request, CancellationToken cancellationToken)
    {
        var auditRequest = await _auditRequestService.GetAuditRequestAsync(request.AuditRequestId, true, cancellationToken);

        switch (auditRequest.Status)
        {
            case AuditRequestStatus.ReadyForCalculation:
                MoveToDraft(auditRequest);
                break;

            default:
                throw new BusinessException(
                    "Only requests in 'Ready for Calculation' status can be moved to 'Draft'.",
                    ErrorCodes.StatusCannotBeChanged);
        }

        await _mediator.Send(
            new AddAuditRequestLogCommand(AuditRequestLogActionType.Updated, auditRequest.Id), cancellationToken);
        await _dbContext.SaveChangesAsync(cancellationToken);
    }

    private void MoveToDraft(AuditRequest auditRequest)
    {
        foreach (var auditProduct in auditRequest.AuditProducts)
        {
            _dbContext.AuditProductSerialNumbers.RemoveRange(auditProduct.SerialNumbers);

            auditProduct.ExpectedQuantity = 0;
            auditProduct.CountedQuantity = 0;
        }

        auditRequest.Status = AuditRequestStatus.Draft;
        auditRequest.ReturnedToDraft = true;
    }
}
