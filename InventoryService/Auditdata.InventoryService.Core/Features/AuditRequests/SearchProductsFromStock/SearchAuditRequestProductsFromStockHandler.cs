using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Entities.AuditRequests;
using Auditdata.InventoryService.Core.Entities.Products;
using Auditdata.InventoryService.Core.Exceptions;
using Auditdata.InventoryService.Core.Features.AuditRequests.Models.Search;
using Auditdata.InventoryService.Core.OperationContext;
using Microsoft.EntityFrameworkCore;

namespace Auditdata.InventoryService.Core.Features.AuditRequests.SearchProductsFromStock;

public class SearchAuditRequestProductsFromStockHandler :
    IRequestHandler<SearchAuditRequestProductsFromStockQuery, SearchAuditRequestProductsFromStockResult>
{
    private readonly IDbContext _dbContext;
    private readonly IInventoryServiceOperationContext _operationContext;

    public SearchAuditRequestProductsFromStockHandler(
        IDbContext dbContext,
        IInventoryServiceOperationContext operationContext)
    {
        _dbContext = dbContext;
        _operationContext = operationContext;
    }

    public async Task<SearchAuditRequestProductsFromStockResult> Handle(
        SearchAuditRequestProductsFromStockQuery query, CancellationToken cancellationToken)
    {
        var auditRequest = await _dbContext.AuditRequests.AsNoTracking()
            .FirstOrDefaultAsync(t => t.Id == query.AuditRequestId, cancellationToken)
            ?? throw new EntityNotFoundException<AuditRequest>(query.AuditRequestId);

        var requestProductsQuery = GetAuditRequestProductsQuery(query);

        var (auditRequestProducts, total) = await requestProductsQuery
            .Select(x => new SearchAuditRequestProductFromStockDto
            {
                Id = x.Id,
                Name = x.Name,
                CategoryName = x.Category.Name,
                ManufacturerName = x.Manufacturer != null ? x.Manufacturer.Name : null,
                Quantity = x.IsSerialized
                    ? x.StockProducts
                        .Where(stockProduct => query.LocationIds.Contains(stockProduct.Stock.LocationId))
                        .SelectMany(stockProduct => stockProduct.StockProductItems)
                        .Count(stockProductItem =>
                            stockProductItem.Status == StockProductItemStatus.Available ||
                            stockProductItem.Status == StockProductItemStatus.Reserved ||
                            stockProductItem.Status == StockProductItemStatus.ReservedByOrder ||
                            stockProductItem.Status == StockProductItemStatus.TransferInProcess ||
                            (auditRequest.IncludeOnTrialProducts && stockProductItem.Status == StockProductItemStatus.OnTrial)
                        )
                    : x.StockProducts
                        .Where(stockProduct => query.LocationIds.Contains(stockProduct.Stock.LocationId))
                        .Sum(stockProduct => stockProduct.Quantity)
            })
            .ApplyTableQueryAsync(query.Page, query.PerPage, query.OrderBy, cancellationToken);

        if (auditRequest.BlindStockCalculation && !_operationContext.UserAuditRequestManager ||
            !_operationContext.UserAuditRequestSpecialist && !_operationContext.UserAuditRequestManager)
        {
            foreach (var auditRequestProduct in auditRequestProducts)
            {
                auditRequestProduct.Quantity = null;
            }
        }

        var result = new SearchAuditRequestProductsFromStockResult
        {
            Products = auditRequestProducts,
            TotalCount = total,
        };

        return result;
    }

    private IQueryable<Product> GetAuditRequestProductsQuery(
        SearchAuditRequestProductsFromStockQuery query)
    {
        var productsQuery = _dbContext.Products.AsNoTracking()
            .Include(t => t.Manufacturer)
            .Include(t => t.Category)
            .Include(t => t.StockProducts)
                .ThenInclude(t => t.Stock)
            .Include(t => t.StockProducts)
                .ThenInclude(t => t.StockProductItems)
            .Where(t => t.IsActive && t.ControlledByStock)
            .AsQueryable();

        if (!string.IsNullOrWhiteSpace(query.SearchText))
        {
            productsQuery = productsQuery.Where(x => x.Name!.Contains(query.SearchText));
        }

        if (query.ManufacturerId.HasValue)
        {
            productsQuery = productsQuery.Where(x => x.ManufacturerId == query.ManufacturerId);
        }

        if (query.CategoryId.HasValue)
        {
            productsQuery = productsQuery.Where(x => x.CategoryId == query.CategoryId);
        }

        return productsQuery;
    }
}
