using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Abstractions.Services;
using Auditdata.InventoryService.Core.Constants;
using Auditdata.InventoryService.Core.Entities.AuditRequests;
using Auditdata.InventoryService.Core.Features.AuditRequests.AddLog;
using Auditdata.Transport.Contracts.Exceptions;

namespace Auditdata.InventoryService.Core.Features.AuditRequests.GoToInProgess;

public class GoToInProgressHandler : IRequestHandler<GoToInProgressCommand>
{
    private readonly IDbContext _dbContext;
    private readonly IAuditRequestService _auditRequestService;
    private readonly IMediator _mediator;

    public GoToInProgressHandler(IDbContext dbContext, IAuditRequestService auditRequestService, IMediator mediator)
    {
        _dbContext = dbContext;
        _auditRequestService = auditRequestService;
        _mediator = mediator;
    }

    public async Task Handle(GoToInProgressCommand request, CancellationToken cancellationToken)
    {
        var auditRequest = await _auditRequestService.GetAuditRequestAsync(request.AuditRequestId, false, cancellationToken);

        if (auditRequest.Status != AuditRequestStatus.ReadyForCalculation)
        {
            throw new BusinessException(
                "Only requests in 'ReadyForCalculation' status can be moved to 'InProgress'.",
                ErrorCodes.StatusCannotBeChanged);
        }

        auditRequest.Status = AuditRequestStatus.InProgress;

        await _mediator.Send(
            new AddAuditRequestLogCommand(AuditRequestLogActionType.Updated, auditRequest.Id), cancellationToken);
        await _dbContext.SaveChangesAsync(cancellationToken);
    }
}
