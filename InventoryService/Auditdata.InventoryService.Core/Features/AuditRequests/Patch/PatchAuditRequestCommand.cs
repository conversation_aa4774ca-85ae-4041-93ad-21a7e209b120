using Auditdata.InventoryService.Core.Features.AuditRequests.Models;
using Morcatko.AspNetCore.JsonMergePatch;

namespace Auditdata.InventoryService.Core.Features.AuditRequests.Patch;

public record PatchAuditRequestCommand(Guid AuditRequestId, JsonMergePatchDocument<PatchAuditRequestDto> Patch) : IRequest;


public record PatchAuditRequestDto
{
    public DateTimeOffset DateTimeDue { get; set; }
    public string? Notes { get; set; }
    public List<AuditLocationDto> Locations { get; set; } = [];
    public Dictionary<Guid, PatchAuditProductDto?> Products { get; set; } = [];
}
