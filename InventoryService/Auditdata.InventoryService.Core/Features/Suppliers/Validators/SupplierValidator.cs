using Auditdata.InventoryService.Core.Abstractions.Validators;
using Auditdata.InventoryService.Core.Abstractions;
using Microsoft.EntityFrameworkCore;
using Auditdata.InventoryService.Core.Constants;
using ValidationException = Auditdata.InventoryService.Core.Exceptions.ValidationException;
using Auditdata.Transport.Contracts.Exceptions;
using Auditdata.InventoryService.Core.Features.Suppliers.Commands;

namespace Auditdata.InventoryService.Core.Features.Suppliers.Validators;

public class SupplierValidator : ISupplierValidator
{
    private readonly IDbContext _dbContext;

    public SupplierValidator(IDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    public async Task Validate(string name, CancellationToken ct)
    {
        var supplierExists = await _dbContext.Suppliers.AnyAsync(x => x.Name == name, ct);
        CheckSupplierExists(supplierExists);
    }

    public async Task Validate(UpdateSupplierCommand command, bool isActive, CancellationToken ct)
    {
        var supplierExists = await _dbContext.Suppliers.AnyAsync(x => x.Id != command.Id && x.Name == command.Name, ct);
        CheckSupplierExists(supplierExists);
        await CheckSupplierIsInSku(command, isActive, ct);
    }

    private void CheckSupplierExists(bool supplierExists)
    {
        if (supplierExists)
        {
            throw new ValidationException(ErrorCodes.SupplierNameAlreadyExists);
        }
    }

    private async Task CheckSupplierIsInSku(UpdateSupplierCommand command, bool isActive, CancellationToken ct)
    {
        if (isActive && !command.IsActive)
        {
            var isInSkuUse = await _dbContext.Skus.AnyAsync(x =>
                x.SupplierId == command.Id && !x.Product.IsDeleted, ct);

            if (isInSkuUse)
            {
                throw new BusinessException(
                    "The Supplier has been used for SKU creation. Please remove any existing SKUs before updating.",
                    ErrorCodes.SupplierUsedInSkuAndCannotUpdate);
            }
        }
    }
}
