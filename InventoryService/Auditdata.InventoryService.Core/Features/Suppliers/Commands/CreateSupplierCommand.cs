using Auditdata.InventoryService.Core.Features.Suppliers.Models;

namespace Auditdata.InventoryService.Core.Features.Suppliers.Commands;

public record CreateSupplierCommand : IRequest<CreatedSupplier>
{
    public string Name { get; set; } = null!;
    public bool IsActive { get; set; }

    public bool IsManufacturer { get; set; }
    public string? Website { get; set; }
    public string? EmailAddress { get; set; }
    public string PhoneNumber { get; set; } = null!;
    public string? FaxNumber { get; set; }
    public string Address1 { get; set; } = null!;
    public string? Address2 { get; set; }

    public Guid? CountryId { get; set; }

    public string City { get; set; } = null!;
    public Guid? StateId { get; set; }
    public string? State { get; set; }
    public string? PostalCode { get; set; }

    public SupplierContact? SalesContact { get; set; }
    public SupplierContact? AccountReceivableContact { get; set; }
}
