using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Abstractions.Validators;
using Auditdata.InventoryService.Core.Features.Suppliers.Commands;
using Auditdata.InventoryService.Core.Features.Suppliers.Models;

namespace Auditdata.InventoryService.Core.Features.Suppliers.Handlers;

public class CreateSupplierHandler : IRequestHandler<CreateSupplierCommand, CreatedSupplier>
{
    private readonly IDbContext _dbContext;
    private readonly IMapper _mapper;
    private readonly ISupplierValidator _supplierValidator;
    private readonly IManufacturerValidator _manufacturerValidator;
    private readonly IEventPublisher _eventPublisher;

    public CreateSupplierHandler(
        IDbContext dbContext,
        IMapper mapper,
        ISupplierValidator supplierValidator,
        IManufacturerValidator manufacturerValidator,
        IEventPublisher eventPublisher)
    {
        _dbContext = dbContext;
        _mapper = mapper;
        _supplierValidator = supplierValidator;
        _manufacturerValidator = manufacturerValidator;
        _eventPublisher = eventPublisher;
    }

    public async Task<CreatedSupplier> Handle(CreateSupplierCommand request, CancellationToken cancellationToken)
    {
        await _supplierValidator.Validate(request.Name, cancellationToken);

        var supplier = _mapper.Map<Supplier>(request);
        supplier.Id = Guid.NewGuid();
        _dbContext.Suppliers.Add(supplier);

        if (request.IsManufacturer)
        {
            await _manufacturerValidator.Validate(request.Name, cancellationToken);

            var manufacturer = _mapper.Map<Manufacturer>(request);
            manufacturer.Id = Guid.NewGuid();
            _dbContext.Manufacturers.Add(manufacturer);

            await _eventPublisher.ManufacturerCreated(manufacturer, cancellationToken);
        }

        await _eventPublisher.SupplierCreated(supplier, cancellationToken);
        await _dbContext.SaveChangesAsync(cancellationToken);

        return new CreatedSupplier(supplier.Id);
    }
}
