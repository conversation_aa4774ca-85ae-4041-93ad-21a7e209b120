using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Features.Suppliers.Models;
using Auditdata.InventoryService.Core.Features.Suppliers.Queries;
using Microsoft.EntityFrameworkCore;

namespace Auditdata.InventoryService.Core.Features.Suppliers.Handlers;

public class GetSuppliersByIdsHandler : IRequestHandler<GetSuppliersByIdsQuery, GetSuppliersByIdsResult>
{
    private readonly IDbContext _dbContext;

    public GetSuppliersByIdsHandler(IDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    public async Task<GetSuppliersByIdsResult> Handle(GetSuppliersByIdsQuery request, CancellationToken cancellationToken)
    {
        var suppliers = await _dbContext.Suppliers
            .Include(c => c.Country)
            .Where(x => request.SupplierIds.Contains(x.Id))
            .ToListAsync(cancellationToken);
        return new GetSuppliersByIdsResult(suppliers);
    }
}
