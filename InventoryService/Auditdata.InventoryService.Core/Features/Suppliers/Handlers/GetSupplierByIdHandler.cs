using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Features.Suppliers.Models;
using Auditdata.InventoryService.Core.Features.Suppliers.Queries;
using Microsoft.EntityFrameworkCore;

namespace Auditdata.InventoryService.Core.Features.Suppliers.Handlers;

public class GetSupplierByIdHandler : IRequestHandler<GetSupplierByIdQuery, GetSupplierResult>
{
    private readonly IDbContext _dbContext;

    public GetSupplierByIdHandler(IDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    public async Task<GetSupplierResult> Handle(GetSupplierByIdQuery request, CancellationToken cancellationToken)
    {
        var supplier = await _dbContext.Suppliers
            .Include(c => c.Country)
            .FirstOrDefaultAsync(c => c.Id == request.Id, cancellationToken);
        return new GetSupplier<PERSON><PERSON><PERSON>(supplier);
    }
}
