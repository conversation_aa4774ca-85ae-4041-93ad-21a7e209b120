using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Abstractions.Validators;
using Auditdata.InventoryService.Core.Exceptions;
using Auditdata.InventoryService.Core.Features.Suppliers.Commands;
using Microsoft.EntityFrameworkCore;

namespace Auditdata.InventoryService.Core.Features.Suppliers.Handlers;

public class UpdateSupplierHandler : IRequestHandler<UpdateSupplierCommand>
{
    private readonly IDbContext _dbContext;
    private readonly IMapper _mapper;
    private readonly ISupplierValidator _validator;
    private readonly IEventPublisher _eventPublisher;

    public UpdateSupplierHandler(
        IDbContext dbContext,
        IMapper mapper,
        ISupplierValidator validator,
        IEventPublisher eventPublisher)
    {
        _dbContext = dbContext;
        _mapper = mapper;
        _validator = validator;
        _eventPublisher = eventPublisher;
    }

    public async Task Handle(UpdateSupplierCommand request, CancellationToken cancellationToken)
    {
        var supplier = await _dbContext.Suppliers.FirstOrDefaultAsync(x => x.Id == request.Id, cancellationToken) ??
                       throw new EntityNotFoundException<Supplier>(request.Id);

        await _validator.Validate(request, supplier.IsActive, cancellationToken);
        _mapper.Map(request, supplier);

        await _eventPublisher.SupplierUpdated(supplier, cancellationToken);
        await _dbContext.SaveChangesAsync(cancellationToken);
    }
}
