using Auditdata.InventoryService.Core.Features.Products.Models;
using Auditdata.InventoryService.Core.Features.StockProductItems.Models;

namespace Auditdata.InventoryService.Core.Features.StockProductItems.Commands;

public class ExchangeProductsCommand : IRequest
{
    public Guid SaleId { get; set; }
    public IEnumerable<ProductReturnedItem> ReturnedItems { get; set; } = null!;

    public Guid NewSaleId { get; set; }
    public IEnumerable<ReserveProductItem> NewItems { get; set; } = null!;

    public string NewInvoiceNumber { get; set; } = null!;
    public string PatientName { get; set; } = null!;
}
