using Auditdata.InventoryService.Core.Features.StockProductItems.Models;
using Auditdata.Microservice.Messages.OriginEntities.Invoicing;

namespace Auditdata.InventoryService.Core.Features.StockProductItems.Commands;

public record CancelStockProductItemsTrialCommand(Guid LocationId, IEnumerable<TrialStockProductItem> ProductItems, 
    ReturnProductItemAction CancelTrialAction, string CancellationReasonName) : IRequest;
