using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Abstractions.Validators;
using Auditdata.InventoryService.Core.Constants;
using Auditdata.InventoryService.Core.Entities.StockProductItems;
using Auditdata.InventoryService.Core.Exceptions;
using Auditdata.InventoryService.Core.Features.StockProductItems.Events;
using Auditdata.Transport.Contracts.Exceptions;
using Microsoft.EntityFrameworkCore;

namespace Auditdata.InventoryService.Core.Features.StockProductItems.UpdateStockProductItem;

public class UpdateStockProductItemHandler : IRequestHandler<UpdateStockProductItemCommand>
{
    private readonly IDbContext _dbContext;
    private readonly ISerialNumbersValidator _serialNumbersValidator;
    private readonly IMediator _mediator;

    public UpdateStockProductItemHandler(
        IDbContext dbContext,
        ISerialNumbersValidator serialNumbersValidator,
        IMediator mediator)
    {
        _dbContext = dbContext;
        _serialNumbersValidator = serialNumbersValidator;
        _mediator = mediator;
    }
    
    public async Task Handle(UpdateStockProductItemCommand request, CancellationToken cancellationToken)
    {
        var stockProductItem = await _dbContext.StockProductItems
            .Include(x => x.Attributes)
            .Include(x => x.StockProduct)
            .ThenInclude(x => x.Product)
            .ThenInclude(x => x.Category)
            .Include(x => x.StockProduct)
            .ThenInclude(x => x.Product)
            .ThenInclude(x => x.Colors)
            .Include(x => x.StockProduct)
            .ThenInclude(x => x.Product)
            .ThenInclude(x => x.BatteryTypes)
            .Include(x => x.StockProduct)
            .ThenInclude(x => x.Product)
            .ThenInclude(x => x.Attributes)
            .ThenInclude(x => x.Attribute)
            .ThenInclude(x => x!.ProductCategories)
            .FirstOrDefaultAsync(x => x.Id == request.Id, cancellationToken);
        if (stockProductItem is null)
        {
            throw new EntityNotFoundException<StockProductItem>(request.Id);
        }

        if (!stockProductItem.IsAvailable())
        {
            throw new BusinessException(ErrorCodes.StockProductItemsNotAvailable);
        }

        var updatedProperties = new List<(string, object)>();

        if (stockProductItem.SerialNumber != request.SerialNumber)
        {
            await _serialNumbersValidator.CheckDuplicatedSerialNumbers(
                stockProductItem.StockProduct.Product.ManufacturerId!.Value,
                new List<string>
                {
                    request.SerialNumber
                },
                cancellationToken);
            stockProductItem.SerialNumber = request.SerialNumber;
            updatedProperties.Add((nameof(StockProductItem.SerialNumber), request.SerialNumber));
        }

        if (stockProductItem.ColorId != request.ColorId)
        {
            var newColor = await _dbContext.Colors.FindAsync(new object?[] { request.ColorId }, cancellationToken);
            stockProductItem.AddColor(newColor);
            updatedProperties.Add((nameof(StockProductItem.Color), stockProductItem.Color!.Name));
        }

        if (stockProductItem.BatteryTypeId != request.BatteryTypeId)
        {
            var newBatteryType = await _dbContext.BatteryTypes.FindAsync(new object?[] { request.BatteryTypeId }, cancellationToken);
            stockProductItem.AddBatteryType(newBatteryType);
            updatedProperties.Add((nameof(StockProductItem.BatteryType), stockProductItem.BatteryType!.Name));
        }

        foreach (var attribute in stockProductItem.Attributes)
        {
            var requestAttribute = request.Attributes.First(x => x.AttributeId == attribute.AttributeId);
            if (attribute.Value.ValueId != requestAttribute.Value.Id)
            {
                stockProductItem.UpdateAttribute(
                    attribute.AttributeId, requestAttribute.Value.Id, requestAttribute.Value.Value);
                attribute.Value = new StockProductItemAttributeValue
                {
                    ValueId = requestAttribute.Value.Id,
                    Value = requestAttribute.Value.Value
                };
                updatedProperties.Add((nameof(StockProductItem.Attributes), new string[] { requestAttribute.AttributeName!, requestAttribute.Value.Value }));
            }
        }

        await _mediator.Publish(new StockProductItemUpdatedEvent(stockProductItem, updatedProperties), cancellationToken);

        await _dbContext.SaveChangesAsync(cancellationToken);
    }
}
