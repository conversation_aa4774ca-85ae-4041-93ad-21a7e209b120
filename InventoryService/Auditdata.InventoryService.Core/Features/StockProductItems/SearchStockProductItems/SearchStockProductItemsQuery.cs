using Auditdata.InventoryService.Contracts.Responses.ProductItems;
using Auditdata.Transport.Contracts.PageResults;
using Auditdata.Transport.Contracts.Table;

namespace Auditdata.InventoryService.Core.Features.StockProductItems.SearchStockProductItems;

public class SearchStockProductItemsQuery : TableQueryBase, IRequest<TablePageResult<StockProductItemResponse>>
{
    public IReadOnlyList<Guid> StockIds { get; set; }
    public Guid ProductId { get; set; }
    public StockProductItemStatus? Status { get; set; }
    public string? SearchText { get; set; }
    public Guid? RegionId { get; set; }
}
