using Auditdata.InventoryService.Contracts.Responses.ProductItems;
using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.Transport.Contracts.PageResults;
using Auditdata.Transport.Contracts.Table;
using Microsoft.EntityFrameworkCore;

namespace Auditdata.InventoryService.Core.Features.StockProductItems.SearchStockProductItems;

public class SearchStockProductItemsHandler : IRequestHandler<SearchStockProductItemsQuery, TablePageResult<StockProductItemResponse>>
{
    private readonly IDbContext _dbContext;
    private readonly IMapper _mapper;

    public SearchStockProductItemsHandler(
        IDbContext dbContext,
        IMapper mapper)
    {
        _dbContext = dbContext;
        _mapper = mapper;
    }

    public async Task<TablePageResult<StockProductItemResponse>> Handle(
        SearchStockProductItemsQuery request, CancellationToken cancellationToken)
    {
        var query = _dbContext.StockProductItems
            .Include(x => x.BatteryType)
            .Include(x => x.Color)
            .Include(x => x.StockProduct.Stock)
            .Where(x => x.StockProduct.ProductId == request.ProductId &&
                        request.StockIds.Contains(x.StockProduct.StockId));
        if (request.Status is not null)
        {
            query = query.Where(x => x.Status == request.Status);
        }

        if (request.SearchText is not null)
        {
            query = query.Where(x => x.SerialNumber!.Contains(request.SearchText));
        }

        if (request.RegionId is not null)
        {
            query = query.Where(x => x.StockProduct.Stock.RegionId == request.RegionId);
        }

        var transfersQuery = _dbContext.StockProductItems
            .Include(x => x.BatteryType)
            .Include(x => x.Color)
            .Include(x => x.StockProduct.Stock)
            .Where(x => _dbContext.Transfers.Any(y => y.TransferId == x.TransferId &&
                                                      request.StockIds.Contains(y.ToStockId)) &&
                        x.StockProduct.ProductId == request.ProductId);
        if (request.Status is not null)
        {
            transfersQuery = transfersQuery.Where(x => x.Status == request.Status);
        }

        if (request.SearchText is not null)
        {
            transfersQuery = transfersQuery.Where(x => x.SerialNumber!.Contains(request.SearchText));
        }

        query = query.Union(transfersQuery);

        var (result, total) = await query
            .ApplyTableQueryAsync(request.Page, request.PerPage, request.OrderBy);

        var tableResponse = result.Select(x =>
        {
            var rowResponse = new TableRow<StockProductItemResponse>(
                _mapper.Map<StockProductItemResponse>(x));
            rowResponse.Model!.PendingTransferAcceptance = x!.TransferId is not null &&
                                                           !request.StockIds.Contains(x.StockProduct.StockId);

            return rowResponse;
        });

        return new TablePageResult<StockProductItemResponse>(tableResponse.ToArray(), request, total);
    }
}
