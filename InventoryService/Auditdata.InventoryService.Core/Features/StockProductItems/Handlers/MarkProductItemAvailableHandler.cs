using Auditdata.InventoryService.Contracts.Commands;
using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Abstractions.Azure;
using Auditdata.InventoryService.Core.Constants;
using Auditdata.InventoryService.Core.Exceptions;
using Auditdata.InventoryService.Core.Features.StockProductItems.Commands;
using Auditdata.Transport.Contracts.Exceptions;
using Microsoft.EntityFrameworkCore;
using StockTransactionType = Auditdata.InventoryService.Contracts.StockTransactionType;

namespace Auditdata.InventoryService.Core.Features.StockProductItems.Handlers;

public class MarkProductItemAvailableHandler : IRequestHandler<MarkProductItemAvailableCommand>
{
    private readonly IAzureServiceBusPublisher _azureServiceBusPublisher;
    private readonly IEventPublisher _eventPublisher;
    private readonly IDbContext _dbContext;

    public MarkProductItemAvailableHandler(
        IAzureServiceBusPublisher azureServiceBusPublisher,
        IEventPublisher eventPublisher,
        IDbContext dbContext)
    {
        _azureServiceBusPublisher = azureServiceBusPublisher;
        _eventPublisher = eventPublisher;
        _dbContext = dbContext;
    }
    
    public async Task Handle(MarkProductItemAvailableCommand request, CancellationToken cancellationToken)
    {
        var stockProductItem = await _dbContext.StockProductItems
            .Include(x => x.StockProduct).ThenInclude(x => x.Stock)
            .Include(x => x.StockProduct).ThenInclude(x => x.Product)
            .FirstOrDefaultAsync(x => x.Id == request.StockProductItemId, cancellationToken)
            ?? throw new EntityNotFoundException<StockProductItem>(request.StockProductItemId);

        if (stockProductItem.Status is StockProductItemStatus.Reserved
            && !stockProductItem.AddToStockAllowed)
        {
            throw new BusinessException(
                "Add item to stock is not allowed.",
                ErrorCodes.StockProductItemAddToStockNotAllowed,
                stockProductItem.Status, stockProductItem.AddToStockAllowed);
        }

        stockProductItem.AddToStockAllowed = false;
        stockProductItem.Status = StockProductItemStatus.Available;
        stockProductItem.StockProduct.AdjustQuantity(1);
        _dbContext.StockProductItemLogs.Add(StockProductItemLog.ReturnedToStockLog(stockProductItem));
        
        await _eventPublisher.SerializedStockAdjusted(stockProductItem.StockProduct, 1, [stockProductItem],
            cancellationToken);

        await _azureServiceBusPublisher.PublishAddStockTransactionCommand(new AddStockTransactionCommand
        {
            StockId = stockProductItem.StockProduct.StockId,
            ProductId = stockProductItem.StockProduct.ProductId,
            Type = StockTransactionType.StockAdjustment,
            Quantity = 1
        });

        await _dbContext.SaveChangesAsync(cancellationToken);
    }
}
