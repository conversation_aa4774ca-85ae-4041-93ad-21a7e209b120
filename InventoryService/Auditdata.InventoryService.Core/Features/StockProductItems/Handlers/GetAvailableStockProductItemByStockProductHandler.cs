using Auditdata.InventoryService.Core.Features.StockProductItems.Models;
using Auditdata.InventoryService.Core.Features.StockProductItems.Queries;

namespace Auditdata.InventoryService.Core.Features.StockProductItems.Handlers;

public class GetAvailableStockProductItemByStockProductHandler : IRequestHandler<GetAvailableStockProductItemByStockProductQuery, GetStockProductItemByProductResult>
{
    private readonly IStockProductItemsRepository _stockProductItemsRepository;

    public GetAvailableStockProductItemByStockProductHandler(IStockProductItemsRepository stockProductItemsRepository)
    {
        _stockProductItemsRepository = stockProductItemsRepository;
    }

    public async Task<GetStockProductItemByProductResult> Handle(GetAvailableStockProductItemByStockProductQuery request, CancellationToken cancellationToken)
    {
        var stockProductItems = await _stockProductItemsRepository.GetAvailableAsync(request.ProductId, request.StockId);
        return new GetStockProductItemByProductResult(stockProductItems.Where(x => !string.IsNullOrEmpty(x.SerialNumber)));
    }
}
