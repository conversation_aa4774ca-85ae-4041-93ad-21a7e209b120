using Auditdata.InventoryService.Contracts.Commands;
using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Abstractions.Azure;
using Auditdata.InventoryService.Core.Abstractions.Validators;
using Auditdata.InventoryService.Core.Exceptions;
using Auditdata.InventoryService.Core.Features.Products.Models;
using Auditdata.InventoryService.Core.Features.StockProductItems.Commands;
using Auditdata.InventoryService.Core.Features.StockProductItems.Dtos;
using Auditdata.InventoryService.Core.Features.StockProductItems.Models;
using Microsoft.EntityFrameworkCore;
using StockTransactionType = Auditdata.InventoryService.Contracts.StockTransactionType;

namespace Auditdata.InventoryService.Core.Features.StockProductItems.Handlers;

public class CreateStockProductItemHandler : IRequestHandler<CreateStockProductItemsCommand, IEnumerable<StockProductItemCreated>>
{
    private readonly IDbContext _dbContext;
    private readonly IAzureServiceBusPublisher _azureServiceBusPublisher;
    private readonly ISerialNumbersValidator _serialNumbersValidator;
    private readonly IEventPublisher _eventPublisher;

    public CreateStockProductItemHandler(
        IDbContext dbContext,
        IAzureServiceBusPublisher azureServiceBusPublisher,
        ISerialNumbersValidator serialNumbersValidator,
        IEventPublisher eventPublisher)
    {
        _dbContext = dbContext;
        _azureServiceBusPublisher = azureServiceBusPublisher;
        _serialNumbersValidator = serialNumbersValidator;
        _eventPublisher = eventPublisher;
    }

    public async Task<IEnumerable<StockProductItemCreated>> Handle(CreateStockProductItemsCommand request, CancellationToken cancellationToken)
    {
        var result = new List<StockProductItemCreated>();

        var transactionMessages = new List<TransactionMessage>();

        foreach (var item in request.Items)
        {
            var stockProduct = await _dbContext.StockProducts
                .Include(x => x.Stock)
                .Include(x => x.Product)
                .FirstOrDefaultAsync(x => x.Id == item.StockProductId, cancellationToken);
            if (stockProduct is null)
            {
                throw new EntityNotFoundException<StockProduct>(item.StockProductId);
            }

            await _serialNumbersValidator.CheckDuplicatedSerialNumbers(
                stockProduct.Product.ManufacturerId!.Value,
                new[] { item.SerialNumber },
                cancellationToken);

            var stockProductItem = stockProduct.AdjustSerializedItems(
                new List<string> { item.SerialNumber }, null, null, new List<StockProductItemAttributeDto>())[0];
            _dbContext.StockProductItems.Add(stockProductItem);

            result.Add(new StockProductItemCreated(
                item.StockProductId,
                stockProductItem.Id,
                stockProductItem.SerialNumber,
                item.ProductItemTransactionId));

            await _eventPublisher.StockProductItemCreated(stockProductItem, cancellationToken);
            _dbContext.StockProductItemLogs.Add(StockProductItemLog.StockAdjustedActionLog(stockProductItem));
            _dbContext.StockProductItemLogs.Add(StockProductItemLog.SerialNumberAddedActionLog(stockProductItem));

            await _eventPublisher.SerializedStockAdjusted(
                stockProduct,
                1,
                new[] { stockProductItem },
                cancellationToken);

            transactionMessages.Add(new TransactionMessage(stockProduct, 1, null, StockTransactionType.StockAdjustment));
        }

        await CreateTransactionsAsync(transactionMessages);
        await _dbContext.SaveChangesAsync(cancellationToken);

        return result;
    }

    private async Task CreateTransactionsAsync(List<TransactionMessage> transactionMessages)
    {
        var messages = transactionMessages.GroupBy(x => x.StockProduct);
        foreach (var message in messages)
        {
            await _azureServiceBusPublisher.PublishAddStockTransactionCommand(new AddStockTransactionCommand
            {
                Quantity = message.Sum(x => x.Quantity),
                Type = StockTransactionType.StockAdjustment,
                ProductId = message.Key.ProductId,
                StockId = message.Key.StockId
            });
        }
    }
}
