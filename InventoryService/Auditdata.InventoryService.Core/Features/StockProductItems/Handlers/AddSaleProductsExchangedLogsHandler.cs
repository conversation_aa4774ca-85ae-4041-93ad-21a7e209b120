using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Features.StockProductItems.Commands;
using Microsoft.EntityFrameworkCore;

namespace Auditdata.InventoryService.Core.Features.StockProductItems.Handlers;
public class AddSaleProductsExchangedLogsHandler : IRequestHandler<AddSaleProductsExchangedLogsCommand>
{
    private readonly IDbContext _dbContext;

    public AddSaleProductsExchangedLogsHandler(IDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    public async Task Handle(AddSaleProductsExchangedLogsCommand request, CancellationToken cancellationToken)
    {
        var returnedItemSpiIds = request.ReturnedItems.Select(x => x.StockProductItemId).Where(x => x is not null); 
        var returnedItemSPIds = request.ReturnedItems.Select(x => x.StockProductId); 
        var returnedStockProductItems = await _dbContext.StockProductItems
            .Include(x => x.StockProduct).ThenInclude(x => x.Product)
            .Where(x => (x.Status == StockProductItemStatus.Sold
                        && x.SaleId == request.SaleId
                        && returnedItemSPIds.Contains(x.StockProductId))
            || returnedItemSpiIds.Contains(x.Id))
            .ToListAsync(cancellationToken);

        foreach (var returnedStockProductItem in returnedStockProductItems)
        {
            _dbContext.StockProductItemLogs
                .Add(StockProductItemLog.ExchangedBySaleLog(returnedStockProductItem, request.NewInvoiceNumber));
        }

        var newItemSpiIds = request.NewItems.Select(x => x.StockProductItemId).Where(x => x is not null);
        var newStockProductItems = await _dbContext.StockProductItems
            .Include(x => x.StockProduct).ThenInclude(x => x.Product)
            .Where(x => newItemSpiIds.Contains(x.Id))
            .ToListAsync(cancellationToken);

        foreach (var newStockProductItem in newStockProductItems)
        {
            _dbContext.StockProductItemLogs
                .Add(StockProductItemLog.ExchangeBySaleLog(newStockProductItem, request.NewInvoiceNumber));
        }

        await _dbContext.SaveChangesAsync(cancellationToken);
    }
}
