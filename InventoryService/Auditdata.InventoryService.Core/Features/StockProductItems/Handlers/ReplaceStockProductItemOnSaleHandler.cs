using Auditdata.InventoryService.Core.Abstractions.Validators;
using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Features.StockProductItems.Commands;
using Microsoft.Extensions.Logging;
using Auditdata.InventoryService.Core.Exceptions;
using Microsoft.EntityFrameworkCore;
using Auditdata.Transport.Contracts.Exceptions;
using Auditdata.InventoryService.Core.Constants;
using Auditdata.InventoryService.Contracts.Commands;
using Auditdata.InventoryService.Core.Abstractions.Azure;

namespace Auditdata.InventoryService.Core.Features.StockProductItems.Handlers;
public class ReplaceStockProductItemOnSaleHandler : IRequestHandler<ReplaceStockProductItemOnSaleCommand>
{
    private readonly ILogger<ReplaceStockProductItemOnSaleHandler> _logger;
    private readonly ISerialNumbersValidator _serialNumbersValidator;
    private readonly IEventPublisher _eventPublisher;
    private readonly IAzureServiceBusPublisher _azureServiceBusPublisher;
    private readonly IDbContext _dbContext;

    public ReplaceStockProductItemOnSaleHandler(
        ILogger<ReplaceStockProductItemOnSaleHandler> logger,
        IDbContext dbContext,
        ISerialNumbersValidator serialNumbersValidator,
        IEventPublisher eventPublisher,
        IAzureServiceBusPublisher azureServiceBusPublisher)
    {
        _logger = logger;
        _dbContext = dbContext;
        _serialNumbersValidator = serialNumbersValidator;
        _eventPublisher = eventPublisher;
        _azureServiceBusPublisher = azureServiceBusPublisher;
    }

    public async Task Handle(ReplaceStockProductItemOnSaleCommand command, CancellationToken cancellationToken)
    {
        var oldStockProductItem = await GetStockProductItemAsync(command, cancellationToken) ??
            throw new EntityNotFoundException<StockProductItem>(command.OldStockProductItemId);

        if (oldStockProductItem.SaleId is null)
        {
            throw new BusinessException("Old stock product item was not sold",
                ErrorCodes.StockProductItemSellSaleIdNotFound);
        }

        var hasNewId = command.NewStockProductItemId.HasValue;
        var hasNewSerialNumber = !string.IsNullOrEmpty(command.NewSerialNumber);
        if ((hasNewId && hasNewSerialNumber)
            || (!hasNewId && !hasNewSerialNumber)
            || (!hasNewId && oldStockProductItem.Status == StockProductItemStatus.Replaced))
        {
            throw new ArgumentException("Wrong command values");
        }

        var newStockProductItem = hasNewId
            ? await _dbContext.StockProductItems
                .Include(x => x.StockProduct).ThenInclude(x => x.Product)
                .FirstOrDefaultAsync(x => x.Id == command.NewStockProductItemId, cancellationToken)
                ?? throw new EntityNotFoundException<StockProductItem>(command.NewStockProductItemId!.Value)
            : await CreateStockProductItemAsync(command, oldStockProductItem, cancellationToken);

        if (oldStockProductItem.Status == StockProductItemStatus.Replaced)
        {
            _logger.LogInformation("StockProductItem '{@OriginalSaleProductId}' was already replaced", oldStockProductItem.Id);
            await _eventPublisher.StockProductItemReplacedOnSale(
                command.CorrelationId,
                oldStockProductItem.Id,
                newStockProductItem!.Id,
                cancellationToken);
            await _dbContext.SaveChangesAsync(cancellationToken);
            return;
        }

        newStockProductItem.Reserve(oldStockProductItem.SaleId!.Value);
        newStockProductItem.Sell();
        await _eventPublisher.StockProductItemSold(newStockProductItem, cancellationToken);

        oldStockProductItem.Replace();

        await LogStockProductItemsHistoryAsync(oldStockProductItem, newStockProductItem, command.PatientName);

        await _eventPublisher.StockProductItemReplaced(oldStockProductItem.Id, newStockProductItem!.Id, cancellationToken);
        await _eventPublisher.StockProductItemReplacedOnSale(
            command.CorrelationId,
            oldStockProductItem.Id,
            newStockProductItem!.Id,
            cancellationToken);
        await _dbContext.SaveChangesAsync(cancellationToken);

        _logger.LogInformation(
            "StockProductItem '{@OriginalSaleProductId}' replaced with '{@ReplacedStockProductItemId}' for Sale '{@SaleId}' and Patient '{@PatientName}'",
            command.OldStockProductItemId, newStockProductItem!.Id, oldStockProductItem.SaleId, command.PatientName);
    }

    private async Task<StockProductItem?> GetStockProductItemAsync(ReplaceStockProductItemOnSaleCommand command,
        CancellationToken cancellationToken)
    {
        return await _dbContext.StockProductItems
                        .Include(x => x.StockProduct)
                            .ThenInclude(x => x.Product)
                            .ThenInclude(x => x.BatteryTypes)
                            .ThenInclude(x => x.BatteryType)
                        .Include(x => x.StockProduct)
                            .ThenInclude(x => x.Product)
                            .ThenInclude(x => x.Colors)
                            .ThenInclude(x => x.Color)
                        .Include(x => x.StockProduct)
                            .ThenInclude(x => x.Product)
                            .ThenInclude(x => x.Category)
                        .Include(x => x.StockProduct)
                            .ThenInclude(x => x.Product)
                            .ThenInclude(x => x.Attributes)
                            .ThenInclude(x => x.Attribute)
                            .ThenInclude(x => x!.ProductCategories)
                        .FirstOrDefaultAsync(x => x.Id == command.OldStockProductItemId, cancellationToken);
    }

    private async Task<StockProductItem> CreateStockProductItemAsync(
        ReplaceStockProductItemOnSaleCommand command,
        StockProductItem oldStockProductItem,
        CancellationToken cancellationToken)
    {
        await _serialNumbersValidator.CheckDuplicatedSerialNumbers(
            oldStockProductItem.StockProduct.Product.ManufacturerId!.Value,
            new[] { command.NewSerialNumber! },
            cancellationToken);

        var batteryType = await FetchBatteryTypeAsync(command.BatteryTypeId, cancellationToken);
        var color = await FetchColorAsync(command.ColorId, cancellationToken);

        var newStockProductItems = oldStockProductItem.StockProduct.AdjustSerializedItems(
            new List<string> { command.NewSerialNumber! }, color, batteryType, command.Attributes);

        oldStockProductItem.StockProduct.AdjustQuantity(-newStockProductItems.Count);

        return newStockProductItems[0];
    }

    private async Task LogStockProductItemsHistoryAsync(
        StockProductItem oldStockProductItem, StockProductItem newStockProductItem, string patientName)
    {
        await _azureServiceBusPublisher.PublishAddStockTransactionCommand(new AddStockTransactionCommand
        {
            Quantity = 1,
            Type = Contracts.StockTransactionType.DeliveryNote,
            ProductId = oldStockProductItem.StockProduct.ProductId,
            StockId = oldStockProductItem.StockProduct.StockId,
            ChangeRunningTotal = false
        });

        _dbContext.StockProductItemLogs.Add(
            StockProductItemLog.ReplacedBySaleLog(oldStockProductItem, newStockProductItem.SerialNumber!));
        _dbContext.StockProductItemLogs.Add(
            StockProductItemLog.ReplacementBySaleLog(newStockProductItem, oldStockProductItem.SerialNumber!));
        _dbContext.StockProductItemLogs.Add(
            StockProductItemLog.DeliveredLog(newStockProductItem, patientName));
    }

    private async Task<BatteryType?> FetchBatteryTypeAsync(Guid? batteryTypeId, CancellationToken cancellationToken)
    {
        if (batteryTypeId is null)
        {
            return null;
        }

        var batteryType = await _dbContext.BatteryTypes.FindAsync(
            new object?[] { batteryTypeId }, cancellationToken: cancellationToken);
        return batteryType ?? throw new EntityNotFoundException<BatteryType>(batteryTypeId.Value);
    }

    private async Task<Color?> FetchColorAsync(Guid? colorId, CancellationToken cancellationToken)
    {
        if (colorId is null)
        {
            return null;
        }

        var color = await _dbContext.Colors.FindAsync(
            new object?[] { colorId }, cancellationToken);
        return color ?? throw new EntityNotFoundException<Color>(colorId.Value);
    }
}
