using Auditdata.InventoryService.Contracts.Commands;
using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Abstractions.Azure;
using Auditdata.InventoryService.Core.Features.StockProductItems.Events;
using Auditdata.Microservice.Messages.OriginEntities.Invoicing;
using StockTransactionType = Auditdata.InventoryService.Contracts.StockTransactionType;

namespace Auditdata.InventoryService.Core.Features.StockProductItems.Handlers;

public class StockProductItemTrialCancelledEventHandler : INotificationHandler<StockProductItemTrialCancelledEvent>
{
    private readonly IAzureServiceBusPublisher _azureServiceBusPublisher;
    private readonly IDbContext _dbContext;

    public StockProductItemTrialCancelledEventHandler(
        IAzureServiceBusPublisher azureServiceBusPublisher,
        IDbContext dbContext)
    {
        _azureServiceBusPublisher = azureServiceBusPublisher;
        _dbContext = dbContext;
    }
    
    public async Task Handle(StockProductItemTrialCancelledEvent notification, CancellationToken cancellationToken)
    {
        _dbContext.StockProductItemLogs.Add(StockProductItemLog.TrialCancelledLog(
            notification.StockProductItem, notification.CancelTrialAction, notification.CancellationReasonName));
        
        await _azureServiceBusPublisher.PublishAddStockTransactionCommand(new AddStockTransactionCommand
        {
            Quantity = 1,
            Type = StockTransactionType.TrialCancellation,
            ProductId = notification.StockProductItem.StockProduct.ProductId,
            StockId = notification.StockProductItem.StockProduct.StockId,
            SaleId = notification.StockProductItem.SaleId,
            ChangeRunningTotal = notification.CancelTrialAction == ReturnProductItemAction.ToStock
        });
    }
}
