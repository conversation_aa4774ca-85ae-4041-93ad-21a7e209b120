using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Features.StockProductItems.Events;

namespace Auditdata.InventoryService.Core.Features.StockProductItems.Handlers;

public class StockProductItemUpdatedEventHandler : INotificationHandler<StockProductItemUpdatedEvent>
{
    private readonly IDbContext _dbContext;
    private readonly IEventPublisher _eventPublisher;

    public StockProductItemUpdatedEventHandler(
        IDbContext dbContext,
        IEventPublisher eventPublisher)
    {
        _dbContext = dbContext;
        _eventPublisher = eventPublisher;
    }
    
    public async Task Handle(StockProductItemUpdatedEvent notification, CancellationToken cancellationToken)
    {
        foreach (var (field, value) in notification.UpdatedProperties)
        {
            var newLog = StockProductItemLog.StockProductItemUpdatedLog(
                notification.StockProductItem, field, value);
            _dbContext.StockProductItemLogs.Add(newLog);
        }

        await _eventPublisher.StockProductItemUpdated(notification.StockProductItem, cancellationToken);
    }
}
