using Auditdata.InventoryService.Contracts.Commands;
using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Abstractions.Azure;
using Auditdata.InventoryService.Core.Features.StockProductItems.Commands;
using StockTransactionType = Auditdata.InventoryService.Contracts.StockTransactionType;

namespace Auditdata.InventoryService.Core.Features.StockProductItems.Handlers;

public class AssignSaleProductSerialNumberHandler : IRequestHandler<AssignSaleProductSerialNumberCommand>
{
    private readonly IDbContext _dbContext;
    private readonly IStockProductItemsRepository _stockProductItemsRepository;
    private readonly IAzureServiceBusPublisher _azureServiceBusPublisher;
    private readonly IEventPublisher _eventPublisher;

    public AssignSaleProductSerialNumberHandler(
        IDbContext dbContext,
        IStockProductItemsRepository stockProductItemsRepository,
        IAzureServiceBusPublisher azureServiceBusPublisher,
        IEventPublisher eventPublisher)
    {
        _dbContext = dbContext;
        _stockProductItemsRepository = stockProductItemsRepository;
        _azureServiceBusPublisher = azureServiceBusPublisher;
        _eventPublisher = eventPublisher;
    }
    
    public async Task Handle(AssignSaleProductSerialNumberCommand request, CancellationToken cancellationToken)
    {
        var (saleId, stockProductId, serialNumber) = request;

        if (await AssignSaleSerialNumberAsync(saleId, stockProductId, serialNumber))
        {
            return;
        }

        var availableProductItemAssignedToSale = await _stockProductItemsRepository.GetAvailableProductItemAssignedToSale(saleId, stockProductId, serialNumber);

        var stockProductItem = await SaleAssignmentAsync(availableProductItemAssignedToSale, saleId, serialNumber, stockProductId);
        if (stockProductItem is not null)
        {
            await _eventPublisher.StockProductItemReserved(stockProductItem, cancellationToken);
            await CreateTransactionsAsync(stockProductItem.StockProduct, saleId, StockTransactionType.Sales);
        }

        await _dbContext.SaveChangesAsync(cancellationToken);
    }

    private async Task<StockProductItem?> SaleAssignmentAsync(
        StockProductItem? stockProductItem, Guid saleId, string serialNumber, Guid stockProductId)
    {
        if (stockProductItem is not null)
        {
            AssignSale(stockProductItem, saleId, serialNumber);
            return stockProductItem;
        }

        stockProductItem = await _stockProductItemsRepository
            .GetAvailableBySerialNumberAsync(stockProductId, serialNumber);
        if (stockProductItem is not null)
        {
            AssignSale(stockProductItem, saleId, serialNumber);
        }

        return stockProductItem;
    }

    private async Task<bool> AssignSaleSerialNumberAsync(Guid saleId, Guid stockProductId, string serialNumber)
    {
        var itemLinkedToSale = await _stockProductItemsRepository.GetItemLinkedToSale(saleId, stockProductId);
        if (itemLinkedToSale is null)
        {
            return false;
        }

        var itemWithSerialNumber = await _stockProductItemsRepository.GetAvailableBySerialNumberAsync(stockProductId, serialNumber);
        if (itemWithSerialNumber is not null)
        {
            itemWithSerialNumber.SerialNumber = null;
            await _stockProductItemsRepository.UpdateAsync(itemWithSerialNumber);
        }

        itemLinkedToSale.SerialNumber = serialNumber;

        await _stockProductItemsRepository.UpdateAsync(itemLinkedToSale);

        return true;
    }

    private void AssignSale(StockProductItem item, Guid saleId, string serialNumber)
    {
        item.SerialNumber = serialNumber;
        item.StockProduct.ReserveItem(item, saleId);
    }

    private async Task CreateTransactionsAsync(StockProduct stockProduct, Guid? saleId, StockTransactionType type)
    {
        await _azureServiceBusPublisher.PublishAddStockTransactionCommand(new AddStockTransactionCommand
        {
            Quantity = 1,
            Type = type,
            ProductId = stockProduct.ProductId,
            StockId = stockProduct.StockId,
            SaleId = saleId
        });
    }
}
