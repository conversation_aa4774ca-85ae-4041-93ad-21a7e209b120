using Auditdata.InventoryService.Core.Features.StockProductItems.Models;
using Auditdata.InventoryService.Core.Features.StockProductItems.Queries;

namespace Auditdata.InventoryService.Core.Features.StockProductItems.Handlers;

public class GetProductItemsStatusesHandler : IRequestHandler<GetProductItemsStatusesQuery, GetProductItemsStatusesResult>
{
    private readonly IStockProductItemsRepository _stockProductItemsRepository;

    public GetProductItemsStatusesHandler(
        IStockProductItemsRepository stockProductItemsRepository)
    {
        _stockProductItemsRepository = stockProductItemsRepository;
    }

    public async Task<GetProductItemsStatusesResult> Handle(GetProductItemsStatusesQuery request, CancellationToken cancellationToken)
    {
        var stockProductItems = await _stockProductItemsRepository.GetByIdsAsync(request.StockProductItemIds);
        return new GetProductItemsStatusesResult(stockProductItems);
    }
}
