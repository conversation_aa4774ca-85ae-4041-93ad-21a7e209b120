using Auditdata.InventoryService.Contracts.Responses.ProductItems;
using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Exceptions;
using Auditdata.InventoryService.Core.Features.StockProductItems.Queries;
using Microsoft.EntityFrameworkCore;

namespace Auditdata.InventoryService.Core.Features.StockProductItems.Handlers;

public class GetStockProductItemQueryHandler : IRequestHandler<GetStockProductItemQuery, GetStockProductItemResponse>
{
    private readonly IDbContext _dbContext;
    private readonly IMapper _mapper;

    public GetStockProductItemQueryHandler(
        IDbContext dbContext, 
        IMapper mapper)
    {
        _dbContext = dbContext;
        _mapper = mapper;
    }

    public async Task<GetStockProductItemResponse> Handle(GetStockProductItemQuery request, CancellationToken cancellationToken)
    {
        var stockProductItem = await _dbContext.StockProductItems
            .Include(x => x.StockProduct)
                .ThenInclude(x => x.Product)
                   .ThenInclude(x => x.Manufacturer)
            .Include(x => x.StockProduct)
                .ThenInclude(x => x.Product)
                      .ThenInclude(x => x.Category)
            .Include(x => x.StockProduct)
                .ThenInclude(x => x.Product)
                     .ThenInclude(x => x.Supplier)
            .Include(x => x.BatteryType)
            .Include(x => x.Color)
            .Include(x => x.Attributes)
                .ThenInclude(x => x.Attribute)
            .FirstOrDefaultAsync(x => x.Id == request.Id, cancellationToken);

        if (stockProductItem is null)
            throw new EntityNotFoundException<StockProductItem>(request.Id);
        
        var result = _mapper.Map<GetStockProductItemResponse>(stockProductItem);
        return result;
    }
}
