using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Exceptions;
using Auditdata.InventoryService.Core.Features.StockProductItems.Commands;
using Auditdata.InventoryService.Core.Features.StockProductItems.Events;
using Microsoft.EntityFrameworkCore;

namespace Auditdata.InventoryService.Core.Features.StockProductItems.Handlers;

public class SwapReservationCommandHandler : IRequestHandler<SwapReservationCommand>
{
    private readonly IDbContext _dbContext;
    private readonly IMediator _mediator;
    public SwapReservationCommandHandler(
        IDbContext dbContext,
        IMediator mediator)
    {
        _dbContext = dbContext;
        _mediator = mediator;
    }

    public async Task Handle(SwapReservationCommand request, CancellationToken cancellationToken)
    {
        var stockProduct = await _dbContext.StockProducts
            .Include(x => x.Product)
            .Include(x => x.Stock)
            .Include(x => x.StockProductItems.Where(stockProductItem => stockProductItem.Status == StockProductItemStatus.Available ||
                                                                        stockProductItem.Status == StockProductItemStatus.Reserved))
            .Where(x => x.Id == request.StockProductId)
            .FirstOrDefaultAsync(cancellationToken);

        if (stockProduct is null)
            throw new EntityNotFoundException<StockProduct>(request.StockProductId);

        var (reserved, unReserved) = stockProduct.SwapReservation(request.OldSerialNumber, request.NewSerialNumber);

        await _mediator.Publish(new ReservationSwappedEvent(reserved, unReserved, request.PatientName), cancellationToken);
        await _dbContext.SaveChangesAsync(cancellationToken);
    }
}
