using Auditdata.InventoryService.Contracts.Responses.ProductItems;
using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Features.StockProductItems.Queries;
using Auditdata.Transport.Contracts.Exceptions;
using Microsoft.EntityFrameworkCore;

namespace Auditdata.InventoryService.Core.Features.StockProductItems.Handlers;

public class GetStockProductItemByProductSerialNumberHandler 
    : IRequestHandler<GetStockProductItemByProductSerialNumberQuery, GetStockProductItemResponse>
{
    private readonly IDbContext _dbContext;
    private readonly IMapper _mapper;

    public GetStockProductItemByProductSerialNumberHandler(
        IDbContext dbContext,
        IMapper mapper)
    {
        _dbContext = dbContext;
        _mapper = mapper;
    }

    public async Task<GetStockProductItemResponse> Handle(GetStockProductItemByProductSerialNumberQuery request, CancellationToken cancellationToken)
    {
        var stockProductItem = await _dbContext.StockProductItems
            .Include(x => x.StockProduct)
                .ThenInclude(x => x.Product)
                   .ThenInclude(x => x.Manufacturer)
            .Include(x => x.StockProduct)
                .ThenInclude(x => x.Product)
                      .ThenInclude(x => x.Category)
            .Include(x => x.StockProduct)
                .ThenInclude(x => x.Product)
                     .ThenInclude(x => x.Supplier)
            .Include(x => x.StockProduct)
                .ThenInclude(x => x.Stock)
            .Include(x => x.BatteryType)
            .Include(x => x.Color)
            .Include(x => x.Attributes)
                .ThenInclude(x => x.Attribute)
            .FirstOrDefaultAsync(x => x.SerialNumber == request.SerialNumber
                && x.StockProduct.ProductId == request.ProductId, cancellationToken);

        if (stockProductItem is null)
            throw new BusinessException("StockProductItem cannot be found", "stockproductitem.notfound");

        var result = _mapper.Map<GetStockProductItemResponse>(stockProductItem);
        return result;
    }
}
