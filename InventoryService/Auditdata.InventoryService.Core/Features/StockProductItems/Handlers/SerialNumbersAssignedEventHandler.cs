using Auditdata.InventoryService.Contracts.Commands;
using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Abstractions.Azure;
using Auditdata.InventoryService.Core.Features.StockProductItems.Events;
using StockTransactionType = Auditdata.InventoryService.Contracts.StockTransactionType;

namespace Auditdata.InventoryService.Core.Features.StockProductItems.Handlers;

public class SerialNumbersAssignedEventHandler : INotificationHandler<SerialNumbersAssignedEvent>
{
    private readonly IDbContext _dbContext;
    private readonly IAzureServiceBusPublisher _azureServiceBusPublisher;
    private readonly IEventPublisher _eventPublisher;

    public SerialNumbersAssignedEventHandler(
        IDbContext dbContext,
        IAzureServiceBusPublisher azureServiceBusPublisher,
        IEventPublisher eventPublisher)
    {
        _dbContext = dbContext;
        _azureServiceBusPublisher = azureServiceBusPublisher;
        _eventPublisher = eventPublisher;
    }

    public async Task Handle(SerialNumbersAssignedEvent notification, CancellationToken cancellationToken)
    {
        foreach (var stockProductItem in notification.StockProductItems)
        {
            var stockAdjustedActionLog = StockProductItemLog.StockAdjustedActionLog(stockProductItem);
            var serialNumberAddedActionLog = StockProductItemLog.SerialNumberAddedActionLog(stockProductItem);
            _dbContext.StockProductItemLogs.Add(stockAdjustedActionLog);
            _dbContext.StockProductItemLogs.Add(serialNumberAddedActionLog);

            await _eventPublisher.StockProductItemCreated(stockProductItem, cancellationToken);
        }

        await _azureServiceBusPublisher.PublishAddStockTransactionCommand(new AddStockTransactionCommand
        {
            StockId = notification.StockProduct.StockId,
            ProductId = notification.StockProduct.ProductId,
            Type = StockTransactionType.StockAdjustment,
            Quantity = notification.StockProductItems.Count
        });
    }
}
