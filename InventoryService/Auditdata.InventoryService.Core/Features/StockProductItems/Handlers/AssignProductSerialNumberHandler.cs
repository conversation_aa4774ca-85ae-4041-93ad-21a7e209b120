using Auditdata.InventoryService.Contracts.Responses.Products;
using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Abstractions.Validators;
using Auditdata.InventoryService.Core.Entities.Products;
using Auditdata.InventoryService.Core.Exceptions;
using Auditdata.InventoryService.Core.Features.StockProductItems.Commands;
using Microsoft.EntityFrameworkCore;

namespace Auditdata.InventoryService.Core.Features.StockProductItems.Handlers;

public class AssignProductSerialNumberHandler : IRequestHandler<AssignProductSerialNumberCommand, List<AssignProductSerialNumberResponse>>
{
    private readonly ISerialNumbersValidator _serialNumbersValidator;
    private readonly IEventPublisher _eventPublisher;
    private readonly IDbContext _dbContext;

    public AssignProductSerialNumberHandler(
        IDbContext dbContext,
        ISerialNumbersValidator serialNumbersValidator,
        IEventPublisher eventPublisher)
    {
        _dbContext = dbContext;
        _serialNumbersValidator = serialNumbersValidator;
        _eventPublisher = eventPublisher;
    }

    public async Task<List<AssignProductSerialNumberResponse>> Handle(AssignProductSerialNumberCommand request,
        CancellationToken cancellationToken)
    {
        var product = await _dbContext.Products.Include(x => x.Category)
                          .Include(x => x.Colors)
                          .Include(x => x.BatteryTypes)
                          .Include(x => x.Attributes)
                          .ThenInclude(x => x.Attribute)
                          .ThenInclude(x => x!.ProductCategories)
                          .FirstOrDefaultAsync(x => x.Id == request.ProductId, cancellationToken) ??
                      throw new EntityNotFoundException<Product>(request.ProductId);

        await _serialNumbersValidator.CheckDuplicatedSerialNumbers(product.ManufacturerId!.Value, request.SerialNumbers, cancellationToken);

        var stockProduct = await _dbContext.StockProducts
                               .Include(x => x.Product)
                               .Include(x => x.StockProductItems.Where(y => y.SerialNumber == null
                                                                            && y.Status == StockProductItemStatus.Available
                                                                            && !y.IsDeleted))
                               .ThenInclude(x => x.Attributes)
                               .Where(x => x.ProductId == request.ProductId && x.Stock.LocationId == request.LocationId)
                               .FirstOrDefaultAsync(cancellationToken) ??
                           throw new EntityNotFoundException<Product>(request.ProductId);


        Color? color = null;
        BatteryType? batteryType = null;

        if (request.ColorId.HasValue)
        {
            color = await _dbContext.Colors.FirstOrDefaultAsync(x => x.Id == request.ColorId.Value, cancellationToken);
            if (color is null)
                throw new EntityNotFoundException<Color>(request.ColorId.Value);
        }

        if (request.BatteryTypeId.HasValue)
        {
            batteryType = await _dbContext.BatteryTypes.FirstOrDefaultAsync(x => x.Id == request.BatteryTypeId.Value, cancellationToken);
            if (batteryType is null)
                throw new EntityNotFoundException<BatteryType>(request.BatteryTypeId.Value);
        }

        var stockProductItems = stockProduct.AssignSerialNumbers(request.SerialNumbers.ToList(),
            color,
            batteryType,
            request.Attributes.ToList());

        foreach (var stockProductItem in stockProductItems)
        {
            _dbContext.StockProductItemLogs.Add(StockProductItemLog.SerialNumberAddedActionLog(stockProductItem));
            await _eventPublisher.StockProductItemUpdated(stockProductItem, cancellationToken);
        }

        await _dbContext.SaveChangesAsync(cancellationToken);

        return stockProductItems.Select(x => 
            new AssignProductSerialNumberResponse(x.Id, x.SerialNumber!)).ToList();
    }
}
