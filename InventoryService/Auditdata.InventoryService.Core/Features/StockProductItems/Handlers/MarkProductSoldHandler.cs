using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Exceptions;
using Auditdata.InventoryService.Core.Features.StockProductItems.Commands;
using Auditdata.InventoryService.Core.Features.StockProductItems.Events;
using Auditdata.InventoryService.Core.Features.StockProducts.Models;
using Microsoft.EntityFrameworkCore;

namespace Auditdata.InventoryService.Core.Features.StockProductItems.Handlers;

public class MarkProductSoldHandler : IRequestHandler<MarkProductSoldCommand>
{
    private readonly IDbContext _dbContext;
    private readonly IMediator _mediator;

    public MarkProductSoldHandler(
        IDbContext dbContext,
        IMediator mediator)
    {
        _dbContext = dbContext;
        _mediator = mediator;
    }

    public async Task Handle(MarkProductSoldCommand request, CancellationToken cancellationToken)
    {
        var stockProductItemIds = request.Items
            .Where(x => x.StockProductItemId is not null)
            .Select(x => x.StockProductItemId).ToList();
        var stockProductIds = request.Items.Select(x => x.StockProductId).ToList();

        var stockProducts = await _dbContext.StockProducts
            .Include(x => x.Product)
            .Include(x => x.Stock)
            .Include(x => x.StockProductItems.Where(y => stockProductItemIds.Contains(y.Id)))
            .Where(x => stockProductIds.Contains(x.Id))
            .ToListAsync(cancellationToken);

        var soldItems = new List<StockProductSoldItem>();
        foreach (var soldItem in request.Items)
        {
            var stockProduct = stockProducts.Find(x => x.Id == soldItem.StockProductId);
            if (stockProduct is null)
            {
                throw new EntityNotFoundException<StockProduct>(soldItem.StockProductId);
            }

            var item = stockProduct.SellItem(soldItem.Quantity, request.SaleId, soldItem.StockProductItemId);
            soldItems.Add(new StockProductSoldItem(stockProduct, soldItem.Quantity)
            {
                Item = item
            });
        }

        await _mediator.Publish(
            new StockProductItemsSoldEvent(soldItems, request.SaleId, request.PatientName),
            cancellationToken);
        await _dbContext.SaveChangesAsync(cancellationToken);
    }
}
