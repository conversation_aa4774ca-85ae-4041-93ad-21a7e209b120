using Auditdata.InventoryService.Contracts.Commands;
using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Abstractions.Azure;
using Auditdata.InventoryService.Core.Features.StockProductItems.Events;
using StockTransactionType = Auditdata.InventoryService.Contracts.StockTransactionType;

namespace Auditdata.InventoryService.Core.Features.StockProductItems.Handlers;

public class StockProductItemsSoldEventHandler : INotificationHandler<StockProductItemsSoldEvent>
{
    private readonly IAzureServiceBusPublisher _azureServiceBusPublisher;
    private readonly IDbContext _dbContext;
    private readonly IEventPublisher _eventPublisher;

    public StockProductItemsSoldEventHandler(
        IAzureServiceBusPublisher azureServiceBusPublisher,
        IDbContext dbContext,
        IEventPublisher eventPublisher)
    {
        _azureServiceBusPublisher = azureServiceBusPublisher;
        _dbContext = dbContext;
        _eventPublisher = eventPublisher;
    }
    
    public async Task Handle(StockProductItemsSoldEvent notification, CancellationToken cancellationToken)
    {
        foreach (var serializedItem in notification.Items.Where(x => x.Item is not null))
        {
            _dbContext.StockProductItemLogs.Add(StockProductItemLog.DeliveredLog(serializedItem.Item!, notification.Patient));

            await _eventPublisher.StockProductItemSold(serializedItem.Item!, cancellationToken);
        }

        foreach (var stockProductGroup in notification.Items.GroupBy(x => x.StockProduct))
        {
            var quantity = stockProductGroup.Sum(x => x.Quantity);
            var stockProduct = stockProductGroup.First().StockProduct;

            await _azureServiceBusPublisher.PublishAddStockTransactionCommand(new AddStockTransactionCommand
            {
                Quantity = quantity,
                Type = StockTransactionType.DeliveryNote,
                ProductId = stockProduct.ProductId,
                StockId = stockProduct.StockId,
                SaleId = notification.SaleId
            });
        }
    }
}
