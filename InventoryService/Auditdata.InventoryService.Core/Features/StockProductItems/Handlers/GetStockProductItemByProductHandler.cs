using Auditdata.InventoryService.Core.Features.StockProductItems.Models;
using Auditdata.InventoryService.Core.Features.StockProductItems.Queries;

namespace Auditdata.InventoryService.Core.Features.StockProductItems.Handlers;

public class GetStockProductItemByProductHandler : IRequestHandler<GetStockProductItemByProductQuery, GetStockProductItemByProductResult>
{
    private readonly IStockProductItemsRepository _stockProductItemsRepository;

    public GetStockProductItemByProductHandler(
        IStockProductItemsRepository stockProductItemsRepository)
    {
        _stockProductItemsRepository = stockProductItemsRepository;
    }

    public async Task<GetStockProductItemByProductResult> Handle(GetStockProductItemByProductQuery request, CancellationToken cancellationToken)
    {
        var (productId, searchText, stockId, status) = request;

        var stockProductItems = await _stockProductItemsRepository.GetByProductIdWithStockAsync(
            productId,
            searchText,
            stockId,
            status);

        var stockProductItemsToBeTransferred = await _stockProductItemsRepository.GetStockProductItemsPendingTransferToStockAsync(
            productId,
            stockId,
            searchText,
            status);

        stockProductItemsToBeTransferred = stockProductItemsToBeTransferred.Select(x => { x!.PendingTransferAcceptance = true; return x; });
        return new GetStockProductItemByProductResult(stockProductItems.Concat(stockProductItemsToBeTransferred));
    }
}
