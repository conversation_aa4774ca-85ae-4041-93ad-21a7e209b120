using Auditdata.InventoryService.Core.Abstractions;
using Auditdata.InventoryService.Core.Entities.Products;
using Auditdata.InventoryService.Core.Features.NhsContracts.Models;
using Auditdata.InventoryService.Core.Features.NhsContracts.Queries;
using Microsoft.EntityFrameworkCore;

namespace Auditdata.InventoryService.Core.Features.NhsContracts.Handlers;

public class GetNhsContractProductsByContractIdHandler : IRequestHandler<GetNhsContractProductsByContractIdQuery, NhsContractProductsResult>
{
    private readonly IDbContext _dbContext;

    public GetNhsContractProductsByContractIdHandler(
        IDbContext dbContext)
    {
        _dbContext = dbContext;
    }
    
    public async Task<NhsContractProductsResult> Handle(GetNhsContractProductsByContractIdQuery request, CancellationToken cancellationToken)
    {
        var query  = _dbContext.NhsContractProducts
            .Where(x => x.ContractId == request.ContractId && ((UKProduct?)x.Product)!.IsNHS);
        if (!string.IsNullOrEmpty(request.SearchText))
        {
            query = query.Where(x => x.Product.Name.Contains(request.SearchText));
        }

        var contractProducts = await query.ToListAsync(cancellationToken);

        return new NhsContractProductsResult(contractProducts.Select(x => x.ProductId));
    }
}
