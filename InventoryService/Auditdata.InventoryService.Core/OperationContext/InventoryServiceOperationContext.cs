using Auditdata.Infrastructure.Core.OperationContext;

namespace Auditdata.InventoryService.Core.OperationContext;

public class InventoryServiceOperationContext : DefaultOperationContext, IInventoryServiceOperationContext
{
    public bool UserCanViewProductCost { get; set; }
    public bool UserCanEditProductCost { get; set; }
    public bool UserAuditRequestSpecialist { get; set; }
    public bool UserAuditRequestManager { get; set; }
}
