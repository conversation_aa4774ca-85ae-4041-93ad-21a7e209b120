using Auditdata.Infrastructure.DataPopulation.Interfaces;
using Auditdata.InventoryService.Core.Abstractions;
using Microsoft.EntityFrameworkCore;

namespace Auditdata.InventoryService.Core.Services;

public class CountryPropertySetter : ICustomPropertySetter
{
    private readonly IDbContext _dbContext;

    public CountryPropertySetter(IDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    public async Task SetPropertyValueAsync(object obj, string value)
    {
        var country = _dbContext.ChangeTracker.Entries()
            .Where(x => x.State == EntityState.Added && x.Entity is Country)
            .Select(x => x.Entity as Country)
            .FirstOrDefault(x => string.Equals(x!.Name, value, StringComparison.OrdinalIgnoreCase))
            ?? await _dbContext.Countries.FirstOrDefaultAsync(x => x.Name == value);

        if (obj is Manufacturer manufacturer)
        {
            manufacturer.Country = country;
            return;
        }

        if (obj is Supplier supplier)
        {
            supplier.Country = country;
        }
    }
}
