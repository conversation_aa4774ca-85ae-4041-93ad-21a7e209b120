using Auditdata.Infrastructure.DataPopulation.Interfaces;
using Auditdata.InventoryService.Core.Abstractions;

namespace Auditdata.InventoryService.Core.Services;
public class ManufacturersAfterPopulationHandler : IAfterPopulationHandler
{
    private readonly IEventPublisher _eventPublisher;

    public ManufacturersAfterPopulationHandler(IEventPublisher eventPublisher)
    {
        _eventPublisher = eventPublisher;
    }

    public async Task HandleAsync(IEnumerable<object> models)
    {
        foreach (var manufacturer in models.Cast<Manufacturer>())
        {
            manufacturer.Id = Guid.NewGuid();
            await _eventPublisher.ManufacturerCreated(manufacturer);
        }
    }
}
