using Auditdata.Infrastructure.DataPopulation.Interfaces;
using Auditdata.InventoryService.Core.Abstractions;

namespace Auditdata.InventoryService.Core.Services;
public class SuppliersAfterPopulationHandler : IAfterPopulationHandler
{
    private readonly IDbContext _dbContext;
    private readonly IMapper _mapper;
    private readonly IEventPublisher _eventPublisher;

    public SuppliersAfterPopulationHandler(IDbContext dbContext, IMapper mapper, IEventPublisher eventPublisher)
    {
        _dbContext = dbContext;
        _mapper = mapper;
        _eventPublisher = eventPublisher;
    }

    public async Task HandleAsync(IEnumerable<object> models)
    {
        foreach (var supplier in models.Cast<Supplier>())
        {
            if (supplier.IsManufacturer)
            {
                var manufacturer = _mapper.Map<Manufacturer>(supplier);
                manufacturer.Id = Guid.NewGuid();

                _dbContext.Manufacturers.Add(manufacturer);
                await _eventPublisher.ManufacturerCreated(manufacturer);
            }

            supplier.Id = Guid.NewGuid();
            await _eventPublisher.SupplierCreated(supplier);
        }
    }
}
