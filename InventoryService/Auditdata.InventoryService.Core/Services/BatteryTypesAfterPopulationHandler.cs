using Auditdata.Infrastructure.DataPopulation.Interfaces;
using Auditdata.InventoryService.Core.Abstractions.Azure;
using Auditdata.Microservice.Messages.Events.Inventory;

namespace Auditdata.InventoryService.Core.Services;
public class BatteryTypesAfterPopulationHandler : IAfterPopulationHandler
{
    private readonly IAzureServiceBusPublisher _publisher;
    private readonly IMapper _mapper;

    public BatteryTypesAfterPopulationHandler(IAzureServiceBusPublisher publisher, IMapper mapper)
    {
        _publisher = publisher;
        _mapper = mapper;
    }

    public async Task HandleAsync(IEnumerable<object> models)
    {
        foreach (var batteryType in models.Cast<BatteryType>())
        {
            batteryType.Id = Guid.NewGuid();
            var batteryTypeCreatedEvent = _mapper.Map<BatteryTypeCreated>(batteryType);
            await _publisher.PublishBatteryTypeCreated(batteryTypeCreatedEvent);
        }
    }
}
