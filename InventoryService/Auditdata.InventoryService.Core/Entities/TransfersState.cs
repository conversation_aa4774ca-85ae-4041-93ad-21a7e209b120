namespace Auditdata.InventoryService.Core.Entities;

public class TransfersState : SagaStateMachineInstance
{
    public Guid CorrelationId { get; set; }

    public Guid TransferId { get; set; }
    public string? CurrentState { get; set; }
    public Guid FromStockId { get; set; }
    public Guid ToStockId { get; set; }

    public Guid StockProductItemId { get; set; }
    public StockProductItem? StockProductItem { get; set; }

    public DateTime? RequestedAt { get; set; }
    public DateTime? AcceptedAt { get; set; }
    public DateTime? CancelledAt { get; set; }

    public byte[]? RowVersion { get; set; }
}
