using Auditdata.Infrastructure.Contracts.DataModel;
using Auditdata.InventoryService.Core.Abstractions.Entities;

namespace Auditdata.InventoryService.Core.Entities;

public record BatteryType : BaseLookup, ISoftDeleteDataModel, ITenancyDataModel
{
    public bool IsActive { get; set; }
    public bool IsDeleted { get; set; }
    public Guid TenantId { get; set; }

    public static BatteryType Create(string name)
    {
        return new BatteryType
        {
            Id = Guid.NewGuid(),
            Name = name,
            IsActive = true
        };
    }
}
