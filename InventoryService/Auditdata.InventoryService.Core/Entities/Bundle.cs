using Auditdata.Infrastructure.Contracts.DataModel;
using Auditdata.InventoryService.Core.Abstractions.Entities;

namespace Auditdata.InventoryService.Core.Entities;

public record Bundle : BaseEntity, ITenancyDataModel, ISoftDeleteDataModel
{
    public string Name { get; set; } = null!;
    public string? Description { get; set; }
    public decimal TotalCost { get; set; }
    public bool IsActive { get; set; }
    public bool IsAutomaticDelivery { get; set; }
    public bool IsSellable { get; set; }
    public bool IsPriceChangeAllowed { get; set; }
    
    public Guid TenantId { get; set; }
    public bool IsDeleted { get; set; }
    public ICollection<BundleProduct> Products { get; set; } = null!;

    public static Bundle CreateWithProducts(
        string name,
        string? description,
        decimal totalCost,
        bool isActive,
        bool isAutomaticDelivery,
        bool isSellable,
        bool isPriceChangeAllowed,
        IEnumerable<BundleProduct> bundleProducts)
    {
        return new Bundle
        {
            Id = Guid.NewGuid(),
            Name = name,
            Description = description,
            IsActive = isActive,
            IsSellable = isSellable,
            TotalCost = totalCost,
            IsAutomaticDelivery = isAutomaticDelivery,
            IsPriceChangeAllowed = isPriceChangeAllowed,
            Products = bundleProducts.ToList()
        };
    }
}
