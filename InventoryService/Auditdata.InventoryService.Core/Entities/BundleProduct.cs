using Auditdata.Infrastructure.Contracts.DataModel;
using Auditdata.InventoryService.Core.Abstractions.Entities;
using Auditdata.InventoryService.Core.Entities.Products;

namespace Auditdata.InventoryService.Core.Entities;

public record BundleProduct : BaseEntity, ITenancyDataModel, ISoftDeleteDataModel
{
    public Guid BundleId { get; set; }
    public Bundle? Bundle { get; set; }
    
    public Guid ProductId { get; set; }
    public Product? Product { get; set; }
    
    public int Quantity { get; set; }
    public decimal Price { get; set; }
    
    public Guid TenantId { get; set; }
    public bool IsDeleted { get; set; }
    public static BundleProduct Create(Guid productId, int quantity, decimal price)
    {
        return new BundleProduct
        {
            Id = Guid.NewGuid(),
            Quantity = quantity,
            Price = price,
            ProductId = productId
        };
    }
}
