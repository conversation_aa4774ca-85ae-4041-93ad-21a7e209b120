using System.ComponentModel.DataAnnotations.Schema;
using Auditdata.Infrastructure.Contracts.CountryLayers;
using Auditdata.Infrastructure.Contracts.DataModel;
using Auditdata.InventoryService.Core.Abstractions.Entities;

namespace Auditdata.InventoryService.Core.Entities.Products;

public record Product : BaseEntity, ISoftDeleteDataModel, ITenancyDataModel, IDescriptionDataModel
{
    public string Name { get; set; } = null!;
    public string? Description { get; set; }
    public string? DescriptionValue { get; set; }
    public string? Code { get; set; }

    public ProductCategory Category { get; set; } = null!;
    public Guid CategoryId { get; set; }

    public Guid? HearingAidTypeId { get; set; }
    public HearingAidType? HearingAidType { get; set; }

    public Manufacturer? Manufacturer { get; set; }
    public Guid? ManufacturerId { get; set; }

    public Supplier? Supplier { get; set; }
    public Guid? SupplierId { get; set; }

    public ICollection<ProductPathway> ProductPathways { get; set; } = null!;
    public ICollection<NhsContractProduct> NhsContractProducts { get; set; } = null!;
    public ICollection<StockProduct> StockProducts { get; set; } = null!;
    public ICollection<ProductColor> Colors { get; set; } = null!;
    public ICollection<ProductBatteryType> BatteryTypes { get; set; } = new List<ProductBatteryType>();
    public ICollection<ProductMeasureType> MeasureTypes { get; set; } = new List<ProductMeasureType>();
    public ICollection<ProductAttribute> Attributes { get; set; } = null!;
    public ICollection<BundleProduct> Bundles { get; set; } = null!;
    public ICollection<ProductSuggestedProduct> SuggestedProducts { get; set; } = null!;
    public ICollection<Sku> Skus { get; set; } = null!;
    public ICollection<SkuConfig> SkuConfigs { get; set; } = null!;

    public int? Warranty { get; set; }
    public int? LDWarranty { set; get; }
    public int Quantity { get; set; }
    public decimal RetailPrice { get; set; }

    public bool IsSellable { get; set; }
    public bool IsActive { get; set; }
    public bool IsSerialized { get; set; }
    public bool IsFastTrack { get; set; }
    public bool PriceChangesAllowed { get; set; }
    public bool AutoDeliver { get; set; }
    public bool ControlledByStock { get; set; }
    public bool IsDeleted { get; set; }
    public Guid TenantId { get; set; }
    public string? VendorProductNumber { get; set; }
    public decimal? MaximumDiscount { get; set; }
    public decimal? Cost { get; set; }

    [NotMapped]
    public int InStock { get; set; }

    [NotMapped]
    public bool IsInBundle { get; set; }
    
    public void MarkSoftDeleted() => IsDeleted = true;

    public static Product Create(CountryEnum country)
    {
        return country switch
        {
            CountryEnum.Australia => new AUProduct(),
            CountryEnum.UnitedKingdom => new UKProduct(),
            CountryEnum.NewZealand => new NZProduct(),
            CountryEnum.Ireland => new ROIProduct(),
            CountryEnum.UnitedStates => new USProduct(),
            CountryEnum.Undefined => throw new ArgumentOutOfRangeException(),
            _ => throw new ArgumentOutOfRangeException()
        };
    }

    public void SetBatteryTypes(IEnumerable<Guid> batteryTypeIds)
    {
        BatteryTypes.Clear();
        BatteryTypes.AddRange(batteryTypeIds.Select(ProductBatteryType.Create));
    }

    public void SetBatteryTypes(IEnumerable<BatteryType> batteryTypes)
    {
        BatteryTypes.Clear();
        BatteryTypes.AddRange(batteryTypes.Select(ProductBatteryType.Create));
    }

    public void SetMeasureTypes(IEnumerable<Guid> measureTypeIds)
    {
        MeasureTypes.Clear();
        MeasureTypes.AddRange(measureTypeIds.Select(ProductMeasureType.Create));
    }

    public void SetMeasureTypes(IEnumerable<MeasureType> measureTypes)
    {
        MeasureTypes.Clear();
        MeasureTypes.AddRange(measureTypes.Select(ProductMeasureType.Create));
    }
}
