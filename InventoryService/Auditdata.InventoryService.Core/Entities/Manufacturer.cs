using Auditdata.Infrastructure.Contracts.DataModel;
using Auditdata.InventoryService.Core.Abstractions.Entities;

namespace Auditdata.InventoryService.Core.Entities;

public record Manufacturer : BaseEntity, ISoftDeleteDataModel, ITenancyDataModel
{
    private const string RequiredFieldsUnknown = "Unknown";

    public string Name { get; set; } = null!;

    public bool IsActive { get; set; }

    public bool IsDeleted { get; set; }

    public Guid TenantId { get; set; }

    public string? Website { get; set; }

    public string? EmailAddress { get; set; }

    public string PhoneNumber { get; set; } = null!;

    public string? FaxNumber { get; set; }

    public string Address1 { get; set; } = null!;

    public string? Address2 { get; set; }

    public Guid? CountryId { get; set; }

    public Country? Country { get; set; }

    public string City { get; set; } = null!;

    public Guid? StateId { get; set; }
    public string? State { get; set; }

    public string? PostalCode { get; set; }

    public ManufacturerContact? SalesContact { get; set; }

    public ManufacturerContact? AccountReceivableContact { get; set; }

    public static Manufacturer Create(string name)
    {
        return new Manufacturer
        {
            Name = name,
            IsActive = true,
            AccountReceivableContact = new ManufacturerContact(),
            SalesContact = new ManufacturerContact(),
            City = RequiredFieldsUnknown,
            PhoneNumber = RequiredFieldsUnknown,
            State = RequiredFieldsUnknown,
            Address1 = RequiredFieldsUnknown
        };
    }
}
