using Auditdata.Infrastructure.Contracts.DataModel;
using Auditdata.InventoryService.Core.Abstractions.Entities;

namespace Auditdata.InventoryService.Core.Entities.AuditRequests;

public record AuditLocation : BaseEntity, ITenancyDataModel, IVersionedDataModel
{
    public Guid AuditRequestId { get; set; }
    public AuditRequest AuditRequest { get; set; } = null!;
    public Guid LocationId { get; set; }
    public string? LocationName { get; set; }
    public Stock? Stock { get; set; }
    public Guid TenantId { get; set; }
    public byte[] Version { get; set; } = null!;

    public static AuditLocation Create(Guid locationId, string? locationName, AuditRequest auditRequest)
    {
        return new AuditLocation
        {
            Id = Guid.NewGuid(),
            LocationId = locationId,
            LocationName = locationName,
            AuditRequestId = auditRequest.Id,
            AuditRequest = auditRequest,
        };
    }
}

