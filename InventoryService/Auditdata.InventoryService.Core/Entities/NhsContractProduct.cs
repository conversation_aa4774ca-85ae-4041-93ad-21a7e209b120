using Auditdata.Infrastructure.Contracts.DataModel;
using Auditdata.InventoryService.Core.Abstractions.Entities;
using Auditdata.InventoryService.Core.Entities.Products;

namespace Auditdata.InventoryService.Core.Entities;

public record NhsContractProduct : BaseEntity, ITenancyDataModel
{
    public Guid ContractId { get; set; }
    
    public Guid ProductId { get; set; }
    public Product Product { get; set; } = null!;

    public Guid TenantId { get; set; }
}
