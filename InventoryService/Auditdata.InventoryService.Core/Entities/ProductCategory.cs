using Auditdata.Infrastructure.Contracts.DataModel;
using Auditdata.InventoryService.Core.Abstractions.Entities;

namespace Auditdata.InventoryService.Core.Entities;

public record ProductCategory : BaseLookup, ISoftDeleteDataModel, ITenancyDataModel
{
    public ProductCategoryCode Code { get; set; }
    public bool IsDeleted { get; set; }
    public Guid TenantId { get; set; }
    public ICollection<Attribute> Attributes { get; set; } = null!;
    public ICollection<ImportMetadata> ImportMetadatas { get; set; } = null!;
}
