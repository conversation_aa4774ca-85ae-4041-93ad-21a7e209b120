using Auditdata.Infrastructure.Contracts.DataModel;
using Auditdata.InventoryService.Core.Abstractions.Entities;

namespace Auditdata.InventoryService.Core.Entities;

public record CPTCode : BaseEntity, ISoftDeleteDataModel, ITenancyDataModel
{
    public string Name { get; set; } = null!;
    public string Code { get; set; } = null!;
    public string? Description { get; set; }
    public bool IsActive { get; set; }
    
    public bool IsDeleted { get; set; }
    public Guid TenantId { get; set; }
}
