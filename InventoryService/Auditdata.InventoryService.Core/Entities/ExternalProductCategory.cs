namespace Auditdata.InventoryService.Core.Entities;

public enum ExternalProductCategory
{
    HearingAids,
    Earmolds,
    Batteries,
    Accessories,
    Remote,
    RICReceivers
}

public static class ExternalProductCategoryExtensions
{
    public static ProductCategoryCode? ToProductCategoryCode(this ExternalProductCategory externalProductCategory)
    {
        return externalProductCategory switch
        {
            ExternalProductCategory.HearingAids => ProductCategoryCode.HearingAids,
            ExternalProductCategory.Accessories => ProductCategoryCode.Accessories,
            ExternalProductCategory.Batteries => ProductCategoryCode.Batteries,
            ExternalProductCategory.Earmolds => ProductCategoryCode.Earmolds,
            ExternalProductCategory.Remote => ProductCategoryCode.Remote,
            ExternalProductCategory.RICReceivers => ProductCategoryCode.RICReceivers,
            _ => null
        };
    }
}
