using Auditdata.Infrastructure.Contracts.DataModel;
using Auditdata.InventoryService.Core.Abstractions.Entities;

namespace Auditdata.InventoryService.Core.Entities;

public record Supplier : BaseEntity, ISoftDeleteDataModel, ITenancyDataModel
{
    private const string RequiredFieldsUnknown = "Unknown";

    public string Name { get; set; } = null!;
    public bool IsActive { get; set; }
    public bool IsDeleted { get; set; }
    public Guid TenantId { get; set; }
    public bool IsManufacturer { get; set; }
    public string? Website { get; set; }
    public string? EmailAddress { get; set; }
    public string PhoneNumber { get; set; } = null!;
    public string? FaxNumber { get; set; }
    public string Address1 { get; set; } = null!;
    public string? Address2 { get; set; }

    public Guid? CountryId { get; set; }
    public Country? Country { get; set; }

    public Guid? StateId { get; set; }
    public string? State { get; set; }
    public string City { get; set; } = null!;
    public string? PostalCode { get; set; }
    
    public SupplierContact? SalesContact { get; set; }
    public SupplierContact? AccountReceivableContact { get; set; }

    public static Supplier Create(string name)
    {
        return new Supplier
        {
            Name = name,
            IsActive = true,
            AccountReceivableContact = new SupplierContact(),
            SalesContact = new SupplierContact(),
            City = RequiredFieldsUnknown,
            PhoneNumber = RequiredFieldsUnknown,
            State = RequiredFieldsUnknown,
            Address1 = RequiredFieldsUnknown
        };
    }
}
