using Auditdata.Infrastructure.Contracts.DataModel;
using Auditdata.InventoryService.Core.Abstractions.Entities;
using Auditdata.InventoryService.Core.Entities.Products;

namespace Auditdata.InventoryService.Core.Entities;

public record ProductMeasureType : BaseEntity, ITenancyDataModel
{
    public Guid ProductId { get; set; }
    public Product Product { get; set; } = null!;

    public Guid MeasureTypeId { get; set; }
    public MeasureType MeasureType { get; set; } = null!;

    public Guid TenantId { get; set; }

    public static ProductMeasureType Create(Guid measureTypeId)
    {
        return new ProductMeasureType { MeasureTypeId = measureTypeId };
    }

    public static ProductMeasureType Create(MeasureType measureType)
    {
        return new ProductMeasureType { MeasureType = measureType };
    }
}
