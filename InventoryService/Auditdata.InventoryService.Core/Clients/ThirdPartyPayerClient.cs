using Auditdata.InventoryService.Core.Abstractions.Clients;
using Auditdata.Microservice.Messages.RequestReply.ThirdPartyPayerContract;

namespace Auditdata.InventoryService.Core.Clients;

public class ThirdPartyPayerClient : IThirdPartyPayerClient
{
    private readonly IBus _bus;

    public ThirdPartyPayerClient(IBus bus)
    {
        _bus = bus;
    }

    public async Task<string[]> GetActiveContractHspCategoryCodesAsync(CancellationToken cancellationToken)
    {
        var client = _bus.CreateRequestClient<GetActiveContractHspCategoryCodesRequest>();
        var request = new GetActiveContractHspCategoryCodesRequest();

        var response = await client.GetResponse<GetActiveContractHspCategoryCodesReply>(request, cancellationToken);

        return response.Message.CategoryCodes;
    }

    public async Task<string[]> GetActiveContractHspServiceNumbersAsync(CancellationToken cancellationToken)
    {
        var client = _bus.CreateRequestClient<GetActiveContractHspServiceNumbersRequest>();
        var request = new GetActiveContractHspServiceNumbersRequest();

        var response = await client.GetResponse<GetActiveContractHspServiceNumbersReply>(request, cancellationToken);

        return response.Message.ServiceNumbers;
    }
}
