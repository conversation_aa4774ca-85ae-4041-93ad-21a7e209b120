using System.Text.Json.Nodes;

namespace Auditdata.InventoryService.Core.Extensions;

internal static class DescriptionExtensions
{
    private const string DescriptionJson =
        """
        {
            "blocks": [
                {
                    "key": "",
                    "text": "",
                    "type": "unstyled",
                    "depth": 0,
                    "inlineStyleRanges": [],
                    "entityRanges": [],
                    "data": {}
                }
            ],
            "entityMap": {}
        }
        """;

    /// <summary>
    /// Constructs a JSON-formatted description string containing the provided text value within the first block's text property.
    /// Handles null, empty, or whitespace input strings by returning a default JSON structure.
    /// </summary>
    /// <param name="descriptionValue">The input text value to be included in the JSON-formatted description string.</param>
    /// <returns>
    /// A JSON-formatted string containing the input text within the description structure, or a default JSON string for null or whitespace input.
    /// </returns>
    public static string ToDescription(this string? descriptionValue)
    {
        if (string.IsNullOrWhiteSpace(descriptionValue))
        {
            return "\"\"";
        }

        var descriptionNode = JsonNode.Parse(DescriptionJson);
        
        descriptionNode!["blocks"]![0]!["key"] = Guid.NewGuid().ToString();
        descriptionNode!["blocks"]![0]!["text"] = descriptionValue;
        return descriptionNode.ToJsonString();
    }

    /// <summary>
    /// Converts a JSON-formatted string description into a text value by parsing the first block's text property.
    /// Handles null, empty, or whitespace strings and returns them appropriately.
    /// </summary>
    /// <param name="description">The input JSON-formatted string description.</param>
    /// <returns>
    /// The parsed text value from the JSON string if valid;
    /// otherwise, returns null for empty strings or the original string in case of parsing errors.
    /// </returns>
    public static string? ToDescriptionValue(this string? description)
    {
        try
        {
            return string.IsNullOrWhiteSpace(description) || description == "\"\""
                ? null
                : JsonNode.Parse(description)!["blocks"]![0]!["text"]!.ToString();
        }
        catch
        {
            return description;
        }
    }
}
