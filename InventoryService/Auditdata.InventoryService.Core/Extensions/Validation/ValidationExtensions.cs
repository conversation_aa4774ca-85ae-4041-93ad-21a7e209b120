using Auditdata.Infrastructure.Contracts.DataModel;
using Auditdata.Infrastructure.Excel.Exceptions;
using Auditdata.Infrastructure.Excel.Models;
using Auditdata.InventoryService.Core.Constants;
using Auditdata.Transport.Contracts.Exceptions;

namespace Auditdata.InventoryService.Core.Extensions.Validation;

public static class ValidationExtensions
{
    public static void ValidateIsAllIdsFound<TEntity, TData>(
        this IReadOnlyCollection<TEntity> existingEntities,
        IReadOnlyCollection<ExcelImportData<TData>> codesWithFilledIds)
        where TEntity : IIdentityGuidDataModel
        where TData : IIdentityGuidDataModel
    {
        if (existingEntities.Count != codesWithFilledIds.Count)
        {
            var rows = codesWithFilledIds.Where(x => !existingEntities.Any(y => y.Id == x.Data.Id))
                .Select(x => x.RowNumber);
            throw new BusinessException(
                $"Cannot import. ID is not found in rows: {string.Join(", ", rows)}.",
                ErrorCodes.DocumentContainsNotFoundIds, rows.Cast<object>().ToArray());
        }
    }

    public static void ValidateImportResult<TData>(this ExcelImportResult<TData> importResult) where TData : IIdentityGuidDataModel
    {
        if (!importResult.IsSuccess)
        {
            var excelFormatException = importResult.Exceptions.First();
            var localizationKey = GetLocalizationKey(excelFormatException);

            throw new BusinessException(excelFormatException.Message,
                localizationKey,
                excelFormatException.PlaceholderValues);
        }
    }

    private static string GetLocalizationKey(ExcelFormatException excelFormatException)
    {
        if (excelFormatException is InvalidRecordsException)
        {
            return ErrorCodes.ExcelInvalidRecords;
        }

        if (excelFormatException is ColumnsCountException)
        {
            return ErrorCodes.ExcelWrongColumnsCount;
        }

        if (excelFormatException is InvalidHeaderException)
        {
            return ErrorCodes.ExcelWrongColumnName;
        }

        if (excelFormatException is RowsCountException)
        {
            return ErrorCodes.ExcelRowsCountExceedsMaxCount;
        }

        if (excelFormatException is WorksheetCountException
            or InvalidStartColumnException or InvalidStartRowException)
        {
            return ErrorCodes.ExcelWrongFormat;
        }

        throw new NotSupportedException($"Not supported exception type {excelFormatException.GetType()}");
    }
}
