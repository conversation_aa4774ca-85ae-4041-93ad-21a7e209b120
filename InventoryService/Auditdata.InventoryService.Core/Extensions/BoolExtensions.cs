namespace Auditdata.InventoryService.Core.Extensions;

public static class BoolExtensions
{
    public static string ToLowerString(this bool boolValue)
    {
        return boolValue ? "true" : "false";
    }
    
    public static string? ToLowerString(this bool? boolValue)
    {
        if (boolValue is null)
        {
            return null;
        }
        return boolValue.Value ? "true" : "false";
    }
}
