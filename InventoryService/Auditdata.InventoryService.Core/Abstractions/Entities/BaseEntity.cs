using Auditdata.Infrastructure.Contracts.DataModel;

namespace Auditdata.InventoryService.Core.Abstractions.Entities;

public abstract record BaseEntity : IIdentityGuidDataModel, IAuditablePersonalizedDataModel
{
    public Guid Id { get; set; }
    public DateTimeOffset CreationDate { get; set; }
    public DateTimeOffset? ChangeDate { get; set; }
    public string CreatedBy { get; set; } = null!;
    public string? ChangedBy { get; set; }
}
