using Auditdata.InventoryService.Core.Abstractions.Entities;
using Microsoft.EntityFrameworkCore;

namespace Auditdata.InventoryService.Core.Abstractions.Repositories;

public class BaseEntityRepository<TEntity> : IEntityRepository<TEntity> where TEntity : BaseEntity
{
    protected readonly IDbContext DbContext;
    protected readonly DbSet<TEntity> DbSet;

    public BaseEntityRepository(IDbContext dbContext)
    {
        DbContext = dbContext;
        DbSet = dbContext.Set<TEntity>();
    }
    
    public async Task<IEnumerable<TEntity>> GetAsync()
    {
        return await DbContext.Set<TEntity>().ToListAsync();
    }

    public async Task<TEntity?> GetByIdAsync(Guid id)
    {
        return await DbContext.Set<TEntity>().FindAsync(id);
    }

    public async Task CreateAsync(TEntity entity)
    {
        DbSet.Add(entity);
        await DbContext.SaveChangesAsync();
    }

    public async Task UpdateAsync(TEntity entity)
    {
        DbSet.Update(entity);
        await DbContext.SaveChangesAsync();
    }

    public async Task DeleteAsync(TEntity entity)
    {
        DbSet.Remove(entity);
        await DbContext.SaveChangesAsync();
    }
}
