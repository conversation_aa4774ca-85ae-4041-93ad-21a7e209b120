namespace Auditdata.InventoryService.Core.Abstractions.Repositories;

public interface IStockProductItemsRepository : IEntityRepository<StockProductItem>
{
    Task DeleteSerialNumberAsync(Guid stockProductItemId);
    Task<IEnumerable<StockProductItem>> GetByProductIdWithStockAsync(Guid productId, string? searchText, Guid? stockId, StockProductItemStatus? status);
    Task<IEnumerable<StockProductItem>> GetAvailableAsync(Guid productId, Guid? stockId);
    Task<IEnumerable<StockProductItem>> GetAvailableAsync(Guid stockProductId);
    Task<StockProductItem?> GetAvailableBySerialNumberAsync(Guid stockProductId, string serialNumber);
    Task<StockProductItem?> GetAvailableProductItemAssignedToSale(Guid saleId, Guid stockProductId, string serialNumber);
    Task<IEnumerable<StockProductItem>> GetStockProductItemsPendingTransferToStockAsync(Guid productId, Guid? stockId, string? searchText, StockProductItemStatus? status);
    Task<StockProductItem?> GetRepairProductItemAsync(Guid stockProductItemId);
    Task<StockProductItem?> GetRepairProductItemAsync(Guid productId, string serialNumber);
    Task<StockProductItem?> GetItemLinkedToSale(Guid saleId, Guid stockProductId);
    Task<StockProductItem?> GetByIdWithStockProductAsync(Guid stockProductItemId);
    Task<StockProductItem?> GetReservedAsync(Guid stockProductId, Guid saleId);
    Task<StockProductItem?> GetSoldAsync(Guid stockProductId, Guid saleId);
    Task<IEnumerable<StockProductItem>> GetByIdsAsync(IEnumerable<Guid> ids);
    Task<IEnumerable<StockProductItem>> GetReservedByOrderAsync(Guid locationId, IEnumerable<Guid> orderIds, CancellationToken cancellationToken);
}
