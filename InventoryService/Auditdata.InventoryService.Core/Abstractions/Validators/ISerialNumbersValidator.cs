using Auditdata.InventoryService.Core.Features.StockProductItems.Dtos;

namespace Auditdata.InventoryService.Core.Abstractions.Validators;

public interface ISerialNumbersValidator
{
    Task CheckDuplicatedSerialNumbers(Guid manufacturerId, IEnumerable<string> serialNumbers,
        CancellationToken cancellationToken);

    Task CheckDuplicatedSerialNumbers(List<StockProductItemDto> items, CancellationToken cancellationToken);

    Task<CheckDuplicatedSerialNumbersResult> CheckDuplicates(
        IReadOnlyList<(Guid ProductId, string SerialNumber)> items, CancellationToken cancellationToken);
}

public record CheckDuplicatedSerialNumbersResult(IReadOnlyList<string> DuplicatedSerialNumbers);
