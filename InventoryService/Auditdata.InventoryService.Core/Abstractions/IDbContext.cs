using Auditdata.InventoryService.Core.Entities.AuditRequests;
using Auditdata.InventoryService.Core.Entities.Products;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Attribute = Auditdata.InventoryService.Core.Entities.Attribute;
using State = Auditdata.InventoryService.Core.Entities.State;

namespace Auditdata.InventoryService.Core.Abstractions;

public interface IDbContext : IDisposable
{
    DbSet<BatteryType> BatteryTypes { get; }
    DbSet<MeasureType> MeasureTypes { get; }
    DbSet<HearingAidType> HearingAidTypes { get; }
    DbSet<Product> Products { get; }
    DbSet<ProductCategory> ProductCategories { get; }
    DbSet<ProductCategoryAccountCode> ProductCategoryAccountCodes { get; }
    DbSet<ProductColor> ProductColors { get; }
    DbSet<Color> Colors { get; }
    DbSet<ProductBatteryType> ProductBatteryTypes { get; }
    DbSet<ProductMeasureType> ProductMeasureTypes { get; }
    DbSet<CPTCode> CPTCodes { get; }
    DbSet<ProductCPTCode> ProductCPTCodes { get; }
    
    DbSet<Bundle> Bundles { get; }
    DbSet<BundleProduct> BundleProducts { get; }

    DbSet<Manufacturer> Manufacturers { get; }
    DbSet<Stock> Stocks { get; }
    DbSet<StockProduct> StockProducts { get; }
    DbSet<StockTransaction> StockTransactions { get; }
    DbSet<StockProductItem> StockProductItems { get; }
    DbSet<StockProductItemLog> StockProductItemLogs { get; }
    DbSet<StockSetting> StockSettings { get; set; }
    DbSet<Supplier> Suppliers { get; }
    DbSet<NhsContractProduct> NhsContractProducts { get; }
    DbSet<Pathway> Pathways { get; }
    DbSet<ProductPathway> ProductPathways { get; }
    DbSet<StockAdjustmentReason> StockAdjustmentReasons { get; }
    DbSet<Country> Countries { get; }
    DbSet<TransfersState> Transfers { get; }
    
    DbSet<ExternalProductImport> ExternalProductImports { get; }
    DbSet<ExternalProduct> ExternalProducts { get; }
    DbSet<ExternalProductOption> ExternalProductOptions { get; }
    
    DbSet<Attribute> Attributes { get; }
    DbSet<AttributeProductCategory> AttributeProductCategories { get; }
    DbSet<ProductAttribute> ProductAttributes { get; }

    DbSet<StockKeepingUnit> StockKeepingUnits { get; }
    DbSet<Sku> Skus { get; }
    DbSet<SkuConfig> SkuConfigs { get; }
    DbSet<SkuAttribute> SkuAttributes { get; }
    DbSet<ImportMetadata> ImportMetadatas { get; }

    DbSet<AuditRequest> AuditRequests { get; }
    DbSet<AuditProduct> AuditProducts { get; }
    DbSet<AuditLocation> AuditLocations { get; }
    DbSet<AuditProductSerialNumber> AuditProductSerialNumbers { get; }
    DbSet<AuditRequestLog> AuditRequestLogs { get; }

    DbSet<ProductSuggestedProduct> ProductSuggestedProducts { get; set; }

    DbSet<State> States { get; set; }

    DatabaseFacade Database { get; }
    DbSet<TEntity> Set<TEntity>() where TEntity : class;
    Task<int> SaveChangesAsync(CancellationToken cancellationToken = default);
    Task InsertBulkAsync<T>(IList<T> entities, CancellationToken cancellationToken = default) where T : class;
    Task<IAsyncDisposable> AcquireLockAsync(string name, CancellationToken cancellationToken = default);
    void SetOperationTimeout(TimeSpan timeout);
    
    ChangeTracker ChangeTracker { get; }
}
