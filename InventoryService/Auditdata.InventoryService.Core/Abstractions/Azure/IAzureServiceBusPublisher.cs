using Auditdata.InventoryService.Contracts.Commands;
using Auditdata.Microservice.Messages.Events.Inventory;
using Auditdata.Microservice.Messages.Events.Inventory.Attributes;

namespace Auditdata.InventoryService.Core.Abstractions.Azure;

public interface IAzureServiceBusPublisher
{
    Task PublishStockProductCreated(StockProductCreated message);
    Task PublishStockProductDeleted(StockProductDeleted message);
    Task PublishStockProductCreated(IEnumerable<StockProductCreated> messages);
    Task PublishAddStockTransactionCommand(AddStockTransactionCommand message);
    Task PublishAddProductToAllStocksCommand(AddProductToAllStocksCommand message);
    Task PublishAddProductsToAllStocksCommand(IEnumerable<AddProductToAllStocksCommand> messages);
    Task PublishBatteryTypeUpdated(BatteryTypeUpdated message);
    Task PublishBatteryTypeCreated(BatteryTypeCreated message);
    Task PublishBatteryTypeDeleted(BatteryTypeDeleted message);
    Task PublishAttributeUpdated(AttributeUpdated message);
    Task PublishAttributeCreated(AttributeCreated message);
    Task PublishAttributeDeleted(AttributeDeleted message);
}
