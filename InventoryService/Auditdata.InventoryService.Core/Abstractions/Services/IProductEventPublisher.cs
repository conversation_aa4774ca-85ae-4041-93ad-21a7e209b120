using Auditdata.InventoryService.Core.Entities.Products;

namespace Auditdata.InventoryService.Core.Abstractions.Services;

public interface IProductEventPublisher
{
    Task PublishProductCreatedAsync(Product product, CancellationToken cancellationToken);
    Task PublishProductsCreatedAsync(IEnumerable<Product> products, CancellationToken cancellationToken);
    Task PublishProductUpdatedAsync(Product product, bool isActiveChanged, CancellationToken cancellationToken);
}
