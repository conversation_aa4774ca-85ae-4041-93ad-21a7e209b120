version: '3.4'

services:
  auditdata.inventoryservice.api:
    image: ${DOCKER_REGISTRY-}auditdatainventoryserviceapi
    restart: always
    build:
      context: .
      dockerfile: Auditdata.InventoryService.Api/Dockerfile
    ports:
      - "8080:80"
    depends_on:
        - mssql
  mssql:
    image: ${DOCKER_REGISTRY-}mssql
    build:
        context: .
        dockerfile: mssql.Dockerfile
    ports:
        - "1433:1433"
    environment:
        - ACCEPT_EULA=Y
        - SA_PASSWORD=SuperStrognPassword@123