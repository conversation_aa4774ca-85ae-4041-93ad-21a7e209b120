# Please refer https://aka.ms/HTTPSinContainer on how to setup an https developer certificate for your ASP .NET Core service.

version: '3.4'

services:
  auditdatainvoicingserviceapi:
    image: auditdatainventoryserviceapi
    build:
      context: .
      dockerfile: Auditdata.InventoryService.Api/Dockerfile
    ports:
      - "8080:80"
      - "7281:443"
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=https://+:443;http://+:80
      - ASPNETCORE_Kestrel__Certificates__Default__Password=auditdata
      - ASPNETCORE_Kestrel__Certificates__Default__Path=/https/aspnetapp.pfx
    volumes:
      - ~/.vsdbg:/remote_debugger:rw
      - ~/.aspnet/https:/https:ro
  mssql:
    image: ${DOCKER_REGISTRY-}mssql
    build:
      context: .
      dockerfile: mssql.Dockerfile
    ports:
      - "1433:1433"
    environment:
      - ACCEPT_EULA=Y
      - SA_PASSWORD=SuperStrognPassword@123